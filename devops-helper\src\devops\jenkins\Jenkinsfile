#!/usr/bin/env groovy

def AGENT = 'sa-hsbc-11465671-unity-linux-unity-dev-jenkins-agent'

// Function to get available projects based on build type
@NonCPS
def getProjectsByBuildType(buildType = 'maven') {
    try {
        def projects = []

        if (buildType == 'maven') {
            // Maven projects
            projects = ['all', 'change-dashboard', 'risc', 'spring']
        } else if (buildType == 'nodejs') {
            // Node.js projects
            projects = ['all', 'hss-unity-admin']
        } else {
            // Default fallback
            projects = ['all', 'change-dashboard', 'risc', 'spring', 'hss-unity-admin']
        }

        return projects

    } catch (Exception e) {
        // Fallback to static list if dynamic discovery fails
        echo "Warning: Could not dynamically discover projects: ${e.message}"
        return ['all', 'change-dashboard', 'risc', 'spring']
    }
}

// Legacy function for backward compatibility (deprecated)
@NonCPS
def getAvailableProjects() {
    return getProjectsByBuildType('maven')
}

pipeline {
    agent { label env.AGENT_LABEL ?: AGENT }
    
    parameters {        
        choice(
            name: 'BUILD_TYPE',
            choices: ['maven', 'nodejs'],
            description: 'Select the build type'
        )
        choice(
            name: 'PROJECT_TO_BUILD',
            choices: getProjectsByBuildType(params.BUILD_TYPE ?: 'maven'),
            description: 'Select the project to build (filtered by build type)'
        )
        booleanParam(
            name: 'STAGE_0_CHECKOUT',
            defaultValue: true,
            description: 'Stage 0: Checkout repository'
        )
        booleanParam(
            name: 'STAGE_1_PROJECT_BUILD',
            defaultValue: true,
            description: 'Stage 1: Project build and package'
        )
        booleanParam(
            name: 'STAGE_2_SECURITY_SCAN',
            defaultValue: false,
            description: 'Stage 2: Security scanning (SAST, container scan)'
        )
        booleanParam(
            name: 'STAGE_3_IMAGE_BUILD',
            defaultValue: true,
            description: 'Stage 3: Docker image build and push'
        )       
        booleanParam(
            name: 'STAGE_4_TAG_BUILD_VERSION',
            defaultValue: true,
            description: 'Stage 4: Tag the build version'
        )
        booleanParam(
            name: 'STAGE_5_DEPLOY',
            defaultValue: false,
            description: 'Stage 5: Deploy to Kubernetes'
        )
        choice(
            name: 'TARGET_ENVIRONMENT',
            choices: ['dev', 'uat', 'prod'],
            description: 'Target environment for deployment'
        )
        booleanParam(
            name: 'SKIP_TESTS',
            defaultValue: true,
            description: 'Skip Maven tests in build stage'
        )
        booleanParam(
            name: 'ENABLE_SAST_SCAN',
            defaultValue: true,
            description: 'Enable SAST scanning in security stage'
        )
        booleanParam(
            name: 'ENABLE_CONTAINER_SCAN',
            defaultValue: true,
            description: 'Enable container scanning after image build'
        )
        booleanParam(
            name: 'PUSH_TO_NEXUS',
            defaultValue: true,
            description: 'Push Docker images to Nexus registry'
        )
        booleanParam(
            name: 'PUSH_TO_GCR',
            defaultValue: true,
            description: 'Push Docker images to GCR registry'
        )
        booleanParam(
            name: 'WAIT_FOR_DEPLOYMENT',
            defaultValue: true,
            description: 'Wait for deployment to complete'
        )
        booleanParam(
            name: 'PARALLEL_EXECUTION',
            defaultValue: false,
            description: 'Run projects in parallel (when building multiple)'
        )
        string(
            name: 'CHANGE_ORDER',
            defaultValue: '',
            description: 'SNOW change order number (required for UAT/PROD)'
        )
        choice(
            name: 'JDK_VERSION',
            choices: ['21', '17'],
            description: 'Select the JDK version to use'
        )
        string(
            name: 'GIT_BRANCH',
            defaultValue: '*/development',
            description: 'Git branch to checkout'
        )
        string(
            name: 'MAVEN_BUILD_BRANCH',
            defaultValue: 'development-branch-build',
            description: 'Build branch identifier for Maven (used in devops-helper) - determine if build is push to nexus'
        )
        string(
            name: 'MAVEN_OPTS',
            defaultValue: '-Xmx2048m -Djavax.net.ssl.trustStore=/etc/pki/java/cacerts -Dfile.encoding=UTF-8 -Dmaven.wagon.http.ssl.insecure=true -Dmaven.wagon.http.ssl.allowall=true -Dhttp.keepAlive=true -Dhttp.maxConnections=20 -Dhttps.keepAlive=true -Dhttps.maxConnections=20',
            description: 'Maven build options'
        )
        booleanParam(
            name: 'DOCKER_BUILD_RECURSIVE',
            defaultValue: true,
            description: 'Build Docker images for all sub-modules recursively'
        )
        string(
            name: 'DOCKER_REGISTRY_NEXUS_URL_DEV',
            defaultValue: 'nexus3.hk.hsbc:18080/hsbc-11465671-mssbbd-unity',
            description: 'Nexus Docker registry URL for development'
        )
        string(
            name: 'DOCKER_REGISTRY_GCR_URL_DEV',
            defaultValue: 'us-docker.pkg.dev/hsbc-11465671-unity-dev/gcr.io',
            description: 'GCR Docker registry URL for development'
        )
        string(
            name: 'DOCKER_REGISTRY_NEXUS_PATH',
            defaultValue: '/com/hsbc/gbm/hss/unity/i15n/',
            description: 'Nexus Docker registry path'
        )
        string(
            name: 'DOCKER_REGISTRY_GCR_PATH',
            defaultValue: '/unity/i15n/sit/',
            description: 'GCR Docker registry path'
        )
    }
    
    environment {
        // Jenkins environment variables
        ICE_AUTH_TOKEN = credentials('ICE_AUTH_TOKEN')
        CYBERFLOW_USER = credentials('ICE_AUTH_TOKEN')
        CYBERFLOW_PASS = credentials('ICE_AUTH_TOKEN')
        
        // Registry configuration
        DOCKER_REGISTRY_NEXUS_URL_DEV = "${params.DOCKER_REGISTRY_NEXUS_URL_DEV}"
        DOCKER_REGISTRY_GCR_URL_DEV = "${params.DOCKER_REGISTRY_GCR_URL_DEV}"
        DOCKER_REGISTRY_NEXUS_PATH = "${params.DOCKER_REGISTRY_NEXUS_PATH}"
        DOCKER_REGISTRY_GCR_PATH = "${params.DOCKER_REGISTRY_GCR_PATH}"
        
        // Build configuration
        MAVEN_OPTS = "${params.MAVEN_OPTS}"
        JDK_VERSION = "${params.JDK_VERSION}"
        BUILD_TYPE = "${params.BUILD_TYPE}"
        
        // Pipeline configuration
        PROJECTS_TO_BUILD = "${params.PROJECT_TO_BUILD}"
        TARGET_ENV = "${params.TARGET_ENVIRONMENT}"
        NAMESPACE = "ns-i15n-${params.TARGET_ENVIRONMENT}"
        
        // Git information
        GIT_COMMIT_SHORT = "${env.GIT_COMMIT?.take(8) ?: 'unknown'}"
        BUILD_VERSION = "${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
        GIT_BRANCH = "${params.GIT_BRANCH}"
        
        // SNOW integration
        SNOW_CHANGE_ORDER = "${params.CHANGE_ORDER}"
        
        // devops-helper configuration
        MVN_SKIP_TESTS = "${params.SKIP_TESTS}"
        MVN_MAVEN_TEST_SKIP = "${params.SKIP_TESTS}"
        MVN_BUILD_BRANCH = "${params.MAVEN_BUILD_BRANCH}"
        DOCKER_BUILD_ENABLE = "true"
        DOCKER_PUSH_NEXUS = "${params.PUSH_TO_NEXUS}"
        DOCKER_PUSH_GCR = "${params.PUSH_TO_GCR}"
        SAST_SCAN = "${params.ENABLE_SAST_SCAN}"
        CONTAINER_SCAN = "${params.ENABLE_CONTAINER_SCAN}"
        EIM_ID = "EIM0123456"  // Replace with actual EIM ID
        SAST_CONFIGURATION_NAME = "unity-i15n-sast-scan"
    }
    
    options {
        timeout(time: 90, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: '10'))
        disableConcurrentBuilds()
        timestamps()
    }
    
    stages {
        stage('Initialize') {
            steps {
                script {
                    echo "=== Pipeline Configuration ==="
                    echo "Project(s): ${params.PROJECT_TO_BUILD}"
                    echo "Target Environment: ${params.TARGET_ENVIRONMENT}"
                    echo "Namespace: ${env.NAMESPACE}"
                    echo "Parallel Execution: ${params.PARALLEL_EXECUTION}"
                    echo "Build Version: ${env.BUILD_VERSION}"
                    echo "Git Commit: ${env.GIT_COMMIT_SHORT}"
                    echo ""
                    echo "Stages to execute:"
                    echo "  Stage 0 (Checkout): ${params.STAGE_0_CHECKOUT}"
                    echo "  Stage 1 (Build): ${params.STAGE_1_PROJECT_BUILD}"
                    echo "  Stage 2 (Security): ${params.STAGE_2_SECURITY_SCAN}"
                    echo "  Stage 3 (Image): ${params.STAGE_3_IMAGE_BUILD}"
                    echo "  Stage 4 (Deploy): ${params.STAGE_5_DEPLOY}"
                    echo "  Stage 5 (Tag Build Version): ${params.STAGE_4_TAG_BUILD_VERSION}"

                    // Validate change order for non-dev environments
                    if (params.TARGET_ENVIRONMENT != 'dev' && params.STAGE_5_DEPLOY) {
                        if (!params.CHANGE_ORDER) {
                            error "Change order is required for ${params.TARGET_ENVIRONMENT} deployment"
                        }
                        echo "Change Order: ${params.CHANGE_ORDER}"
                        
                        // Validate change order format (example: CHG0123456)
                        if (!params.CHANGE_ORDER.matches(/CHG\d{7}/)) {
                            error "Invalid change order format. Expected format: CHG1234567"
                        }
                    }
                    
                    // Set projects list
                    if (params.PROJECT_TO_BUILD == 'all') {
                        // Get all available projects dynamically
                        env.PROJECT_LIST = getAllProjectsFromWorkspace()
                    } else {
                        env.PROJECT_LIST = params.PROJECT_TO_BUILD
                    }
                    echo "Projects to build: ${env.PROJECT_LIST}"
                    
                    // Set build description
                    currentBuild.description = "Projects: ${params.PROJECT_TO_BUILD} | Env: ${params.TARGET_ENVIRONMENT} | Change: ${params.CHANGE_ORDER ?: 'N/A'}"
                    
                }
            }
        }
        
        stage('Stage 0: Checkout') {
            when {
                expression { params.STAGE_0_CHECKOUT }
            }
            steps {
                script {
                    echo "=== Stage 0: Repository Checkout ==="

                    // Checkout main repository
                    checkoutProject()
                    
                    // Verify required files exist
                    sh '''
                        #echo "Verifying devops-helper..."
                        #ls -la src/devops/scripts/build/actions.bash
                        
                        tree ./ || true
                    '''

                    // Validate prerequisites
                    validatePrerequisites()       

                    echo "✅ Checkout completed successfully"
                }
            }
        }
        
        stage('Stage 1: Project Build') {
            when {
                expression { params.STAGE_1_PROJECT_BUILD }
            }
            steps {
                script {
                    echo "=== Stage 1: Project Build ==="
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def buildTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            buildTasks[proj] = {
                                if (env.BUILD_TYPE == 'maven') {
                                    runMavenBuild(proj)
                                } else if (env.BUILD_TYPE == 'nodejs') {
                                    runNpmBuild(proj)
                                }
                            }
                        }
                        parallel buildTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            if (env.BUILD_TYPE == 'maven') {
                                runMavenBuild(proj)
                            } else if (env.BUILD_TYPE == 'nodejs') {
                                runNpmBuild(proj)
                            }
                        }
                    }
                    
                    echo "✅ Maven build stage completed"
                }
            }
        }
        
        stage('Stage 2: Security Scanning') {
            when {
                expression { params.STAGE_2_SECURITY_SCAN }
            }
            steps {
                script {
                    echo "=== Stage 2: Security Scanning ==="
                    
                    def projects = env.PROJECT_LIST.split(',')  
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            scanTasks[proj] = {
                                runSecurityScan(proj)
                            }
                        }
                        parallel scanTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runSecurityScan(proj)
                        }
                    }
                    
                    echo "✅ Security scanning stage completed"
                }
            }
        }
        
        stage('Stage 3: Image Build & Push') {
            when {
                expression { params.STAGE_3_IMAGE_BUILD }
            }
            steps {
                script {
                    echo "=== Stage 3: Docker Image Build & Push ==="
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def imageTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            imageTasks[proj] = {
                                runImageBuild(proj)
                            }
                        }
                        parallel imageTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runImageBuild(proj)
                        }
                    }
                    
                    echo "✅ Image build stage completed"
                }
            }
        }        
        stage('Stage 4: Tag Build Version') {
            when {
                expression { params.STAGE_4_TAG_BUILD_VERSION }
            }
            steps {
                script {
                    echo "=== Stage 5: Tag Build Version ==="
                    runTagBuildVersion()
                    echo "✅ Tag build version stage completed"
                }
            }
        }
        stage('Stage 5: Deploy') {
            when {
                expression { params.STAGE_5_DEPLOY }
            }
            steps {
                script {
                    echo "=== Stage 4: Deployment ==="
                    
                    // Verify change order for non-dev environments
                    if (params.TARGET_ENVIRONMENT != 'dev') {
                        echo "Validating change order: ${params.CHANGE_ORDER}"
                        // Add change order validation logic here if needed
                    }
                    
                    def projects = env.PROJECT_LIST.split(',')
                    def deployTasks = [:]
                    
                    if (params.PARALLEL_EXECUTION && projects.size() > 1) {
                        // Parallel execution
                        for (project in projects) {
                            def proj = project.trim()
                            deployTasks[proj] = {
                                runDeploy(proj)
                            }
                        }
                        parallel deployTasks
                    } else {
                        // Sequential execution
                        for (project in projects) {
                            def proj = project.trim()
                            runDeploy(proj)
                        }
                    }
                    
                    echo "✅ Deployment stage completed"
                }
            }
        }
    }
    
    post {        
        always {
            script {
                echo "=== Pipeline Summary ==="
                echo "Project(s): ${params.PROJECT_TO_BUILD}"
                echo "Environment: ${params.TARGET_ENVIRONMENT}"
                echo "Build Status: ${currentBuild.currentResult}"
                echo "Duration: ${currentBuild.durationString}"
                echo "Build Version: ${env.BUILD_VERSION}"       
            }
        }
        success {
            script {
                echo "✅ Pipeline completed successfully!"
            }
        }
        failure {
            script {
                echo "❌ Pipeline failed!"
                echo "Check the logs above for specific error details"
            }
        }        
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}

// Helper functions using devops-helper actions
def runMavenBuild(project) {
    echo "Building Maven project: ${project}"
    sh """
        echo "=== Maven Build: ${project} ===" 
        export mvn_modules=${project}
        export mvn_skip_tests=${env.MVN_SKIP_TESTS}     
        export mvn_maven_test_skip=${env.MVN_MAVEN_TEST_SKIP}
        export mvn_build_branch=${env.MVN_BUILD_BRANCH}
        export mvn_opts="${env.MAVEN_OPTS}"
        export jdk_version=${env.JDK_VERSION}
        export build_action=mvn_build
        source ${env.WORKSPACE}/src/devops/scripts/build/actions.bash
        main \${build_action}
    """
}

// Helper functions using devops-helper actions
def runNpmBuild(project) {
    echo "Building Maven project: ${project}"
    sh """
        echo "=== NPM Build: ${project} ===" 
        source /opt/hsbc/hss/apps/nodes/.nodeuse_rc
        use-node-15
        npm -version
        npm install -g node-gyp-build
        npm install -g gulp
        cd ${project}
        npm i
        npm run build
        cd -
    """
}

def runSecurityScan(project) {
    echo "Running security scan for: ${project}"

    // SAST Scan
    if (params.ENABLE_SAST_SCAN) {
        withEnv([
            "sast_scan=${env.SAST_SCAN}",
            "EIM_ID=${env.EIM_ID}",
            "SAST_CONFIGURATION_NAME=${env.SAST_CONFIGURATION_NAME}"
        ]) {
            sh """
                echo "=== SAST Scan: ${project} ==="
                devops-helper/src/devops/scripts/build/actions.bash sast_scan
            """
        }
    }

    // Container Scan (will be run after image build)
    echo "Container scan will be performed after image build"
}

def runImageBuild(project) {
    echo "Building Docker image for: ${project}"

    // Set environment variables for devops-helper
    withCredentials([usernamePassword(credentialsId: 'NEXUS3_CONNECT', passwordVariable: 'NEXUS_CRED', usernameVariable: 'NEXUS_USER')]) {       
          sh """
              export nexus_user=$NEXUS_USER
              export nexus_cred=$NEXUS_CRED
              export docker_build_enable=${env.DOCKER_BUILD_ENABLE}              
              export docker_build_enable=${env.DOCKER_BUILD_ENABLE}
              export docker_push_nexus=${env.DOCKER_PUSH_NEXUS}
              export docker_push_gcr=${env.DOCKER_PUSH_GCR}
              export docker_registry_nexus_url_dev=${env.DOCKER_REGISTRY_NEXUS_URL_DEV}
              export docker_registry_gcr_url_dev=${env.DOCKER_REGISTRY_GCR_URL_DEV}
              export docker_registry_nexus_path=${env.DOCKER_REGISTRY_NEXUS_PATH}
              export docker_registry_gcr_path=${env.DOCKER_REGISTRY_GCR_PATH}              
              export docker_build_recursive=${env.DOCKER_BUILD_RECURSIVE}
              export docker_image_name_prefix=unity2-
              export jdk_version=${env.JDK_VERSION}
              export build_action=docker_build
              export build_type=${env.BUILD_TYPE}
              export DOCKER_CONFIG=${env.WORKSPACE}/.docker
              [[ "${env.DOCKER_BUILD_RECURSIVE}" == "false" ]] && export mvn_modules=${project}
              
              echo "Logging into Nexus Docker registry..."
              docker login nexus3.hk.hsbc:18080/hsbc-11465671-mssbbd-unity -u $NEXUS_USER --password-stdin <<< $NEXUS_CRED
              docker login nexus3.systems.uk.hsbc:18096 -u $NEXUS_USER --password-stdin <<< $NEXUS_CRED
 
              echo "Logging into GCR Docker registry..."
              gcloud auth configure-docker us-docker.pkg.dev --quiet
              docker login us-docker.pkg.dev
              echo "Logging into Docker registry complete"
              echo "Executing devops-helper for image build..."
              
              if [[ "${env.DOCKER_BUILD_RECURSIVE}" != "true" ]]; then
                # Copy default Dockerfile if not found
                if [[ ! -f "${project}/Dockerfile" ]]; then                
                  echo "Dockerfile not found in ${project}"
                  cp ${env.WORKSPACE}/src/devops/scripts/build/Dockerfile ${project}/Dockerfile
                fi
              else
                # Change to project directory for submodule builds
                echo "Changing to project directory: ${project}"
                cd ${project}
              fi
              
              echo "=== Image Build: ${project} ==="              
              source ${env.WORKSPACE}/src/devops/scripts/build/actions.bash 
              main \${build_action}
          """
    }    

    // Container Security Scan
    if (params.ENABLE_CONTAINER_SCAN) {
        withEnv([
            "mvn_modules=${project}",
            "container_scan=${env.CONTAINER_SCAN}",
            "EIM_ID=${env.EIM_ID}",
            "docker_registry_nexus_url_dev=${env.DOCKER_REGISTRY_NEXUS_URL_DEV}",
            "docker_registry_nexus_path=${env.DOCKER_REGISTRY_NEXUS_PATH}",
            "docker_image_name_prefix=${project}-"
        ]) {
            sh """
                echo "=== Container Scan: ${project} ==="
                #source ${env.WORKSPACE}/src/devops/scripts/build/actions.bash 
            """
        }
    }
}

def runDeploy(project) {
    echo "Deploying project: ${project}"

    def waitFlag = params.WAIT_FOR_DEPLOYMENT ? '--wait' : ''

    sh """
        echo "=== Deploy: ${project} ==="
        chmod +x gke-deployment/deploy.sh
        cd gke-deployment
        ./deploy.sh ${project} ${env.NAMESPACE} 5 start
    """

    // Verify deployment
    if (params.WAIT_FOR_DEPLOYMENT) {
        sh """
            echo "Waiting for deployment to complete..."
            kubectl rollout status deployment/${project} -n ${env.NAMESPACE} --timeout=300s
        """
    }
}

def runTagBuildVersion() {
    def projects = env.PROJECT_LIST.split(',')
    for (project in projects) {
        def proj = project.trim()
        def prefix = env.DOCKER_REGISTRY_GCR_PATH.replaceAll('/', '')
        echo "Tagging project: ${proj} with version: ${env.BUILD_VERSION}"
        withCredentials([usernamePassword(credentialsId: 'STASH_ACCESS', usernameVariable: 'GIT_USER', passwordVariable: 'GIT_PASS')]) {
            sh """
                cd ${proj}
                git config user.email "<EMAIL>"
                git config user.name "Jenkins CI"
                if [[ \"${env.BUILD_TYPE}\" = \"maven\" ]]; then
                  version=\$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout -f ./pom.xml)
                else
                  version=\$(grep -E '^   \"version\":' package.json | sed -E 's/.*\"version\": \"(.+)\".*/\\1/')
                fi 
                TAG_NAME=${prefix}-\$version-\$(date \"+%Y%m%d%H%M%S\")
                commit=\$(git rev-parse HEAD)
                existingTag=\$(git tag --list --points-at \$commit | grep ${prefix}-\$version- || true)
                if [[ \"\$existingTag\" != \"${prefix}-\$version-*\" ]]; then
                    git tag \$TAG_NAME -m \"\$TAG_NAME\"
                    git remote set-url origin https://\$GIT_USER:\$<EMAIL>/scm/unity-i15n-poc/${proj}.git
                    git push origin \$TAG_NAME
                    echo \$TAG_NAME
                else
                    TAG_NAME=\$existingTag
                    echo \$TAG_NAME
                fi
            """
        }
    }
}

def getAllProjectsFromWorkspace() {
    echo "Discovering projects in workspace..."

    def projects = []
    def workspaceContents = sh(
        script: 'find . -maxdepth 2 -name "pom.xml" -type f | grep -v target | head -20',
        returnStdout: true
    ).trim().split('\n')

    for (pomPath in workspaceContents) {
        if (pomPath && pomPath != '.') {
            def projectDir = pomPath.replaceAll('/pom.xml', '').replaceAll('^\\./+', '')
            if (projectDir && projectDir != '.' && !projectDir.contains('/')) {
                // Verify it's a valid Java project
                if (fileExists("${projectDir}/src") &&
                    !projectDir.startsWith('.') &&
                    !projectDir.equals('target')) {
                    projects.add(projectDir)
                }
            }
        }
    }

    if (projects.size() == 0) {
        echo "No projects found, using fallback list"
        return 'change-dashboard,risc'
    }

    def projectList = projects.join(',')
    echo "Discovered projects: ${projectList}"
    return projectList
}

def validatePrerequisites() {
    echo "Validating pipeline prerequisites..."

    // Check required tools
    sh '''
        echo "Checking required tools..."
        java -version
        mvn -version
        docker --version
        kubectl version --client
        helm version --client
    '''

    // Validate project directories
    def projects = env.PROJECT_LIST.split(',')
    for (project in projects) {
        def proj = project.trim()
        echo "Validating project: ${proj}"

        if (!fileExists("${proj}/pom.xml") && env.BUILD_TYPE == 'maven') {
            error "Project ${proj} does not have a pom.xml file"
        }

        // Check if Dockerfile exists, if not, warn but don't fail
        if (!fileExists("${proj}/Dockerfile")) {
            echo "⚠️  Warning: Project ${proj} does not have a Dockerfile - will be created during pipeline"
        }

        // Check if Helm values exist, if not, warn but don't fail
        if (!fileExists("gke-deployment/helm-charts/i15n-helmchart/${proj}/values.yaml")) {
            echo "⚠️  Warning: Helm values not found for project ${proj} - will be created during pipeline"
        }

        // Add condition to check if submodule is included
        // Check if the project is a Maven submodule by inspecting the parent pom.xml
        if (env.BUILD_TYPE == 'maven') {
          def parentPomPath = "${env.WORKSPACE}/${proj}/pom.xml"
          if (fileExists(parentPomPath)) {
              def parentPom = readFile(parentPomPath)
              if (parentPom.contains("<module>")) {
                  echo "✅ Maven submodule in pom.xml, setting DOCKER_BUILD_RECURSIVE to true"
                  env.DOCKER_BUILD_RECURSIVE = "true"
              } else {
                  echo "✅ No Maven submodule in pom.xml, setting DOCKER_BUILD_RECURSIVE to false"
                  env.DOCKER_BUILD_RECURSIVE = "false"
              }
          } else {
              echo "⚠️  Warning: Parent pom.xml not found in workspace, cannot verify submodule inclusion"
          }
        } else {
          echo "✅ Build type is not Maven, skipping submodule check"
          env.DOCKER_BUILD_RECURSIVE = "false"
        }

        echo "✅ Project ${proj} validation completed"
    }

    echo "✅ Prerequisites validation passed"
}

def getDomainForEnvironment(environment) {
    switch(environment) {
        case 'dev':
            return 'hsbc-11465671-unity-dev.dev.gcp.cloud.hk.hsbc'
        case 'uat':
            return 'hsbc-11465671-unity-uat.uat.gcp.cloud.hk.hsbc'
        case 'prod':
            return 'hsbc-11465671-unity-prod.prod.gcp.cloud.hk.hsbc'
        default:
            return 'unknown'
    }
}

def sendNotifications() {
    def status = currentBuild.currentResult
    def projects = env.PROJECT_LIST.split(',')
    def environment = params.TARGET_ENVIRONMENT
    def changeOrder = params.CHANGE_ORDER ?: 'N/A'

    def message = """
Pipeline ${status}: ${env.JOB_NAME} #${env.BUILD_NUMBER}
Projects: ${params.PROJECT_TO_BUILD}
Environment: ${environment}
Change Order: ${changeOrder}
Duration: ${currentBuild.durationString}
Build Version: ${env.BUILD_VERSION}

Build URL: ${env.BUILD_URL}
"""

    // Email notification
    if (status == 'FAILURE') {
        emailext (
            subject: "❌ Pipeline Failed: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
            body: message,
            to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}",
            attachLog: true
        )
    } else if (status == 'SUCCESS' && environment != 'dev') {
        emailext (
            subject: "✅ Pipeline Success: ${env.JOB_NAME} #${env.BUILD_NUMBER}",
            body: message,
            to: "${env.CHANGE_AUTHOR_EMAIL ?: '<EMAIL>'}"
        )
    }

    // Slack notification (if configured)
    try {
        def color = status == 'SUCCESS' ? 'good' : 'danger'
        def emoji = status == 'SUCCESS' ? ':white_check_mark:' : ':x:'

        slackSend(
            channel: '#unity-deployments',
            color: color,
            message: "${emoji} Pipeline ${status}: ${env.JOB_NAME} #${env.BUILD_NUMBER}\nProjects: ${params.PROJECT_TO_BUILD} | Env: ${environment}"
        )
    } catch (Exception e) {
        echo "Slack notification failed: ${e.message}"
    }
}

def checkoutProject(branch = env.GIT_BRANCH) {
    def projects = env.PROJECT_LIST.split(',')
    for (project in projects) {
        def proj = project.trim()
        echo "Checking out project: ${proj}"
    
        // Define the repository URL
        def repoUrl = " https://gbmt-bitbucket.prd.fx.gbm.cloud.uk.hsbc/scm/unity-i15n-poc/${proj}.git"
    
        // Checkout the latest commit only
        checkout([
            $class: 'GitSCM',
            branches: [[name: branch]],
            doGenerateSubmoduleConfigurations: false,
            extensions: [[$class: 'SparseCheckoutPaths', sparseCheckoutPaths: []], [$class: 'CloneOption', shallow: true, noTags: true, reference: '', timeout: 10], [$class: 'RelativeTargetDirectory', relativeTargetDir: proj]],
            submoduleCfg: [],
            userRemoteConfigs: [[url: repoUrl, credentialsId: 'STASH_ACCESS']]
        ])
        echo "✅ Checkout completed for project: ${proj}"
    }
}