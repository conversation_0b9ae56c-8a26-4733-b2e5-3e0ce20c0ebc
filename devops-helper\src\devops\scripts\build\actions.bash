#!/bin/bash

function cyberflowLogon(){
  #Authorize to cyberflow
  echo "[INFO]Authorization to Cyberflow[start]"
  if [[ -z "${bearer_token}" ]] ; then 
    response=$(curl -X 'POST' \
    'https://cyberflows.systems.uk.hsbc/api/v1/auth/login/' \
    -H 'accept: application/json' \
    -H 'Content-Type: application/x-www-form-urlencoded' \
    -d 'grant_type=&username='${CYBERFLOW_USER}'&password='${CYBERFLOW_PASS}'&scope=&client_id=&client_secret=')
    bearer_token=$(echo ${response}| jq -r ".access_token")
  fi
  echo "[INFO]Authorization to Cyberflow[end]"  
}

function cyberflowGetConfiguration(){  
  #Get configuration id
  #requires env.EIM_ID
  #requires env.CYBERFLOW_USER
  #requires env.CYBERFLOW_PASS
  #requires scan configuration name
  echo "[INFO]Getting Cyberflow Configuration ID[start]"
  cyberflow_configuration=$(curl -X 'GET' \
  'https://cyberflows.systems.uk.hsbc/api/v1/configuration/eim/'${EIM_ID}'/'   \
  -H 'accept: application/json'   \
  -H 'Authorization: Bearer '${bearer_token})
  projectId=$(curl -X 'GET' \
  'https://cyberflows.systems.uk.hsbc/api/v1/onboarding/project/eim/'${EIM_ID}'/'   \
  -H 'accept: application/json'   \
  -H 'Authorization: Bearer '${bearer_token} | jq -r '.id')
  echo "[INFO]Getting Cyberflow Configuration ID[end]"
}

function container_scan() {
if [[ "${container_scan}" = "true" ]]; then
  local buildModule=$1
  local currentPath=$PWD 
  local resultJson 
  mkdir -p ${currentPath}/ice

  cd ./${buildModule}
  if [[ -f "Dockerfile" || -f "dockerfile" ]]; then
    #logon to cyberflow
    cyberflowLogon
    cyberflowGetConfiguration  

    artifactId=$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout -f ./pom.xml)
    version=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout -f ./pom.xml)
    nexus_registry=${docker_registry_nexus_url_dev}    
    nexus_imagename=${nexus_registry}${docker_registry_nexus_path}${docker_image_name_prefix}${artifactId}:${version}   
    resultJson=cont-scan.${docker_image_name_prefix}${artifactId}.json 
    if echo "${version}" | grep -iq "snapshot"; then
      CONT_CONFIGURATION_NAME="nightly-${docker_image_name_prefix}${artifactId}"      
    elif echo "${git_branch}" | egrep -iq "^release|^hotfix|^master"; then
      CONT_CONFIGURATION_NAME="release-${docker_image_name_prefix}${artifactId}"      
    else 
      CONT_CONFIGURATION_NAME="nightly-${docker_image_name_prefix}${artifactId}"      
    fi

    #check if scan configuration exists
    # yes -> update scan 
    #     -> launch scan 
    # no  -> create configuration
    #     -> get configurationId
    #     -> launch scan

    configurationId=$(echo ${cyberflow_configuration} | jq -r --arg key "^${CONT_CONFIGURATION_NAME}.*" '.workflows|.[]|select(.name|test($key))|.id' | head -1)

    request_json=$(mktemp)

#####################################################################
# create/update configuration: start
#####################################################################
    if [[ -z ${configurationId} ]]; then
cat > ${request_json} <<-EOT
{
  "name": "${CONT_CONFIGURATION_NAME}-$(echo $version|sed -e 's|-SNAPSHOT||g' -e 's|-snapshot||g')",
  "cont_registry": "Nexus3-HK-18080",
  "cont_repo": "$(echo ${nexus_registry}|cut -f2- -d'/')${docker_registry_nexus_path}${docker_image_name_prefix}${artifactId}",
  "cont_tag": "${version}",
  "project_id": "${projectId}",
  "scan_type": "Container Security Testing",
  "force_create": true
}
EOT
    cat ${request_json}
    echo "creating scan configuration: ${CONT_CONFIGURATION_NAME}-$(echo $version|sed -e 's|-SNAPSHOT||g' -e 's|-snapshot||g')"
    response=$(curl -X 'POST' \
    'https://cyberflows.systems.uk.hsbc/api/v1/configuration/' \
    -H 'accept: application/json' \
    -H 'Authorization: Bearer '${bearer_token} \
    -H 'Content-Type: application/json' \
    -d @${request_json})
    echo ${response}

    configurationId=$(echo ${response} | jq -r '.id')
    echo configurationId:${configurationId}

    else
    
cat > ${request_json} <<-EOT
{
  "name": "${CONT_CONFIGURATION_NAME}-$(echo $version|sed -e 's|-SNAPSHOT||g' -e 's|-snapshot||g')",
  "cont_registry": "Nexus3-HK-18080",
  "cont_repo": "$(echo ${nexus_registry}|cut -f2- -d'/')${docker_registry_nexus_path}${docker_image_name_prefix}${artifactId}",
  "cont_tag": "${version}"
}
EOT
    cat ${request_json}
    echo "updating scan configuration: ${CONT_CONFIGURATION_NAME}-$(echo $version|sed -e 's|-SNAPSHOT||g' -e 's|-snapshot||g')"
    response=$(curl -X 'PUT' \
    'https://cyberflows.systems.uk.hsbc/api/v1/configuration/'${configurationId}'/' \
    -H 'accept: application/json' \
    -H 'Authorization: Bearer '${bearer_token} \
    -H 'Content-Type: application/json' \
    -d @${request_json})
    echo ${response}

    fi
#####################################################################
# create/update configuration: end
#####################################################################

    #launch scan
    echo configurationID[${CONT_CONFIGURATION_NAME}]: ${configurationId}
    echo launch scan
    response=$(curl -X 'POST' \
    'https://cyberflows.systems.uk.hsbc/api/v1/configuration/'${configurationId}'/launch_scan/' \
    -H 'accept: application/json' \
    -H 'Authorization: Bearer '${bearer_token} \
    -H 'Content-Type: multipart/form-data' \
    -F 'file=')
    scanId=$(echo ${response}| jq -r ".id")

echo "writing scan history to : ${resultJson}"
cat > ${currentPath}/ice/${resultJson} <<-EOT
$(echo ${response} | jq -r --arg k "https://cyberflows.systems.uk.hsbc/scanhistory/${projectId}/review/${scanId}" '. += {"contScanUrl": $k}')
EOT

  fi
  cd ${currentPath}
fi
}  

function sast_scan() {
if [[ "${sast_scan}" = "true" ]]; then
  local currentPath=$PWD
  mkdir -p ${currentPath}/ice
  local resultJson=sast-scan.json
  set -e
  
  #Authorize to cyberflow
  echo "[INFO]Authorization to Cyberflow[start]"
  response=$(curl -X 'POST' \
  'https://cyberflows.systems.uk.hsbc/api/v1/auth/login/' \
  -H 'accept: application/json' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -d 'grant_type=&username='${CYBERFLOW_USER}'&password='${CYBERFLOW_PASS}'&scope=&client_id=&client_secret=')
  bearer_token=$(echo ${response}| jq -r ".access_token")
  echo "[INFO]Authorization to Cyberflow[end]"
  
  #Get configuration id
  #requires env.EIM_ID
  #requires env.CYBERFLOW_USER
  #requires env.CYBERFLOW_PASS
  #requires scan configuration name
  
  echo "[INFO]Getting Cyberflow Configuration ID[start]"
  response=$(curl -X 'GET' \
  'https://cyberflows.systems.uk.hsbc/api/v1/configuration/eim/'${EIM_ID}'/'   \
  -H 'accept: application/json'   \
  -H 'Authorization: Bearer '${bearer_token})
  configurationId=$(echo ${response} | jq -r --arg key "${SAST_CONFIGURATION_NAME}" '.workflows|.[]|select(.name == $key)|.id')
  projectId=$(echo ${response} | jq -r --arg key "${SAST_CONFIGURATION_NAME}" '.workflows|.[]|select(.name == $key)|.project_id')
  echo "[INFO]Getting Cyberflow Configuration ID[end]"
  
  echo configurationID[${sast_scan_configuration}]: ${configurationId}
  echo launch scan
  response=$(curl -X 'POST' \
  'https://cyberflows.systems.uk.hsbc/api/v1/configuration/'${configurationId}'/launch_scan/' \
  -H 'accept: application/json' \
  -H 'Authorization: Bearer '${bearer_token} \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=')
  scanId=$(echo ${response}| jq -r ".id")
  
cat > ${currentPath}/ice/${resultJson} <<-EOT
$(echo ${response} | jq -r --arg k "https://cyberflows.systems.uk.hsbc/scanhistory/${projectId}/review/${scanId}" '. += {"sastScanUrl": $k}')
EOT
  set +e
fi
}

function sonar_iq_scan(){
if [[ "${sonar_iq_scan}" = "true" ]]; then
  local buildModule=$1
  local currentPath=$PWD
  local resultJson=${buildModule:-parent}-iq-result.json
  cd ./${buildModule}  
  artifactId=$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout -f pom.xml)
  mkdir -p ${currentPath}/ice
  if [[ "${jdk_version}" = "17" ]]; then
    export MAVEN_OPTS="--add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.lang=ALL-UNNAMED"
  fi
  (set -x
  time mvn -U clean install \
  com.sonatype.clm:clm-maven-plugin:evaluate \
  --fail-never \
  -Pnexus-iq \
  -Dclm.applicationId=${sonar_iq_project_prefix}${artifactId} \
  -Dclm.additionalScopes=provided,system \
  -DskipTests=${mvn_skip_tests} \
  -Dmaven.test.skip=${mvn_maven_test_skip} \
  -Dclm.resultFile=${currentPath}/ice/${resultJson}
  )
  cd ${currentPath}
fi
}

function docker_build(){
local buildModule=$1
local currentPath=$PWD
cd ./${buildModule}
if [[ -f "Dockerfile" || -f "dockerfile" ]] && [[ "${docker_build_enable}" = "true" ]] && [[ $(ls target/*.jar 2>/dev/null| wc -l) -gt 0 || ${build_type:-maven} = "nodejs" ]]; then
  [[ -f "Dockerfile" ]] && dockerfile=Dockerfile
  [[ -f "dockerfile" ]] && dockerfile=dockerfile
  export DOCKER_BUILDKIT=1
  if [[ ${build_type:-maven} = "maven" ]]; then
    artifactId=$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout -f ./pom.xml)
    version=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout -f ./pom.xml)
  else
    artifactId=${buildModule}
    version=$(grep -E '^   "version":' package.json |sed -E 's/.*"version": "(.+)".*/\1/')
  fi 

  nexus_registry=${docker_registry_nexus_url_dev}
#  if echo "${version}" | grep -iq "snapshot"; then
#    nexus_registry=${docker_registry_nexus_url_dev}
#  elif echo "${git_branch}" | egrep -iq "^release|^hotfix|^master"; then
#    nexus_registry=${docker_registry_nexus_url_prod}
#  fi
  nexus_imagename=${nexus_registry}${docker_registry_nexus_path}${docker_image_name_prefix}${artifactId}:${version}
  gcr_imagename=${docker_registry_gcr_url_dev}${docker_registry_gcr_path}${docker_image_name_prefix}${artifactId}:${version}
  echo machine nexus3.systems.uk.hsbc:8081 login ${nexus_user} password ${nexus_cred} > auth.conf

  if [[ "${jdk_version}" = "17" && ${build_type:-maven} = "maven" ]]; then
    #build image
    (set -x
      time docker build --secret=type=file,id=aptconf,src=auth.conf --build-arg version=${version} --build-arg image_name=${artifactId} --build-arg JAVA_BASE_IMAGE="${java_base_image:-nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/azuljava-jre-ubuntu-17:17.52}" -f ${dockerfile} -t $nexus_imagename .
    )
  elif [[ "${jdk_version}" = "21" && ${build_type:-maven} = "maven" ]]; then
    #build image
    (set -x
      time docker build --secret=type=file,id=aptconf,src=auth.conf --build-arg version=${version} --build-arg image_name=${artifactId} --build-arg JAVA_BASE_IMAGE="${java_base_image:-nexus3.systems.uk.hsbc:18096/com/hsbc/group/itid/es/mw/java/azuljava-jre-ubuntu-21:21.38}" -f ${dockerfile} -t $nexus_imagename .
    )    
  else
    #build image
    (set -x
      time docker build --secret=type=file,id=aptconf,src=auth.conf --build-arg version=${version} --build-arg image_name=${artifactId} -f ${dockerfile} -t $nexus_imagename .
    )
  fi
  [[ $? != 0 ]] && ((_error_cnt_=_error_cnt_+1)) && cd ${currentPath} && return
  (set -x
    docker tag $nexus_imagename $gcr_imagename
  )
  [[ $? != 0 ]] && ((_error_cnt_=_error_cnt_+1)) && cd ${currentPath} && return
  #push image to onprem nexus registry
  if ${docker_push_nexus}; then 
    (set -x
      time docker push $nexus_imagename
    )
    [[ $? != 0 ]] && ((_error_cnt_=_error_cnt_+1)) && cd ${currentPath} && return
  fi
  if ${docker_push_gcr}; then 
    #push image to gcr registry
    (set -x
      time docker push $gcr_imagename
    )
    [[ $? != 0 ]] && ((_error_cnt_=_error_cnt_+1)) && cd ${currentPath} && return
  fi
  #clean image to gcr registry
  (set -x
    time docker rmi $gcr_imagename
  )
  [[ $? != 0 ]] && ((_error_cnt_=_error_cnt_+1)) && cd ${currentPath} && return
  (set -x
      time docker rmi $nexus_imagename
  )
  [[ $? != 0 ]] && ((_error_cnt_=_error_cnt_+1)) && cd ${currentPath} && return  
  rm -f auth.conf  
fi
cd ${currentPath}
}

function mvn_build(){
local buildModule=$1
local currentPath=$PWD
cd ./${buildModule}
if [[ -f "pom.xml" ]]; then 
  export MAVEN_OPTS=${mvn_opts}
  local releaseDeploymentRepository=""
  local skipMvnDeploy="-Dmaven.deploy.skip=true"
  [[ "${mvn_build_branch}" = "development-branch-build" ]] && \
    deploymentRepository="-DaltSnapshotDeploymentRepository=unity-snapshots::default::https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-snapshots" && \
    skipMvnDeploy=""
  [[ "${mvn_build_branch}" = "disabled-master-branch-build" ]] && \
    deploymentRepository="-DaltSnapshotDeploymentRepository=unity-releases::default::https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-release" && \    
  [[ "${mvn_build_branch}" = "master-branch-build" ]] && \
    releaseDeploymentRepository="-DaltReleaseDeploymentRepository=unity-releases::default::https://gbmt-nexus.prd.fx.gbm.cloud.uk.hsbc/repository/maven-release" && \
    skipMvnDeploy="" 
    set -x 
    mvn_build_target=${mvn_build_target:-"clean deploy"}
    mvn ${mvn_build_target} \
    --batch-mode \
    -U -DskipTests=${mvn_skip_tests} \
    -Dmaven.test.skip=${mvn_maven_test_skip} \
    ${skipMvnDeploy} \
    ${deploymentRepository} \
    ${releaseDeploymentRepository} | tee mvn_build.hist
  [[ ${PIPESTATUS[0]} != 0 ]] && ((_error_cnt_=_error_cnt_+1))
  set +x
fi
cd ${currentPath}
}

function create_ice_artifact(){
buildModule=${$1:-parent}
git_tag=${git_tag:-$2}
groupId=$(mvn help:evaluate -Dexpression=project.groupId -q -DforceStdout)
version=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)
releaseRepository=$(mvn help:evaluate -Dexpression=project.distributionManagement.repository.url -q -DforceStdout)
snapshotRepository=$(mvn help:evaluate -Dexpression=project.distributionManagement.snapshotRepository.url -q -DforceStdout)
targetDir=ice
outfile=${targetDir}/artifact.ice.json
mkdir -p ${targetDir}
cat > $outfile <<-EOT
{
  "change": {
EOT
if [[ $(ls ice/*iq-result.json 2> /dev/null) ]]; then
cat >> $outfile <<-EOT
    "vulnerabilityScanUrl": "$(cat ${targetDir}/parent-iq-result.json | jq -r '.reportHtmlUrl')",
EOT
fi
if [[ $(ls ice/sast-scan.json 2> /dev/null) ]]; then
cat >> $outfile <<-EOT
    "sastScanUrl": "$(cat ${targetDir}/sast-scan.json | jq -r '.sastScanUrl')",
EOT
sastScanUrl='"sastScanUrl": "'$(cat ${targetDir}/sast-scan.json | jq -r '.sastScanUrl')'",'
fi
if [[ $(ls ice/cont-scan.unity2-cache.json 2> /dev/null) ]]; then
cat >> $outfile <<-EOT
    "contScanUrl": "$(cat ${targetDir}/cont-scan.unity2-cache.json | jq -r '.contScanUrl')",
EOT
contScanUrl='"contScanUrl": "'$(cat ice/cont-scan.unity2-cache.json | jq -r '.contScanUrl')'",'
fi
cat >> $outfile <<-EOT
    "artifacts": {
      "artifacts": [
EOT

mvn help:evaluate -Dexpression=project.modules -q -DforceStdout | tail -n +2 | head -n -1 | sed 's/\s*<.*>\(.*\)<.*>/\1/' | while read module ;
do
#artifactId=$(mvn help:evaluate -Dexpression=project.artifactId -q -DforceStdout -f ${module}/pom.xml)
#version=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout -f ${module}/pom.xml)
artifactId=${module}
extension=jar
[[ $(grep packaging ${module}/pom.xml | grep pom) ]] && extension=pom
if echo "${version}" | grep -iq "snapshot"; then
  repository=${snapshotRepository}
  url=$(egrep "Uploading:.*${module}/${version}/${module}.*${extension}" mvn_build.hist | egrep -o "http.*")  
else
  repository=${releaseRepository}
  url=${repository}/$(echo ${groupId}|sed 's|\.|/|g')/${module}/${version}/${module}-${version}.${extension}
fi
contScanJson="cont-scan.${docker_image_name_prefix}${artifactId}.json"
if [[ $(ls ice/${contScanJson} 2> /dev/null) ]]; then
contScanUrl='"contScanUrl": "'$(cat ice/${contScanJson} | jq -r '.contScanUrl')'",'
else
contScanUrl=""
fi
cat >> $outfile <<-EOT
        ${separator}{
          ${sastScanUrl}
          ${contScanUrl}
          "sourceCodeUrl": "https://stash.hk.hsbc/projects/UNITY-I15N-POC/repos/spring/browse?at=refs%2Ftags%2F${git_tag}",
          "componentId": "${groupId}/${module}",
          "version": "${version}",
          "url": "${url}",
          "componentSourceCodeUrl": "https://stash.hk.hsbc/projects/UNITY-I15N-POC/repos/spring",
          "id": "${groupId}:${module}:${version}:${extension}"
        }
EOT
separator=,
done
cat >> $outfile <<-EOT
      ]
    }
  },
  "fieldsToUpdate": [
    "artifacts"
EOT
if [[ $(ls ice/*iq-result.json 2> /dev/null) ]]; then
cat >> $outfile <<-EOT
    ,"vulnerabilityScanUrl"
EOT
fi
if [[ $(ls ice/sast-scan.json 2> /dev/null) ]]; then
cat >> $outfile <<-EOT
    ,"sastScanUrl"
EOT
fi
if [[ $(ls ice/cont-scan.unity2-cache.json 2> /dev/null) ]]; then
cat >> $outfile <<-EOT
    ,"contScanUrl"
EOT
fi
cat >> $outfile <<-EOT
  ]
}
EOT
#remove all blank lines
sed -i '/^[[:space:]]*$/d' ${outfile}
ls -l ${outfile}
}

function main(){
#export PATH=/opt/disk1/hss/apps/teamcity/build-agent-1/tools/maven3_6/bin:/opt/apache-maven-3.3.9/bin:$PATH
if [[ "${jdk_version}" = "17" ]]; then
  echo "export JAVA_HOME=/opt/java/zulu17"
  export JAVA_HOME=/opt/java/zulu17
elif [[ "${jdk_version}" = "21" ]]; then
  echo "export JAVA_HOME=/opt/java/zulu21"
  export JAVA_HOME=/opt/java/zulu21  
fi
local action=$1
case $action in
  sast_scan)
    sast_scan
    exit $?
    ;;
  sonar_iq_scan)
    _error_cnt_=0
    if [ -z "${mvn_modules}" ]; then
      sonar_iq_scan
    else
      for _module_ in $(echo "${mvn_modules}" | grep -v "^#" | sed 's|/n| |g' | sed '/^$/d');
      do
        sonar_iq_scan ${_module_}
      done
    fi
    exit ${_error_cnt_:-0}
    ;;
  mvn_build)
    _error_cnt_=0
    if [ -z "${mvn_modules}" ]; then
      mvn_build
    else
      for _module_ in $(echo "${mvn_modules}" | grep -v "^#" | sed 's|/n| |g' | sed '/^$/d');
      do
        mvn_build ${_module_}
      done
    fi
    exit ${_error_cnt_:-0}
    ;;
  docker_build)
    _error_cnt_=0
    if [[ "${docker_build_enable}" = "true" ]]; then 
      if ${docker_build_recursive} && [[ -z ${mvn_modules} ]]; then 
        mvn_modules=$(ls -d */ | cut -f1 -d'/')
      fi
      if [ -z "${mvn_modules}" ]; then
        docker_build
      else
        for _module_ in $(echo "${mvn_modules}" | grep -v "^#" | sed 's|/n| |g' | sed '/^$/d');
        do
          docker_build ${_module_}
        done
      fi
      echo _error_cnt_:${_error_cnt_}
      exit ${_error_cnt_:-0}
    fi
    ;;
    container_scan)
      _error_cnt_=0
      if [[ "${container_scan}" = "true" ]]; then
         if ${docker_build_recursive} && [[ -z ${mvn_modules} ]]; then 
           mvn_modules=$(ls -d */ | cut -f1 -d'/')
         fi
         if [ -z "${mvn_modules}" ]; then
           container_scan
         else
           for _module_ in $(echo "${mvn_modules}" | grep -v "^#" | sed 's|/n| |g' | sed '/^$/d');
           do
             container_scan ${_module_}
           done
         fi
      fi
      exit ${_error_cnt_:-0}
    ;;
    create_ice_artifact)
      create_ice_artifact ${buildModule} ${git_tag}
    ;;
esac
}

