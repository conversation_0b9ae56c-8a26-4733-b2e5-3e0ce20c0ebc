#!/bin/bash

function maven_setup() {
  project_id=`gcloud config list --format 'value(core.project)'`
  echo "INFO: Bootstrapping Maven repository and SSH keys..."
  #bootstrap maven repository and ssh keys
  # This script is intended to be run on a Jenkins agent to set up the Maven repository and SSH keys.
  if [[ ! -d "/home/<USER>/.m2/repository" ]]; then
    echo "INFO: Setting up Maven repository..."
    gsutil cp gs://${project_id}-app-runtime-dependencies/m2/repository.tar /home/<USER>/.m2/
    cd /home/<USER>/.m2
    tar -xf repository.tar
    rm -f repository.tar    
  fi

  if [[ ! -L "/hss/apps/respository" ]]; then
    echo "INFO: Setting up Maven repository on /hss/apps/repository..."    
    ln -sf /home/<USER>/.m2/repository /hss/apps/repository
  fi

  if [[ ! -f "/home/<USER>/.ssh/id_rsa_teamcity" ]]; then
    echo "INFO: Setting up SSH keys..."
    gsutil cp gs://${project_id}-app-runtime-dependencies/secrets/env/dev/id_rsa_teamcity* /home/<USER>/.ssh/  
    chmod 600 /home/<USER>/.ssh/id_rsa_teamcity*
  fi
  echo "INFO: Bootstrapping Maven repository and SSH keys..."
}   

function copy_settings_xml() {
  echo "INFO: Copying settings.xml file..."
  scp -i /home/<USER>/.ssh/id_rsa_teamcity -o StrictHostKeyChecking=no -rp <EMAIL>:/home/<USER>/.m2/settings.xml /home/<USER>/.m2/settings-gbmt.xml
  cp /home/<USER>/.m2/settings-gbmt.xml /home/<USER>/.m2/settings.xml 
  echo "INFO: Copied settings.xml file..."
}

function constructor() {
  _check_inteval_=300
} 

constructor
while [ 1 = 1 ]; do 
  maven_setup 
  copy_settings_xml
  sleep ${_check_inteval_}
done  
