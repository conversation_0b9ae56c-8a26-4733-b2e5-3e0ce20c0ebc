spring:
  config:
    import: application-jasypt.yml
  flyway:
    locations: classpath:db/migration/h2,classpath:db/migration/common
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    initialization-mode: never
  auth:
    ldap:
      server: ldaps://aa-lds-prod.hk.hsbc:3269
      truststore: "C:/hss/apps/certs/env/dev/unity-microservices.ts"
      service-account-dn: ENC(npH7X9FAGFqQhU3AtfbZNYbKMrIA6NqQDFvHt0vWwC0cjktFZRJr/hdWVe06IQs+nlHado7N32tCtrylfq+VkCLmOiSVnCqNUx9tm9TbLywvxKEJ23aVOhGtuj2Om0J0pA9PIAPuZlq/HxwEBFeR7g==)
      service-account-password: ENC(oqzCWk0tl3+0/yYZ9QwEA4ypavrWpZ92dniTFy8aj8KyHMSs+5PdC0KI8eo188Q9)
      user-search-attribute: HSBCPeople
      user-filter: "(|(hsbc-ad-SAMAccountName={0})(${spring.auth.ldap.user-search-attribute}={0}))"
      user-base-dn: DC=InfoDir,DC=Prod,DC=HSBC
      group-base-dn: DC=InfoDir,DC=Prod,DC=HSBC