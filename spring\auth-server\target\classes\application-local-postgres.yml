spring:
  flyway:
    locations: classpath:db/migration/postgres,classpath:db/migration/common
    schemas: auth_server
  datasource:
    url: *****************************************
    username: postgres
    password: #this can be anything as postgresql is running under the same windows user
    hikari:
      schema: auth_server
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        default_schema: auth_server
        dialect: com.poc.hss.fasttrack.jpa.dialect.UnityPostgreSQLDialect
        hbm2ddl:
          auto: update
  h2:
    console:
      enabled: false