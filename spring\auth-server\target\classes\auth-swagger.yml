openapi: 3.0.0
info:
  version: 1.0.0
  title: Auth server
servers:
  - url: http://auth-server/api
paths:
  /login:
    post:
      tags:
        - Auth
      summary: Login
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
                state:
                  description: Value returned by an unauthorzied request, used for redirection
                  type: string
      responses:
        200:
          description: OK
        302:
          description: Redirect based on state
  /logout:
    get:
      tags:
        - Auth
      summary: Logout
      parameters:
        - in: query
          name: redirect_uri
          schema:
            type: string
      responses:
        200:
          description: OK
        302:
          description: Redirect based redirect_uri
  /oauth2/authorize:
    get:
      tags:
        - Auth
      summary: OAuth2 auth code grant
      parameters:
        - in: query
          name: client_id
          required: true
          schema:
            type: string
        - in: query
          name: client_secret
          required: true
          schema:
            type: string
        - in: query
          name: response_type
          required: true
          example: code
          schema:
            type: string
        - in: query
          name: redirect_uri
          description: The URI must be whitelisted for the client
          required: true
          schema:
            type: string
        - in: query
          name: scope
          required: true
          example: PIPELINE:READ CUSTOM_API:WRITE
          description: Space separated scopes
          schema:
            type: string
      responses:
        302:
          description: Redirection with 'state' query param if not authenticated, 'code' query param if authenticated
  /oauth2/token:
    post:
      tags:
        - Auth
      summary: OAuth2 access token
      security:
        - BasicAuth: []
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AccessTokenRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessTokenResponse'
  /oauth2/introspect:
    post:
      tags:
        - Auth
      summary: OAuth2 access token introspection
      security:
        - BasicAuth: []
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessTokenIntrospectionResponse'
  /oauth2/revoke:
    post:
      tags:
        - Auth
      summary: OAuth2 access token revoke
      security:
        - BasicAuth: []
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/TokenRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AccessTokenIntrospectionResponse'
components:
  securitySchemes:
    BasicAuth:
      type: http
      scheme: basic
  schemas:
    TokenRequest:
      type: object
      properties:
        token:
          type: string
          description: Access token
    AccessTokenRequest:
      type: object
      properties:
        code:
          description: Returned code from /oauth2/authorize api, used when grant_type=authorization_code
          type: string
        redirect_uri:
          description: Redirect URI used in /oauth2/authorize api, used when grant_type=authorization_code
          type: string
        grant_type:
          example: [authorization_code, refresh_token]
          type: string
        refresh_token:
          description: Used when grant_type=refresh_token
          type: string
    AccessTokenResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        scope:
          type: string
          description: Space separated scope
        token_type:
          type: string
        expires_in:
          type: integer
    AccessTokenIntrospectionResponse:
      type: object
      properties:
        active:
          type: boolean
        client_id:
          type: string
        iat:
          type: integer
        exp:
          type: integer
        scope:
          type: string
        token_type:
          type: string
        nbf:
          type: integer
        sub:
          type: string
        aud:
          type: array
          items:
            type: string
        iss:
          type: string