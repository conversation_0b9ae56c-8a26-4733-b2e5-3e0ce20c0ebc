openapi: 3.0.0
info:
  version: 1.0.0
  title: Auth server
servers:
  - url: http://auth-server/api
host: auth-server
basePath: /api
paths:
  /clients:
    get:
      tags:
        - Client
      operationId: getClients
      summary: List clients
      parameters:
        - $ref: '#/components/parameters/IdQuery'
        - $ref: '#/components/parameters/ClientNameQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ClientResponse'
    put:
      tags:
        - Client
      operationId: upsertClient
      summary: Upsert client
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Client'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Client'
  /clients/{id}:
    get:
      tags:
        - Client
      operationId: getClient
      summary: Get client
      parameters:
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Client'
    put:
      tags:
        - Client
      operationId: updateClient
      summary: Update client
      parameters:
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Client'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Client'
    delete:
      tags:
        - Client
      operationId: deleteClient
      summary: Delete client
      parameters:
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: id
      in: query
      schema:
        type: string
    ClientIdQuery:
      name: clientId
      in: query
      schema:
        type: string
    ClientNameQuery:
      name: clientName
      in: query
      schema:
        type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
  schemas:
    ClientResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Client'
        total:
          type: integer
          format: int64
    Client:
      type: object
      properties:
        id:
          type: string
        clientId:
          type: string
        clientName:
          type: string
        redirectUris:
          type: array
          items:
            type: string
        authenticationUrl:
          type: string
        permissionIds:
          type: array
          items:
            type: string
        authorizationGrantTypes:
          type: array
          items:
            $ref: '#/components/schemas/AuthorizationGrantType'
        clientAuthenticationMethods:
          type: array
          items:
            $ref: '#/components/schemas/ClientAuthenticationMethod'
        clientIdIssuedAt:
          type: string
          format: date-time
        clientSecret:
          type: string
        clientSecretExpiresAt:
          type: string
          format: date-time
        allPermissions:
          type: boolean
    ClientAuthenticationMethod:
      type: string
      enum: [ basic, client_secret_basic, post, client_secret_post, client_secret_jwt, private_key_jwt, none ]
    AuthorizationGrantType:
      type: string
      enum: [ authorization_code, implicit, refresh_token, client_credentials, password ]
