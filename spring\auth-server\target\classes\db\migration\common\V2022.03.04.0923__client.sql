drop table if exists client cascade;

create table client
  (
    id                 varchar(255) not null              ,
    created_by         varchar(255) default 'system'      ,
    created_date       timestamp default current_timestamp,
    updated_by         varchar(255)                       ,
    updated_date       timestamp                          ,
    all_permissions    boolean default false              ,
    authentication_url varchar(255)                       ,
    authorization_grant_types jsonb                       ,
    client_authentication_methods jsonb                   ,
    client_id                varchar(255) not null                       ,
    client_name              varchar(255) not null                       ,
    client_secret            varchar(255) not null                       ,
    client_secret_expires_at timestamp                                   ,
    redirect_uris jsonb                                                  ,
    primary key (id)
  )
;