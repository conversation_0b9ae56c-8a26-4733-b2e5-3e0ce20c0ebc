drop table if exists feature cascade;

create table feature
  (
    id           varchar(255) not null              ,
    created_by   varchar(255) default 'system'      ,
    created_date timestamp default current_timestamp,
    updated_by   varchar(255)                       ,
    updated_date timestamp                          ,
    name         varchar(255) not null              ,
    primary key (id)
  )
;