drop table if exists permission cascade;

create table permission
  (
    id                   varchar(255) not null                                                               ,
    created_by           varchar(255) default 'system'                                                       ,
    created_date         timestamp default current_timestamp                                                 ,
    updated_by           varchar(255)                                                                        ,
    updated_date         timestamp                                                                           ,
    fk_operation_type_id varchar(255) not null                                                               ,
    fk_feature_id        varchar(255) not null                                                               ,
    primary key (id)                                                                                         ,
    constraint fk_permission_operation_type foreign key (fk_operation_type_id) references operation_type(id) ,
    constraint fk_permission_feature foreign key (fk_feature_id) references feature(id)
  )
;