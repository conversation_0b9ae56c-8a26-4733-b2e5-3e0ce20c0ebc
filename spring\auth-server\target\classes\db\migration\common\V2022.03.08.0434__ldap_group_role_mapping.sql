drop table if exists ldap_group_role_mapping cascade;

create table ldap_group_role_mapping
  (
    id                        varchar(255) not null                                                                   ,
    created_by                varchar(255) default 'system'                                                           ,
    created_date              timestamp default current_timestamp                                                     ,
    updated_by                varchar(255)                                                                            ,
    updated_date              timestamp                                                                               ,
    ldap_group_match_pattern  varchar(255) not null                                                                   ,
    precedence                integer not null                                                                        ,
    resource_name_override    varchar(255)                                                                            ,
    resource_name_regex_group integer                                                                                 ,
    role_regex_group          integer                                                                                 ,
    fk_role_id_override       varchar(255)                                                                            ,
    fk_resource_type_id       varchar(255) not null                                                                   ,
    primary key (id)                                                                                                  ,
    constraint fk_ldap_group_role_mapping_role foreign key (fk_role_id_override) references role(id)                  ,
    constraint fk_ldap_group_role_mapping_resource_type foreign key (fk_resource_type_id) references resource_type(id),
    constraint chk_resource_name check (
        (resource_name_override is null ) <> (resource_name_regex_group is null ) --xor
    ),
    constraint chk_role check (
        (role_regex_group is null) <> (fk_role_id_override is null) --xor
    )
  )