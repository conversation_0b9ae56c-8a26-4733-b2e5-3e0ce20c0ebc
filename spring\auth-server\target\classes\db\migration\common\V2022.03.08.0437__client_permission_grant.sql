drop table if exists client_permission_grant cascade;

create table client_permission_grant
  (
    id               varchar(255) not null                                                       ,
    created_by       varchar(255) default 'system'                                               ,
    created_date     timestamp default current_timestamp                                         ,
    updated_by       varchar(255)                                                                ,
    updated_date     timestamp                                                                   ,
    fk_client_id     varchar(255) not null                                                       ,
    fk_permission_id varchar(255) not null                                                       ,
    primary key (id)                                                                             ,
    constraint fk_client_permission_grant_client foreign key (fk_client_id) references client(id),
    constraint fk_client_permission_grant_permission foreign key (fk_permission_id) references permission(id)
  )
;