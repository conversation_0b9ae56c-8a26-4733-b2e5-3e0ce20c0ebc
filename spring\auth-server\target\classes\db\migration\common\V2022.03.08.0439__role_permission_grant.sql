drop table if exists role_permission_grant cascade;

create table role_permission_grant
  (
    id               varchar(255) not null                                                                 ,
    created_by       varchar(255) default 'system'                                                         ,
    created_date     timestamp default current_timestamp                                                   ,
    updated_by       varchar(255)                                                                          ,
    updated_date     timestamp                                                                             ,
    fk_permission_id varchar(255) not null                                                                 ,
    fk_role_id       varchar(255) not null                                                                 ,
    primary key (id)                                                                                       ,
    constraint fk_role_permission_grant_permission foreign key (fk_permission_id) references permission(id),
    constraint fk_role_permission_grant_role foreign key (fk_role_id) references role(id)
  )