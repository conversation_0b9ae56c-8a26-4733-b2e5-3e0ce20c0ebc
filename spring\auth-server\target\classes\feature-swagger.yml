openapi: 3.0.0
info:
  version: 1.0.0
  title: Auth server
servers:
  - url: http://auth-server/api
host: auth-server
basePath: /api
paths:
  /features:
    get:
      tags:
        - Feature
      operationId: getFeatures
      summary: List features
      parameters:
        - $ref: '#/components/parameters/IdQuery'
        - $ref: '#/components/parameters/NameQuery'
        - $ref: '#/components/parameters/ScopeQuery'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeatureResponse'
    put:
      tags:
        - Feature
      operationId: upsertFeature
      summary: Upsert feature
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Feature'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Feature'
  /features/{id}:
    get:
      tags:
        - Feature
      operationId: getFeature
      summary: Get feature
      parameters:
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Feature'
    put:
      tags:
        - Feature
      operationId: updateFeature
      summary: Update feature
      parameters:
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Feature'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Feature'
    delete:
      tags:
        - Feature
      operationId: deleteFeature
      summary: Delete feature
      parameters:
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: ids
      in: query
      schema:
        type: array
        items:
          type: string
    NameQuery:
      name: names
      in: query
      schema:
        type: array
        items:
          type: string
    ScopeQuery:
      name: scopes
      in: query
      schema:
        type: array
        items:
          type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
  schemas:
    FeatureResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Feature'
        total:
          type: integer
          format: int64
    Feature:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        scope:
          type: string
        parentId:
          type: string
        children:
          type: array
          items:
            $ref: '#/components/schemas/Feature'
