# Domain Component
dn: dc=hsbc
objectClass: top
objectClass: domain
objectClass: extensibleObject
dc: hsbc

# Organization Units
dn: ou=unity2-groups,dc=hsbc
objectClass: top
objectClass: organizationalUnit
ou: unity2-groups

dn: ou=unity2-users,dc=hsbc
objectClass: top
objectClass: organizationalUnit
ou: unity2-users

# Users
dn: uid=unity2superadmin,ou=unity2-users,dc=hsbc
objectClass: top
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: memberOfClass
sn: unity2superadmin
cn: unity2superadmin
uid: unity2superadmin
userPassword: 
displayName: Super Admin
memberof: cn=unity2-admin,ou=unity2-groups,dc=hsbc

dn: uid=unity2-core-rwuser,ou=unity2-users,dc=hsbc
objectClass: top
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: memberOfClass
sn: unity2-core-rwuser
cn: unity2-core-rwuser
uid: unity2-core-rwuser
displayName: Read Write User
userPassword: 
memberof: cn=unity2-core-READER,ou=unity2-groups,dc=hsbc
memberof: cn=unity2-core-EDITOR,ou=unity2-groups,dc=hsbc

dn: uid=unity2-core-rouser,ou=unity2-users,dc=hsbc
objectClass: top
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: memberOfClass
sn: unity2-core-rouser
cn: unity2-core-rouser
uid: unity2-core-rouser
displayName: Read Only User
userPassword: 
memberof: cn=unity2-core-READER,ou=unity2-groups,dc=hsbc

dn: uid=unity2-core-opuser,ou=unity2-users,dc=hsbc
objectClass: top
objectClass: person
objectClass: organizationalPerson
objectClass: inetOrgPerson
objectClass: memberOfClass
sn: unity2-core-opuser
cn: unity2-core-opuser
uid: unity2-core-opuser
displayName: Operator
userPassword: 
memberof: cn=unity2-core-READER,ou=unity2-groups,dc=hsbc
memberof: cn=unity2-core-OPERATOR,ou=unity2-groups,dc=hsbc
memberof: cn=unity2-projectcreator,ou=unity2-groups,dc=hsbc