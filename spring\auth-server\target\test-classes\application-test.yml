spring:
  flyway:
    locations: classpath:db/migration/h2,classpath:db/migration/common
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password:
    initialization-mode: never
  jpa:
    properties:
      hibernate:
        dialect: com.poc.hss.fasttrack.jpa.dialect.UnityH2Dialect
  h2:
    console:
      enabled: true
  auth:
    ldap:
      server: ldap://localhost:8389
      service-account-dn: uid=unity2superadmin,ou=unity2-users,dc=hsbc
      service-account-password: example
      user-search-attribute: uid
      user-filter: ${spring.auth.ldap.user-search-attribute}={0}
      user-base-dn: ou=unity2-users,dc=hsbc
      group-base-dn: ou=unity2-groups,dc=hsbc
  ldap:
    urls: ldap://localhost:8389
    embedded:
      base-dn: dc=hsbc
      ldif: classpath:test/ldap-server.ldif
      port: 8389
      validation:
        enabled: false
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:${server.port}
          jwk-set-uri: http://localhost:${server.port}/oauth2/jwks
init.data: true
auth:
  jwt:
    issuer: http://localhost:${server.port}
    private-key: classpath:test/private_key.der
    public-key: classpath:test/public_key.der
  default:
    permissions:
      - feature: PIPELINE
        operationType: READ
      - feature: PIPELINE
        operationType: WRITE
      - feature: CUSTOM_API
        operationType: READ
      - feature: CUSTOM_API
        operationType: WRITE
      - feature: BUSINESS_EVENT
        operationType: READ
      - feature: BUSINESS_EVENT
        operationType: WRITE
      - feature: INFRA
        operationType: READ
      - feature: INFRA
        operationType: WRITE
      - feature: K8S
        operationType: READ
      - feature: K8S
        operationType: WRITE
      - feature: VERSION
        operationType: READ
      - feature: VERSION
        operationType: WRITE
      - feature: SOURCE_ADAPTOR
        operationType: READ
      - feature: SOURCE_ADAPTOR
        operationType: WRITE
      - feature: CONSUMER_ADAPTOR
        operationType: READ
      - feature: CONSUMER_ADAPTOR
        operationType: WRITE
      - feature: SYSTEM_CONFIG
        operationType: READ
      - feature: SYSTEM_CONFIG
        operationType: WRITE
      - feature: BASIC
        operationType: READ
      - feature: BASIC
        operationType: WRITE
      - feature: CONFIG_EXPORT
        operationType: READ
      - feature: CONFIG_EXPORT
        operationType: WRITE
      - feature: ACCESS_SCHEMA
        operationType: READ
      - feature: ACCESS_SCHEMA
        operationType: WRITE
      - feature: TRANSFORMER
        operationType: READ
      - feature: TRANSFORMER
        operationType: WRITE
      - feature: BLEND
        operationType: READ
      - feature: BLEND
        operationType: WRITE
      - feature: CREATE
        operationType: WRITE
      - feature: SCRIPT
        operationType: READ
      - feature: SCRIPT
        operationType: WRITE
    clients:
      - clientId: unity-platform-test
        clientName: Unity Platform TEST
        redirectUris:
          - http://127.0.0.1:8080/login
        authenticationUrl: http://localhost:8080/login
        clientSecret: "$2a$10$ZcHcueVDpWXusYkTIsZFg.bYEMUgqGmuJSX048XVduMOl5/RMlAl2"
        authorizationGrantTypes:
          - authorization_code
          - refresh_token
        clientAuthenticationMethods:
          - client_secret_basic
        allPermissions: true
      - clientId: unity-custom-api-catalog-test
        clientName: Unity Custom API Catalog TEST
        clientSecret: "$2a$10$dW5pdHktY3VzdG9tLWFwaS1jYXRhbG9nLXRlc3Q6ZXhhbXBsZQ=="
        authorizationGrantTypes:
          - client_credentials
        clientAuthenticationMethods:
          - client_secret_basic
        allPermissions: true
        system: true
    roles:
      - name: READER
        permissions:
          - feature: PIPELINE
            operationType: READ
          - feature: CUSTOM_API
            operationType: READ
          - feature: BUSINESS_EVENT
            operationType: READ
          - feature: INFRA
            operationType: READ
          - feature: K8S
            operationType: READ
          - feature: VERSION
            operationType: READ
          - feature: SOURCE_ADAPTOR
            operationType: READ
          - feature: CONSUMER_ADAPTOR
            operationType: READ
          - feature: SYSTEM_CONFIG
            operationType: READ
          - feature: BASIC
            operationType: READ
          - feature: ACCESS_SCHEMA
            operationType: READ
          - feature: TRANSFORMER
            operationType: READ
          - feature: BLEND
            operationType: READ
          - feature: SCRIPT
            operationType: READ
      - name: EDITOR
        permissions:
          - feature: PIPELINE
            operationType: WRITE
          - feature: CUSTOM_API
            operationType: WRITE
          - feature: BUSINESS_EVENT
            operationType: WRITE
          - feature: INFRA
            operationType: WRITE
          - feature: K8S
            operationType: WRITE
          - feature: VERSION
            operationType: WRITE
          - feature: SOURCE_ADAPTOR
            operationType: WRITE
          - feature: CONSUMER_ADAPTOR
            operationType: WRITE
          - feature: SYSTEM_CONFIG
            operationType: WRITE
          - feature: BASIC
            operationType: WRITE
          - feature: ACCESS_SCHEMA
            operationType: WRITE
          - feature: TRANSFORMER
            operationType: WRITE
          - feature: BLEND
            operationType: WRITE
          - feature: SCRIPT
            operationType: WRITE
      - name: OPERATOR
        permissions:
          - feature: CONFIG_EXPORT
            operationType: READ
          - feature: CONFIG_EXPORT
            operationType: WRITE
      - name: CREATOR
        permissions:
          - feature: CREATE
            operationType: WRITE
      - name: ADMIN
        allPermissions: true
    ldapGroupRoleMappings:
      - ldapGroupMatchPattern: cn=unity2-(.+)-(.+),ou=unity2-groups,dc=hsbc
        resourceType: PROJECT
        resourceNameRegexGroup: 1
        roleRegexGroup: 2
      - ldapGroupMatchPattern: cn=unity2-projectcreator,ou=unity2-groups,dc=hsbc
        resourceType: PROJECT
        resourceNameOverride: "*"
        roleOverride: CREATOR
      - ldapGroupMatchPattern: cn=unity2-admin,ou=unity2-groups,dc=hsbc
        resourceType: PROJECT
        resourceNameOverride: "*"
        roleOverride: ADMIN
