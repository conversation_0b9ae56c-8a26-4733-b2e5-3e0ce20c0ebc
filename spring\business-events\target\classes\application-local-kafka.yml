spring:
  config:
    import: application-jasypt.yml
#  datasource:
#    url: jdbc:h2:mem:testdb
#    username: sa
#    password: 
  h2:
    console:
      enabled: false
  datasource:
    url: *****************************************
    username: postgres
    password: 
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        default_schema: pipeline_config
        dialect: org.hibernate.dialect.PostgreSQLDialect
        hbm2ddl:
          auto: update
kafkaConfig:
  bootstrap.servers: hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094
#  group.id: ${kafka.consumer.group:spring-poc-dev}
  schema.registry.url: http://hkl20146687.hc.cloud.hk.hsbc:8081/
  auto.offset.reset: earliest
  security.protocol: SSL
  ssl.keystore.location: C:/hss/apps/certs/env/dev/unity-microservices.jks
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: C:/hss/apps/certs/env/dev/unity-microservices.ts
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
kafkaStreamConfig:
  application.id: unity-kstreams-application-test
# client.id: unity-kstreams-client-test
elkConfig:
  hosts: "hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045"
  environment: "DEV"
management:
  endpoint:
    restart:
      enabled: true
  endpoints:
    web:
      cors:
        allowed-origins: "*"
        allowed-methods: OPTIONS, GET, POST
      exposure:
        include: restart,health,prometheus
  metrics:
    tags:
      application: ${spring.application.name}

events:
  - eventName: "status-sa-event"
    sourceTopics:
      - "test-symphony-out-topic2"
    businessEntity: "TRADE"
    projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
    rules:
      - type: "FILTER"
        definition:
          id: "filter-1"
          field: "status"
          value: "s"

defaultSubscription: true

subscriptions:
  - id: 31036f357842b52ea7789bf5
    clientSystem: symphony
    subscriptionType: KAFKA
    eventName: status-sa-event
    kafkaTopic: test-symphony-out-topic3
logging:
  level:
    com.poc.hss.fasttrack: DEBUG