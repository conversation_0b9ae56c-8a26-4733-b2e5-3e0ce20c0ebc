spring:
  config:
    import: application-jasypt.yml
  #  datasource:
  #    url: jdbc:h2:mem:testdb
  #    username: sa
  #    password: example
  h2:
    console:
      enabled: false
  flyway:
    schemas: business_event
    table: flyway_schema_history_business_event
    locations: classpath:db/migration/postgres,classpath:db/migration/common
    baseline-on-migrate: true
    # not doing validation as the initialize script was updated
    validate-on-migrate: false
  datasource:
    url: *****************************************
    username: postgres
    password: example
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        default_schema: business_event
        dialect: org.hibernate.dialect.PostgreSQLDialect
        hbm2ddl:
          auto: none
kafkaConfig:
  bootstrap.servers: "localhost:13333"
  group.id: "${kafka.consumer.group:project-DEV-business-events}"
  auto.offset.reset: latest
kafkaStreamConfig:
  application.id: project-DEV-business-events
elkConfig:
  hosts: "hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045"
  environment: "DEV"
management:
  endpoint:
    restart:
      enabled: true
  endpoints:
    web:
      cors:
        allowed-origins: "*"
        allowed-methods: OPTIONS, GET, POST
      exposure:
        include: restart,health,prometheus
  metrics:
    tags:
      application: ${spring.application.name}

events:
  - eventName: "row16-event"
    sourceTopics:
      - "unity2-local-test-event-a-out"
    targetTopic: unity2-local-row16-event-out
    businessEntity: "TEST"
    projectId: "test-schema"
    rules:
      - type: "FILTER"
        definition:
          id: "filter-1"
          field: "c1"
          value: "row16column1"

defaultSubscription: true

logging:
  level:
    com.poc.hss.fasttrack: DEBUG
    org.hibernate.SQL: DEBUG
