openapi: 3.0.0
info:
  version: 1.0.0
  title: Business Event Subscription Swagger File
servers:
  - url: http://business-events/api
paths:
  /projects/{projectId}/subscriptions:
    get:
      summary: Get list of subscription
      operationId: getSubscriptions
      tags:
        - Subscription
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/OffsetQuery'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
        - $ref: '#/components/parameters/EventName'
        - $ref: '#/components/parameters/ClientSystem'
        - $ref: '#/components/parameters/SubscriptionType'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionResponse'
    put:
      summary: Insert/update Subscriptions
      operationId: upsertSubscriptions
      tags:
        - Subscription
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Subscription'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'
    delete:
      summary: Delete Subscription
      operationId: deleteSubscription
      tags:
        - Subscription
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
      responses:
        204:
          description: OK
  /projects/{projectId}/subscriptions/{id}:
    get:
      summary: Get Subscription By Id
      operationId: getSubscriptionById
      tags:
        - Subscription
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'
    put:
      summary: Update Subscription
      operationId: updateSubscriptionById
      tags:
        - Subscription
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Subscription'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subscription'
    delete:
      summary: Delete Subscription By Id
      operationId: deleteSubscriptionById
      tags:
        - Subscription
      parameters:
        - $ref: '#/components/parameters/ProjectIdPath'
        - $ref: '#/components/parameters/IdPath'
      responses:
        204:
          description: OK
components:
  parameters:
    IdPath:
      name: id
      in: path
      required: true
      schema:
        type: string
    ProjectIdPath:
      name: projectId
      in: path
      required: true
      schema:
        type: string
    IdQuery:
      name: id
      in: query
      schema:
        type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
    EventName:
      in: query
      name: eventName
      required: false
      schema:
        type: string
    ClientSystem:
      in: query
      name: clientSystem
      required: false
      schema:
        type: string
    SubscriptionType:
      in: query
      name: subscriptionType
      required: false
      schema:
        type: string
  schemas:
    SubscriptionResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/Subscription'
        total:
          type: integer
    Subscription:
      type: object
      properties:
        id:
          type: string
        clientSystem:
          type: string
        subscriptionType:
          type: string
          enum: [ 'KAFKA', 'WEBHOOK' ]
        kafkaTopic:
          type: string
        eventName:
          type: string
        callbackUrl:
          type: string