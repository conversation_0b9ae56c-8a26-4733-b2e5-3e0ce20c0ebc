 create table if not exists event_topic (
    id varchar(255) not null,
    created_by varchar(255),
    created_date timestamp,
    updated_by varchar(255),
    updated_date timestamp,
    event varchar(255),
    topic varchar(255),
    primary key (id)
 );

alter table event_topic drop constraint if exists uk_event_topic_topic;
alter table event_topic add constraint uk_event_topic_topic unique (topic);