spring:
  application:
    name: business-events
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: password
  h2:
    console:
      enabled: true
events:
  - eventName: "status-s-event-rest"
    sourceTopics:
      - "test_in_topic"
    targetTopic: "test_out_topic"
    businessEntity: "TRADE"
    projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
    rules:
      - type: "FILTER"
        definition:
          id: "filter-1"
          field: "status"
          value: "s"
logging:
  level:
    com.poc.hss.fasttrack: DEBUG