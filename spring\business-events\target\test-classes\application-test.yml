spring:
  application:
    name: business-events
  flyway:
    locations: classpath:db/migration/h2,classpath:db/migration/common
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: password
  h2:
    console:
      enabled: true
kafkaConfig:
  bootstrap.servers: kafka-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:443
  group.id: test-group-id
  auto.offset.reset: earliest
  key.serializer: org.apache.kafka.common.serialization.StringSerializer
kafkaStreamConfig:
  application.id: test-application-id