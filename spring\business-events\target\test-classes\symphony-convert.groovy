import com.poc.hss.fasttrack.converter.JsonObjectConverter
import io.vertx.core.json.JsonObject

import java.util.stream.Collectors

class SymphonyConvertor implements JsonObjectConverter {
    @Override
    JsonObject convertJsonObjectWithEvent(JsonObject jsonObject, String event) {
        JsonObject target = new JsonObject();
        target.put("data", jsonObject);
        target.put("event", event);
        return target;
    }

    @Override
    String convertJsonObjectToStringWithEvent(JsonObject jsonObject, String event) {
        return "\"<div>" +
                "<card accent=\\\"tempo-bg-color--green\\\" style=\\\"width: 100\\\"" +
                " iconSrc=\\\"data:image/png;base64,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\\\">" +
                "<table>" +
                "<tr>" +
                "<td>" +
                "<h3 style=\\\"color: orange;\\\">Unity 2 Event</h3>" +
                "</td>" +
                "</tr>" +
                "<tr>" +
                "<td>" +
                "<h4><emoji shortcode=\\\"clinking_glass\\\"/> Event Received - " + event + "</h4>" +
                "</td>" +
                "</tr>" +
                "<tr>" +
                "<td>" +
                jsonObject.stream()
                        .map(f -> f.getKey() + ": " + String.valueOf(f.getValue()))
                        .sorted()
                        .collect(Collectors.joining(",")) +
                "</td>" +
                "</tr>" +
                "</table>" +
                "</card>" +
                "</div>\"";
    }
}
