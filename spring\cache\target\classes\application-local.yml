spring:
  config:
    import: application-jasypt.yml
  flyway:
    schemas: cache_schema
    locations: classpath:db/migration/postgres,classpath:db/migration/common
    baselineOnMigrate: true
  datasource:
    url: *****************************************
    username: postgres
    password: password
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        default_schema: cache_schema
        dialect: org.hibernate.dialect.PostgreSQLDialect
        hbm2ddl:
          auto: none
cache:
  topic: unity2-local-cache-metric
kafkaConfig:
  bootstrap.servers: "localhost:13333"
  group.id: project-DEV-cache
  auto.offset.reset: earliest
kubernetes:
  mock:
    enabled: true
    folder: local