server:
  port: 8088
  shutdown: graceful
spring:
  application:
    name: cache
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: true
  datasource:
    hikari:
      poolName: cache-connection-pool
      minimumIdle: 10
  config:
    import: optional:configtree:/hss/apps/secrets/
management:
  tracing:
    enabled: false
cache:
  retention:
    cron: '0 0 12 * * *'
    beforeDays: 180
reconciliation:
  scan:
    cron: '0 * * * * *'
    idleSeconds: 600
    expiredSeconds: 604800
supportAttributes:
  scan:
    cron: '0 */5 * * * *'