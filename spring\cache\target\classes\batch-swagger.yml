openapi: 3.0.0
info:
  version: 3.0.0
  title: Cache service
servers:
  - url: http://cache-service/api/v3
paths:
  /batch:
    get:
      summary: Search batch
      tags:
        - Batch
      operationId: searchBatch
      parameters:
        - $ref: '#/components/parameters/GroupQueryParam'
        - $ref: '#/components/parameters/ComponentQueryParam'
        - $ref: '#/components/parameters/BatchQueryParam'
        - $ref: '#/components/parameters/BatchStatusQueryParam'
        - name: offset
          in: query
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          schema:
            type: integer
        - name: sort
          in: query
          required: false
          description: format is {name}:[asc|desc]
          example: id:desc
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BatchPageResponse'
components:
  parameters:
    GroupQueryParam:
      name: group
      in: query
      schema:
        type: string
    ComponentQueryParam:
      name: component
      in: query
      schema:
        type: string
    BatchQueryParam:
      name: batch
      in: query
      schema:
        type: string
    BatchStatusQueryParam:
      name: status
      in: query
      schema:
        $ref: '#/components/schemas/BatchStatus'
  schemas:
    BatchResponse:
      type: object
      properties:
        id:
          type: string
        group:
          type: string
        component:
          type: string
        batch:
          type: string
        deployment:
          type: string
        sourceMetricOut:
          type: integer
          format: int64
        metricIn:
          type: integer
          format: int64
        topic:
          type: string
        consumerGroup:
          type: string
        completed:
          type: boolean
        status:
          $ref: '#/components/schemas/BatchStatus'
    BatchPageResponse:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/BatchResponse'
        total:
          type: integer
          format: int64
    BatchStatus:
      type: string
      enum: [ 'COMPLETED', 'IN_PROGRESS', 'COUNT_MISMATCH' ]