openapi: 3.0.0
info:
  version: 2.0.0
  title: Cache service
servers:
  - url: http://cache-service/api/v2
paths:
  /cache:
    get:
      summary: Get cache names
      tags:
        - CacheV2
      operationId: getCacheNames
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
  /cache/{name}:
    get:
      summary: Search cache
      tags:
        - CacheV2
      operationId: searchCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/LimitQuery'
        - $ref: '#/components/parameters/SortQuery'
        - $ref: '#/components/parameters/OffsetQuery'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CachePageResponseV2'
    delete:
      summary: Clear cache
      tags:
        - CacheV2
      operationId: clearCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
      responses:
        204:
          description: OK
  /cache/{name}/{key}:
    get:
      summary: Get cache
      tags:
        - CacheV2
      operationId: getCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/KeyPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
    put:
      summary: Put cache
      tags:
        - CacheV2
      operationId: putCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/KeyPath'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CacheRequestV2'
      responses:
        204:
          description: OK
    delete:
      summary: Evict cache
      tags:
        - CacheV2
      operationId: evictCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/KeyPath'
      responses:
        204:
          description: OK
components:
  parameters:
    NamePath:
      name: name
      in: path
      required: true
      schema:
        type: string
    KeyPath:
      name: key
      in: path
      required: true
      schema:
        type: string
    LimitQuery:
      in: query
      name: limit
      required: false
      schema:
        type: integer
    SortQuery:
      in: query
      name: sort
      required: false
      description: format is {name}:[asc|desc]
      example: id:desc
      schema:
        type: string
    OffsetQuery:
      in: query
      name: offset
      required: false
      schema:
        type: integer
  schemas:
    CacheRequestV2:
      type: object
      properties:
        value:
          type: object
        type:
          $ref: '#/components/schemas/CacheTypeV2'
        operation:
          $ref: '#/components/schemas/CacheOperationV2'
    CacheTypeV2:
      type: string
      enum:
        - NORMAL
        - COUNTER
    CacheOperationV2:
      type: string
      enum:
        - SET
        - INCREMENT
    CachePageResponseV2:
      type: object
      properties:
        total:
          type: integer
          format: int64
        data:
          type: array
          items:
            $ref: '#/components/schemas/CacheResponseV2'
    CacheResponseV2:
      type: object
      properties:
        id:
          type: string
        name:
          type: string
        key:
          type: string
        value:
          type: object
        type:
          $ref: '#/components/schemas/CacheTypeV2'
        createdTime:
          type: string
          format: date-time
        updatedTime:
          type: string
          format: date-time
