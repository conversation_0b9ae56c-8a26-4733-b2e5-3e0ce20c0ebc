openapi: 3.0.0
info:
  version: 3.0.0
  title: Cache service
servers:
  - url: http://cache-service/api/v3
paths:
  /cache-names:
    get:
      summary: Get cache names
      tags:
        - CacheV3
      operationId: getCacheNames
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
  /cache:
    get:
      summary: Search cache
      tags:
        - CacheV3
      operationId: searchCache
      parameters:
        - name: group
          in: query
          schema:
            type: string
        - name: component
          in: query
          schema:
            type: string
        - name: key
          in: query
          schema:
            type: string
        - $ref: '#/components/parameters/BatchQueryParam'
        - $ref: '#/components/parameters/BatchStatusQueryParam'
        - name: isBatch
          in: query
          required: false
          schema:
            type: boolean
        - name: offset
          in: query
          required: false
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          schema:
            type: integer
        - name: sort
          in: query
          required: false
          description: format is {name}:[asc|desc]
          example: id:desc
          schema:
            type: string
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CachePageResponseV3'
  /cache/{group}/{component}:
    delete:
      summary: Clear cache
      tags:
        - CacheV3
      operationId: clearCache
      parameters:
        - $ref: '#/components/parameters/GroupPathParam'
        - $ref: '#/components/parameters/ComponentPathParam'
      responses:
        204:
          description: OK
  /cache/{group}/{component}/{key}:
    get:
      summary: Get cache
      tags:
        - CacheV3
      operationId: getCache
      parameters:
        - $ref: '#/components/parameters/GroupPathParam'
        - $ref: '#/components/parameters/ComponentPathParam'
        - $ref: '#/components/parameters/KeyPathParam'
        - $ref: '#/components/parameters/BatchQueryParam'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheResponseV3'
    put:
      summary: Update cache
      tags:
        - CacheV3
      operationId: updateCache
      parameters:
        - $ref: '#/components/parameters/GroupPathParam'
        - $ref: '#/components/parameters/ComponentPathParam'
        - $ref: '#/components/parameters/KeyPathParam'
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CacheRequestV3'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CacheResponseV3'
    delete:
      summary: Evict cache
      tags:
        - CacheV3
      operationId: evictCache
      parameters:
        - $ref: '#/components/parameters/GroupPathParam'
        - $ref: '#/components/parameters/ComponentPathParam'
        - $ref: '#/components/parameters/KeyPathParam'
        - $ref: '#/components/parameters/BatchQueryParam'
      responses:
        204:
          description: OK
components:
  parameters:
    GroupPathParam:
      name: group
      in: path
      required: true
      schema:
        type: string
    ComponentPathParam:
      name: component
      in: path
      required: true
      schema:
        type: string
    KeyPathParam:
      name: key
      in: path
      required: true
      schema:
        type: string
    BatchQueryParam:
      name: batch
      in: query
      required: false
      schema:
        type: string
    BatchStatusQueryParam:
      name: status
      in: query
      required: false
      schema:
        $ref: '#/components/schemas/BatchStatus'
  schemas:
    CacheResponseV3:
      type: object
      properties:
        id:
          type: string
        group:
          type: string
        component:
          type: string
        batch:
          type: string
        key:
          type: string
        value:
          type: object
        type:
          $ref: '#/components/schemas/CacheTypeV3'
        status:
          $ref: '#/components/schemas/BatchStatus'
        createdTime:
          type: string
          format: date-time
        updatedTime:
          type: string
          format: date-time
    CachePageResponseV3:
      type: object
      properties:
        data:
          type: array
          items:
            $ref: '#/components/schemas/CacheResponseV3'
        total:
          type: integer
          format: int64
    CacheRequestV3:
      type: object
      properties:
        batch:
          type: string
        value:
          type: object
        type:
          $ref: '#/components/schemas/CacheTypeV3'
        operation:
          $ref: '#/components/schemas/CacheOperationV3'
    CacheTypeV3:
      type: string
      enum:
        - NORMAL
        - COUNTER
    CacheOperationV3:
      type: string
      enum:
        - SET
        - INCREMENT
        - RECONCILE
        - RESET_BATCH
    BatchStatus:
      type: string
      enum: [ 'COMPLETED', 'IN_PROGRESS', 'COUNT_MISMATCH' ]