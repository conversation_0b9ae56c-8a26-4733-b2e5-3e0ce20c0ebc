openapi: 3.0.0
info:
  version: 1.0.0
  title: Cache service
servers:
  - url: http://cache-service/api
paths:
  /cache:
    get:
      summary: Get cache names
      tags:
        - Cache
      operationId: getCacheNames
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  type: string
  /cache/{name}:
    delete:
      summary: Clear cache
      tags:
        - Cache
      operationId: clearCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
      responses:
        204:
          description: OK
  /cache/{name}/{key}:
    get:
      summary: Get cache
      tags:
        - Cache
      operationId: getCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/KeyPath'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                type: object
    put:
      summary: Put cache
      tags:
        - Cache
      operationId: putCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/KeyPath'
      requestBody:
        content:
          application/json:
            schema:
              type: object
      responses:
        204:
          description: OK
    delete:
      summary: Evict cache
      tags:
        - Cache
      operationId: evictCache
      parameters:
        - $ref: '#/components/parameters/NamePath'
        - $ref: '#/components/parameters/KeyPath'
      responses:
        204:
          description: OK
components:
  parameters:
    NamePath:
      name: name
      in: path
      required: true
      schema:
        type: string
    KeyPath:
      name: key
      in: path
      required: true
      schema:
        type: string