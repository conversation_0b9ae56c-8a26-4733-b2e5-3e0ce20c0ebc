create table kafka_offset
(
    id           character varying(255) NOT NULL,
    created_by   character varying(255),
    created_date timestamp without time zone,
    updated_by   character varying(255),
    updated_date timestamp without time zone,
    topic        character varying(255) not null,
    "partition"  integer                not null,
    "offset"     bigint                 not null,
    primary key (id),
    constraint uk_topic_partition unique(topic, "partition")
);

create index idx_kafka_offset_topic_partition on kafka_offset (topic, "partition");