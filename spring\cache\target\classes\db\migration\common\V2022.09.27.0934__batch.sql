create table batch
(
    id           character varying(255) NOT NULL,
    created_by   character varying(255),
    created_date timestamp without time zone,
    updated_by   character varying(255),
    updated_date timestamp without time zone,
    BATCH        character varying(255),
    DEPLOYMENT        character varying(255),
    CACHE_GROUP        character varying(255),
    COMPONENT        character varying(255),
    COMPLETED        boolean not null,
    SOURCE_METRIC_OUT        bigint,
    THIS_METRIC_IN        bigint,
    TOPIC        character varying(255),
    CONSUMER_GROUP        character varying(255),
    primary key (id)
);

create index idx_batch_batch on batch (BATCH);
create index idx_batch_completed on batch (COMPLETED);
create index idx_batch_group_component on batch (CACHE_GROUP,COMPONENT);