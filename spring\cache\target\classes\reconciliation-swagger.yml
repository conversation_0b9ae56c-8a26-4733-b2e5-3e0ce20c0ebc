openapi: 3.0.0
info:
  version: 3.0.0
  title: Reconciliation service
servers:
  - url: http://reconciliation-service/api/reconciliation
paths:
  /reset:
    post:
      summary: Reset the batch
      tags:
        - Reconciliation
      operationId: reset
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResetRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResetResponse'
  /reconcile:
    post:
      summary: Reconcile the metric
      tags:
        - Reconciliation
      operationId: reconcile
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ReconciliationRequest'
      responses:
        200:
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ReconciliationResponse'
components:
  schemas:
    ReconciliationResponse:
      type: object
      properties:
        completed:
          type: boolean
    ReconciliationRequest:
      type: object
      properties:
        group:
          type: string
        component:
          type: string
        batch:
          type: string
    ResetRequest:
      type: object
      properties:
        batch:
          type: string
    ResetResponse:
      type: object
      properties:
        result:
          type: integer
          format: int64