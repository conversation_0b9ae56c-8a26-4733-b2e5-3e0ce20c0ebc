<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">BatchApi.java</span></div><h1>BatchApi.java</h1><pre class="source lang-java linenums">/**
 * NOTE: This class is auto generated by the swagger code generator program (3.0.27).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.model.BatchPageResponse;
import com.poc.hss.fasttrack.model.BatchStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.CookieValue;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Validated
@RequestMapping(&quot;/api/batch&quot;)
public interface BatchApi {

<span class="fc" id="L47">    Logger log = LoggerFactory.getLogger(BatchApi.class);</span>

    default Optional&lt;ObjectMapper&gt; getObjectMapper(){
<span class="nc" id="L50">        return Optional.empty();</span>
    }

    default Optional&lt;HttpServletRequest&gt; getRequest(){
<span class="nc" id="L54">        return Optional.empty();</span>
    }

    default Optional&lt;String&gt; getAcceptHeader() {
<span class="nc" id="L58">        return getRequest().map(r -&gt; r.getHeader(&quot;Accept&quot;));</span>
    }

    @Operation(summary = &quot;Search batch&quot;, description = &quot;&quot;, tags={ &quot;Batch&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = BatchPageResponse.class))) })
    @RequestMapping(value = &quot;/batch&quot;,
        produces = { &quot;application/json&quot; }, 
        method = RequestMethod.GET)
    default ResponseEntity&lt;BatchPageResponse&gt; searchBatch(@Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;group&quot;, required = false) String group, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;component&quot;, required = false) String component, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;batch&quot;, required = false) String batch, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;status&quot;, required = false) BatchStatus status, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;offset&quot;, required = false) Integer offset, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;limit&quot;, required = false) Integer limit, @Parameter(in = ParameterIn.QUERY, description = &quot;format is {name}:[asc|desc]&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;sort&quot;, required = false) String sort) {
<span class="nc bnc" id="L68" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L71">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;{\r\n  \&quot;total\&quot; : 1,\r\n  \&quot;data\&quot; : [ {\r\n    \&quot;component\&quot; : \&quot;component\&quot;,\r\n    \&quot;sourceMetricOut\&quot; : 0,\r\n    \&quot;batch\&quot; : \&quot;batch\&quot;,\r\n    \&quot;topic\&quot; : \&quot;topic\&quot;,\r\n    \&quot;metricIn\&quot; : 6,\r\n    \&quot;id\&quot; : \&quot;id\&quot;,\r\n    \&quot;completed\&quot; : true,\r\n    \&quot;consumerGroup\&quot; : \&quot;consumerGroup\&quot;,\r\n    \&quot;group\&quot; : \&quot;group\&quot;,\r\n    \&quot;deployment\&quot; : \&quot;deployment\&quot;,\r\n    \&quot;status\&quot; : \&quot;COMPLETED\&quot;\r\n  }, {\r\n    \&quot;component\&quot; : \&quot;component\&quot;,\r\n    \&quot;sourceMetricOut\&quot; : 0,\r\n    \&quot;batch\&quot; : \&quot;batch\&quot;,\r\n    \&quot;topic\&quot; : \&quot;topic\&quot;,\r\n    \&quot;metricIn\&quot; : 6,\r\n    \&quot;id\&quot; : \&quot;id\&quot;,\r\n    \&quot;completed\&quot; : true,\r\n    \&quot;consumerGroup\&quot; : \&quot;consumerGroup\&quot;,\r\n    \&quot;group\&quot; : \&quot;group\&quot;,\r\n    \&quot;deployment\&quot; : \&quot;deployment\&quot;,\r\n    \&quot;status\&quot; : \&quot;COMPLETED\&quot;\r\n  } ]\r\n}&quot;, BatchPageResponse.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L72">                } catch (IOException e) {</span>
<span class="nc" id="L73">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L74">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L78">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default BatchApi interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L80">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }

}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>