<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchApiController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">BatchApiController.java</span></div><h1>BatchApiController.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.dto.BatchDTO;
import com.poc.hss.fasttrack.dto.BatchPageDTO;
import com.poc.hss.fasttrack.dto.BatchQueryDTO;
import com.poc.hss.fasttrack.dto.PageDTO;
import com.poc.hss.fasttrack.model.BatchPageResponse;
import com.poc.hss.fasttrack.model.BatchResponse;
import com.poc.hss.fasttrack.model.BatchStatus;
import com.poc.hss.fasttrack.service.BatchService;
import com.poc.hss.fasttrack.util.SortUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.stream.Collectors;

@RestController
<span class="fc" id="L20">public class BatchApiController extends BaseController implements BatchApi {</span>

    @Autowired
    private BatchService batchService;

    @Override
    public ResponseEntity&lt;BatchPageResponse&gt; searchBatch(String group, String component, @Valid String batch, @Valid BatchStatus status, @Valid Integer offset, @Valid Integer limit, @Valid String sort) {
<span class="nc" id="L27">        final BatchPageDTO batchPageDTO = batchService.searchBatch(</span>
                BatchQueryDTO
<span class="nc" id="L29">                        .builder()</span>
<span class="nc" id="L30">                        .group(group)</span>
<span class="nc" id="L31">                        .component(component)</span>
<span class="nc" id="L32">                        .batch(batch)</span>
<span class="nc" id="L33">                        .status(status)</span>
<span class="nc" id="L34">                        .build(),</span>
<span class="nc" id="L35">                PageDTO.builder()</span>
<span class="nc" id="L36">                        .offset(offset)</span>
<span class="nc" id="L37">                        .limit(limit)</span>
<span class="nc" id="L38">                        .sort(SortUtils.getSort(sort))</span>
<span class="nc" id="L39">                        .build()</span>
        );
<span class="nc" id="L41">        return ResponseEntity.ok(new BatchPageResponse()</span>
<span class="nc" id="L42">                .data(batchPageDTO.getData().stream().map(this::convert).collect(Collectors.toList()))</span>
<span class="nc" id="L43">                .total(batchPageDTO.getTotal()));</span>
    }

    private BatchResponse convert(BatchDTO batchDTO) {
<span class="nc" id="L47">        return new BatchResponse()</span>
<span class="nc" id="L48">                .id(batchDTO.getId())</span>
<span class="nc" id="L49">                .batch(batchDTO.getBatch())</span>
<span class="nc" id="L50">                .deployment(batchDTO.getDeployment())</span>
<span class="nc" id="L51">                .group(batchDTO.getGroup())</span>
<span class="nc" id="L52">                .component(batchDTO.getComponent())</span>
<span class="nc" id="L53">                .completed(batchDTO.getCompleted())</span>
<span class="nc" id="L54">                .sourceMetricOut(batchDTO.getSourceMetricOut())</span>
<span class="nc" id="L55">                .metricIn(batchDTO.getMetricIn())</span>
<span class="nc" id="L56">                .topic(batchDTO.getTopic())</span>
<span class="nc" id="L57">                .consumerGroup(batchDTO.getConsumerGroup())</span>
<span class="nc" id="L58">                .status(batchDTO.getStatus());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>