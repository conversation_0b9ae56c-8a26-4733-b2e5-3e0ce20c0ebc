<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">CacheApi.java</span></div><h1>CacheApi.java</h1><pre class="source lang-java linenums">/**
 * NOTE: This class is auto generated by the swagger code generator program (3.0.27).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.poc.hss.fasttrack.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.CookieValue;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Validated
@RequestMapping(&quot;/api&quot;)
public interface CacheApi {

<span class="fc" id="L45">    Logger log = LoggerFactory.getLogger(CacheApi.class);</span>

    default Optional&lt;ObjectMapper&gt; getObjectMapper(){
<span class="nc" id="L48">        return Optional.empty();</span>
    }

    default Optional&lt;HttpServletRequest&gt; getRequest(){
<span class="nc" id="L52">        return Optional.empty();</span>
    }

    default Optional&lt;String&gt; getAcceptHeader() {
<span class="nc" id="L56">        return getRequest().map(r -&gt; r.getHeader(&quot;Accept&quot;));</span>
    }

    @Operation(summary = &quot;Clear cache&quot;, description = &quot;&quot;, tags={ &quot;Cache&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;204&quot;, description = &quot;OK&quot;) })
    @RequestMapping(value = &quot;/cache/{name}&quot;,
        method = RequestMethod.DELETE)
    default ResponseEntity&lt;Void&gt; clearCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;name&quot;) String name) {
<span class="nc bnc" id="L65" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
        } else {
<span class="nc" id="L67">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheApi interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L69">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Evict cache&quot;, description = &quot;&quot;, tags={ &quot;Cache&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;204&quot;, description = &quot;OK&quot;) })
    @RequestMapping(value = &quot;/cache/{name}/{key}&quot;,
        method = RequestMethod.DELETE)
    default ResponseEntity&lt;Void&gt; evictCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;name&quot;) String name, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;key&quot;) String key) {
<span class="nc bnc" id="L79" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
        } else {
<span class="nc" id="L81">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheApi interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L83">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Get cache&quot;, description = &quot;&quot;, tags={ &quot;Cache&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = Object.class))) })
    @RequestMapping(value = &quot;/cache/{name}/{key}&quot;,
        produces = { &quot;application/json&quot; }, 
        method = RequestMethod.GET)
    default ResponseEntity&lt;Object&gt; getCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;name&quot;) String name, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;key&quot;) String key) {
<span class="nc bnc" id="L94" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L97">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;{ }&quot;, Object.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L98">                } catch (IOException e) {</span>
<span class="nc" id="L99">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L100">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L104">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheApi interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L106">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Get cache names&quot;, description = &quot;&quot;, tags={ &quot;Cache&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, array = @ArraySchema(schema = @Schema(implementation = String.class)))) })
    @RequestMapping(value = &quot;/cache&quot;,
        produces = { &quot;application/json&quot; }, 
        method = RequestMethod.GET)
    default ResponseEntity&lt;List&lt;String&gt;&gt; getCacheNames() {
<span class="nc bnc" id="L117" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L118" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L120">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;[ \&quot;\&quot;, \&quot;\&quot; ]&quot;, List.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L121">                } catch (IOException e) {</span>
<span class="nc" id="L122">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L123">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L127">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheApi interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L129">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Put cache&quot;, description = &quot;&quot;, tags={ &quot;Cache&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;204&quot;, description = &quot;OK&quot;) })
    @RequestMapping(value = &quot;/cache/{name}/{key}&quot;,
        consumes = { &quot;application/json&quot; }, 
        method = RequestMethod.PUT)
    default ResponseEntity&lt;Void&gt; putCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;name&quot;) String name, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;key&quot;) String key, @Parameter(in = ParameterIn.DEFAULT, description = &quot;&quot;, schema=@Schema()) @Valid @RequestBody Object body) {
<span class="nc bnc" id="L140" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
        } else {
<span class="nc" id="L142">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheApi interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L144">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }

}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>