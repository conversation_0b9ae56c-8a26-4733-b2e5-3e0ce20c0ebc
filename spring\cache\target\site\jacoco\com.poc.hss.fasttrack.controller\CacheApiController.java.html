<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheApiController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">CacheApiController.java</span></div><h1>CacheApiController.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.facade.CacheFacadeV1;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
<span class="fc" id="L13">public class CacheApiController extends BaseController implements CacheApi {</span>
<span class="fc" id="L14">    private static final ResponseEntity&lt;Void&gt; NO_CONTENT = new ResponseEntity&lt;&gt;(HttpStatus.NO_CONTENT);</span>

    @Autowired
    private CacheFacadeV1 cacheFacadeV1;

    @Override
    public ResponseEntity&lt;Void&gt; clearCache(String name) {
<span class="fc" id="L21">        cacheFacadeV1.clearCache(name);</span>
<span class="fc" id="L22">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;Void&gt; evictCache(String name, String key) {
<span class="fc" id="L27">        cacheFacadeV1.evictCache(name, key);</span>
<span class="fc" id="L28">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;Object&gt; getCache(String name, String key) {
<span class="fc" id="L33">        return ResponseEntity.ok(cacheFacadeV1.getCache(name, key));</span>
    }

    @Override
    public ResponseEntity&lt;List&lt;String&gt;&gt; getCacheNames() {
<span class="fc" id="L38">        return ResponseEntity.ok(cacheFacadeV1.getAllNames());</span>
    }

    @Override
    public ResponseEntity&lt;Void&gt; putCache(String name, String key, @Valid Object body) {
<span class="fc" id="L43">        cacheFacadeV1.putCache(name, key, body);</span>
<span class="fc" id="L44">        return NO_CONTENT;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>