<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheV2ApiController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">CacheV2ApiController.java</span></div><h1>CacheV2ApiController.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.dto.CachePageDTO;
import com.poc.hss.fasttrack.dto.PageDTO;
import com.poc.hss.fasttrack.facade.CacheFacadeV2;
import com.poc.hss.fasttrack.model.CachePageResponseV2;
import com.poc.hss.fasttrack.model.CacheRequestV2;
import com.poc.hss.fasttrack.model.CacheResponseV2;
import com.poc.hss.fasttrack.util.ConversionHelperService;
import com.poc.hss.fasttrack.util.SortUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
<span class="fc" id="L20">public class CacheV2ApiController extends BaseController implements CacheV2Api {</span>

<span class="fc" id="L22">    private static final ResponseEntity&lt;Void&gt; NO_CONTENT = new ResponseEntity&lt;&gt;(HttpStatus.NO_CONTENT);</span>

    @Autowired
    private ConversionHelperService conversionHelperService;

    @Autowired
    private CacheFacadeV2 cacheFacadeV2;

    @Override
    public ResponseEntity&lt;Void&gt; clearCache(String name) {
<span class="fc" id="L32">        cacheFacadeV2.clearCache(name);</span>
<span class="fc" id="L33">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;Void&gt; evictCache(String name, String key) {
<span class="fc" id="L38">        cacheFacadeV2.evictCache(name, key);</span>
<span class="fc" id="L39">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;Object&gt; getCache(String name, String key) {
<span class="fc" id="L44">        return ResponseEntity.ok(cacheFacadeV2.getCache(name, key));</span>
    }

    @Override
    public ResponseEntity&lt;List&lt;String&gt;&gt; getCacheNames() {
<span class="fc" id="L49">        return ResponseEntity.ok(cacheFacadeV2.getAllNames());</span>
    }

    @Override
    public ResponseEntity&lt;Void&gt; putCache(String name, String key, @Valid CacheRequestV2 body) {
<span class="fc" id="L54">        cacheFacadeV2.putCache(name, key, body);</span>
<span class="fc" id="L55">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;CachePageResponseV2&gt; searchCache(String name, Integer limit, String sort, Integer offset) {
<span class="nc" id="L60">        return ResponseEntity.ok(</span>
<span class="nc" id="L61">                cacheFacadeV2.searchCache(</span>
                        name,
<span class="nc" id="L63">                        PageDTO.builder()</span>
<span class="nc" id="L64">                                .limit(limit)</span>
<span class="nc" id="L65">                                .sort(SortUtils.getSort(sort))</span>
<span class="nc" id="L66">                                .offset(offset)</span>
<span class="nc" id="L67">                                .build()</span>
                )
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>