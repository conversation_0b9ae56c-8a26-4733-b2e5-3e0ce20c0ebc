<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheV3Api.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">CacheV3Api.java</span></div><h1>CacheV3Api.java</h1><pre class="source lang-java linenums">/**
 * NOTE: This class is auto generated by the swagger code generator program (3.0.27).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.model.BatchStatus;
import com.poc.hss.fasttrack.model.CachePageResponseV3;
import com.poc.hss.fasttrack.model.CacheRequestV3;
import com.poc.hss.fasttrack.model.CacheResponseV3;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.CookieValue;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Validated
@RequestMapping(&quot;/api/v3&quot;)
public interface CacheV3Api {

<span class="fc" id="L49">    Logger log = LoggerFactory.getLogger(CacheV3Api.class);</span>

    default Optional&lt;ObjectMapper&gt; getObjectMapper(){
<span class="nc" id="L52">        return Optional.empty();</span>
    }

    default Optional&lt;HttpServletRequest&gt; getRequest(){
<span class="nc" id="L56">        return Optional.empty();</span>
    }

    default Optional&lt;String&gt; getAcceptHeader() {
<span class="nc" id="L60">        return getRequest().map(r -&gt; r.getHeader(&quot;Accept&quot;));</span>
    }

    @Operation(summary = &quot;Clear cache&quot;, description = &quot;&quot;, tags={ &quot;CacheV3&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;204&quot;, description = &quot;OK&quot;) })
    @RequestMapping(value = &quot;/cache/{group}/{component}&quot;,
        method = RequestMethod.DELETE)
    default ResponseEntity&lt;Void&gt; clearCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;group&quot;) String group, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;component&quot;) String component) {
<span class="nc bnc" id="L69" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
        } else {
<span class="nc" id="L71">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheV3Api interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L73">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Evict cache&quot;, description = &quot;&quot;, tags={ &quot;CacheV3&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;204&quot;, description = &quot;OK&quot;) })
    @RequestMapping(value = &quot;/cache/{group}/{component}/{key}&quot;,
        method = RequestMethod.DELETE)
    default ResponseEntity&lt;Void&gt; evictCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;group&quot;) String group, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;component&quot;) String component, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;key&quot;) String key, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;batch&quot;, required = false) String batch) {
<span class="nc bnc" id="L83" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
        } else {
<span class="nc" id="L85">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheV3Api interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L87">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Get cache&quot;, description = &quot;&quot;, tags={ &quot;CacheV3&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CacheResponseV3.class))) })
    @RequestMapping(value = &quot;/cache/{group}/{component}/{key}&quot;,
        produces = { &quot;application/json&quot; }, 
        method = RequestMethod.GET)
    default ResponseEntity&lt;CacheResponseV3&gt; getCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;group&quot;) String group, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;component&quot;) String component, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;key&quot;) String key, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;batch&quot;, required = false) String batch) {
<span class="nc bnc" id="L98" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L101">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;{\r\n  \&quot;updatedTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n  \&quot;component\&quot; : \&quot;component\&quot;,\r\n  \&quot;batch\&quot; : \&quot;batch\&quot;,\r\n  \&quot;createdTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n  \&quot;id\&quot; : \&quot;id\&quot;,\r\n  \&quot;type\&quot; : \&quot;NORMAL\&quot;,\r\n  \&quot;value\&quot; : { },\r\n  \&quot;key\&quot; : \&quot;key\&quot;,\r\n  \&quot;group\&quot; : \&quot;group\&quot;,\r\n  \&quot;status\&quot; : \&quot;COMPLETED\&quot;\r\n}&quot;, CacheResponseV3.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L102">                } catch (IOException e) {</span>
<span class="nc" id="L103">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L104">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L108">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheV3Api interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L110">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Get cache names&quot;, description = &quot;&quot;, tags={ &quot;CacheV3&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, array = @ArraySchema(schema = @Schema(implementation = String.class)))) })
    @RequestMapping(value = &quot;/cache-names&quot;,
        produces = { &quot;application/json&quot; }, 
        method = RequestMethod.GET)
    default ResponseEntity&lt;List&lt;String&gt;&gt; getCacheNames() {
<span class="nc bnc" id="L121" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L124">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;[ \&quot;\&quot;, \&quot;\&quot; ]&quot;, List.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L125">                } catch (IOException e) {</span>
<span class="nc" id="L126">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L127">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L131">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheV3Api interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L133">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Search cache&quot;, description = &quot;&quot;, tags={ &quot;CacheV3&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CachePageResponseV3.class))) })
    @RequestMapping(value = &quot;/cache&quot;,
        produces = { &quot;application/json&quot; }, 
        method = RequestMethod.GET)
    default ResponseEntity&lt;CachePageResponseV3&gt; searchCache(@Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;group&quot;, required = false) String group, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;component&quot;, required = false) String component, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;key&quot;, required = false) String key, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;batch&quot;, required = false) String batch, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;status&quot;, required = false) BatchStatus status, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;isBatch&quot;, required = false) Boolean isBatch, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;offset&quot;, required = false) Integer offset, @Parameter(in = ParameterIn.QUERY, description = &quot;&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;limit&quot;, required = false) Integer limit, @Parameter(in = ParameterIn.QUERY, description = &quot;format is {name}:[asc|desc]&quot; ,schema=@Schema()) @Valid @RequestParam(value = &quot;sort&quot;, required = false) String sort) {
<span class="nc bnc" id="L144" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L147">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;{\r\n  \&quot;total\&quot; : 0,\r\n  \&quot;data\&quot; : [ {\r\n    \&quot;updatedTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n    \&quot;component\&quot; : \&quot;component\&quot;,\r\n    \&quot;batch\&quot; : \&quot;batch\&quot;,\r\n    \&quot;createdTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n    \&quot;id\&quot; : \&quot;id\&quot;,\r\n    \&quot;type\&quot; : \&quot;NORMAL\&quot;,\r\n    \&quot;value\&quot; : { },\r\n    \&quot;key\&quot; : \&quot;key\&quot;,\r\n    \&quot;group\&quot; : \&quot;group\&quot;,\r\n    \&quot;status\&quot; : \&quot;COMPLETED\&quot;\r\n  }, {\r\n    \&quot;updatedTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n    \&quot;component\&quot; : \&quot;component\&quot;,\r\n    \&quot;batch\&quot; : \&quot;batch\&quot;,\r\n    \&quot;createdTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n    \&quot;id\&quot; : \&quot;id\&quot;,\r\n    \&quot;type\&quot; : \&quot;NORMAL\&quot;,\r\n    \&quot;value\&quot; : { },\r\n    \&quot;key\&quot; : \&quot;key\&quot;,\r\n    \&quot;group\&quot; : \&quot;group\&quot;,\r\n    \&quot;status\&quot; : \&quot;COMPLETED\&quot;\r\n  } ]\r\n}&quot;, CachePageResponseV3.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L148">                } catch (IOException e) {</span>
<span class="nc" id="L149">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L150">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L154">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheV3Api interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L156">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }


    @Operation(summary = &quot;Update cache&quot;, description = &quot;&quot;, tags={ &quot;CacheV3&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = CacheResponseV3.class))) })
    @RequestMapping(value = &quot;/cache/{group}/{component}/{key}&quot;,
        produces = { &quot;application/json&quot; }, 
        consumes = { &quot;application/json&quot; }, 
        method = RequestMethod.PUT)
    default ResponseEntity&lt;CacheResponseV3&gt; updateCache(@Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;group&quot;) String group, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;component&quot;) String component, @Parameter(in = ParameterIn.PATH, description = &quot;&quot;, required=true, schema=@Schema()) @PathVariable(&quot;key&quot;) String key, @Parameter(in = ParameterIn.DEFAULT, description = &quot;&quot;, schema=@Schema()) @Valid @RequestBody CacheRequestV3 body) {
<span class="nc bnc" id="L168" title="All 4 branches missed.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="nc" id="L171">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;{\r\n  \&quot;updatedTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n  \&quot;component\&quot; : \&quot;component\&quot;,\r\n  \&quot;batch\&quot; : \&quot;batch\&quot;,\r\n  \&quot;createdTime\&quot; : \&quot;2000-01-23T04:56:07.000+00:00\&quot;,\r\n  \&quot;id\&quot; : \&quot;id\&quot;,\r\n  \&quot;type\&quot; : \&quot;NORMAL\&quot;,\r\n  \&quot;value\&quot; : { },\r\n  \&quot;key\&quot; : \&quot;key\&quot;,\r\n  \&quot;group\&quot; : \&quot;group\&quot;,\r\n  \&quot;status\&quot; : \&quot;COMPLETED\&quot;\r\n}&quot;, CacheResponseV3.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L172">                } catch (IOException e) {</span>
<span class="nc" id="L173">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L174">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="nc" id="L178">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default CacheV3Api interface so no example is generated&quot;);</span>
        }
<span class="nc" id="L180">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }

}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>