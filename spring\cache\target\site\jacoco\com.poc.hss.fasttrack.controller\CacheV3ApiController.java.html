<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheV3ApiController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">CacheV3ApiController.java</span></div><h1>CacheV3ApiController.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.dto.CacheCompositeKeyDTO;
import com.poc.hss.fasttrack.dto.CacheQueryDTO;
import com.poc.hss.fasttrack.dto.CacheUpdateDTO;
import com.poc.hss.fasttrack.dto.PageDTO;
import com.poc.hss.fasttrack.facade.CacheFacadeV3;
import com.poc.hss.fasttrack.model.BatchStatus;
import com.poc.hss.fasttrack.model.CachePageResponseV3;
import com.poc.hss.fasttrack.model.CacheRequestV3;
import com.poc.hss.fasttrack.model.CacheResponseV3;
import com.poc.hss.fasttrack.util.SortUtils;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
<span class="fc" id="L23">public class CacheV3ApiController extends BaseController implements CacheV3Api {</span>

<span class="fc" id="L25">    private static final ResponseEntity&lt;Void&gt; NO_CONTENT = new ResponseEntity&lt;&gt;(HttpStatus.NO_CONTENT);</span>

    @Autowired
    private CacheFacadeV3 cacheFacadeV3;

    @Override
    public ResponseEntity&lt;List&lt;String&gt;&gt; getCacheNames() {
<span class="nc" id="L32">        return ResponseEntity.ok(cacheFacadeV3.getAllNames());</span>
    }

    @Override
    public ResponseEntity&lt;Void&gt; clearCache(String group, String component) {
<span class="fc" id="L37">        cacheFacadeV3.clearCache(group, component);</span>
<span class="fc" id="L38">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;Void&gt; evictCache(String group, String component, String key, String batch) {
<span class="fc" id="L43">        cacheFacadeV3.evictCache(</span>
<span class="fc" id="L44">                CacheCompositeKeyDTO.builder()</span>
<span class="fc" id="L45">                        .group(group)</span>
<span class="fc" id="L46">                        .component(component)</span>
<span class="fc" id="L47">                        .key(key)</span>
<span class="fc" id="L48">                        .batch(batch)</span>
<span class="fc" id="L49">                        .build()</span>
        );
<span class="fc" id="L51">        return NO_CONTENT;</span>
    }

    @Override
    public ResponseEntity&lt;CacheResponseV3&gt; getCache(String group, String component, String key, String batch) {
<span class="fc" id="L56">        return ResponseEntity.ok(</span>
<span class="fc" id="L57">                cacheFacadeV3.getCache(</span>
<span class="fc" id="L58">                        CacheCompositeKeyDTO.builder()</span>
<span class="fc" id="L59">                                .group(group)</span>
<span class="fc" id="L60">                                .component(component)</span>
<span class="fc" id="L61">                                .key(key)</span>
<span class="fc" id="L62">                                .batch(batch)</span>
<span class="fc" id="L63">                                .build()</span>
                )
        );
    }

    @Override
    public ResponseEntity&lt;CachePageResponseV3&gt; searchCache(@Valid String group, @Valid String component, @Valid String key, @Valid String batch, @Valid BatchStatus status, @Valid Boolean isBatch, @Valid Integer offset, @Valid Integer limit, @Valid String sort) {
<span class="fc" id="L70">        return ResponseEntity.ok(</span>
<span class="fc" id="L71">                cacheFacadeV3.searchCache(</span>
                        CacheQueryDTO
<span class="fc" id="L73">                                .builder()</span>
<span class="fc" id="L74">                                .group(group)</span>
<span class="fc" id="L75">                                .component(component)</span>
<span class="fc" id="L76">                                .batch(batch)</span>
<span class="fc" id="L77">                                .status(status)</span>
<span class="fc" id="L78">                                .key(key)</span>
<span class="fc" id="L79">                                .isBatch(BooleanUtils.isTrue(isBatch))</span>
<span class="fc" id="L80">                                .build(),</span>
<span class="fc" id="L81">                        PageDTO.builder()</span>
<span class="fc" id="L82">                                .offset(offset)</span>
<span class="fc" id="L83">                                .limit(limit)</span>
<span class="fc" id="L84">                                .sort(SortUtils.getSort(sort))</span>
<span class="fc" id="L85">                                .build()</span>
                )
        );
    }

    @Override
    public ResponseEntity&lt;CacheResponseV3&gt; updateCache(String group, String component, String key, CacheRequestV3 body) {
<span class="fc" id="L92">        return ResponseEntity.ok(</span>
<span class="fc" id="L93">                cacheFacadeV3.putCache(</span>
<span class="fc" id="L94">                        CacheCompositeKeyDTO.builder()</span>
<span class="fc" id="L95">                                .group(group)</span>
<span class="fc" id="L96">                                .component(component)</span>
<span class="fc" id="L97">                                .key(key)</span>
<span class="fc" id="L98">                                .batch(body.getBatch())</span>
<span class="fc" id="L99">                                .build(),</span>
<span class="fc" id="L100">                        CacheUpdateDTO.builder()</span>
<span class="fc" id="L101">                                .value(body.getValue())</span>
<span class="fc" id="L102">                                .type(body.getType())</span>
<span class="fc" id="L103">                                .operation(body.getOperation())</span>
<span class="fc" id="L104">                                .build()</span>
                )
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>