<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationApiController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">ReconciliationApiController.java</span></div><h1>ReconciliationApiController.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.dto.ReconciliationRequestDTO;
import com.poc.hss.fasttrack.model.ReconciliationRequest;
import com.poc.hss.fasttrack.model.ReconciliationResponse;
import com.poc.hss.fasttrack.model.ResetRequest;
import com.poc.hss.fasttrack.model.ResetResponse;
import com.poc.hss.fasttrack.service.ReconciliationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
<span class="fc" id="L16">public class ReconciliationApiController extends BaseController implements ReconciliationApi {</span>
    @Autowired
    private ReconciliationService reconciliationService;

    @Override
    public ResponseEntity&lt;ReconciliationResponse&gt; reconcile(@Valid ReconciliationRequest request) {
<span class="nc" id="L22">        return ResponseEntity.ok(reconciliationService.reconcile(ReconciliationRequestDTO.builder()</span>
<span class="nc" id="L23">                .group(request.getGroup())</span>
<span class="nc" id="L24">                .component(request.getComponent())</span>
<span class="nc" id="L25">                .batch(request.getBatch())</span>
<span class="nc" id="L26">                .build()).toResponse());</span>
    }

    @Override
    public ResponseEntity&lt;ResetResponse&gt; reset(@Valid ResetRequest request) {
<span class="nc" id="L31">        long deleted = reconciliationService.reset(request.getBatch());</span>
<span class="nc" id="L32">        ResetResponse response = new ResetResponse();</span>
<span class="nc" id="L33">        response.setResult(deleted);</span>
<span class="nc" id="L34">        return ResponseEntity.ok(response);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>