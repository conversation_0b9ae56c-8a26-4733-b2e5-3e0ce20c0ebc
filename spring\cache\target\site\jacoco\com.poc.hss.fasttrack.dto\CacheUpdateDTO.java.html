<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheUpdateDTO.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_source">CacheUpdateDTO.java</span></div><h1>CacheUpdateDTO.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dto;

import com.poc.hss.fasttrack.model.CacheOperationV3;
import com.poc.hss.fasttrack.model.CacheTypeV3;
import lombok.Builder;
import lombok.Data;

<span class="pc bnc" id="L8" title="All 30 branches missed.">@Data</span>
<span class="pc" id="L9">@Builder</span>
public class CacheUpdateDTO {
<span class="fc" id="L11">    private Object value;</span>
<span class="fc" id="L12">    private CacheTypeV3 type;</span>
<span class="fc" id="L13">    private CacheOperationV3 operation;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>