<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,087 of 3,490</td><td class="ctr2">11%</td><td class="bar">504 of 504</td><td class="ctr2">0%</td><td class="ctr1">458</td><td class="ctr2">528</td><td class="ctr1">60</td><td class="ctr2">95</td><td class="ctr1">206</td><td class="ctr2">276</td><td class="ctr1">15</td><td class="ctr2">26</td></tr></tfoot><tbody><tr><td id="a0"><a href="BatchDTO.html" class="el_class">BatchDTO</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="513" alt="513"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="94" alt="94"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">75</td><td class="ctr2" id="g0">75</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">28</td><td class="ctr2" id="k0">28</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a9"><a href="CacheDTO.html" class="el_class">CacheDTO</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="95" height="10" title="407" alt="407"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="67" alt="67"/></td><td class="ctr2" id="c10">14%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="86" alt="86"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">57</td><td class="ctr2" id="g1">69</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j4">14</td><td class="ctr2" id="k1">26</td><td class="ctr1" id="l15">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a18"><a href="KafkaCacheRequestDTO.html" class="el_class">KafkaCacheRequestDTO</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="346" alt="346"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="62" alt="62"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">52</td><td class="ctr2" id="g2">52</td><td class="ctr1" id="h1">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j1">21</td><td class="ctr2" id="k2">21</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a24"><a href="SupportAttributesDTO.html" class="el_class">SupportAttributesDTO</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="303" alt="303"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="54" alt="54"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">46</td><td class="ctr2" id="g3">46</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j2">19</td><td class="ctr2" id="k3">19</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a14"><a href="CacheQueryDTO.html" class="el_class">CacheQueryDTO</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="251" alt="251"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="43" alt="43"/></td><td class="ctr2" id="c9">14%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="54" alt="54"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">36</td><td class="ctr2" id="g4">44</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j9">9</td><td class="ctr2" id="k4">17</td><td class="ctr1" id="l16">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a20"><a href="ReconciliationRequestDTO.html" class="el_class">ReconciliationRequestDTO</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="215" alt="215"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="38" alt="38"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">34</td><td class="ctr2" id="g6">34</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j3">15</td><td class="ctr2" id="k6">15</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a5"><a href="BatchQueryDTO.html" class="el_class">BatchQueryDTO</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="212" alt="212"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="38" alt="38"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">33</td><td class="ctr2" id="g7">33</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j5">14</td><td class="ctr2" id="k7">14</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a7"><a href="CacheCompositeKeyDTO.html" class="el_class">CacheCompositeKeyDTO</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="173" alt="173"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="50" alt="50"/></td><td class="ctr2" id="c7">22%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="38" alt="38"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">27</td><td class="ctr2" id="g5">35</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j11">8</td><td class="ctr2" id="k5">16</td><td class="ctr1" id="l17">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a16"><a href="CacheUpdateDTO.html" class="el_class">CacheUpdateDTO</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="31" height="10" title="135" alt="135"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="36" alt="36"/></td><td class="ctr2" id="c8">21%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="30" alt="30"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">21</td><td class="ctr2" id="g8">27</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j14">6</td><td class="ctr2" id="k10">12</td><td class="ctr1" id="l18">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a1"><a href="BatchDTO$BatchDTOBuilder.html" class="el_class">BatchDTO.BatchDTOBuilder</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="109" alt="109"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">14</td><td class="ctr2" id="g10">14</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j6">14</td><td class="ctr2" id="k8">14</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a22"><a href="ReconciliationResponseDTO.html" class="el_class">ReconciliationResponseDTO</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="83" alt="83"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="10" alt="10"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">15</td><td class="ctr2" id="g9">15</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j7">10</td><td class="ctr2" id="k11">10</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a19"><a href="KafkaCacheRequestDTO$KafkaCacheRequestDTOBuilder.html" class="el_class">KafkaCacheRequestDTO.KafkaCacheRequestDTOBuilder</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="75" alt="75"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">10</td><td class="ctr2" id="g12">10</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j8">10</td><td class="ctr2" id="k12">10</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m11">1</td></tr><tr><td id="a25"><a href="SupportAttributesDTO$SupportAttributesDTOBuilder.html" class="el_class">SupportAttributesDTO.SupportAttributesDTOBuilder</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="66" alt="66"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">9</td><td class="ctr2" id="g13">9</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j10">9</td><td class="ctr2" id="k13">9</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m12">1</td></tr><tr><td id="a21"><a href="ReconciliationRequestDTO$ReconciliationRequestDTOBuilder.html" class="el_class">ReconciliationRequestDTO.ReconciliationRequestDTOBuilder</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="46" alt="46"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">7</td><td class="ctr2" id="g15">7</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j12">7</td><td class="ctr2" id="k15">7</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m13">1</td></tr><tr><td id="a6"><a href="BatchQueryDTO$BatchQueryDTOBuilder.html" class="el_class">BatchQueryDTO.BatchQueryDTOBuilder</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="46" alt="46"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">7</td><td class="ctr2" id="g16">7</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j13">7</td><td class="ctr2" id="k16">7</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m14">1</td></tr><tr><td id="a10"><a href="CacheDTO$CacheDTOBuilder.html" class="el_class">CacheDTO.CacheDTOBuilder</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="27" alt="27"/><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="77" alt="77"/></td><td class="ctr2" id="c4">74%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g11">13</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k9">13</td><td class="ctr1" id="l19">0</td><td class="ctr2" id="m15">1</td></tr><tr><td id="a23"><a href="ReconciliationResponseDTO$ReconciliationResponseDTOBuilder.html" class="el_class">ReconciliationResponseDTO.ReconciliationResponseDTOBuilder</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="18" alt="18"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g19">4</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j15">4</td><td class="ctr2" id="k19">4</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m16">1</td></tr><tr><td id="a15"><a href="CacheQueryDTO$CacheQueryDTOBuilder.html" class="el_class">CacheQueryDTO.CacheQueryDTOBuilder</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="11" height="10" title="49" alt="49"/></td><td class="ctr2" id="c3">76%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g14">9</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k14">9</td><td class="ctr1" id="l20">0</td><td class="ctr2" id="m17">1</td></tr><tr><td id="a17"><a href="CacheUpdateDTO$CacheUpdateDTOBuilder.html" class="el_class">CacheUpdateDTO.CacheUpdateDTOBuilder</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="28" alt="28"/></td><td class="ctr2" id="c5">71%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g18">6</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k18">6</td><td class="ctr1" id="l21">0</td><td class="ctr2" id="m18">1</td></tr><tr><td id="a8"><a href="CacheCompositeKeyDTO$CacheCompositeKeyDTOBuilder.html" class="el_class">CacheCompositeKeyDTO.CacheCompositeKeyDTOBuilder</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="35" alt="35"/></td><td class="ctr2" id="c2">77%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g17">7</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k17">7</td><td class="ctr1" id="l22">0</td><td class="ctr2" id="m19">1</td></tr><tr><td id="a2"><a href="BatchPageDTO.html" class="el_class">BatchPageDTO</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="8" alt="8"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f16">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j16">2</td><td class="ctr2" id="k20">2</td><td class="ctr1" id="l12">1</td><td class="ctr2" id="m20">1</td></tr><tr><td id="a3"><a href="BatchPageDTO$BatchPageDTOBuilder.html" class="el_class">BatchPageDTO.BatchPageDTOBuilder</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="7" alt="7"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f17">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j17">2</td><td class="ctr2" id="k21">2</td><td class="ctr1" id="l13">1</td><td class="ctr2" id="m21">1</td></tr><tr><td id="a4"><a href="BatchPageDTO$BatchPageDTOBuilderImpl.html" class="el_class">BatchPageDTO.BatchPageDTOBuilderImpl</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="7" alt="7"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f18">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j18">2</td><td class="ctr2" id="k22">2</td><td class="ctr1" id="l14">1</td><td class="ctr2" id="m22">1</td></tr><tr><td id="a12"><a href="CachePageDTO$CachePageDTOBuilder.html" class="el_class">CachePageDTO.CachePageDTOBuilder</a></td><td class="bar" id="b23"/><td class="ctr2" id="c6">42%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">2</td><td class="ctr1" id="l23">0</td><td class="ctr2" id="m23">1</td></tr><tr><td id="a11"><a href="CachePageDTO.html" class="el_class">CachePageDTO</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="8" alt="8"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">2</td><td class="ctr1" id="l24">0</td><td class="ctr2" id="m24">1</td></tr><tr><td id="a13"><a href="CachePageDTO$CachePageDTOBuilderImpl.html" class="el_class">CachePageDTO.CachePageDTOBuilderImpl</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="7" alt="7"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">2</td><td class="ctr1" id="l25">0</td><td class="ctr2" id="m25">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>