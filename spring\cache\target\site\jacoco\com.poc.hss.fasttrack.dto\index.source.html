<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,087 of 3,490</td><td class="ctr2">11%</td><td class="bar">504 of 504</td><td class="ctr2">0%</td><td class="ctr1">458</td><td class="ctr2">528</td><td class="ctr1">60</td><td class="ctr2">95</td><td class="ctr1">206</td><td class="ctr2">276</td><td class="ctr1">15</td><td class="ctr2">26</td></tr></tfoot><tbody><tr><td id="a0"><a href="BatchDTO.java.html" class="el_source">BatchDTO.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="622" alt="622"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="94" alt="94"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">89</td><td class="ctr2" id="g0">89</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">42</td><td class="ctr2" id="k0">42</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m2">2</td></tr><tr><td id="a4"><a href="CacheDTO.java.html" class="el_source">CacheDTO.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="83" height="10" title="434" alt="434"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="144" alt="144"/></td><td class="ctr2" id="c4">24%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="109" height="10" title="86" alt="86"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">58</td><td class="ctr2" id="g1">82</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j5">15</td><td class="ctr2" id="k1">39</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m3">2</td></tr><tr><td id="a8"><a href="KafkaCacheRequestDTO.java.html" class="el_source">KafkaCacheRequestDTO.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="81" height="10" title="421" alt="421"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="62" alt="62"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">62</td><td class="ctr2" id="g2">62</td><td class="ctr1" id="h1">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j1">31</td><td class="ctr2" id="k2">31</td><td class="ctr1" id="l2">2</td><td class="ctr2" id="m4">2</td></tr><tr><td id="a11"><a href="SupportAttributesDTO.java.html" class="el_source">SupportAttributesDTO.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="71" height="10" title="369" alt="369"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="54" alt="54"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">55</td><td class="ctr2" id="g3">55</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j2">28</td><td class="ctr2" id="k3">28</td><td class="ctr1" id="l3">2</td><td class="ctr2" id="m5">2</td></tr><tr><td id="a6"><a href="CacheQueryDTO.java.html" class="el_source">CacheQueryDTO.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="266" alt="266"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="92" alt="92"/></td><td class="ctr2" id="c3">25%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="68" height="10" title="54" alt="54"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">37</td><td class="ctr2" id="g4">53</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j7">10</td><td class="ctr2" id="k4">26</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m6">2</td></tr><tr><td id="a9"><a href="ReconciliationRequestDTO.java.html" class="el_source">ReconciliationRequestDTO.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="261" alt="261"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="38" alt="38"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">41</td><td class="ctr2" id="g6">41</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j3">22</td><td class="ctr2" id="k6">22</td><td class="ctr1" id="l4">2</td><td class="ctr2" id="m7">2</td></tr><tr><td id="a2"><a href="BatchQueryDTO.java.html" class="el_source">BatchQueryDTO.java</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="49" height="10" title="258" alt="258"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="38" alt="38"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">40</td><td class="ctr2" id="g7">40</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j4">21</td><td class="ctr2" id="k7">21</td><td class="ctr1" id="l5">2</td><td class="ctr2" id="m8">2</td></tr><tr><td id="a3"><a href="CacheCompositeKeyDTO.java.html" class="el_source">CacheCompositeKeyDTO.java</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="183" alt="183"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="85" alt="85"/></td><td class="ctr2" id="c1">31%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="38" alt="38"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">28</td><td class="ctr2" id="g5">42</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j8">9</td><td class="ctr2" id="k5">23</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">2</td></tr><tr><td id="a7"><a href="CacheUpdateDTO.java.html" class="el_source">CacheUpdateDTO.java</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="146" alt="146"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="64" alt="64"/></td><td class="ctr2" id="c2">30%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="30" alt="30"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">22</td><td class="ctr2" id="g8">33</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j9">7</td><td class="ctr2" id="k8">18</td><td class="ctr1" id="l10">0</td><td class="ctr2" id="m10">2</td></tr><tr><td id="a10"><a href="ReconciliationResponseDTO.java.html" class="el_source">ReconciliationResponseDTO.java</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="101" alt="101"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="10" alt="10"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">19</td><td class="ctr2" id="g9">19</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j6">14</td><td class="ctr2" id="k9">14</td><td class="ctr1" id="l6">2</td><td class="ctr2" id="m11">2</td></tr><tr><td id="a1"><a href="BatchPageDTO.java.html" class="el_source">BatchPageDTO.java</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="22" alt="22"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">6</td><td class="ctr2" id="g10">6</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">6</td><td class="ctr2" id="k10">6</td><td class="ctr1" id="l0">3</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a5"><a href="CachePageDTO.java.html" class="el_source">CachePageDTO.java</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="18" alt="18"/></td><td class="ctr2" id="c0">81%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">6</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">6</td><td class="ctr1" id="l11">0</td><td class="ctr2" id="m1">3</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>