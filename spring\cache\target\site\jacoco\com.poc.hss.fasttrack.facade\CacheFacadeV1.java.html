<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheFacadeV1.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.facade</a> &gt; <span class="el_source">CacheFacadeV1.java</span></div><h1>CacheFacadeV1.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.facade;

import com.poc.hss.fasttrack.dto.CacheCompositeKey;
import com.poc.hss.fasttrack.dto.CacheDTO;
import com.poc.hss.fasttrack.dto.CacheCompositeKeyDTO;
import com.poc.hss.fasttrack.dto.CacheUpdateDTO;
import com.poc.hss.fasttrack.model.CacheOperationV3;
import com.poc.hss.fasttrack.model.CacheTypeV3;
import com.poc.hss.fasttrack.service.CacheService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L23">public class CacheFacadeV1 {</span>
<span class="fc" id="L24">    private static final Pattern NAME_PATTERN = Pattern.compile(&quot;unity2-metric:(.+):(.+)&quot;);</span>
<span class="fc" id="L25">    private static final Pattern KEY_PATTERN = Pattern.compile(&quot;^([^:]+)(?::([^:]+))?$&quot;);</span>

    @Autowired
    private CacheService cacheService;

    protected CacheCompositeKeyDTO getCacheCompositeKeyDTO(String name) {
<span class="fc" id="L31">        return getCacheCompositeKeyDTO(name, null);</span>
    }

    protected CacheCompositeKeyDTO getCacheCompositeKeyDTO(String name, String key) {
<span class="fc" id="L35">        Matcher nameMatcher = NAME_PATTERN.matcher(name);</span>
<span class="fc" id="L36">        Matcher keyMatcher = Optional.ofNullable(key).map(KEY_PATTERN::matcher).orElse(null);</span>
<span class="pc bpc" id="L37" title="2 of 6 branches missed.">        if (nameMatcher.find() &amp;&amp; (keyMatcher == null || keyMatcher.find())) {</span>
<span class="fc" id="L38">            return CacheCompositeKeyDTO.builder()</span>
<span class="fc" id="L39">                    .group(nameMatcher.group(1))</span>
<span class="fc" id="L40">                    .component(nameMatcher.group(2))</span>
<span class="fc bfc" id="L41" title="All 2 branches covered.">                    .key(keyMatcher == null ? null : keyMatcher.group(1))</span>
<span class="fc bfc" id="L42" title="All 2 branches covered.">                    .batch(keyMatcher == null ? null : keyMatcher.group(2))</span>
<span class="fc" id="L43">                    .build();</span>
        } else {
<span class="nc" id="L45">            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, &quot;Invalid cache key format&quot;);</span>
        }
    }

    protected String getName(CacheCompositeKey dto) {
<span class="fc" id="L50">        return String.format(&quot;unity2-metric:%s:%s&quot;, dto.getGroup(), dto.getComponent());</span>
    }

    protected String getKey(CacheCompositeKey dto) {
<span class="nc" id="L54">        String key = dto.getKey();</span>
<span class="nc" id="L55">        String batch = dto.getBatch();</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">        if (StringUtils.isBlank(batch))</span>
<span class="nc" id="L57">            return key;</span>
        else
<span class="nc" id="L59">            return String.format(&quot;%s:%s&quot;, key, batch);</span>
    }

    public Object getCache(String name, String key) {
<span class="fc" id="L63">        CacheCompositeKeyDTO cacheCompositeKeyDTO = getCacheCompositeKeyDTO(name, key);</span>
<span class="fc" id="L64">        CacheDTO cacheDTO = cacheService.getCache(cacheCompositeKeyDTO);</span>

<span class="fc" id="L66">        return cacheDTO.getValue();</span>
    }

    public List&lt;String&gt; getAllNames() {
<span class="fc" id="L70">        return cacheService.getDistinctGroupComponent()</span>
<span class="fc" id="L71">                .stream()</span>
<span class="fc" id="L72">                .map(this::getName)</span>
<span class="fc" id="L73">                .collect(Collectors.toList());</span>
    }

    public void putCache(String name, String key, Object value) {
<span class="fc" id="L77">        CacheCompositeKeyDTO cacheCompositeKeyDTO = getCacheCompositeKeyDTO(name, key);</span>
<span class="fc" id="L78">        cacheService.putCache(</span>
                cacheCompositeKeyDTO,
<span class="fc" id="L80">                CacheUpdateDTO.builder()</span>
<span class="fc" id="L81">                        .type(CacheTypeV3.NORMAL)</span>
<span class="fc" id="L82">                        .operation(CacheOperationV3.SET)</span>
<span class="fc" id="L83">                        .value(value)</span>
<span class="fc" id="L84">                        .build()</span>
        );
<span class="fc" id="L86">    }</span>

    public void evictCache(String name, String key) {
<span class="fc" id="L89">        CacheCompositeKeyDTO cacheCompositeKeyDTO = getCacheCompositeKeyDTO(name, key);</span>
<span class="fc" id="L90">        cacheService.evictCache(cacheCompositeKeyDTO);</span>
<span class="fc" id="L91">    }</span>

    public void clearCache(String name) {
<span class="fc" id="L94">        CacheCompositeKeyDTO cacheCompositeKeyDTO = getCacheCompositeKeyDTO(name);</span>
<span class="fc" id="L95">        cacheService.clearCache(cacheCompositeKeyDTO.getGroup(), cacheCompositeKeyDTO.getComponent());</span>
<span class="fc" id="L96">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>