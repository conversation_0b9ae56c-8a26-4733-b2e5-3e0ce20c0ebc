<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheFacadeV2.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.facade</a> &gt; <span class="el_source">CacheFacadeV2.java</span></div><h1>CacheFacadeV2.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.facade;

import com.poc.hss.fasttrack.dto.*;
import com.poc.hss.fasttrack.model.*;
import com.poc.hss.fasttrack.service.CacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L13">public class CacheFacadeV2 extends CacheFacadeV1 {</span>

    @Autowired
    private CacheService cacheService;

    private CacheUpdateDTO getCacheUpdateDTO(CacheRequestV2 request) {
<span class="fc" id="L19">        return CacheUpdateDTO</span>
<span class="fc" id="L20">                .builder()</span>
<span class="fc" id="L21">                .value(Optional.ofNullable(request).map(CacheRequestV2::getValue).orElse(null))</span>
<span class="fc" id="L22">                .operation(Optional.ofNullable(request).map(CacheRequestV2::getOperation).map(CacheOperationV2::toString).map(CacheOperationV3::fromValue).orElse(null))</span>
<span class="fc" id="L23">                .type(Optional.ofNullable(request).map(CacheRequestV2::getType).map(CacheTypeV2::toString).map(CacheTypeV3::fromValue).orElse(null))</span>
<span class="fc" id="L24">                .build();</span>
    }

    @Override
    @Deprecated
    public void putCache(String name, String key, Object value) {
<span class="nc" id="L30">        throw new UnsupportedOperationException();</span>
    }

    public void putCache(String name, String key, CacheRequestV2 request) {
<span class="fc" id="L34">        CacheCompositeKeyDTO cacheCompositeKeyDTO = this.getCacheCompositeKeyDTO(name, key);</span>
<span class="fc" id="L35">        CacheUpdateDTO cacheUpdateDTO = this.getCacheUpdateDTO(request);</span>
<span class="fc" id="L36">        cacheService.putCache(cacheCompositeKeyDTO, cacheUpdateDTO);</span>
<span class="fc" id="L37">    }</span>

    public CachePageResponseV2 searchCache(String name, PageDTO pageDTO) {
<span class="nc" id="L40">        CacheCompositeKeyDTO cacheCompositeKeyDTO = getCacheCompositeKeyDTO(name);</span>
<span class="nc" id="L41">        CachePageDTO cachePageDTO = cacheService.searchCache(</span>
<span class="nc" id="L42">                CacheQueryDTO.builder()</span>
<span class="nc" id="L43">                        .group(cacheCompositeKeyDTO.getGroup())</span>
<span class="nc" id="L44">                        .component(cacheCompositeKeyDTO.getComponent())</span>
<span class="nc" id="L45">                        .isBatch(true)</span>
<span class="nc" id="L46">                        .build(),</span>
                pageDTO
        );

<span class="nc" id="L50">        return new CachePageResponseV2()</span>
<span class="nc" id="L51">                .data(cachePageDTO.getData().stream().map(this::convert).collect(Collectors.toList()))</span>
<span class="nc" id="L52">                .total(cachePageDTO.getTotal());</span>
    }

    private CacheResponseV2 convert(CacheDTO cacheDTO) {
<span class="nc" id="L56">        return new CacheResponseV2()</span>
<span class="nc" id="L57">                .id(cacheDTO.getId())</span>
<span class="nc" id="L58">                .name(getName(cacheDTO))</span>
<span class="nc" id="L59">                .key(getKey(cacheDTO))</span>
<span class="nc" id="L60">                .type(CacheTypeV2.fromValue(cacheDTO.getType().toString()))</span>
<span class="nc" id="L61">                .createdTime(cacheDTO.getCreatedTime())</span>
<span class="nc" id="L62">                .updatedTime(cacheDTO.getUpdatedTime());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>