<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheFacadeV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.facade</a> &gt; <span class="el_source">CacheFacadeV3.java</span></div><h1>CacheFacadeV3.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.facade;

import com.poc.hss.fasttrack.dto.*;
import com.poc.hss.fasttrack.model.CachePageResponseV3;
import com.poc.hss.fasttrack.model.CacheResponseV3;
import com.poc.hss.fasttrack.service.CacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L15">@Slf4j</span>
<span class="fc" id="L16">public class CacheFacadeV3 extends CacheFacadeV1 {</span>

    @Autowired
    private CacheService cacheService;

    public void clearCache(String group, String component) {
<span class="fc" id="L22">        cacheService.clearCache(group, component);</span>
<span class="fc" id="L23">    }</span>

    public void evictCache(CacheCompositeKeyDTO cacheCompositeKeyDTO) {
<span class="fc" id="L26">        cacheService.evictCache(cacheCompositeKeyDTO);</span>
<span class="fc" id="L27">    }</span>

    public CacheResponseV3 getCache(CacheCompositeKeyDTO cacheCompositeKeyDTO) {
<span class="pc bpc" id="L30" title="1 of 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="fc" id="L31">            log.debug(&quot;getCache: {}&quot;, cacheCompositeKeyDTO);</span>
<span class="fc" id="L32">        CacheDTO cache = cacheService.getCache(cacheCompositeKeyDTO);</span>
<span class="fc" id="L33">        return convert(cache);</span>
    }

    public CachePageResponseV3 searchCache(CacheQueryDTO cacheQueryDTO, PageDTO pageDTO) {
<span class="fc" id="L37">        CachePageDTO cachePageDTO = cacheService.searchCache(</span>
                cacheQueryDTO,
                pageDTO
        );

<span class="fc" id="L42">        return new CachePageResponseV3()</span>
<span class="fc" id="L43">                .data(cachePageDTO.getData().stream().map(this::convert).collect(Collectors.toList()))</span>
<span class="fc" id="L44">                .total(cachePageDTO.getTotal());</span>
    }

    public CacheResponseV3 putCache(CacheCompositeKeyDTO cacheCompositeKeyDTO, CacheUpdateDTO cacheUpdateDTO) {
<span class="pc bpc" id="L48" title="1 of 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="fc" id="L49">            log.debug(&quot;putCache: {}, {}&quot;, cacheCompositeKeyDTO, cacheUpdateDTO);</span>
<span class="fc" id="L50">        CacheDTO cacheDTO = cacheService.putCache(cacheCompositeKeyDTO, cacheUpdateDTO);</span>
<span class="fc" id="L51">        return convert(cacheDTO);</span>
    }

    private CacheResponseV3 convert(CacheDTO cache) {
<span class="fc" id="L55">        return new CacheResponseV3()</span>
<span class="fc" id="L56">                .id(cache.getId())</span>
<span class="fc" id="L57">                .group(cache.getGroup())</span>
<span class="fc" id="L58">                .component(cache.getComponent())</span>
<span class="fc" id="L59">                .batch(cache.getBatch())</span>
<span class="fc" id="L60">                .key(cache.getKey())</span>
<span class="fc" id="L61">                .type(cache.getType())</span>
<span class="fc" id="L62">                .value(cache.getValue())</span>
<span class="fc" id="L63">                .status(cache.getStatus())</span>
<span class="fc" id="L64">                .createdTime(cache.getCreatedTime())</span>
<span class="fc" id="L65">                .updatedTime(cache.getUpdatedTime());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>