<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaCacheFacade.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.facade</a> &gt; <span class="el_source">KafkaCacheFacade.java</span></div><h1>KafkaCacheFacade.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.facade;

import com.poc.hss.fasttrack.dto.CacheCompositeKeyDTO;
import com.poc.hss.fasttrack.dto.CacheUpdateDTO;
import com.poc.hss.fasttrack.dto.KafkaCacheRequestDTO;
import com.poc.hss.fasttrack.dto.ReconciliationRequestDTO;
import com.poc.hss.fasttrack.model.CacheOperationV3;
import com.poc.hss.fasttrack.service.CacheService;
import com.poc.hss.fasttrack.service.KafkaOffsetService;
import com.poc.hss.fasttrack.service.ReconciliationService;
import com.poc.hss.fasttrack.util.JsonUtils;
import io.vertx.core.json.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import java.util.List;

@Service
<span class="fc" id="L22">@Slf4j</span>
<span class="fc" id="L23">public class KafkaCacheFacade {</span>

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ReconciliationService reconciliationService;

    @Autowired
    private KafkaOffsetService&lt;String, String&gt; kafkaOffsetService;

    private CacheCompositeKeyDTO getCacheCompositeKeyDTO(KafkaCacheRequestDTO kafkaCacheRequestDTO) {
<span class="nc" id="L35">        return CacheCompositeKeyDTO.builder()</span>
<span class="nc" id="L36">                .group(kafkaCacheRequestDTO.getGroup())</span>
<span class="nc" id="L37">                .component(kafkaCacheRequestDTO.getComponent())</span>
<span class="nc" id="L38">                .key(kafkaCacheRequestDTO.getKey())</span>
<span class="nc" id="L39">                .batch(kafkaCacheRequestDTO.getBatch())</span>
<span class="nc" id="L40">                .build();</span>
    }

    private CacheUpdateDTO getCacheUpdateDTO(KafkaCacheRequestDTO kafkaCacheRequestDTO) {
<span class="nc" id="L44">        return CacheUpdateDTO.builder()</span>
<span class="nc" id="L45">                .type(kafkaCacheRequestDTO.getType())</span>
<span class="nc" id="L46">                .operation(kafkaCacheRequestDTO.getOperation())</span>
<span class="nc" id="L47">                .value(kafkaCacheRequestDTO.getValue())</span>
<span class="nc" id="L48">                .build();</span>
    }

    @Transactional
    public void processRecords(List&lt;ConsumerRecord&lt;String, String&gt;&gt; consumerRecords) {
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L54">            log.debug(&quot;Received {} records&quot;, consumerRecords.size());</span>
<span class="nc" id="L55">        List&lt;ConsumerRecord&lt;String, String&gt;&gt; recordsToProcess = kafkaOffsetService.filterRecords(consumerRecords);</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">        for (ConsumerRecord&lt;String, String&gt; consumerRecord : recordsToProcess) {</span>
<span class="nc" id="L57">            final JsonObject record = new JsonObject(consumerRecord.value());</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">            if (log.isDebugEnabled())</span>
<span class="nc" id="L59">                log.debug(&quot;Processing kafka message: {}&quot;, record);</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">            if (record.getString(&quot;operation&quot;).equals(CacheOperationV3.RECONCILE.toString())) {</span>
<span class="nc" id="L61">                ReconciliationRequestDTO reconcileRequestDTO = JsonUtils.deserialize(consumerRecord.value(), ReconciliationRequestDTO.class);</span>
<span class="nc" id="L62">                reconciliationService.reconcile(reconcileRequestDTO);</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">            } else if (record.getString(&quot;operation&quot;).equals(CacheOperationV3.RESET_BATCH.toString())) {</span>
<span class="nc" id="L64">                ReconciliationRequestDTO reconcileRequestDTO = JsonUtils.deserialize(consumerRecord.value(), ReconciliationRequestDTO.class);</span>
<span class="nc" id="L65">                reconciliationService.reset(reconcileRequestDTO.getBatch());</span>
<span class="nc" id="L66">            } else {</span>
<span class="nc" id="L67">                KafkaCacheRequestDTO cacheRequestDTO = JsonUtils.deserialize(consumerRecord.value(), KafkaCacheRequestDTO.class);</span>
<span class="nc" id="L68">                CacheCompositeKeyDTO cacheCompositeKeyDTO = getCacheCompositeKeyDTO(cacheRequestDTO);</span>
<span class="nc" id="L69">                CacheUpdateDTO cacheUpdateDTO = getCacheUpdateDTO(cacheRequestDTO);</span>
<span class="nc" id="L70">                cacheService.putCache(cacheCompositeKeyDTO, cacheUpdateDTO);</span>
            }
<span class="nc" id="L72">        }</span>
<span class="nc" id="L73">        kafkaOffsetService.commitRecords(recordsToProcess);</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L75">            log.debug(&quot;Committed {} records&quot;, recordsToProcess.size());</span>
<span class="nc" id="L76">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>