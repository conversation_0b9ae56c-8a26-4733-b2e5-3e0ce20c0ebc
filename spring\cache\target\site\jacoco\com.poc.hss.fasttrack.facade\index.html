<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.facade</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <span class="el_package">com.poc.hss.fasttrack.facade</span></div><h1>com.poc.hss.fasttrack.facade</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">239 of 555</td><td class="ctr2">56%</td><td class="bar">18 of 28</td><td class="ctr2">35%</td><td class="ctr1">18</td><td class="ctr2">44</td><td class="ctr1">58</td><td class="ctr2">135</td><td class="ctr1">7</td><td class="ctr2">30</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a3"><a href="KafkaCacheFacade.html" class="el_class">KafkaCacheFacade</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="142" alt="142"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="7" alt="7"/></td><td class="ctr2" id="c3">4%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g1">11</td><td class="ctr1" id="h0">34</td><td class="ctr2" id="i1">36</td><td class="ctr1" id="j0">3</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CacheFacadeV2.html" class="el_class">CacheFacadeV2</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="67" alt="67"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="53" alt="53"/></td><td class="ctr2" id="c2">44%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h1">18</td><td class="ctr2" id="i2">29</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k2">6</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="CacheFacadeV1.html" class="el_class">CacheFacadeV1</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="30" alt="30"/><img src="../jacoco-resources/greenbar.gif" width="99" height="10" title="146" alt="146"/></td><td class="ctr2" id="c1">82%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="80" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">66%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h2">6</td><td class="ctr2" id="i0">41</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k0">11</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="CacheFacadeV3.html" class="el_class">CacheFacadeV3</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="110" alt="110"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g2">10</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">29</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>