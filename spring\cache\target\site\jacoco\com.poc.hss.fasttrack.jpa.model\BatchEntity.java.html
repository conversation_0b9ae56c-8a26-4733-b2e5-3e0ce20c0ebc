<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchEntity.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jpa.model</a> &gt; <span class="el_source">BatchEntity.java</span></div><h1>BatchEntity.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jpa.model;

import com.poc.hss.fasttrack.dto.BatchQueryDTO;
import com.poc.hss.fasttrack.model.BatchStatus;
import lombok.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import java.io.Serializable;
import java.util.Objects;
import java.util.stream.Stream;

<span class="nc bnc" id="L15" title="All 88 branches missed.">@EqualsAndHashCode(callSuper = true)</span>
@Entity
@Table(name = &quot;BATCH&quot;)
<span class="nc" id="L18">@Data</span>
<span class="nc" id="L19">@Builder</span>
<span class="fc" id="L20">@NoArgsConstructor</span>
<span class="nc" id="L21">@AllArgsConstructor</span>

public class BatchEntity extends BaseEntity implements Serializable {
    @Column(name = &quot;BATCH&quot;)
<span class="nc" id="L25">    private String batch;</span>

    @Column(name = &quot;DEPLOYMENT&quot;)
<span class="nc" id="L28">    private String deployment;</span>

    @Column(name = &quot;CACHE_GROUP&quot;)
<span class="nc" id="L31">    private String group;</span>

    @Column(name = &quot;COMPONENT&quot;)
<span class="nc" id="L34">    private String component;</span>

    @Column(name = &quot;COMPLETED&quot;)
<span class="nc" id="L37">    private Boolean completed;</span>

    @Column(name = &quot;SOURCE_METRIC_OUT&quot;)
<span class="nc" id="L40">    private Long sourceMetricOut;</span>

    @Column(name = &quot;THIS_METRIC_IN&quot;)
<span class="nc" id="L43">    private Long metricIn;</span>

    @Column(name = &quot;TOPIC&quot;)
<span class="nc" id="L46">    private String topic;</span>

    @Column(name = &quot;CONSUMER_GROUP&quot;)
<span class="nc" id="L49">    private String consumerGroup;</span>

    @Column(name = &quot;STATUS&quot;)
    @Enumerated(EnumType.STRING)
<span class="nc" id="L53">    private BatchStatus status;</span>

    public static Specification&lt;BatchEntity&gt; getSpecification(BatchQueryDTO dto) {
<span class="nc" id="L56">        return (root, query, cb) -&gt; cb.and(</span>
<span class="nc" id="L57">                Stream.of(</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">                        StringUtils.isBlank(dto.getGroup()) ? null : cb.equal(root.get(&quot;group&quot;), dto.getGroup()),</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">                        StringUtils.isBlank(dto.getComponent()) ? null : cb.equal(root.get(&quot;component&quot;), dto.getComponent()),</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">                        StringUtils.isBlank(dto.getBatch()) ? null : cb.equal(root.get(&quot;batch&quot;), dto.getBatch()),</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">                        dto.getStatus() == null ? null : cb.equal(root.get(&quot;status&quot;), dto.getStatus())</span>
<span class="nc" id="L62">                ).filter(Objects::nonNull).toArray(Predicate[]::new)</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>