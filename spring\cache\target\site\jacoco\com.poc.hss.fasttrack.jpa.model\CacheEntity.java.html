<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheEntity.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jpa.model</a> &gt; <span class="el_source">CacheEntity.java</span></div><h1>CacheEntity.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jpa.model;

import com.poc.hss.fasttrack.dto.CacheQueryDTO;
import com.poc.hss.fasttrack.enums.CacheType;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import jakarta.persistence.criteria.Predicate;
import lombok.*;
import org.apache.commons.lang.StringUtils;
import org.hibernate.annotations.Type;
import org.springframework.data.jpa.domain.Specification;

import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;

<span class="nc bnc" id="L17" title="All 72 branches missed.">@EqualsAndHashCode(callSuper = true)</span>
@Entity
@Table(name = &quot;CACHE&quot;)
<span class="pc" id="L20">@Data</span>
<span class="fc" id="L21">@NoArgsConstructor</span>
<span class="nc" id="L22">@AllArgsConstructor</span>
public class CacheEntity extends BaseEntity {

<span class="pc" id="L25">    @Builder</span>
<span class="fc" id="L26">    public CacheEntity(String group, String component, String batch, String key, CacheType type) {</span>
<span class="fc" id="L27">        this.group = group;</span>
<span class="fc" id="L28">        this.component = component;</span>
<span class="fc" id="L29">        this.batch = batch;</span>
<span class="fc" id="L30">        this.key = key;</span>
<span class="fc" id="L31">        this.type = Optional.ofNullable(type).orElse(CacheType.NORMAL);</span>
<span class="fc" id="L32">    }</span>

    @Column(name = &quot;group&quot;)
<span class="fc" id="L35">    private String group;</span>

    @Column(name = &quot;component&quot;)
<span class="fc" id="L38">    private String component;</span>

    @Column(name = &quot;batch&quot;)
<span class="fc" id="L41">    private String batch;</span>

    @Column(name = &quot;key&quot;)
<span class="fc" id="L44">    private String key;</span>

<span class="pc" id="L46">    @Enumerated(EnumType.STRING)</span>
    @Column(name = &quot;type&quot;, nullable = false)
<span class="fc" id="L48">    private CacheType type = CacheType.NORMAL;</span>

    @Type(JsonType.class)
    @Column(name = &quot;value&quot;, columnDefinition = &quot;jsonb&quot;)
    @Getter(value = AccessLevel.NONE)
    @Setter(value = AccessLevel.NONE)
    private Object value;

    @Column(name = &quot;int_value&quot;)
    @Getter(value = AccessLevel.NONE)
    @Setter(value = AccessLevel.NONE)
    private Long intValue;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumns({
            @JoinColumn(name = &quot;group&quot;, referencedColumnName = &quot;CACHE_GROUP&quot;, insertable = false, updatable = false),
            @JoinColumn(name = &quot;component&quot;, referencedColumnName = &quot;COMPONENT&quot;, insertable = false, updatable = false),
            @JoinColumn(name = &quot;batch&quot;, referencedColumnName = &quot;BATCH&quot;, insertable = false, updatable = false)
    })
<span class="fc" id="L67">    private BatchEntity batchEntity;</span>

    @Transient
    public Object getTypedValue() {
<span class="fc bfc" id="L71" title="All 2 branches covered.">        switch (type) {</span>
            case COUNTER:
<span class="fc" id="L73">                return intValue;</span>
            case NORMAL:
            default:
<span class="fc" id="L76">                return value;</span>
        }
    }

    @Transient
    public void setTypedValue(Object value) {
<span class="fc bfc" id="L82" title="All 2 branches covered.">        switch (type) {</span>
            case COUNTER:
<span class="fc" id="L84">                this.intValue = Long.parseLong(value.toString());</span>
            case NORMAL:
            default:
<span class="fc" id="L87">                this.value = value;</span>
        }
<span class="fc" id="L89">    }</span>

    public static Specification&lt;CacheEntity&gt; getSpecification(CacheQueryDTO dto) {
<span class="fc" id="L92">        return (root, query, cb) -&gt; cb.and(</span>
<span class="fc" id="L93">                Stream.of(</span>
<span class="pc bpc" id="L94" title="1 of 2 branches missed.">                        dto.isBatch() ? cb.isNotNull(root.get(&quot;batch&quot;)) : cb.isNull(root.get(&quot;batch&quot;)),</span>
<span class="pc bpc" id="L95" title="1 of 2 branches missed.">                        StringUtils.isBlank(dto.getGroup()) ? null : cb.equal(root.get(&quot;group&quot;), dto.getGroup()),</span>
<span class="pc bpc" id="L96" title="1 of 2 branches missed.">                        StringUtils.isBlank(dto.getComponent()) ? null : cb.equal(root.get(&quot;component&quot;), dto.getComponent()),</span>
<span class="pc bpc" id="L97" title="1 of 2 branches missed.">                        StringUtils.isBlank(dto.getBatch()) ? null : cb.equal(root.get(&quot;batch&quot;), dto.getBatch()),</span>
<span class="pc bpc" id="L98" title="1 of 2 branches missed.">                        StringUtils.isBlank(dto.getKey()) ? null : cb.equal(root.get(&quot;key&quot;), dto.getKey()),</span>
<span class="pc bpc" id="L99" title="1 of 2 branches missed.">                        dto.getStatus()==null ? null : cb.equal(root.get(&quot;batchEntity&quot;).get(&quot;status&quot;), dto.getStatus())</span>
<span class="fc" id="L100">                ).filter(Objects::nonNull).toArray(Predicate[]::new)</span>
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>