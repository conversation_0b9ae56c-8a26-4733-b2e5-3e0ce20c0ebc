<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaOffsetEntity.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jpa.model</a> &gt; <span class="el_source">KafkaOffsetEntity.java</span></div><h1>KafkaOffsetEntity.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jpa.model;

import lombok.*;
import org.apache.kafka.common.TopicPartition;
import org.springframework.data.jpa.domain.Specification;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.criteria.Predicate;
import java.util.Collection;

<span class="nc bnc" id="L13" title="All 20 branches missed.">@EqualsAndHashCode(callSuper = true)</span>
@Entity
@Table(name = &quot;KAFKA_OFFSET&quot;)
<span class="nc" id="L16">@Data</span>
<span class="fc" id="L17">@NoArgsConstructor</span>
<span class="nc" id="L18">@AllArgsConstructor</span>
<span class="pc bnc" id="L19" title="All 4 branches missed.">@Builder</span>
public class KafkaOffsetEntity extends BaseEntity {

    @Column(name = &quot;topic&quot;, nullable = false)
<span class="nc" id="L23">    private String topic;</span>

    @Column(name = &quot;partition&quot;, nullable = false)
    @Builder.Default
<span class="nc" id="L27">    private int partition = 0;</span>

    @Column(name = &quot;offset&quot;, nullable = false)
    @Builder.Default
<span class="nc" id="L31">    private long offset = 0L;</span>

    public static Specification&lt;KafkaOffsetEntity&gt; getSpecification(Collection&lt;TopicPartition&gt; topicPartitions) {
<span class="nc" id="L34">        return (root, query, cb) -&gt;</span>
<span class="nc" id="L35">                cb.or(</span>
<span class="nc" id="L36">                        topicPartitions.stream()</span>
<span class="nc" id="L37">                                .map(tp -&gt; cb.and(</span>
<span class="nc" id="L38">                                        cb.equal(root.get(&quot;topic&quot;), tp.topic()),</span>
<span class="nc" id="L39">                                        cb.equal(root.get(&quot;partition&quot;), tp.partition())</span>
                                ))
<span class="nc" id="L41">                                .toArray(Predicate[]::new)</span>
                );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>