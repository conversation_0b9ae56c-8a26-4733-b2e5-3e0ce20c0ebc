<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.jpa.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <span class="el_package">com.poc.hss.fasttrack.jpa.model</span></div><h1>com.poc.hss.fasttrack.jpa.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,309 of 1,525</td><td class="ctr2">14%</td><td class="bar">198 of 208</td><td class="ctr2">4%</td><td class="ctr1">177</td><td class="ctr2">205</td><td class="ctr1">36</td><td class="ctr2">72</td><td class="ctr1">75</td><td class="ctr2">101</td><td class="ctr1">2</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a0"><a href="BatchEntity.html" class="el_class">BatchEntity</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="119" height="10" title="557" alt="557"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="96" alt="96"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">77</td><td class="ctr2" id="g0">78</td><td class="ctr1" id="h0">21</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j0">29</td><td class="ctr2" id="k0">30</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="CacheEntity.html" class="el_class">CacheEntity</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="398" alt="398"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="158" alt="158"/></td><td class="ctr2" id="c1">28%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">11%</td><td class="ctr1" id="f1">52</td><td class="ctr2" id="g1">69</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i0">35</td><td class="ctr1" id="j3">10</td><td class="ctr2" id="k1">25</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a4"><a href="KafkaOffsetEntity.html" class="el_class">KafkaOffsetEntity</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="185" alt="185"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c2">6%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="20" alt="20"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">26</td><td class="ctr2" id="g2">29</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j1">16</td><td class="ctr2" id="k2">19</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="BatchEntity$BatchEntityBuilder.html" class="el_class">BatchEntity.BatchEntityBuilder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="100" alt="100"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f3">13</td><td class="ctr2" id="g3">13</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j2">13</td><td class="ctr2" id="k3">13</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a5"><a href="KafkaOffsetEntity$KafkaOffsetEntityBuilder.html" class="el_class">KafkaOffsetEntity.KafkaOffsetEntityBuilder</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="56" alt="56"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">8</td><td class="ctr2" id="g4">8</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">6</td><td class="ctr2" id="k5">6</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a3"><a href="CacheEntity$CacheEntityBuilder.html" class="el_class">CacheEntity.CacheEntityBuilder</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="42" alt="42"/></td><td class="ctr2" id="c0">76%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">8</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k4">8</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>