<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.jpa.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <span class="el_package">com.poc.hss.fasttrack.jpa.model</span></div><h1>com.poc.hss.fasttrack.jpa.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,309 of 1,525</td><td class="ctr2">14%</td><td class="bar">198 of 208</td><td class="ctr2">4%</td><td class="ctr1">177</td><td class="ctr2">205</td><td class="ctr1">36</td><td class="ctr2">72</td><td class="ctr1">75</td><td class="ctr2">101</td><td class="ctr1">2</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a0"><a href="BatchEntity.java.html" class="el_source">BatchEntity.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="119" height="10" title="657" alt="657"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="96" alt="96"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">90</td><td class="ctr2" id="g0">91</td><td class="ctr1" id="h0">21</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j0">42</td><td class="ctr2" id="k0">43</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a1"><a href="CacheEntity.java.html" class="el_source">CacheEntity.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="74" height="10" title="411" alt="411"/><img src="../jacoco-resources/greenbar.gif" width="36" height="10" title="200" alt="200"/></td><td class="ctr2" id="c0">32%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="97" height="10" title="78" alt="78"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">11%</td><td class="ctr1" id="f1">53</td><td class="ctr2" id="g1">77</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i0">35</td><td class="ctr1" id="j2">11</td><td class="ctr2" id="k1">33</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a2"><a href="KafkaOffsetEntity.java.html" class="el_source">KafkaOffsetEntity.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="241" alt="241"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="13" alt="13"/></td><td class="ctr2" id="c1">5%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="24" alt="24"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">34</td><td class="ctr2" id="g2">37</td><td class="ctr1" id="h1">13</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j1">22</td><td class="ctr2" id="k2">25</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">2</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>