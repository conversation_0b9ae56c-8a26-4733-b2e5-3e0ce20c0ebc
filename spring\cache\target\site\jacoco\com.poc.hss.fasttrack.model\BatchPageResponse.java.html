<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchPageResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">BatchPageResponse.java</span></div><h1>BatchPageResponse.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.BatchResponse;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * BatchPageResponse
 */
@Validated


<span class="nc" id="L20">public class BatchPageResponse   {</span>
<span class="nc" id="L21">  @JsonProperty(&quot;data&quot;)</span>
  @Valid
  private List&lt;BatchResponse&gt; data = null;

<span class="nc" id="L25">  @JsonProperty(&quot;total&quot;)</span>
  private Long total = null;

  public BatchPageResponse data(List&lt;BatchResponse&gt; data) {
<span class="nc" id="L29">    this.data = data;</span>
<span class="nc" id="L30">    return this;</span>
  }

  public BatchPageResponse addDataItem(BatchResponse dataItem) {
<span class="nc bnc" id="L34" title="All 2 branches missed.">    if (this.data == null) {</span>
<span class="nc" id="L35">      this.data = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L37">    this.data.add(dataItem);</span>
<span class="nc" id="L38">    return this;</span>
  }

  /**
   * Get data
   * @return data
   **/
  @Schema(description = &quot;&quot;)
      @Valid
    public List&lt;BatchResponse&gt; getData() {
<span class="nc" id="L48">    return data;</span>
  }

  public void setData(List&lt;BatchResponse&gt; data) {
<span class="nc" id="L52">    this.data = data;</span>
<span class="nc" id="L53">  }</span>

  public BatchPageResponse total(Long total) {
<span class="nc" id="L56">    this.total = total;</span>
<span class="nc" id="L57">    return this;</span>
  }

  /**
   * Get total
   * @return total
   **/
  @Schema(description = &quot;&quot;)
  
    public Long getTotal() {
<span class="nc" id="L67">    return total;</span>
  }

  public void setTotal(Long total) {
<span class="nc" id="L71">    this.total = total;</span>
<span class="nc" id="L72">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L77" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L78">      return true;</span>
    }
<span class="nc bnc" id="L80" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L81">      return false;</span>
    }
<span class="nc" id="L83">    BatchPageResponse batchPageResponse = (BatchPageResponse) o;</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">    return Objects.equals(this.data, batchPageResponse.data) &amp;&amp;</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">        Objects.equals(this.total, batchPageResponse.total);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L90">    return Objects.hash(data, total);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L95">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L96">    sb.append(&quot;class BatchPageResponse {\n&quot;);</span>
    
<span class="nc" id="L98">    sb.append(&quot;    data: &quot;).append(toIndentedString(data)).append(&quot;\n&quot;);</span>
<span class="nc" id="L99">    sb.append(&quot;    total: &quot;).append(toIndentedString(total)).append(&quot;\n&quot;);</span>
<span class="nc" id="L100">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L101">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L109" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L110">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L112">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>