<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchStatus.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">BatchStatus.java</span></div><h1>BatchStatus.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * Gets or Sets BatchStatus
 */
<span class="fc" id="L14">public enum BatchStatus {</span>
<span class="fc" id="L15">  COMPLETED(&quot;COMPLETED&quot;),</span>
<span class="fc" id="L16">    IN_PROGRESS(&quot;IN_PROGRESS&quot;),</span>
<span class="fc" id="L17">    COUNT_MISMATCH(&quot;COUNT_MISMATCH&quot;);</span>

  private String value;

<span class="fc" id="L21">  BatchStatus(String value) {</span>
<span class="fc" id="L22">    this.value = value;</span>
<span class="fc" id="L23">  }</span>

  @Override
  @JsonValue
  public String toString() {
<span class="nc" id="L28">    return String.valueOf(value);</span>
  }

  @JsonCreator
  public static BatchStatus fromValue(String text) {
<span class="nc bnc" id="L33" title="All 2 branches missed.">    for (BatchStatus b : BatchStatus.values()) {</span>
<span class="nc bnc" id="L34" title="All 2 branches missed.">      if (String.valueOf(b.value).equals(text)) {</span>
<span class="nc" id="L35">        return b;</span>
      }
    }
<span class="nc" id="L38">    return null;</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>