<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheOperationV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CacheOperationV3.java</span></div><h1>CacheOperationV3.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonValue;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * Gets or Sets CacheOperationV3
 */
<span class="fc" id="L14">public enum CacheOperationV3 {</span>
<span class="fc" id="L15">  SET(&quot;SET&quot;),</span>
<span class="fc" id="L16">    INCREMENT(&quot;INCREMENT&quot;),</span>
<span class="fc" id="L17">    RECONCILE(&quot;RECONCILE&quot;),</span>
<span class="fc" id="L18">    RESET_BATCH(&quot;RESET_BATCH&quot;);</span>

  private String value;

<span class="fc" id="L22">  CacheOperationV3(String value) {</span>
<span class="fc" id="L23">    this.value = value;</span>
<span class="fc" id="L24">  }</span>

  @Override
  @JsonValue
  public String toString() {
<span class="fc" id="L29">    return String.valueOf(value);</span>
  }

  @JsonCreator
  public static CacheOperationV3 fromValue(String text) {
<span class="pc bpc" id="L34" title="1 of 2 branches missed.">    for (CacheOperationV3 b : CacheOperationV3.values()) {</span>
<span class="fc bfc" id="L35" title="All 2 branches covered.">      if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L36">        return b;</span>
      }
    }
<span class="nc" id="L39">    return null;</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>