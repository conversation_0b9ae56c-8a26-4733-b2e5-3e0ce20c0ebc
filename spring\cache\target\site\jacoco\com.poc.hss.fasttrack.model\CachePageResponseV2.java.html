<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CachePageResponseV2.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CachePageResponseV2.java</span></div><h1>CachePageResponseV2.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.CacheResponseV2;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * CachePageResponseV2
 */
@Validated


<span class="nc" id="L20">public class CachePageResponseV2   {</span>
<span class="nc" id="L21">  @JsonProperty(&quot;total&quot;)</span>
  private Long total = null;

<span class="nc" id="L24">  @JsonProperty(&quot;data&quot;)</span>
  @Valid
  private List&lt;CacheResponseV2&gt; data = null;

  public CachePageResponseV2 total(Long total) {
<span class="nc" id="L29">    this.total = total;</span>
<span class="nc" id="L30">    return this;</span>
  }

  /**
   * Get total
   * @return total
   **/
  @Schema(description = &quot;&quot;)
  
    public Long getTotal() {
<span class="nc" id="L40">    return total;</span>
  }

  public void setTotal(Long total) {
<span class="nc" id="L44">    this.total = total;</span>
<span class="nc" id="L45">  }</span>

  public CachePageResponseV2 data(List&lt;CacheResponseV2&gt; data) {
<span class="nc" id="L48">    this.data = data;</span>
<span class="nc" id="L49">    return this;</span>
  }

  public CachePageResponseV2 addDataItem(CacheResponseV2 dataItem) {
<span class="nc bnc" id="L53" title="All 2 branches missed.">    if (this.data == null) {</span>
<span class="nc" id="L54">      this.data = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L56">    this.data.add(dataItem);</span>
<span class="nc" id="L57">    return this;</span>
  }

  /**
   * Get data
   * @return data
   **/
  @Schema(description = &quot;&quot;)
      @Valid
    public List&lt;CacheResponseV2&gt; getData() {
<span class="nc" id="L67">    return data;</span>
  }

  public void setData(List&lt;CacheResponseV2&gt; data) {
<span class="nc" id="L71">    this.data = data;</span>
<span class="nc" id="L72">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L77" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L78">      return true;</span>
    }
<span class="nc bnc" id="L80" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L81">      return false;</span>
    }
<span class="nc" id="L83">    CachePageResponseV2 cachePageResponseV2 = (CachePageResponseV2) o;</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">    return Objects.equals(this.total, cachePageResponseV2.total) &amp;&amp;</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">        Objects.equals(this.data, cachePageResponseV2.data);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L90">    return Objects.hash(total, data);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L95">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L96">    sb.append(&quot;class CachePageResponseV2 {\n&quot;);</span>
    
<span class="nc" id="L98">    sb.append(&quot;    total: &quot;).append(toIndentedString(total)).append(&quot;\n&quot;);</span>
<span class="nc" id="L99">    sb.append(&quot;    data: &quot;).append(toIndentedString(data)).append(&quot;\n&quot;);</span>
<span class="nc" id="L100">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L101">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L109" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L110">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L112">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>