<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheRequestV2.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CacheRequestV2.java</span></div><h1>CacheRequestV2.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.CacheOperationV2;
import com.poc.hss.fasttrack.model.CacheTypeV2;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * CacheRequestV2
 */
@Validated


<span class="fc" id="L19">public class CacheRequestV2   {</span>
<span class="fc" id="L20">  @JsonProperty(&quot;value&quot;)</span>
  private Object value = null;

<span class="fc" id="L23">  @JsonProperty(&quot;type&quot;)</span>
  private CacheTypeV2 type = null;

<span class="fc" id="L26">  @JsonProperty(&quot;operation&quot;)</span>
  private CacheOperationV2 operation = null;

  public CacheRequestV2 value(Object value) {
<span class="fc" id="L30">    this.value = value;</span>
<span class="fc" id="L31">    return this;</span>
  }

  /**
   * Get value
   * @return value
   **/
  @Schema(description = &quot;&quot;)
  
    public Object getValue() {
<span class="fc" id="L41">    return value;</span>
  }

  public void setValue(Object value) {
<span class="fc" id="L45">    this.value = value;</span>
<span class="fc" id="L46">  }</span>

  public CacheRequestV2 type(CacheTypeV2 type) {
<span class="fc" id="L49">    this.type = type;</span>
<span class="fc" id="L50">    return this;</span>
  }

  /**
   * Get type
   * @return type
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public CacheTypeV2 getType() {
<span class="fc" id="L61">    return type;</span>
  }

  public void setType(CacheTypeV2 type) {
<span class="fc" id="L65">    this.type = type;</span>
<span class="fc" id="L66">  }</span>

  public CacheRequestV2 operation(CacheOperationV2 operation) {
<span class="fc" id="L69">    this.operation = operation;</span>
<span class="fc" id="L70">    return this;</span>
  }

  /**
   * Get operation
   * @return operation
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public CacheOperationV2 getOperation() {
<span class="fc" id="L81">    return operation;</span>
  }

  public void setOperation(CacheOperationV2 operation) {
<span class="fc" id="L85">    this.operation = operation;</span>
<span class="fc" id="L86">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L91" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L92">      return true;</span>
    }
<span class="nc bnc" id="L94" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L95">      return false;</span>
    }
<span class="nc" id="L97">    CacheRequestV2 cacheRequestV2 = (CacheRequestV2) o;</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">    return Objects.equals(this.value, cacheRequestV2.value) &amp;&amp;</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">        Objects.equals(this.type, cacheRequestV2.type) &amp;&amp;</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">        Objects.equals(this.operation, cacheRequestV2.operation);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L105">    return Objects.hash(value, type, operation);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L110">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L111">    sb.append(&quot;class CacheRequestV2 {\n&quot;);</span>
    
<span class="nc" id="L113">    sb.append(&quot;    value: &quot;).append(toIndentedString(value)).append(&quot;\n&quot;);</span>
<span class="nc" id="L114">    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</span>
<span class="nc" id="L115">    sb.append(&quot;    operation: &quot;).append(toIndentedString(operation)).append(&quot;\n&quot;);</span>
<span class="nc" id="L116">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L117">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L125" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L126">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L128">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>