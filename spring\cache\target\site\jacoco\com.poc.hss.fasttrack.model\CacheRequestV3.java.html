<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheRequestV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CacheRequestV3.java</span></div><h1>CacheRequestV3.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.CacheOperationV3;
import com.poc.hss.fasttrack.model.CacheTypeV3;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * CacheRequestV3
 */
@Validated


<span class="fc" id="L19">public class CacheRequestV3   {</span>
<span class="fc" id="L20">  @JsonProperty(&quot;batch&quot;)</span>
  private String batch = null;

<span class="fc" id="L23">  @JsonProperty(&quot;value&quot;)</span>
  private Object value = null;

<span class="fc" id="L26">  @JsonProperty(&quot;type&quot;)</span>
  private CacheTypeV3 type = null;

<span class="fc" id="L29">  @JsonProperty(&quot;operation&quot;)</span>
  private CacheOperationV3 operation = null;

  public CacheRequestV3 batch(String batch) {
<span class="fc" id="L33">    this.batch = batch;</span>
<span class="fc" id="L34">    return this;</span>
  }

  /**
   * Get batch
   * @return batch
   **/
  @Schema(description = &quot;&quot;)
  
    public String getBatch() {
<span class="fc" id="L44">    return batch;</span>
  }

  public void setBatch(String batch) {
<span class="fc" id="L48">    this.batch = batch;</span>
<span class="fc" id="L49">  }</span>

  public CacheRequestV3 value(Object value) {
<span class="fc" id="L52">    this.value = value;</span>
<span class="fc" id="L53">    return this;</span>
  }

  /**
   * Get value
   * @return value
   **/
  @Schema(description = &quot;&quot;)
  
    public Object getValue() {
<span class="fc" id="L63">    return value;</span>
  }

  public void setValue(Object value) {
<span class="fc" id="L67">    this.value = value;</span>
<span class="fc" id="L68">  }</span>

  public CacheRequestV3 type(CacheTypeV3 type) {
<span class="fc" id="L71">    this.type = type;</span>
<span class="fc" id="L72">    return this;</span>
  }

  /**
   * Get type
   * @return type
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public CacheTypeV3 getType() {
<span class="fc" id="L83">    return type;</span>
  }

  public void setType(CacheTypeV3 type) {
<span class="fc" id="L87">    this.type = type;</span>
<span class="fc" id="L88">  }</span>

  public CacheRequestV3 operation(CacheOperationV3 operation) {
<span class="fc" id="L91">    this.operation = operation;</span>
<span class="fc" id="L92">    return this;</span>
  }

  /**
   * Get operation
   * @return operation
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public CacheOperationV3 getOperation() {
<span class="fc" id="L103">    return operation;</span>
  }

  public void setOperation(CacheOperationV3 operation) {
<span class="fc" id="L107">    this.operation = operation;</span>
<span class="fc" id="L108">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L113" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L114">      return true;</span>
    }
<span class="nc bnc" id="L116" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L117">      return false;</span>
    }
<span class="nc" id="L119">    CacheRequestV3 cacheRequestV3 = (CacheRequestV3) o;</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">    return Objects.equals(this.batch, cacheRequestV3.batch) &amp;&amp;</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">        Objects.equals(this.value, cacheRequestV3.value) &amp;&amp;</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">        Objects.equals(this.type, cacheRequestV3.type) &amp;&amp;</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">        Objects.equals(this.operation, cacheRequestV3.operation);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L128">    return Objects.hash(batch, value, type, operation);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L133">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L134">    sb.append(&quot;class CacheRequestV3 {\n&quot;);</span>
    
<span class="nc" id="L136">    sb.append(&quot;    batch: &quot;).append(toIndentedString(batch)).append(&quot;\n&quot;);</span>
<span class="nc" id="L137">    sb.append(&quot;    value: &quot;).append(toIndentedString(value)).append(&quot;\n&quot;);</span>
<span class="nc" id="L138">    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</span>
<span class="nc" id="L139">    sb.append(&quot;    operation: &quot;).append(toIndentedString(operation)).append(&quot;\n&quot;);</span>
<span class="nc" id="L140">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L141">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L149" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L150">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L152">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>