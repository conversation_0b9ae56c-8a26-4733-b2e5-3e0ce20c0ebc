<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheResponseV2.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CacheResponseV2.java</span></div><h1>CacheResponseV2.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.CacheTypeV2;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * CacheResponseV2
 */
@Validated


<span class="nc" id="L19">public class CacheResponseV2   {</span>
<span class="nc" id="L20">  @JsonProperty(&quot;id&quot;)</span>
  private String id = null;

<span class="nc" id="L23">  @JsonProperty(&quot;name&quot;)</span>
  private String name = null;

<span class="nc" id="L26">  @JsonProperty(&quot;key&quot;)</span>
  private String key = null;

<span class="nc" id="L29">  @JsonProperty(&quot;value&quot;)</span>
  private Object value = null;

<span class="nc" id="L32">  @JsonProperty(&quot;type&quot;)</span>
  private CacheTypeV2 type = null;

<span class="nc" id="L35">  @JsonProperty(&quot;createdTime&quot;)</span>
  private LocalDateTime createdTime = null;

<span class="nc" id="L38">  @JsonProperty(&quot;updatedTime&quot;)</span>
  private LocalDateTime updatedTime = null;

  public CacheResponseV2 id(String id) {
<span class="nc" id="L42">    this.id = id;</span>
<span class="nc" id="L43">    return this;</span>
  }

  /**
   * Get id
   * @return id
   **/
  @Schema(description = &quot;&quot;)
  
    public String getId() {
<span class="nc" id="L53">    return id;</span>
  }

  public void setId(String id) {
<span class="nc" id="L57">    this.id = id;</span>
<span class="nc" id="L58">  }</span>

  public CacheResponseV2 name(String name) {
<span class="nc" id="L61">    this.name = name;</span>
<span class="nc" id="L62">    return this;</span>
  }

  /**
   * Get name
   * @return name
   **/
  @Schema(description = &quot;&quot;)
  
    public String getName() {
<span class="nc" id="L72">    return name;</span>
  }

  public void setName(String name) {
<span class="nc" id="L76">    this.name = name;</span>
<span class="nc" id="L77">  }</span>

  public CacheResponseV2 key(String key) {
<span class="nc" id="L80">    this.key = key;</span>
<span class="nc" id="L81">    return this;</span>
  }

  /**
   * Get key
   * @return key
   **/
  @Schema(description = &quot;&quot;)
  
    public String getKey() {
<span class="nc" id="L91">    return key;</span>
  }

  public void setKey(String key) {
<span class="nc" id="L95">    this.key = key;</span>
<span class="nc" id="L96">  }</span>

  public CacheResponseV2 value(Object value) {
<span class="nc" id="L99">    this.value = value;</span>
<span class="nc" id="L100">    return this;</span>
  }

  /**
   * Get value
   * @return value
   **/
  @Schema(description = &quot;&quot;)
  
    public Object getValue() {
<span class="nc" id="L110">    return value;</span>
  }

  public void setValue(Object value) {
<span class="nc" id="L114">    this.value = value;</span>
<span class="nc" id="L115">  }</span>

  public CacheResponseV2 type(CacheTypeV2 type) {
<span class="nc" id="L118">    this.type = type;</span>
<span class="nc" id="L119">    return this;</span>
  }

  /**
   * Get type
   * @return type
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public CacheTypeV2 getType() {
<span class="nc" id="L130">    return type;</span>
  }

  public void setType(CacheTypeV2 type) {
<span class="nc" id="L134">    this.type = type;</span>
<span class="nc" id="L135">  }</span>

  public CacheResponseV2 createdTime(LocalDateTime createdTime) {
<span class="nc" id="L138">    this.createdTime = createdTime;</span>
<span class="nc" id="L139">    return this;</span>
  }

  /**
   * Get createdTime
   * @return createdTime
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public LocalDateTime getCreatedTime() {
<span class="nc" id="L150">    return createdTime;</span>
  }

  public void setCreatedTime(LocalDateTime createdTime) {
<span class="nc" id="L154">    this.createdTime = createdTime;</span>
<span class="nc" id="L155">  }</span>

  public CacheResponseV2 updatedTime(LocalDateTime updatedTime) {
<span class="nc" id="L158">    this.updatedTime = updatedTime;</span>
<span class="nc" id="L159">    return this;</span>
  }

  /**
   * Get updatedTime
   * @return updatedTime
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public LocalDateTime getUpdatedTime() {
<span class="nc" id="L170">    return updatedTime;</span>
  }

  public void setUpdatedTime(LocalDateTime updatedTime) {
<span class="nc" id="L174">    this.updatedTime = updatedTime;</span>
<span class="nc" id="L175">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L180" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L181">      return true;</span>
    }
<span class="nc bnc" id="L183" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L184">      return false;</span>
    }
<span class="nc" id="L186">    CacheResponseV2 cacheResponseV2 = (CacheResponseV2) o;</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">    return Objects.equals(this.id, cacheResponseV2.id) &amp;&amp;</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">        Objects.equals(this.name, cacheResponseV2.name) &amp;&amp;</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">        Objects.equals(this.key, cacheResponseV2.key) &amp;&amp;</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">        Objects.equals(this.value, cacheResponseV2.value) &amp;&amp;</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">        Objects.equals(this.type, cacheResponseV2.type) &amp;&amp;</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">        Objects.equals(this.createdTime, cacheResponseV2.createdTime) &amp;&amp;</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">        Objects.equals(this.updatedTime, cacheResponseV2.updatedTime);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L198">    return Objects.hash(id, name, key, value, type, createdTime, updatedTime);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L203">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L204">    sb.append(&quot;class CacheResponseV2 {\n&quot;);</span>
    
<span class="nc" id="L206">    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</span>
<span class="nc" id="L207">    sb.append(&quot;    name: &quot;).append(toIndentedString(name)).append(&quot;\n&quot;);</span>
<span class="nc" id="L208">    sb.append(&quot;    key: &quot;).append(toIndentedString(key)).append(&quot;\n&quot;);</span>
<span class="nc" id="L209">    sb.append(&quot;    value: &quot;).append(toIndentedString(value)).append(&quot;\n&quot;);</span>
<span class="nc" id="L210">    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</span>
<span class="nc" id="L211">    sb.append(&quot;    createdTime: &quot;).append(toIndentedString(createdTime)).append(&quot;\n&quot;);</span>
<span class="nc" id="L212">    sb.append(&quot;    updatedTime: &quot;).append(toIndentedString(updatedTime)).append(&quot;\n&quot;);</span>
<span class="nc" id="L213">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L214">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L222" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L223">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L225">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>