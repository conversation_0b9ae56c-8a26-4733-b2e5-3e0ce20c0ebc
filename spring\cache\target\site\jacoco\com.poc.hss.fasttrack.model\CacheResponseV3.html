<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheResponseV3</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_class">CacheResponseV3</span></div><h1>CacheResponseV3</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">270 of 423</td><td class="ctr2">36%</td><td class="bar">28 of 28</td><td class="ctr2">0%</td><td class="ctr1">18</td><td class="ctr2">49</td><td class="ctr1">33</td><td class="ctr2">94</td><td class="ctr1">4</td><td class="ctr2">35</td></tr></tfoot><tbody><tr><td id="a31"><a href="CacheResponseV3.java.html#L274" class="el_method">toString()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="125" alt="125"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h1">14</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a4"><a href="CacheResponseV3.java.html#L248" class="el_method">equals(Object)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="81" alt="81"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="26" alt="26"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">14</td><td class="ctr2" id="g0">14</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a16"><a href="CacheResponseV3.java.html#L269" class="el_method">hashCode()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="54" alt="54"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a30"><a href="CacheResponseV3.java.html#L296" class="el_method">toIndentedString(Object)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="10" alt="10"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">3</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="CacheResponseV3.java.html#L20" class="el_method">CacheResponseV3()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="33" alt="33"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a17"><a href="CacheResponseV3.java.html#L52" class="el_method">id(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a15"><a href="CacheResponseV3.java.html#L71" class="el_method">group(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="CacheResponseV3.java.html#L90" class="el_method">component(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a0"><a href="CacheResponseV3.java.html#L109" class="el_method">batch(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a18"><a href="CacheResponseV3.java.html#L128" class="el_method">key(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a34"><a href="CacheResponseV3.java.html#L147" class="el_method">value(Object)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a32"><a href="CacheResponseV3.java.html#L166" class="el_method">type(CacheTypeV3)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a29"><a href="CacheResponseV3.java.html#L186" class="el_method">status(BatchStatus)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="CacheResponseV3.java.html#L206" class="el_method">createdTime(LocalDateTime)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i12">2</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a33"><a href="CacheResponseV3.java.html#L226" class="el_method">updatedTime(LocalDateTime)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a23"><a href="CacheResponseV3.java.html#L67" class="el_method">setId(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a22"><a href="CacheResponseV3.java.html#L86" class="el_method">setGroup(String)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a20"><a href="CacheResponseV3.java.html#L105" class="el_method">setComponent(String)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a19"><a href="CacheResponseV3.java.html#L124" class="el_method">setBatch(String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i17">2</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a24"><a href="CacheResponseV3.java.html#L143" class="el_method">setKey(String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i18">2</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a28"><a href="CacheResponseV3.java.html#L162" class="el_method">setValue(Object)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a26"><a href="CacheResponseV3.java.html#L182" class="el_method">setType(CacheTypeV3)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">0</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">0</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j21">0</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a25"><a href="CacheResponseV3.java.html#L202" class="el_method">setStatus(BatchStatus)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c18">100%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">0</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">0</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j22">0</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a21"><a href="CacheResponseV3.java.html#L222" class="el_method">setCreatedTime(LocalDateTime)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c19">100%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">0</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">0</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j23">0</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a27"><a href="CacheResponseV3.java.html#L242" class="el_method">setUpdatedTime(LocalDateTime)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c20">100%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">0</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">0</td><td class="ctr2" id="i23">2</td><td class="ctr1" id="j24">0</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a9"><a href="CacheResponseV3.java.html#L63" class="el_method">getId()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c21">100%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">0</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">0</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j25">0</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a8"><a href="CacheResponseV3.java.html#L82" class="el_method">getGroup()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c22">100%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">0</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">0</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j26">0</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a6"><a href="CacheResponseV3.java.html#L101" class="el_method">getComponent()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c23">100%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">0</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">0</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j27">0</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a5"><a href="CacheResponseV3.java.html#L120" class="el_method">getBatch()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c24">100%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">0</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">0</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j28">0</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a10"><a href="CacheResponseV3.java.html#L139" class="el_method">getKey()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c25">100%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">0</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">0</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j29">0</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a14"><a href="CacheResponseV3.java.html#L158" class="el_method">getValue()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c26">100%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">0</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">0</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j30">0</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a12"><a href="CacheResponseV3.java.html#L178" class="el_method">getType()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c27">100%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">0</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">0</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j31">0</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a11"><a href="CacheResponseV3.java.html#L198" class="el_method">getStatus()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c28">100%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">0</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">0</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j32">0</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a7"><a href="CacheResponseV3.java.html#L218" class="el_method">getCreatedTime()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c29">100%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">0</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">0</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">0</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a13"><a href="CacheResponseV3.java.html#L238" class="el_method">getUpdatedTime()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c30">100%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">0</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">0</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">0</td><td class="ctr2" id="k34">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>