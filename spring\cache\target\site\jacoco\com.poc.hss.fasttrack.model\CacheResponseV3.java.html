<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheResponseV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CacheResponseV3.java</span></div><h1>CacheResponseV3.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.BatchStatus;
import com.poc.hss.fasttrack.model.CacheTypeV3;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * CacheResponseV3
 */
@Validated


<span class="fc" id="L20">public class CacheResponseV3   {</span>
<span class="fc" id="L21">  @JsonProperty(&quot;id&quot;)</span>
  private String id = null;

<span class="fc" id="L24">  @JsonProperty(&quot;group&quot;)</span>
  private String group = null;

<span class="fc" id="L27">  @JsonProperty(&quot;component&quot;)</span>
  private String component = null;

<span class="fc" id="L30">  @JsonProperty(&quot;batch&quot;)</span>
  private String batch = null;

<span class="fc" id="L33">  @JsonProperty(&quot;key&quot;)</span>
  private String key = null;

<span class="fc" id="L36">  @JsonProperty(&quot;value&quot;)</span>
  private Object value = null;

<span class="fc" id="L39">  @JsonProperty(&quot;type&quot;)</span>
  private CacheTypeV3 type = null;

<span class="fc" id="L42">  @JsonProperty(&quot;status&quot;)</span>
  private BatchStatus status = null;

<span class="fc" id="L45">  @JsonProperty(&quot;createdTime&quot;)</span>
  private LocalDateTime createdTime = null;

<span class="fc" id="L48">  @JsonProperty(&quot;updatedTime&quot;)</span>
  private LocalDateTime updatedTime = null;

  public CacheResponseV3 id(String id) {
<span class="fc" id="L52">    this.id = id;</span>
<span class="fc" id="L53">    return this;</span>
  }

  /**
   * Get id
   * @return id
   **/
  @Schema(description = &quot;&quot;)
  
    public String getId() {
<span class="fc" id="L63">    return id;</span>
  }

  public void setId(String id) {
<span class="fc" id="L67">    this.id = id;</span>
<span class="fc" id="L68">  }</span>

  public CacheResponseV3 group(String group) {
<span class="fc" id="L71">    this.group = group;</span>
<span class="fc" id="L72">    return this;</span>
  }

  /**
   * Get group
   * @return group
   **/
  @Schema(description = &quot;&quot;)
  
    public String getGroup() {
<span class="fc" id="L82">    return group;</span>
  }

  public void setGroup(String group) {
<span class="fc" id="L86">    this.group = group;</span>
<span class="fc" id="L87">  }</span>

  public CacheResponseV3 component(String component) {
<span class="fc" id="L90">    this.component = component;</span>
<span class="fc" id="L91">    return this;</span>
  }

  /**
   * Get component
   * @return component
   **/
  @Schema(description = &quot;&quot;)
  
    public String getComponent() {
<span class="fc" id="L101">    return component;</span>
  }

  public void setComponent(String component) {
<span class="fc" id="L105">    this.component = component;</span>
<span class="fc" id="L106">  }</span>

  public CacheResponseV3 batch(String batch) {
<span class="fc" id="L109">    this.batch = batch;</span>
<span class="fc" id="L110">    return this;</span>
  }

  /**
   * Get batch
   * @return batch
   **/
  @Schema(description = &quot;&quot;)
  
    public String getBatch() {
<span class="fc" id="L120">    return batch;</span>
  }

  public void setBatch(String batch) {
<span class="fc" id="L124">    this.batch = batch;</span>
<span class="fc" id="L125">  }</span>

  public CacheResponseV3 key(String key) {
<span class="fc" id="L128">    this.key = key;</span>
<span class="fc" id="L129">    return this;</span>
  }

  /**
   * Get key
   * @return key
   **/
  @Schema(description = &quot;&quot;)
  
    public String getKey() {
<span class="fc" id="L139">    return key;</span>
  }

  public void setKey(String key) {
<span class="fc" id="L143">    this.key = key;</span>
<span class="fc" id="L144">  }</span>

  public CacheResponseV3 value(Object value) {
<span class="fc" id="L147">    this.value = value;</span>
<span class="fc" id="L148">    return this;</span>
  }

  /**
   * Get value
   * @return value
   **/
  @Schema(description = &quot;&quot;)
  
    public Object getValue() {
<span class="fc" id="L158">    return value;</span>
  }

  public void setValue(Object value) {
<span class="fc" id="L162">    this.value = value;</span>
<span class="fc" id="L163">  }</span>

  public CacheResponseV3 type(CacheTypeV3 type) {
<span class="fc" id="L166">    this.type = type;</span>
<span class="fc" id="L167">    return this;</span>
  }

  /**
   * Get type
   * @return type
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public CacheTypeV3 getType() {
<span class="fc" id="L178">    return type;</span>
  }

  public void setType(CacheTypeV3 type) {
<span class="fc" id="L182">    this.type = type;</span>
<span class="fc" id="L183">  }</span>

  public CacheResponseV3 status(BatchStatus status) {
<span class="fc" id="L186">    this.status = status;</span>
<span class="fc" id="L187">    return this;</span>
  }

  /**
   * Get status
   * @return status
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public BatchStatus getStatus() {
<span class="fc" id="L198">    return status;</span>
  }

  public void setStatus(BatchStatus status) {
<span class="fc" id="L202">    this.status = status;</span>
<span class="fc" id="L203">  }</span>

  public CacheResponseV3 createdTime(LocalDateTime createdTime) {
<span class="fc" id="L206">    this.createdTime = createdTime;</span>
<span class="fc" id="L207">    return this;</span>
  }

  /**
   * Get createdTime
   * @return createdTime
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public LocalDateTime getCreatedTime() {
<span class="fc" id="L218">    return createdTime;</span>
  }

  public void setCreatedTime(LocalDateTime createdTime) {
<span class="fc" id="L222">    this.createdTime = createdTime;</span>
<span class="fc" id="L223">  }</span>

  public CacheResponseV3 updatedTime(LocalDateTime updatedTime) {
<span class="fc" id="L226">    this.updatedTime = updatedTime;</span>
<span class="fc" id="L227">    return this;</span>
  }

  /**
   * Get updatedTime
   * @return updatedTime
   **/
  @Schema(description = &quot;&quot;)
  
    @Valid
    public LocalDateTime getUpdatedTime() {
<span class="fc" id="L238">    return updatedTime;</span>
  }

  public void setUpdatedTime(LocalDateTime updatedTime) {
<span class="fc" id="L242">    this.updatedTime = updatedTime;</span>
<span class="fc" id="L243">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L248" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L249">      return true;</span>
    }
<span class="nc bnc" id="L251" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L252">      return false;</span>
    }
<span class="nc" id="L254">    CacheResponseV3 cacheResponseV3 = (CacheResponseV3) o;</span>
<span class="nc bnc" id="L255" title="All 2 branches missed.">    return Objects.equals(this.id, cacheResponseV3.id) &amp;&amp;</span>
<span class="nc bnc" id="L256" title="All 2 branches missed.">        Objects.equals(this.group, cacheResponseV3.group) &amp;&amp;</span>
<span class="nc bnc" id="L257" title="All 2 branches missed.">        Objects.equals(this.component, cacheResponseV3.component) &amp;&amp;</span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">        Objects.equals(this.batch, cacheResponseV3.batch) &amp;&amp;</span>
<span class="nc bnc" id="L259" title="All 2 branches missed.">        Objects.equals(this.key, cacheResponseV3.key) &amp;&amp;</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">        Objects.equals(this.value, cacheResponseV3.value) &amp;&amp;</span>
<span class="nc bnc" id="L261" title="All 2 branches missed.">        Objects.equals(this.type, cacheResponseV3.type) &amp;&amp;</span>
<span class="nc bnc" id="L262" title="All 2 branches missed.">        Objects.equals(this.status, cacheResponseV3.status) &amp;&amp;</span>
<span class="nc bnc" id="L263" title="All 2 branches missed.">        Objects.equals(this.createdTime, cacheResponseV3.createdTime) &amp;&amp;</span>
<span class="nc bnc" id="L264" title="All 2 branches missed.">        Objects.equals(this.updatedTime, cacheResponseV3.updatedTime);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L269">    return Objects.hash(id, group, component, batch, key, value, type, status, createdTime, updatedTime);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L274">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L275">    sb.append(&quot;class CacheResponseV3 {\n&quot;);</span>
    
<span class="nc" id="L277">    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</span>
<span class="nc" id="L278">    sb.append(&quot;    group: &quot;).append(toIndentedString(group)).append(&quot;\n&quot;);</span>
<span class="nc" id="L279">    sb.append(&quot;    component: &quot;).append(toIndentedString(component)).append(&quot;\n&quot;);</span>
<span class="nc" id="L280">    sb.append(&quot;    batch: &quot;).append(toIndentedString(batch)).append(&quot;\n&quot;);</span>
<span class="nc" id="L281">    sb.append(&quot;    key: &quot;).append(toIndentedString(key)).append(&quot;\n&quot;);</span>
<span class="nc" id="L282">    sb.append(&quot;    value: &quot;).append(toIndentedString(value)).append(&quot;\n&quot;);</span>
<span class="nc" id="L283">    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</span>
<span class="nc" id="L284">    sb.append(&quot;    status: &quot;).append(toIndentedString(status)).append(&quot;\n&quot;);</span>
<span class="nc" id="L285">    sb.append(&quot;    createdTime: &quot;).append(toIndentedString(createdTime)).append(&quot;\n&quot;);</span>
<span class="nc" id="L286">    sb.append(&quot;    updatedTime: &quot;).append(toIndentedString(updatedTime)).append(&quot;\n&quot;);</span>
<span class="nc" id="L287">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L288">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L296" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L297">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L299">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>