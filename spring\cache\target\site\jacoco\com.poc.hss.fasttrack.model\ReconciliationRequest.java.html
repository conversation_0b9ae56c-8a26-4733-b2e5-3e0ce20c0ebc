<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">ReconciliationRequest.java</span></div><h1>ReconciliationRequest.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ReconciliationRequest
 */
@Validated


<span class="nc" id="L17">public class ReconciliationRequest   {</span>
<span class="nc" id="L18">  @JsonProperty(&quot;group&quot;)</span>
  private String group = null;

<span class="nc" id="L21">  @JsonProperty(&quot;component&quot;)</span>
  private String component = null;

<span class="nc" id="L24">  @JsonProperty(&quot;batch&quot;)</span>
  private String batch = null;

  public ReconciliationRequest group(String group) {
<span class="nc" id="L28">    this.group = group;</span>
<span class="nc" id="L29">    return this;</span>
  }

  /**
   * Get group
   * @return group
   **/
  @Schema(description = &quot;&quot;)
  
    public String getGroup() {
<span class="nc" id="L39">    return group;</span>
  }

  public void setGroup(String group) {
<span class="nc" id="L43">    this.group = group;</span>
<span class="nc" id="L44">  }</span>

  public ReconciliationRequest component(String component) {
<span class="nc" id="L47">    this.component = component;</span>
<span class="nc" id="L48">    return this;</span>
  }

  /**
   * Get component
   * @return component
   **/
  @Schema(description = &quot;&quot;)
  
    public String getComponent() {
<span class="nc" id="L58">    return component;</span>
  }

  public void setComponent(String component) {
<span class="nc" id="L62">    this.component = component;</span>
<span class="nc" id="L63">  }</span>

  public ReconciliationRequest batch(String batch) {
<span class="nc" id="L66">    this.batch = batch;</span>
<span class="nc" id="L67">    return this;</span>
  }

  /**
   * Get batch
   * @return batch
   **/
  @Schema(description = &quot;&quot;)
  
    public String getBatch() {
<span class="nc" id="L77">    return batch;</span>
  }

  public void setBatch(String batch) {
<span class="nc" id="L81">    this.batch = batch;</span>
<span class="nc" id="L82">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L87" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L88">      return true;</span>
    }
<span class="nc bnc" id="L90" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L91">      return false;</span>
    }
<span class="nc" id="L93">    ReconciliationRequest reconciliationRequest = (ReconciliationRequest) o;</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">    return Objects.equals(this.group, reconciliationRequest.group) &amp;&amp;</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">        Objects.equals(this.component, reconciliationRequest.component) &amp;&amp;</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">        Objects.equals(this.batch, reconciliationRequest.batch);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L101">    return Objects.hash(group, component, batch);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L106">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L107">    sb.append(&quot;class ReconciliationRequest {\n&quot;);</span>
    
<span class="nc" id="L109">    sb.append(&quot;    group: &quot;).append(toIndentedString(group)).append(&quot;\n&quot;);</span>
<span class="nc" id="L110">    sb.append(&quot;    component: &quot;).append(toIndentedString(component)).append(&quot;\n&quot;);</span>
<span class="nc" id="L111">    sb.append(&quot;    batch: &quot;).append(toIndentedString(batch)).append(&quot;\n&quot;);</span>
<span class="nc" id="L112">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L113">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L121" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L122">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L124">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>