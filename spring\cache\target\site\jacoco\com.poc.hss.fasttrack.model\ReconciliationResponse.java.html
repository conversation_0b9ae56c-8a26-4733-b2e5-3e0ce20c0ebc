<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">ReconciliationResponse.java</span></div><h1>ReconciliationResponse.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * ReconciliationResponse
 */
@Validated


<span class="nc" id="L17">public class ReconciliationResponse   {</span>
<span class="nc" id="L18">  @JsonProperty(&quot;completed&quot;)</span>
  private Boolean completed = null;

  public ReconciliationResponse completed(Boolean completed) {
<span class="nc" id="L22">    this.completed = completed;</span>
<span class="nc" id="L23">    return this;</span>
  }

  /**
   * Get completed
   * @return completed
   **/
  @Schema(description = &quot;&quot;)
  
    public Boolean isCompleted() {
<span class="nc" id="L33">    return completed;</span>
  }

  public void setCompleted(Boolean completed) {
<span class="nc" id="L37">    this.completed = completed;</span>
<span class="nc" id="L38">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L43" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L44">      return true;</span>
    }
<span class="nc bnc" id="L46" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L47">      return false;</span>
    }
<span class="nc" id="L49">    ReconciliationResponse reconciliationResponse = (ReconciliationResponse) o;</span>
<span class="nc" id="L50">    return Objects.equals(this.completed, reconciliationResponse.completed);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L55">    return Objects.hash(completed);</span>
  }

  @Override
  public String toString() {
<span class="nc" id="L60">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L61">    sb.append(&quot;class ReconciliationResponse {\n&quot;);</span>
    
<span class="nc" id="L63">    sb.append(&quot;    completed: &quot;).append(toIndentedString(completed)).append(&quot;\n&quot;);</span>
<span class="nc" id="L64">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L65">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L73" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L74">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L76">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>