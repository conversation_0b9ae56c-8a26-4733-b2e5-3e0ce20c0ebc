<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">BatchService.java</span></div><h1>BatchService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.dto.BatchDTO;
import com.poc.hss.fasttrack.dto.BatchPageDTO;
import com.poc.hss.fasttrack.dto.BatchQueryDTO;
import com.poc.hss.fasttrack.dto.PageDTO;
import com.poc.hss.fasttrack.jpa.model.BatchEntity;
import com.poc.hss.fasttrack.jpa.repository.BatchRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

@Service
<span class="fc" id="L16">public class BatchService {</span>
    @Autowired
    private BatchRepository batchRepository;

    public BatchPageDTO searchBatch(BatchQueryDTO queryDTO, PageDTO pageDTO) {
<span class="nc" id="L21">        Page&lt;BatchEntity&gt; page = batchRepository.findAll(BatchEntity.getSpecification(queryDTO), pageDTO.toPageRequest());</span>

<span class="nc" id="L23">        return BatchPageDTO.builder()</span>
<span class="nc" id="L24">                .data(page.stream()</span>
<span class="nc" id="L25">                        .map(this::fromEntity)</span>
<span class="nc" id="L26">                        .collect(Collectors.toList())</span>
                )
<span class="nc" id="L28">                .total(page.getTotalElements())</span>
<span class="nc" id="L29">                .build();</span>
    }

    private BatchDTO fromEntity(BatchEntity batchEntity) {
<span class="nc" id="L33">        return BatchDTO.builder()</span>
<span class="nc" id="L34">                .id(batchEntity.getId())</span>
<span class="nc" id="L35">                .batch(batchEntity.getBatch())</span>
<span class="nc" id="L36">                .deployment(batchEntity.getDeployment())</span>
<span class="nc" id="L37">                .group(batchEntity.getGroup())</span>
<span class="nc" id="L38">                .component(batchEntity.getComponent())</span>
<span class="nc" id="L39">                .completed(batchEntity.getCompleted())</span>
<span class="nc" id="L40">                .sourceMetricOut(batchEntity.getSourceMetricOut())</span>
<span class="nc" id="L41">                .metricIn(batchEntity.getMetricIn())</span>
<span class="nc" id="L42">                .topic(batchEntity.getTopic())</span>
<span class="nc" id="L43">                .consumerGroup(batchEntity.getConsumerGroup())</span>
<span class="nc" id="L44">                .status(batchEntity.getStatus())</span>
<span class="nc" id="L45">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>