<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheRetentionService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">CacheRetentionService.java</span></div><h1>CacheRetentionService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

<span class="fc" id="L11">@Slf4j</span>
@Service
<span class="fc" id="L13">public class CacheRetentionService {</span>

    @Value(&quot;${cache.retention.beforeDays}&quot;)
    private int beforeDays;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private ReconciliationService reconciliationService;

    @Scheduled(cron = &quot;${cache.retention.cron}&quot;)
    public void triggerHousekeeping() {
<span class="fc" id="L26">        LocalDateTime date = LocalDateTime.now().minusDays(beforeDays);</span>
<span class="fc" id="L27">        cacheService.runHousekeeping(date);</span>
<span class="fc" id="L28">        reconciliationService.runHousekeeping(date);</span>
<span class="fc" id="L29">    }</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>