<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">CacheService.java</span></div><h1>CacheService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.dto.*;
import com.poc.hss.fasttrack.enums.CacheType;
import com.poc.hss.fasttrack.jpa.model.CacheEntity;
import com.poc.hss.fasttrack.jpa.repository.CacheRepository;
import com.poc.hss.fasttrack.model.CacheOperationV3;
import com.poc.hss.fasttrack.model.CacheTypeV3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import jakarta.transaction.Transactional;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

<span class="fc" id="L21">@Slf4j</span>
@Service
<span class="fc" id="L23">public class CacheService {</span>

    @Autowired
    private CacheRepository cacheRepository;

    private CacheDTO defaultResponse(CacheCompositeKeyDTO cacheCompositeKeyDTO) {
<span class="fc" id="L29">        return CacheDTO</span>
<span class="fc" id="L30">                .builder()</span>
<span class="fc" id="L31">                .group(cacheCompositeKeyDTO.getGroup())</span>
<span class="fc" id="L32">                .component(cacheCompositeKeyDTO.getComponent())</span>
<span class="fc" id="L33">                .key(cacheCompositeKeyDTO.getKey())</span>
<span class="fc" id="L34">                .batch(cacheCompositeKeyDTO.getBatch())</span>
<span class="fc" id="L35">                .type(CacheTypeV3.NORMAL)</span>
<span class="fc" id="L36">                .build();</span>
    }

    private CacheEntity defaultEntity(CacheCompositeKeyDTO cacheCompositeKeyDTO) {
<span class="fc" id="L40">        return CacheEntity.builder()</span>
<span class="fc" id="L41">                .group(cacheCompositeKeyDTO.getGroup())</span>
<span class="fc" id="L42">                .component(cacheCompositeKeyDTO.getComponent())</span>
<span class="fc" id="L43">                .key(cacheCompositeKeyDTO.getKey())</span>
<span class="fc" id="L44">                .batch(cacheCompositeKeyDTO.getBatch())</span>
<span class="fc" id="L45">                .build();</span>
    }

    private CacheDTO fromEntity(CacheEntity cacheEntity) {
<span class="fc" id="L49">        return CacheDTO</span>
<span class="fc" id="L50">                .builder()</span>
<span class="fc" id="L51">                .id(cacheEntity.getId())</span>
<span class="fc" id="L52">                .group(cacheEntity.getGroup())</span>
<span class="fc" id="L53">                .component(cacheEntity.getComponent())</span>
<span class="fc" id="L54">                .key(cacheEntity.getKey())</span>
<span class="fc" id="L55">                .batch(cacheEntity.getBatch())</span>
<span class="fc" id="L56">                .type(CacheTypeV3.fromValue(cacheEntity.getType().toString()))</span>
<span class="fc" id="L57">                .value(cacheEntity.getTypedValue())</span>
<span class="pc bpc" id="L58" title="1 of 2 branches missed.">                .status(cacheEntity.getBatchEntity() == null ? null : cacheEntity.getBatchEntity().getStatus())</span>
<span class="fc" id="L59">                .createdTime(cacheEntity.getCreatedTime())</span>
<span class="fc" id="L60">                .updatedTime(cacheEntity.getUpdatedTime())</span>
<span class="fc" id="L61">                .build();</span>
    }

    public CacheDTO getCache(CacheCompositeKeyDTO cacheCompositeKeyDTO) {
<span class="fc" id="L65">        return cacheRepository.findByGroupAndComponentAndKeyAndBatch(</span>
<span class="fc" id="L66">                cacheCompositeKeyDTO.getGroup(),</span>
<span class="fc" id="L67">                cacheCompositeKeyDTO.getComponent(),</span>
<span class="fc" id="L68">                cacheCompositeKeyDTO.getKey(),</span>
<span class="fc" id="L69">                cacheCompositeKeyDTO.getBatch()</span>
        )
<span class="fc" id="L71">                .map(this::fromEntity)</span>
<span class="fc" id="L72">                .orElseGet(() -&gt; defaultResponse(cacheCompositeKeyDTO));</span>
    }

    public CachePageDTO searchCache(CacheQueryDTO cacheQueryDTO, PageDTO pageDTO) {
<span class="pc bpc" id="L76" title="3 of 4 branches missed.">        Assert.isTrue(cacheQueryDTO.isBatch() || StringUtils.isBlank(cacheQueryDTO.getBatch()), &quot;Batch id must be blank when isBatch=false&quot;);</span>

<span class="fc" id="L78">        Page&lt;CacheEntity&gt; cachePage = cacheRepository.findAll(CacheEntity.getSpecification(cacheQueryDTO), pageDTO.toPageRequest());</span>

<span class="fc" id="L80">        return CachePageDTO.builder()</span>
<span class="fc" id="L81">                .data(cachePage.stream()</span>
<span class="fc" id="L82">                        .map(this::fromEntity)</span>
<span class="fc" id="L83">                        .collect(Collectors.toList())</span>
                )
<span class="fc" id="L85">                .total(cachePage.getTotalElements())</span>
<span class="fc" id="L86">                .build();</span>
    }

    @Transactional
    public CacheDTO putCache(CacheCompositeKeyDTO cacheCompositeKeyDTO, CacheUpdateDTO request) {
<span class="pc bpc" id="L91" title="1 of 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="fc" id="L92">            log.debug(&quot;putCache: {} {}&quot;, cacheCompositeKeyDTO, request);</span>
<span class="fc" id="L93">        String group = cacheCompositeKeyDTO.getGroup();</span>
<span class="fc" id="L94">        String component = cacheCompositeKeyDTO.getComponent();</span>
<span class="fc" id="L95">        String key = cacheCompositeKeyDTO.getKey();</span>
<span class="fc" id="L96">        String batch = cacheCompositeKeyDTO.getBatch();</span>

<span class="fc" id="L98">        CacheEntity cacheEntity = cacheRepository.findByGroupAndComponentAndKeyAndBatch(group, component, key, batch)</span>
<span class="fc" id="L99">                .orElseGet(() -&gt; defaultEntity(cacheCompositeKeyDTO));</span>

<span class="fc bfc" id="L101" title="All 4 branches covered.">        if (cacheEntity.getId() == null || request.getOperation() == CacheOperationV3.SET) {</span>
<span class="fc" id="L102">            cacheEntity.setType(CacheType.fromValue(request.getType().toString()));</span>
<span class="fc" id="L103">            cacheEntity.setTypedValue(request.getValue());</span>
<span class="fc" id="L104">            cacheRepository.save(cacheEntity);</span>
<span class="pc bpc" id="L105" title="1 of 2 branches missed.">        } else if (request.getOperation() == CacheOperationV3.INCREMENT) {</span>
<span class="fc" id="L106">            long incrementValue = Long.parseLong(request.getValue().toString());</span>
<span class="pc bpc" id="L107" title="1 of 2 branches missed.">            if (cacheEntity.getType() == CacheType.NORMAL) {</span>
<span class="nc" id="L108">                long previousValue = Long.parseLong(cacheEntity.getTypedValue().toString());</span>
<span class="nc" id="L109">                cacheEntity.setType(CacheType.fromValue(request.getType().toString()));</span>
<span class="nc" id="L110">                cacheEntity.setTypedValue(previousValue + incrementValue);</span>
<span class="nc" id="L111">                cacheRepository.save(cacheEntity);</span>
<span class="nc" id="L112">            } else {</span>
<span class="pc bpc" id="L113" title="1 of 2 branches missed.">                if (StringUtils.isBlank(batch))</span>
<span class="nc" id="L114">                    cacheRepository.incrementValue(group, component, key, incrementValue);</span>
                else
<span class="fc" id="L116">                    cacheRepository.incrementValue(group, component, key, batch, incrementValue);</span>
            }
        }

<span class="fc" id="L120">        return fromEntity(cacheEntity);</span>
    }

    @Transactional
    public void evictCache(CacheCompositeKeyDTO cacheCompositeKeyDTO) {
<span class="fc" id="L125">        String group = cacheCompositeKeyDTO.getGroup();</span>
<span class="fc" id="L126">        String component = cacheCompositeKeyDTO.getComponent();</span>
<span class="fc" id="L127">        String key = cacheCompositeKeyDTO.getKey();</span>
<span class="fc" id="L128">        String batch = cacheCompositeKeyDTO.getBatch();</span>

<span class="fc" id="L130">        cacheRepository.deleteByGroupAndComponentAndKeyAndBatch(group, component, key, batch);</span>
<span class="fc" id="L131">    }</span>

    @Transactional
    public void clearCache(String group, String component) {
<span class="fc" id="L135">        cacheRepository.deleteByGroupAndComponent(group, component);</span>
<span class="fc" id="L136">    }</span>

    @Transactional
    public void runHousekeeping(LocalDateTime date) {
<span class="fc" id="L140">        Long deletedCount = cacheRepository.deleteByCreatedTimeLessThan(date);</span>
<span class="fc" id="L141">        log.info(&quot;{} records created before {} deleted&quot;, deletedCount, date);</span>
<span class="fc" id="L142">    }</span>

    public List&lt;CacheCompositeKeyDTO&gt; getDistinctGroupComponent() {
<span class="fc" id="L145">        return cacheRepository.distinctByGroupComponent();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>