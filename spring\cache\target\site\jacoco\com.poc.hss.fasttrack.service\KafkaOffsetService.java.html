<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaOffsetService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">KafkaOffsetService.java</span></div><h1>KafkaOffsetService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.jpa.model.KafkaOffsetEntity;
import com.poc.hss.fasttrack.jpa.repository.KafkaOffsetRepository;
import jakarta.annotation.PostConstruct;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.TopicPartition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import jakarta.transaction.Transactional;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L23">public class KafkaOffsetService&lt;K, V&gt; {</span>

    @Autowired
    private KafkaOffsetRepository kafkaOffsetRepository;

    @PostConstruct
    @Profile(&quot;local-test&quot;)
    public void removeAllKafkaOffset() {
<span class="fc" id="L31">        kafkaOffsetRepository.deleteAll();</span>
<span class="fc" id="L32">    }</span>

    public List&lt;ConsumerRecord&lt;K, V&gt;&gt; filterRecords(List&lt;ConsumerRecord&lt;K, V&gt;&gt; records) {
<span class="nc" id="L35">        Set&lt;TopicPartition&gt; topicPartitions = records.stream()</span>
<span class="nc" id="L36">                .map(record -&gt; new TopicPartition(record.topic(), record.partition()))</span>
<span class="nc" id="L37">                .collect(Collectors.toSet());</span>

<span class="nc" id="L39">        Map&lt;TopicPartition, Long&gt; processedOffsets = kafkaOffsetRepository.findAll(KafkaOffsetEntity.getSpecification(topicPartitions))</span>
<span class="nc" id="L40">                .stream()</span>
<span class="nc" id="L41">                .collect(Collectors.toMap(</span>
<span class="nc" id="L42">                        record -&gt; new TopicPartition(record.getTopic(), record.getPartition()),</span>
                        KafkaOffsetEntity::getOffset
                ));

<span class="nc" id="L46">        return records.stream()</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">                .filter(record -&gt; record.offset() &gt;= processedOffsets.getOrDefault(new TopicPartition(record.topic(), record.partition()), 0L))</span>
<span class="nc" id="L48">                .collect(Collectors.toList());</span>
    }

    @Transactional
    public void commitRecords(List&lt;ConsumerRecord&lt;K, V&gt;&gt; records) {
<span class="nc" id="L53">        Map&lt;TopicPartition, ConsumerRecord&lt;K, V&gt;&gt; lastRecordOfPartitions = records.stream()</span>
<span class="nc" id="L54">                .collect(Collectors.toMap(</span>
<span class="nc" id="L55">                        record -&gt; new TopicPartition(record.topic(), record.partition()),</span>
<span class="nc" id="L56">                        Function.identity(),</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">                        (r1, r2) -&gt; r1.offset() &gt; r2.offset() ? r1 : r2</span>
                ));

<span class="nc" id="L60">        Map&lt;TopicPartition, KafkaOffsetEntity&gt; entityMap = kafkaOffsetRepository.findAll(KafkaOffsetEntity.getSpecification(lastRecordOfPartitions.keySet()))</span>
<span class="nc" id="L61">                .stream()</span>
<span class="nc" id="L62">                .collect(Collectors.toMap(</span>
<span class="nc" id="L63">                        record -&gt; new TopicPartition(record.getTopic(), record.getPartition()),</span>
<span class="nc" id="L64">                        Function.identity()</span>
                ));

<span class="nc" id="L67">        List&lt;KafkaOffsetEntity&gt; updatedEntities = lastRecordOfPartitions.entrySet()</span>
<span class="nc" id="L68">                .stream()</span>
<span class="nc" id="L69">                .map(entry -&gt; {</span>
<span class="nc" id="L70">                    KafkaOffsetEntity entity = Optional.ofNullable(entityMap.get(entry.getKey()))</span>
<span class="nc" id="L71">                            .orElseGet(() -&gt;</span>
<span class="nc" id="L72">                                    KafkaOffsetEntity.builder()</span>
<span class="nc" id="L73">                                            .topic(entry.getKey().topic())</span>
<span class="nc" id="L74">                                            .partition(entry.getKey().partition())</span>
<span class="nc" id="L75">                                            .build()</span>
                            );

<span class="nc" id="L78">                    entity.setOffset(entry.getValue().offset());</span>
<span class="nc" id="L79">                    return entity;</span>
                })
<span class="nc" id="L81">                .collect(Collectors.toList());</span>

<span class="nc" id="L83">        kafkaOffsetRepository.saveAll(updatedEntities);</span>
<span class="nc" id="L84">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>