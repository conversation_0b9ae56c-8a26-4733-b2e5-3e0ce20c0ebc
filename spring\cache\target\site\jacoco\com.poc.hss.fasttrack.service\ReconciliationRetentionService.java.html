<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationRetentionService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">ReconciliationRetentionService.java</span></div><h1>ReconciliationRetentionService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.constant.ErrorCodeConstant;
import com.poc.hss.fasttrack.dto.ReconciliationRequestDTO;
import com.poc.hss.fasttrack.jpa.model.BatchEntity;
import com.poc.hss.fasttrack.jpa.repository.BatchRepository;
import com.poc.hss.fasttrack.model.BatchStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

<span class="fc" id="L17">@Slf4j</span>
@Service
<span class="fc" id="L19">public class ReconciliationRetentionService {</span>
    @Autowired
    private ReconciliationService reconciliationService;

    @Autowired
    private BatchRepository batchRepository;

    @Value(&quot;${reconciliation.scan.idleSeconds}&quot;)
    private long idleSeconds;

    @Value(&quot;${reconciliation.scan.expiredSeconds}&quot;)
    private long expiredSeconds;

    @Scheduled(cron = &quot;${reconciliation.scan.cron}&quot;)
    public void listIncompleteBatch() {
<span class="fc" id="L34">        LocalDateTime toDate = LocalDateTime.now().minusSeconds(idleSeconds);</span>
<span class="fc" id="L35">        LocalDateTime fromDate = LocalDateTime.now().minusSeconds(expiredSeconds);</span>
<span class="fc" id="L36">        List&lt;BatchEntity&gt; incompleteBatchEntityList = batchRepository.findAllByCompletedFalseAndUpdatedTimeBetween(fromDate, toDate);</span>

<span class="pc bpc" id="L38" title="1 of 2 branches missed.">        for (BatchEntity entity : incompleteBatchEntityList) {</span>
<span class="nc" id="L39">            reconciliationService.reconcile(ReconciliationRequestDTO.builder()</span>
<span class="nc" id="L40">                    .batch(entity.getBatch())</span>
<span class="nc" id="L41">                    .group(entity.getGroup())</span>
<span class="nc" id="L42">                    .component(entity.getComponent())</span>
<span class="nc" id="L43">                    .build());</span>
<span class="nc" id="L44">        }</span>
<span class="fc" id="L45">        incompleteBatchEntityList = batchRepository.findAllByCompletedFalseAndUpdatedTimeBetween(fromDate, toDate);</span>

<span class="pc bpc" id="L47" title="1 of 2 branches missed.">        for (BatchEntity entity : incompleteBatchEntityList) {</span>
<span class="nc" id="L48">            log.error(&quot;{} &gt;&gt; [Reconciliation] Message count doesn't match, Batch ID: {}, Group: {}, Component: {}, Source Metric Out: {}, Metric In: {}, Consumer Group: {}, Topic: {}&quot;,</span>
<span class="nc" id="L49">                    ErrorCodeConstant.PIPELINE_FATAL_ERROR, entity.getBatch(), entity.getGroup(), entity.getComponent(), entity.getSourceMetricOut(), entity.getMetricIn(), entity.getConsumerGroup(), entity.getTopic());</span>
<span class="nc" id="L50">            reconciliationService.updateBatchStatus(entity.getBatch(), BatchStatus.COUNT_MISMATCH);</span>
<span class="nc" id="L51">        }</span>
<span class="fc" id="L52">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>