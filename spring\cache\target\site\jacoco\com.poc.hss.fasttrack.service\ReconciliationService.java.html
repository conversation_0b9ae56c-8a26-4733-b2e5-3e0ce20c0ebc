<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">ReconciliationService.java</span></div><h1>ReconciliationService.java</h1><pre class="source lang-java linenums">
package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.constant.Constants;
import com.poc.hss.fasttrack.dto.*;
import com.poc.hss.fasttrack.jpa.model.BatchEntity;
import com.poc.hss.fasttrack.jpa.repository.BatchRepository;
import com.poc.hss.fasttrack.kafka.factory.KafkaTemplateFactory;
import com.poc.hss.fasttrack.kafka.model.Unity2KafkaMessage;
import com.poc.hss.fasttrack.model.BatchStatus;
import com.poc.hss.fasttrack.model.CacheOperationV3;
import com.poc.hss.fasttrack.model.CacheTypeV3;
import com.poc.hss.fasttrack.model.Unity2Component;
import com.poc.hss.fasttrack.util.K8sUtil;
import jakarta.transaction.Transactional;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Predicate;
import java.util.stream.Collectors;

<span class="fc" id="L34">@Slf4j</span>
@Service
public class ReconciliationService {

    @Autowired
    private CacheService cacheService;

    @Autowired
    private BatchRepository batchRepository;

    @Value(&quot;${NAMESPACE:}&quot;)
    private String namespace;

    @Autowired
    private K8sUtil k8sUtil;

<span class="fc" id="L50">    private final AtomicReference&lt;LocalDateTime&gt; lastReloadTime = new AtomicReference&lt;&gt;(LocalDateTime.now());</span>

<span class="fc" id="L52">    private final Map&lt;String, SupportAttributesDTO&gt; supportAttributesDTOs = new ConcurrentHashMap&lt;&gt;();</span>

    private final KafkaTemplate&lt;String, Unity2KafkaMessage&gt; kafkaTemplate;

<span class="fc" id="L56">    public ReconciliationService(KafkaTemplateFactory&lt;String, Unity2KafkaMessage&gt; kafkaTemplateFactory) {</span>
<span class="fc" id="L57">        this.kafkaTemplate = kafkaTemplateFactory.createKafkaTemplate();</span>
<span class="fc" id="L58">    }</span>

    @Transactional
    public ReconciliationResponseDTO reconcile(ReconciliationRequestDTO request) {
<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L63">            log.debug(&quot;[Reconciliation] reconcile request: {}&quot;, request);</span>
<span class="nc" id="L64">        boolean completed = false;</span>
<span class="nc" id="L65">        CacheDTO inCache = getCache(request, Constants.MESSAGE_IN);</span>
<span class="nc bnc" id="L66" title="All 2 branches missed.">        if (sourceCacheCompleted(request, getLongValue(inCache))) {</span>
<span class="nc" id="L67">            markCompleted(request, true);</span>
<span class="nc bnc" id="L68" title="All 2 branches missed.">            if (request.getComponent().equals(Unity2Component.SOURCE_ADAPTOR.toString()) ||</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">                    request.getComponent().equals(Unity2Component.TRANSFORM.toString()) ||</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">                    request.getComponent().equals(Unity2Component.BLEND.toString()) ||</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">                    request.getComponent().equals(Unity2Component.ACCESS.toString()) ||</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">                    request.getComponent().equals(Unity2Component.FUSION.toString())</span>
            ) {

<span class="nc" id="L75">                List&lt;PipelineReconcileResult&gt; results = checkSubsequentComponentCompleted(request);</span>
<span class="nc" id="L76">                log.info(&quot;[Reconciliation] Check Subsequent Component Completed, results: {}&quot;, results);</span>
<span class="nc" id="L77">                boolean allCompleted = true;</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">                for (PipelineReconcileResult result : results) {</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">                    if (result.getAllCompleted()) {</span>
<span class="nc" id="L80">                        triggerConsumerAdaptor(result.getRequest(), result.getOutCount());</span>
                    } else {
<span class="nc" id="L82">                        allCompleted = false;</span>
                    }
<span class="nc" id="L84">                }</span>
<span class="nc" id="L85">                completed = allCompleted;</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">                updateBatchStatus(request.getBatch(), allCompleted ? BatchStatus.COMPLETED : BatchStatus.IN_PROGRESS);</span>
<span class="nc" id="L87">            }</span>
        } else {
<span class="nc" id="L89">            markCompleted(request, false);</span>
<span class="nc" id="L90">            updateBatchStatus(request.getBatch(), BatchStatus.IN_PROGRESS);</span>
        }

<span class="nc" id="L93">        return ReconciliationResponseDTO.builder()</span>
<span class="nc" id="L94">                .completed(completed)</span>
<span class="nc" id="L95">                .build();</span>
    }

    @Transactional
    public void updateBatchStatus(String batch, BatchStatus statusToSet) {
<span class="nc bnc" id="L100" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L101">            log.debug(&quot;[Reconciliation] UpdateBatchStatus batch: {}, status: {}&quot;, batch, statusToSet);</span>
<span class="nc" id="L102">        batchRepository.updateBatchStatus(batch, statusToSet);</span>
<span class="nc" id="L103">    }</span>

<span class="nc bnc" id="L105" title="All 30 branches missed.">    @Data</span>
<span class="nc" id="L106">    @Builder</span>
    static class PipelineReconcileResult {
<span class="nc" id="L108">        private ReconciliationRequestDTO request;</span>
<span class="nc" id="L109">        private Boolean allCompleted;</span>
<span class="nc" id="L110">        private Long outCount;</span>
    }

    private List&lt;PipelineReconcileResult&gt; checkSubsequentComponentCompleted(ReconciliationRequestDTO request) {
<span class="nc bnc" id="L114" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L115">            log.debug(&quot;Check Subsequent Component Completed, request: {}&quot;, request);</span>
<span class="nc" id="L116">        SupportAttributesDTO curComponent = SupportAttributesDTO.builder()</span>
<span class="nc" id="L117">                .cacheGroup(request.getGroup())</span>
<span class="nc" id="L118">                .componentType(request.getComponent())</span>
<span class="nc" id="L119">                .build();</span>
<span class="nc" id="L120">        curComponent = loadSupportAttributesDTO(curComponent);</span>
<span class="nc" id="L121">        List&lt;PipelineReconcileResult&gt; resultList = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">        if (curComponent.getComponentType().equals(Unity2Component.ACCESS.toString()) ||</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">                curComponent.getComponentType().equals(Unity2Component.FUSION.toString())) {</span>
<span class="nc" id="L124">            resultList.add(PipelineReconcileResult.builder()</span>
<span class="nc" id="L125">                    .request(request)</span>
<span class="nc" id="L126">                    .allCompleted(true)</span>
<span class="nc" id="L127">                    .outCount(getLongValue(getCache(curComponent, request.getBatch(), Constants.MESSAGE_OUT)))</span>
<span class="nc" id="L128">                    .build());</span>
        }
<span class="nc" id="L130">        final List&lt;String&gt; targetTopics = curComponent.getTargetTopics();</span>
<span class="nc bnc" id="L131" title="All 4 branches missed.">        if (targetTopics != null &amp;&amp; targetTopics.size() &gt;= 1) {</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">            for (String targetTopic : targetTopics) {</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">                if (log.isDebugEnabled())</span>
<span class="nc" id="L134">                    log.debug(&quot;predicate: sourceTopic={}&quot;, targetTopic);</span>
<span class="nc bnc" id="L135" title="All 4 branches missed.">                List&lt;SupportAttributesDTO&gt; nextComponents = getSupportAttributes(dto -&gt; dto.getSourceTopics().contains(targetTopic) &amp;&amp; !dto.getComponentType().equals(Unity2Component.CONSUMER_ADAPTOR.toString()));</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">                for (SupportAttributesDTO nextComponent : nextComponents) {</span>
<span class="nc bnc" id="L137" title="All 2 branches missed.">                    if (log.isDebugEnabled())</span>
<span class="nc" id="L138">                        log.debug(&quot;Check Subsequent Component Completed, nextComponent: {}&quot;, nextComponent);</span>
<span class="nc bnc" id="L139" title="All 2 branches missed.">                    if (nextComponent == null) {</span>
<span class="nc" id="L140">                        log.warn(&quot;[Reconciliation] nextComponent = null, request: {}, targetTopic: {}&quot;, request, targetTopic);</span>
                    } else {
<span class="nc" id="L142">                        long inCount = getLongValue(getCache(nextComponent, request.getBatch(), Constants.MESSAGE_IN));</span>
<span class="nc bnc" id="L143" title="All 6 branches missed.">                        if ((nextComponent.getComponentType().equals(Unity2Component.BLEND.toString()) || nextComponent.getComponentType().equals(Unity2Component.SOURCE_ADAPTOR.toString())) &amp;&amp; inCount == 0L) {</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">                            if (log.isDebugEnabled())</span>
<span class="nc" id="L145">                                log.debug(&quot;Not processing blend with zero inCount, nextComponent: {}&quot;, nextComponent);</span>
                        } else {
<span class="nc" id="L147">                            final ReconciliationRequestDTO nextComponentRequest = ReconciliationRequestDTO.builder()</span>
<span class="nc" id="L148">                                    .batch(request.getBatch())</span>
<span class="nc" id="L149">                                    .group(nextComponent.getCacheGroup())</span>
<span class="nc" id="L150">                                    .component(nextComponent.getComponentType())</span>
<span class="nc" id="L151">                                    .build();</span>
<span class="nc" id="L152">                            boolean result = sourceCacheCompleted(nextComponentRequest, inCount);</span>
<span class="nc" id="L153">                            markCompleted(nextComponent, request.getBatch(), result);</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">                            if (!result) {</span>
<span class="nc" id="L155">                                resultList.add(PipelineReconcileResult.builder()</span>
<span class="nc" id="L156">                                        .request(nextComponentRequest)</span>
<span class="nc" id="L157">                                        .allCompleted(false)</span>
<span class="nc" id="L158">                                        .build());</span>
                            } else {
<span class="nc" id="L160">                                final List&lt;PipelineReconcileResult&gt; followingPipelineReconcileResult = checkSubsequentComponentCompleted(ReconciliationRequestDTO.builder()</span>
<span class="nc" id="L161">                                        .component(nextComponent.getComponentType())</span>
<span class="nc" id="L162">                                        .group(nextComponent.getCacheGroup())</span>
<span class="nc" id="L163">                                        .batch(request.getBatch())</span>
<span class="nc" id="L164">                                        .build());</span>
<span class="nc" id="L165">                                resultList.addAll(followingPipelineReconcileResult);</span>
                            }
                        }
                    }
<span class="nc" id="L169">                }</span>
<span class="nc" id="L170">            }</span>
        } else {
<span class="nc" id="L172">            log.warn(&quot;[Reconciliation] Target Topics = 0, request: {}&quot;, request);</span>
        }
<span class="nc" id="L174">        return resultList;</span>
    }

    private void markCompleted(ReconciliationRequestDTO request, boolean value) {
<span class="nc" id="L178">        final CacheDTO cache = getCache(request, Constants.BATCH_COMPLETION);</span>
<span class="nc" id="L179">        cacheService.putCache(CacheCompositeKeyDTO.builder()</span>
<span class="nc" id="L180">                        .group(cache.getGroup())</span>
<span class="nc" id="L181">                        .component(cache.getComponent())</span>
<span class="nc" id="L182">                        .batch(request.getBatch())</span>
<span class="nc" id="L183">                        .key(Constants.BATCH_COMPLETION)</span>
<span class="nc" id="L184">                        .build(),</span>
<span class="nc" id="L185">                CacheUpdateDTO.builder()</span>
<span class="nc" id="L186">                        .operation(CacheOperationV3.SET)</span>
<span class="nc" id="L187">                        .type(CacheTypeV3.NORMAL)</span>
<span class="nc" id="L188">                        .value(value)</span>
<span class="nc" id="L189">                        .build());</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L191">            log.debug(&quot;[Reconciliation] Mark Cache {} completion as {}&quot;, cache, value);</span>
<span class="nc" id="L192">    }</span>

    private void upsertBatchEntity(ReconciliationRequestDTO request, SupportAttributesDTO supportAttributesDTO, long outCount, long inCount, boolean result) {
<span class="nc" id="L195">        final BatchEntity batchEntity = batchRepository.findByBatchAndDeployment(request.getBatch(), supportAttributesDTO.getDeployment())</span>
<span class="nc" id="L196">                .orElse(BatchEntity.builder()</span>
<span class="nc" id="L197">                        .batch(request.getBatch())</span>
<span class="nc" id="L198">                        .group(supportAttributesDTO.getCacheGroup())</span>
<span class="nc" id="L199">                        .component(supportAttributesDTO.getComponentType())</span>
<span class="nc" id="L200">                        .deployment(supportAttributesDTO.getDeployment())</span>
<span class="nc" id="L201">                        .consumerGroup(supportAttributesDTO.getOtherAttributes().get(&quot;consumerGroup&quot;))</span>
<span class="nc" id="L202">                        .topic(String.join(&quot;,&quot;, supportAttributesDTO.getSourceTopics()))</span>
<span class="nc" id="L203">                        .build());</span>
<span class="nc" id="L204">        batchEntity.setSourceMetricOut(outCount);</span>
<span class="nc" id="L205">        batchEntity.setMetricIn(inCount);</span>
<span class="nc" id="L206">        batchEntity.setCompleted(result);</span>
<span class="nc" id="L207">        batchEntity.setStatus(BatchStatus.IN_PROGRESS);</span>
<span class="nc" id="L208">        batchRepository.save(batchEntity);</span>
<span class="nc" id="L209">    }</span>

    private void markCompleted(SupportAttributesDTO supportAttributesDTO, String batch, boolean value) {
<span class="nc" id="L212">        final CacheDTO cache = getCache(supportAttributesDTO, batch, Constants.BATCH_COMPLETION);</span>
<span class="nc" id="L213">        cacheService.putCache(CacheCompositeKeyDTO.builder()</span>
<span class="nc" id="L214">                        .group(cache.getGroup())</span>
<span class="nc" id="L215">                        .component(cache.getComponent())</span>
<span class="nc" id="L216">                        .batch(batch)</span>
<span class="nc" id="L217">                        .key(Constants.BATCH_COMPLETION)</span>
<span class="nc" id="L218">                        .build(),</span>
<span class="nc" id="L219">                CacheUpdateDTO.builder()</span>
<span class="nc" id="L220">                        .operation(CacheOperationV3.SET)</span>
<span class="nc" id="L221">                        .type(CacheTypeV3.NORMAL)</span>
<span class="nc" id="L222">                        .value(value)</span>
<span class="nc" id="L223">                        .build());</span>
<span class="nc" id="L224">        log.info(&quot;Mark Cache {} completion as {}&quot;, cache, value);</span>
<span class="nc" id="L225">    }</span>

    private boolean sourceCacheCompleted(ReconciliationRequestDTO request, long inCount) {
<span class="nc" id="L228">        SupportAttributesDTO curComponent = SupportAttributesDTO.builder()</span>
<span class="nc" id="L229">                .cacheGroup(request.getGroup())</span>
<span class="nc" id="L230">                .componentType(request.getComponent())</span>
<span class="nc" id="L231">                .build();</span>
<span class="nc" id="L232">        final SupportAttributesDTO supportAttributesDTO = loadSupportAttributesDTO(curComponent);</span>
<span class="nc" id="L233">        final List&lt;String&gt; sourceTopics = supportAttributesDTO.getSourceTopics();</span>
<span class="nc" id="L234">        final long outCount = sourceTopics.stream()</span>
<span class="nc" id="L235">                .flatMap(topic -&gt; {</span>
<span class="nc bnc" id="L236" title="All 2 branches missed.">                    if (log.isDebugEnabled())</span>
<span class="nc" id="L237">                        log.debug(&quot;predicate: targetTopic={}&quot;, topic);</span>
<span class="nc" id="L238">                    return getSupportAttributes(e -&gt; e.getTargetTopics().contains(topic)).stream().map(dto -&gt; getCache(dto, request.getBatch(),</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">                            request.getComponent().equals(Unity2Component.SOURCE_ADAPTOR.toString()) ? Constants.MESSAGE_OUT + &quot;.&quot; + topic : Constants.MESSAGE_OUT));</span>
                })
<span class="nc" id="L241">                .mapToLong(this::getLongValue)</span>
<span class="nc" id="L242">                .sum();</span>
        final boolean allSourceCompleted =
<span class="nc bnc" id="L244" title="All 2 branches missed.">                supportAttributesDTO.getComponentType().equals(Unity2Component.BLEND.toString()) ?</span>
<span class="nc" id="L245">                        sourceTopics.stream()</span>
<span class="nc" id="L246">                                .flatMap(topic -&gt; {</span>
<span class="nc bnc" id="L247" title="All 2 branches missed.">                                    if (log.isDebugEnabled())</span>
<span class="nc" id="L248">                                        log.debug(&quot;predicate: targetTopic={}&quot;, topic);</span>
<span class="nc" id="L249">                                    return getSupportAttributes(dto -&gt; dto.getTargetTopics().contains(topic)).stream();</span>
                                })
<span class="nc bnc" id="L251" title="All 4 branches missed.">                                .allMatch(dto -&gt; getBooleanValue(getCache(dto, request.getBatch(), Constants.BATCH_COMPLETION)) || getLongValue(getCache(dto, request.getBatch(), Constants.MESSAGE_OUT)) == 0L) :</span>
<span class="nc" id="L252">                        sourceTopics.stream()</span>
<span class="nc" id="L253">                                .flatMap(topic -&gt; {</span>
<span class="nc bnc" id="L254" title="All 2 branches missed.">                                    if (log.isDebugEnabled())</span>
<span class="nc" id="L255">                                        log.debug(&quot;predicate: targetTopic={}&quot;, topic);</span>
<span class="nc" id="L256">                                    return getSupportAttributes(dto -&gt; dto.getTargetTopics().contains(topic)).stream();</span>
                                })
<span class="nc" id="L258">                                .map(dto -&gt; getCache(dto, request.getBatch(), Constants.BATCH_COMPLETION))</span>
<span class="nc" id="L259">                                .allMatch(this::getBooleanValue);</span>
<span class="nc bnc" id="L260" title="All 2 branches missed.">        if (log.isDebugEnabled())</span>
<span class="nc" id="L261">            log.debug(&quot;[Reconciliation] batch: {}. group: {}, component: {}, out: {}, in: {}, sourceCompleted: {}&quot;, request.getBatch(), request.getGroup(), request.getComponent(), outCount, inCount, allSourceCompleted);</span>
<span class="nc bnc" id="L262" title="All 4 branches missed.">        final boolean result = outCount == inCount &amp;&amp; allSourceCompleted;</span>
<span class="nc" id="L263">        upsertBatchEntity(request, supportAttributesDTO, outCount, inCount, result);</span>
<span class="nc" id="L264">        return result;</span>
    }

    private void triggerConsumerAdaptor(ReconciliationRequestDTO request, Long batchSize) {
<span class="nc" id="L268">        SupportAttributesDTO curComponent = SupportAttributesDTO.builder()</span>
<span class="nc" id="L269">                .cacheGroup(request.getGroup())</span>
<span class="nc" id="L270">                .componentType(request.getComponent())</span>
<span class="nc" id="L271">                .build();</span>
<span class="nc" id="L272">        curComponent = loadSupportAttributesDTO(curComponent);</span>

<span class="nc bnc" id="L274" title="All 2 branches missed.">        if (curComponent != null &amp;&amp;</span>
<span class="nc bnc" id="L275" title="All 2 branches missed.">                (curComponent.getComponentType().equals(Unity2Component.ACCESS.toString())</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">                        || curComponent.getComponentType().equals(Unity2Component.FUSION.toString()))) {</span>
<span class="nc" id="L277">            final String batchTargetTopic = getBatchTargetTopic(curComponent);</span>
<span class="nc" id="L278">            final String tableName = curComponent.getOtherAttributes().get(&quot;tableName&quot;);</span>
<span class="nc bnc" id="L279" title="All 4 branches missed.">            if (batchTargetTopic != null &amp;&amp; tableName != null) {</span>
<span class="nc" id="L280">                kafkaTemplate.send(batchTargetTopic, Unity2KafkaMessage.builder()</span>
<span class="nc" id="L281">                        .batchId(request.getBatch())</span>
<span class="nc" id="L282">                        .batchSize(batchSize)</span>
<span class="nc" id="L283">                        .source(tableName)</span>
<span class="nc" id="L284">                        .build());</span>
<span class="nc" id="L285">                log.info(&quot;[Reconciliation] Send message to batch topic, topic: {}, batch: {}, size: {}, tableName: {}&quot;, batchTargetTopic, request.getBatch(), batchSize, tableName);</span>
            } else {
<span class="nc" id="L287">                log.info(&quot;[Reconciliation] no batch topic, topic: {}, batch: {}, size: {}, tableName: {}&quot;, batchTargetTopic, request.getBatch(), batchSize, tableName);</span>
            }
        }
<span class="nc" id="L290">    }</span>

    private String getBatchTargetTopic(SupportAttributesDTO attributesDTO) {
<span class="nc" id="L293">        final List&lt;String&gt; consumerAdaptorTargetTopics = attributesDTO.getTargetTopics().stream().filter(</span>
<span class="nc bnc" id="L294" title="All 4 branches missed.">                topic -&gt; !topic.contains(&quot;access-event&quot;) &amp;&amp; !topic.contains(&quot;fusion-event&quot;)</span>
<span class="nc" id="L295">        ).collect(Collectors.toList());</span>
<span class="nc bnc" id="L296" title="All 2 branches missed.">        if (consumerAdaptorTargetTopics.size() == 1) {</span>
<span class="nc" id="L297">            return consumerAdaptorTargetTopics.get(0) + attributesDTO.getOtherAttributes().get(&quot;targetBatchTopicSuffix&quot;);</span>
        } else {
<span class="nc" id="L299">            log.warn(&quot;[Reconciliation] Consumer Adaptor Target Topics &lt;&gt; 1, {}&quot;, consumerAdaptorTargetTopics);</span>
        }
<span class="nc" id="L301">        return null;</span>
    }

    private List&lt;SupportAttributesDTO&gt; getSupportAttributes(Predicate&lt;SupportAttributesDTO&gt; predicate) {
<span class="nc" id="L305">        List&lt;SupportAttributesDTO&gt; resultList = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L306" title="All 2 branches missed.">        for (SupportAttributesDTO dto : supportAttributesDTOs.values()) {</span>
<span class="nc bnc" id="L307" title="All 2 branches missed.">            if (predicate.test(dto)) {</span>
<span class="nc" id="L308">                resultList.add(dto);</span>
            }
<span class="nc" id="L310">        }</span>
<span class="nc bnc" id="L311" title="All 2 branches missed.">        if (resultList.size() &gt; 0)</span>
<span class="nc" id="L312">            return resultList;</span>
<span class="nc bnc" id="L313" title="All 2 branches missed.">        else if (lastReloadTime.get().isBefore(LocalDateTime.now().minusMinutes(1))) {</span>
<span class="nc" id="L314">            reloadSupportAttributes();</span>
<span class="nc" id="L315">            return getSupportAttributes(predicate);</span>
        } else {
<span class="nc" id="L317">            log.warn(&quot;Cannot load attribute with request using predicate&quot;);</span>
<span class="nc" id="L318">            return resultList;</span>
        }
    }

    @Scheduled(cron = &quot;${supportAttributes.scan.cron}&quot;)
    public void reloadSupportAttributes() {
<span class="nc" id="L324">        lastReloadTime.set(LocalDateTime.now());</span>
<span class="nc" id="L325">        Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes = k8sUtil.getConfigMap(&quot;.*\\-support-attributes&quot;, namespace);</span>
<span class="nc bnc" id="L326" title="All 2 branches missed.">        final List&lt;String&gt; removedKeys = supportAttributesDTOs.keySet().stream().filter(key -&gt; !supportAttributes.containsKey(key)).collect(Collectors.toList());</span>
<span class="nc bnc" id="L327" title="All 2 branches missed.">        for (String key : removedKeys)</span>
<span class="nc" id="L328">            supportAttributesDTOs.remove(key);</span>
<span class="nc" id="L329">        AtomicInteger updated = new AtomicInteger(0);</span>
<span class="nc" id="L330">        supportAttributes</span>
<span class="nc" id="L331">                .forEach((key, valueMap) -&gt; {</span>
<span class="nc bnc" id="L332" title="All 2 branches missed.">                    if (valueMap.getOrDefault(&quot;componentType&quot;, &quot;&quot;).equals(&quot;&quot;)) {</span>
<span class="nc" id="L333">                        return;</span>
                    }
<span class="nc" id="L335">                    SupportAttributesDTO newAttributes = constructSupportAttributes(valueMap);</span>
<span class="nc bnc" id="L336" title="All 4 branches missed.">                    if (!supportAttributesDTOs.containsKey(key) || !newAttributes.equals(supportAttributesDTOs.get(key))) {</span>
<span class="nc" id="L337">                        log.info(&quot;Reload Support Attributes - update, old: {}, new: {}&quot;, supportAttributesDTOs.get(key), newAttributes);</span>
<span class="nc" id="L338">                        supportAttributesDTOs.put(key, newAttributes);</span>
<span class="nc" id="L339">                        updated.incrementAndGet();</span>
                    }
<span class="nc" id="L341">                });</span>
<span class="nc" id="L342">        log.info(&quot;Reload Support Attributes, size: {}, removed: {}, updated: {}&quot;, supportAttributesDTOs.size(), removedKeys.size(), updated.get());</span>
<span class="nc" id="L343">    }</span>

    private SupportAttributesDTO constructSupportAttributes(Map&lt;String, String&gt; valueMap) {
<span class="nc" id="L346">        List&lt;String&gt; sourceTopics = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L347">        List&lt;String&gt; targetTopics = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">        if (valueMap.containsKey(&quot;sourceTopic&quot;))</span>
<span class="nc" id="L349">            sourceTopics.add(valueMap.get(&quot;sourceTopic&quot;));</span>
<span class="nc bnc" id="L350" title="All 2 branches missed.">        if (valueMap.containsKey(&quot;sourceTopics&quot;))</span>
<span class="nc" id="L351">            sourceTopics.addAll(Arrays.asList(valueMap.get(&quot;sourceTopics&quot;).split(&quot;,&quot;)));</span>
<span class="nc bnc" id="L352" title="All 2 branches missed.">        if (valueMap.containsKey(&quot;targetTopic&quot;))</span>
<span class="nc" id="L353">            targetTopics.add(valueMap.get(&quot;targetTopic&quot;));</span>
<span class="nc bnc" id="L354" title="All 2 branches missed.">        if (valueMap.containsKey(&quot;targetTopics&quot;))</span>
<span class="nc" id="L355">            targetTopics.addAll(Arrays.asList(valueMap.get(&quot;targetTopics&quot;).split(&quot;,&quot;)));</span>
<span class="nc" id="L356">        sourceTopics = sourceTopics.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());</span>
<span class="nc" id="L357">        targetTopics = targetTopics.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());</span>
<span class="nc" id="L358">        return SupportAttributesDTO.builder()</span>
<span class="nc" id="L359">                .deployment(valueMap.get(&quot;deployment&quot;))</span>
<span class="nc" id="L360">                .cacheGroup(valueMap.get(&quot;cacheGroup&quot;))</span>
<span class="nc" id="L361">                .componentType(valueMap.get(&quot;componentType&quot;))</span>
<span class="nc" id="L362">                .sourceTopics(sourceTopics)</span>
<span class="nc" id="L363">                .targetTopics(targetTopics)</span>
<span class="nc" id="L364">                .otherAttributes(valueMap)</span>
<span class="nc" id="L365">                .build();</span>
    }

    private SupportAttributesDTO loadSupportAttributesDTO(SupportAttributesDTO attributesDTO) {
<span class="nc" id="L369">        return loadSupportAttributesDTO(attributesDTO, 1);</span>
    }

    private SupportAttributesDTO loadSupportAttributesDTO(SupportAttributesDTO attributesDTO, int trial) {
<span class="nc bnc" id="L373" title="All 2 branches missed.">        for (SupportAttributesDTO dto : supportAttributesDTOs.values()) {</span>
<span class="nc bnc" id="L374" title="All 4 branches missed.">            if (attributesDTO.getCacheGroup().equals(dto.getCacheGroup()) &amp;&amp; matchComponent(attributesDTO.getComponentType(), dto.getComponentType())) {</span>
<span class="nc" id="L375">                return dto;</span>
            }
<span class="nc" id="L377">        }</span>

<span class="nc" id="L379">        int MAX_TRIAL = 3;</span>
<span class="nc bnc" id="L380" title="All 2 branches missed.">        if (trial &lt; MAX_TRIAL) {</span>
            try {
<span class="nc" id="L382">                Thread.sleep(1000L);</span>
<span class="nc" id="L383">            } catch (InterruptedException e) {</span>
<span class="nc" id="L384">                log.error(&quot;&quot;, e);</span>
<span class="nc" id="L385">            }</span>
<span class="nc" id="L386">            reloadSupportAttributes();</span>
<span class="nc" id="L387">            return loadSupportAttributesDTO(attributesDTO, trial + 1);</span>
        } else {
<span class="nc" id="L389">            log.error(&quot;Cannot load attribute with request: {}&quot;, attributesDTO);</span>
<span class="nc" id="L390">            return null;</span>
        }
    }

    private boolean matchComponent(String type1, String type2) {
<span class="nc" id="L395">        return getGenericComponentType(type1).equals(getGenericComponentType(type2));</span>
    }

    private String getGenericComponentType(String type) {
<span class="nc bnc" id="L399" title="All 2 branches missed.">        if (type.equals(Unity2Component.BLEND.toString()))</span>
<span class="nc" id="L400">            return Unity2Component.TRANSFORM.toString();</span>
<span class="nc bnc" id="L401" title="All 2 branches missed.">        else if (type.equals(Unity2Component.FUSION.toString()))</span>
<span class="nc" id="L402">            return Unity2Component.ACCESS.toString();</span>
<span class="nc" id="L403">        return type;</span>
    }

    private long getLongValue(CacheDTO cache) {
<span class="nc" id="L407">        return Optional.ofNullable(cache)</span>
<span class="nc" id="L408">                .map(CacheDTO::getValue)</span>
<span class="nc" id="L409">                .map(e -&gt; Long.parseLong(e.toString()))</span>
<span class="nc" id="L410">                .orElse(0L);</span>
    }

    private boolean getBooleanValue(CacheDTO cache) {
<span class="nc" id="L414">        return Optional.ofNullable(cache)</span>
<span class="nc" id="L415">                .map(CacheDTO::getValue)</span>
<span class="nc" id="L416">                .map(e -&gt; Boolean.parseBoolean(e.toString()))</span>
<span class="nc" id="L417">                .orElse(false);</span>
    }

    private CacheDTO getCache(ReconciliationRequestDTO requestDTO, String key) {
<span class="nc" id="L421">        return getCache(requestDTO.getGroup(), requestDTO.getComponent(), requestDTO.getBatch(), key);</span>
    }

    private CacheDTO getCache(SupportAttributesDTO supportAttributesDTO, String batch, String key) {
<span class="nc" id="L425">        return getCache(supportAttributesDTO.getCacheGroup(), getGenericComponentType(supportAttributesDTO.getComponentType()), batch, key);</span>
    }

    private CacheDTO getCache(String group, String component, String batch, String key) {
<span class="nc" id="L429">        return cacheService.getCache(CacheCompositeKeyDTO.builder()</span>
<span class="nc" id="L430">                .key(key)</span>
<span class="nc" id="L431">                .group(group)</span>
<span class="nc" id="L432">                .component(component)</span>
<span class="nc" id="L433">                .batch(batch)</span>
<span class="nc" id="L434">                .build());</span>
    }

    @Transactional
    public Long reset(String batch) {
<span class="nc" id="L439">        return batchRepository.deleteByBatch(batch);</span>
    }

    @Transactional
    public void runHousekeeping(LocalDateTime date) {
<span class="fc" id="L444">        Long deletedCount = batchRepository.deleteByUpdatedTimeLessThan(date);</span>
<span class="fc" id="L445">        log.info(&quot;{} records updated before {} deleted&quot;, deletedCount, date);</span>
<span class="fc" id="L446">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>