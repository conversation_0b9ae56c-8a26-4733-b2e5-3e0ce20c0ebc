<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.service</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">cache</a> &gt; <span class="el_package">com.poc.hss.fasttrack.service</span></div><h1>com.poc.hss.fasttrack.service</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,826 of 2,196</td><td class="ctr2">16%</td><td class="bar">176 of 188</td><td class="ctr2">6%</td><td class="ctr1">159</td><td class="ctr2">187</td><td class="ctr1">349</td><td class="ctr2">451</td><td class="ctr1">67</td><td class="ctr2">93</td><td class="ctr1">2</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a5"><a href="ReconciliationService.java.html" class="el_source">ReconciliationService.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="117" height="10" title="1,488" alt="1,488"/><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="33" alt="33"/></td><td class="ctr2" id="c5">2%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="162" alt="162"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">136</td><td class="ctr2" id="g0">139</td><td class="ctr1" id="h0">279</td><td class="ctr2" id="i0">288</td><td class="ctr1" id="j0">55</td><td class="ctr2" id="k0">58</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a3"><a href="KafkaOffsetService.java.html" class="el_source">KafkaOffsetService.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="162" alt="162"/></td><td class="ctr2" id="c4">4%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g2">14</td><td class="ctr1" id="h1">34</td><td class="ctr2" id="i2">37</td><td class="ctr1" id="j1">10</td><td class="ctr2" id="k2">12</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a4"><a href="ReconciliationRetentionService.java.html" class="el_source">ReconciliationRetentionService.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="75" alt="75"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="42" alt="42"/></td><td class="ctr2" id="c2">35%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i4">19</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">3</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="BatchService.java.html" class="el_source">BatchService.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="61" alt="61"/></td><td class="ctr2" id="c3">4%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i3">21</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a2"><a href="CacheService.java.html" class="el_source">CacheService.java</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="40" alt="40"/><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="263" alt="263"/></td><td class="ctr2" id="c1">86%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">55%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g1">23</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i1">80</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k1">14</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a1"><a href="CacheRetentionService.java.html" class="el_source">CacheRetentionService.java</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="22" alt="22"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">3</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>