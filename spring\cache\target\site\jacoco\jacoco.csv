GRO<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON><PERSON>_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
cache,com.poc.hss.fasttrack.facade,CacheFacadeV3,0,110,2,2,0,29,2,8,0,8
cache,com.poc.hss.fasttrack.facade,CacheFacadeV2,67,53,0,0,18,11,3,3,3,3
cache,com.poc.hss.fasttrack.facade,CacheFacadeV1,30,146,4,8,6,35,4,13,1,10
cache,com.poc.hss.fasttrack.facade,KafkaCacheFacade,142,7,12,0,34,2,9,2,3,2
cache,com.poc.hss.fasttrack.listener,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,5,3,0,0,2,1,1,1,1,1
cache,com.poc.hss.fasttrack.service,ReconciliationService.PipelineReconcileResult.PipelineReconcileResultBuilder,37,0,0,0,1,0,6,0,6,0
cache,com.poc.hss.fasttrack.service,ReconciliationRetentionService,75,42,2,2,10,9,2,3,0,3
cache,com.poc.hss.fasttrack.service,ReconciliationService.PipelineReconcileResult,169,0,30,0,5,0,27,0,12,0
cache,com.poc.hss.fasttrack.service,CacheService,40,263,8,10,6,74,7,16,0,14
cache,com.poc.hss.fasttrack.service,KafkaOffsetService,162,7,4,0,34,3,12,2,10,2
cache,com.poc.hss.fasttrack.service,CacheRetentionService,0,22,0,0,0,6,0,3,0,3
cache,com.poc.hss.fasttrack.service,ReconciliationService,1282,33,132,0,274,9,103,3,37,3
cache,com.poc.hss.fasttrack.service,BatchService,61,3,0,0,20,1,2,1,2,1
cache,com.poc.hss.fasttrack.jpa.model,BatchEntity,557,3,96,0,21,1,77,1,29,1
cache,com.poc.hss.fasttrack.jpa.model,KafkaOffsetEntity.KafkaOffsetEntityBuilder,56,0,4,0,1,0,8,0,6,0
cache,com.poc.hss.fasttrack.jpa.model,BatchEntity.BatchEntityBuilder,100,0,0,0,1,0,13,0,13,0
cache,com.poc.hss.fasttrack.jpa.model,CacheEntity.CacheEntityBuilder,13,42,0,0,0,1,1,7,1,7
cache,com.poc.hss.fasttrack.jpa.model,CacheEntity,398,158,78,10,2,33,52,17,10,15
cache,com.poc.hss.fasttrack.jpa.model,KafkaOffsetEntity,185,13,20,0,13,2,26,3,16,3
cache,com.poc.hss.fasttrack.model,CacheRequestV2,116,48,14,0,19,19,11,10,4,10
cache,com.poc.hss.fasttrack.model,CacheRequestV3,138,63,16,0,21,25,12,13,4,13
cache,com.poc.hss.fasttrack.model,ReconciliationResponse,86,0,8,0,22,0,12,0,8,0
cache,com.poc.hss.fasttrack.model,CacheOperationV2,2,53,1,3,1,10,1,5,0,4
cache,com.poc.hss.fasttrack.model,CacheOperationV3,2,67,1,3,1,12,1,5,0,4
cache,com.poc.hss.fasttrack.model,BatchPageResponse,142,0,14,0,34,0,19,0,12,0
cache,com.poc.hss.fasttrack.model,ReconciliationRequest,164,0,14,0,38,0,21,0,14,0
cache,com.poc.hss.fasttrack.model,CacheTypeV3,2,53,1,3,1,10,1,5,0,4
cache,com.poc.hss.fasttrack.model,CacheTypeV2,2,53,1,3,1,10,1,5,0,4
cache,com.poc.hss.fasttrack.model,CacheResponseV3,270,153,28,0,33,61,18,31,4,31
cache,com.poc.hss.fasttrack.model,CacheResponseV2,312,0,22,0,70,0,37,0,26,0
cache,com.poc.hss.fasttrack.model,ResetRequest,86,0,8,0,22,0,12,0,8,0
cache,com.poc.hss.fasttrack.model,BatchResponse,460,0,30,0,102,0,53,0,38,0
cache,com.poc.hss.fasttrack.model,CachePageResponseV2,142,0,14,0,34,0,19,0,12,0
cache,com.poc.hss.fasttrack.model,CachePageResponseV3,109,33,14,0,21,13,12,7,5,7
cache,com.poc.hss.fasttrack.model,BatchStatus,30,32,4,0,5,7,4,2,2,2
cache,com.poc.hss.fasttrack.model,ResetResponse,86,0,8,0,22,0,12,0,8,0
cache,com.poc.hss.fasttrack.controller,ReconciliationApiController,35,3,0,0,9,1,2,1,2,1
cache,com.poc.hss.fasttrack.controller,ReconciliationApi,105,4,12,0,19,1,12,1,6,1
cache,com.poc.hss.fasttrack.controller,CacheApiController,0,42,0,0,0,10,0,7,0,7
cache,com.poc.hss.fasttrack.controller,BatchApi,59,4,6,0,11,1,8,1,5,1
cache,com.poc.hss.fasttrack.controller,CacheV2ApiController,15,42,0,0,7,10,1,7,1,7
cache,com.poc.hss.fasttrack.controller,CacheV3ApiController,5,102,0,0,1,48,1,7,1,7
cache,com.poc.hss.fasttrack.controller,CacheApi,155,4,24,0,28,1,21,1,9,1
cache,com.poc.hss.fasttrack.controller,CacheV3Api,231,4,32,0,41,1,26,1,10,1
cache,com.poc.hss.fasttrack.controller,BatchApiController,79,3,0,0,27,1,2,1,2,1
cache,com.poc.hss.fasttrack.controller,CacheV2Api,201,4,30,0,36,1,25,1,10,1
cache,com.poc.hss.fasttrack.enums,CacheType,2,53,1,3,1,10,1,5,0,4
cache,com.poc.hss.fasttrack.dto,ReconciliationResponseDTO.ReconciliationResponseDTOBuilder,18,0,0,0,1,0,4,0,4,0
cache,com.poc.hss.fasttrack.dto,CacheUpdateDTO,135,36,30,0,0,5,21,6,6,6
cache,com.poc.hss.fasttrack.dto,BatchDTO,513,0,94,0,13,0,75,0,28,0
cache,com.poc.hss.fasttrack.dto,KafkaCacheRequestDTO,346,0,62,0,11,0,52,0,21,0
cache,com.poc.hss.fasttrack.dto,CacheDTO,407,67,86,0,1,11,57,12,14,12
cache,com.poc.hss.fasttrack.dto,ReconciliationRequestDTO.ReconciliationRequestDTOBuilder,46,0,0,0,1,0,7,0,7,0
cache,com.poc.hss.fasttrack.dto,BatchQueryDTO.BatchQueryDTOBuilder,46,0,0,0,1,0,7,0,7,0
cache,com.poc.hss.fasttrack.dto,ReconciliationResponseDTO,83,0,10,0,8,0,15,0,10,0
cache,com.poc.hss.fasttrack.dto,BatchPageDTO,8,0,0,0,1,0,2,0,2,0
cache,com.poc.hss.fasttrack.dto,CacheUpdateDTO.CacheUpdateDTOBuilder,11,28,0,0,0,1,1,5,1,5
cache,com.poc.hss.fasttrack.dto,CachePageDTO,0,8,0,0,0,1,0,2,0,2
cache,com.poc.hss.fasttrack.dto,CacheQueryDTO.CacheQueryDTOBuilder,15,49,0,0,0,1,1,8,1,8
cache,com.poc.hss.fasttrack.dto,CacheQueryDTO,251,43,54,0,1,7,36,8,9,8
cache,com.poc.hss.fasttrack.dto,BatchPageDTO.BatchPageDTOBuilder,7,0,0,0,1,0,2,0,2,0
cache,com.poc.hss.fasttrack.dto,CachePageDTO.CachePageDTOBuilder,4,3,0,0,0,1,1,1,1,1
cache,com.poc.hss.fasttrack.dto,CacheCompositeKeyDTO.CacheCompositeKeyDTOBuilder,10,35,0,0,0,1,1,6,1,6
cache,com.poc.hss.fasttrack.dto,BatchDTO.BatchDTOBuilder,109,0,0,0,1,0,14,0,14,0
cache,com.poc.hss.fasttrack.dto,CachePageDTO.CachePageDTOBuilderImpl,0,7,0,0,0,1,0,2,0,2
cache,com.poc.hss.fasttrack.dto,ReconciliationRequestDTO,215,0,38,0,8,0,34,0,15,0
cache,com.poc.hss.fasttrack.dto,SupportAttributesDTO,303,0,54,0,10,0,46,0,19,0
cache,com.poc.hss.fasttrack.dto,SupportAttributesDTO.SupportAttributesDTOBuilder,66,0,0,0,1,0,9,0,9,0
cache,com.poc.hss.fasttrack.dto,CacheCompositeKeyDTO,173,50,38,0,1,11,27,8,8,8
cache,com.poc.hss.fasttrack.dto,BatchPageDTO.BatchPageDTOBuilderImpl,7,0,0,0,1,0,2,0,2,0
cache,com.poc.hss.fasttrack.dto,CacheDTO.CacheDTOBuilder,27,77,0,0,0,1,1,12,1,12
cache,com.poc.hss.fasttrack.dto,KafkaCacheRequestDTO.KafkaCacheRequestDTOBuilder,75,0,0,0,1,0,10,0,10,0
cache,com.poc.hss.fasttrack.dto,BatchQueryDTO,212,0,38,0,6,0,33,0,14,0
