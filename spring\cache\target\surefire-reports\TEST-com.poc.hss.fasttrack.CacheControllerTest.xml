<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.poc.hss.fasttrack.CacheControllerTest" time="17.172" tests="3" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\eddiewtchoi\workspace\ide\git\spring\cache\target\test-classes;C:\eddiewtchoi\workspace\ide\git\spring\cache\target\classes;C:\sandbox\maven\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\target\pipeline-common-jpa-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.2.12\spring-boot-starter-data-jpa-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-aop\3.2.12\spring-boot-starter-aop-3.2.12.jar;C:\sandbox\maven\repository\org\aspectj\aspectjweaver\********\aspectjweaver-********.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.12\spring-boot-starter-jdbc-3.2.12.jar;C:\sandbox\maven\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\sandbox\maven\repository\org\springframework\spring-jdbc\6.1.15\spring-jdbc-6.1.15.jar;C:\sandbox\maven\repository\org\hibernate\orm\hibernate-core\6.4.10.Final\hibernate-core-6.4.10.Final.jar;C:\sandbox\maven\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\sandbox\maven\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\sandbox\maven\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\sandbox\maven\repository\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;C:\sandbox\maven\repository\io\smallrye\jandex\3.1.2\jandex-3.1.2.jar;C:\sandbox\maven\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\sandbox\maven\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\sandbox\maven\repository\org\springframework\data\spring-data-jpa\3.2.12\spring-data-jpa-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\data\spring-data-commons\3.2.12\spring-data-commons-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-orm\6.1.15\spring-orm-6.1.15.jar;C:\sandbox\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-aspects\6.1.13\spring-aspects-6.1.13.jar;C:\sandbox\maven\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.2.12\spring-boot-starter-validation-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter\3.2.12\spring-boot-starter-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot\3.2.12\spring-boot-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.12\spring-boot-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.2.12\spring-boot-starter-logging-3.2.12.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\sandbox\maven\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.33\tomcat-embed-el-10.1.33.jar;C:\sandbox\maven\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\target\pipeline-common-core-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\sandbox\maven\repository\io\vertx\vertx-core\4.5.14\vertx-core-4.5.14.jar;C:\sandbox\maven\repository\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler\4.1.119.Final\netty-handler-4.1.119.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler-proxy\4.1.115.Final\netty-handler-proxy-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-socks\4.1.115.Final\netty-codec-socks-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http\4.1.115.Final\netty-codec-http-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http2\4.1.115.Final\netty-codec-http2-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver-dns\4.1.115.Final\netty-resolver-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-dns\4.1.115.Final\netty-codec-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\sandbox\maven\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\sandbox\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-text\1.10.0\commons-text-1.10.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-json\3.2.12\spring-boot-starter-json-3.2.12.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.15.4\jackson-dataformat-xml-2.15.4.jar;C:\sandbox\maven\repository\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;C:\sandbox\maven\repository\com\fasterxml\woodstox\woodstox-core\6.5.1\woodstox-core-6.5.1.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.4\jackson-dataformat-yaml-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-properties\2.15.4\jackson-dataformat-properties-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-jaxb-annotations\2.15.4\jackson-module-jaxb-annotations-2.15.4.jar;C:\sandbox\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\sandbox\maven\repository\org\owasp\esapi\esapi\2.6.0.0\esapi-2.6.0.0.jar;C:\sandbox\maven\repository\xom\xom\1.3.9\xom-1.3.9.jar;C:\sandbox\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;C:\sandbox\maven\repository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\sandbox\maven\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\sandbox\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\sandbox\maven\repository\org\apache-extras\beanshell\bsh\2.0b6\bsh-2.0b6.jar;C:\sandbox\maven\repository\org\owasp\antisamy\antisamy\1.7.7\antisamy-1.7.7.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.5\httpcore5-h2-5.2.5.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5\5.2.5\httpcore5-5.2.5.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-css\1.18\batik-css-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-shared-resources\1.18\batik-shared-resources-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-util\1.18\batik-util-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-constants\1.18\batik-constants-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-i18n\1.18\batik-i18n-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\xmlgraphics-commons\2.10\xmlgraphics-commons-2.10.jar;C:\sandbox\maven\repository\org\htmlunit\neko-htmlunit\4.6.0\neko-htmlunit-4.6.0.jar;C:\sandbox\maven\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\sandbox\maven\repository\xml-apis\xml-apis-ext\1.3.04\xml-apis-ext-1.3.04.jar;C:\sandbox\maven\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\sandbox\maven\repository\net\logstash\logback\logstash-logback-encoder\6.1\logstash-logback-encoder-6.1.jar;C:\sandbox\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\sandbox\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\jaxb-runtime\2.3.1\jaxb-runtime-2.3.1.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\sandbox\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\sandbox\maven\repository\org\jvnet\staxex\stax-ex\2.1.0\stax-ex-2.1.0.jar;C:\sandbox\maven\repository\com\sun\xml\fastinfoset\FastInfoset\2.1.1\FastInfoset-2.1.1.jar;C:\sandbox\maven\repository\org\postgresql\postgresql\42.4.5\postgresql-42.4.5.jar;C:\sandbox\maven\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;C:\sandbox\maven\repository\io\hypersistence\hypersistence-utils-hibernate-63\3.9.0\hypersistence-utils-hibernate-63-3.9.0.jar;C:\sandbox\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\sandbox\maven\repository\io\hypersistence\hypersistence-tsid\2.1.1\hypersistence-tsid-2.1.1.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-web\target\pipeline-common-web-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-web\3.2.12\spring-boot-starter-web-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.12\spring-boot-starter-tomcat-3.2.12.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.33\tomcat-embed-websocket-10.1.33.jar;C:\sandbox\maven\repository\org\springframework\spring-web\6.1.15\spring-web-6.1.15.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.35\tomcat-embed-core-10.1.35.jar;C:\sandbox\maven\repository\org\apache\tomcat\tomcat-annotations-api\10.1.33\tomcat-annotations-api-10.1.33.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.12\spring-boot-starter-actuator-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.12\spring-boot-actuator-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator\3.2.12\spring-boot-actuator-3.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-observation\1.12.13\micrometer-observation-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-commons\1.12.13\micrometer-commons-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-jakarta9\1.12.13\micrometer-jakarta9-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-core\1.12.13\micrometer-core-1.12.13.jar;C:\sandbox\maven\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\sandbox\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\sandbox\maven\repository\org\springframework\spring-webmvc\6.1.15\spring-webmvc-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-aop\6.1.15\spring-aop-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-beans\6.1.15\spring-beans-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-context\6.1.15\spring-context-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-core\6.1.15\spring-core-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-jcl\6.1.15\spring-jcl-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-expression\6.1.15\spring-expression-6.1.15.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-prometheus\target\pipeline-common-prometheus-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-registry-prometheus\1.12.13\micrometer-registry-prometheus-1.12.13.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\sandbox\maven\repository\io\swagger\core\v3\swagger-annotations\2.1.9\swagger-annotations-2.1.9.jar;C:\sandbox\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-local-h2\target\pipeline-common-local-h2-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\com\h2database\h2\2.2.224\h2-2.2.224.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\target\pipeline-common-test-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-test\3.2.12\spring-boot-starter-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test\3.2.12\spring-boot-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.12\spring-boot-test-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\sandbox\maven\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\sandbox\maven\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\sandbox\maven\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\sandbox\maven\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\sandbox\maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter\5.10.2\junit-jupiter-5.10.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-params\5.10.5\junit-jupiter-params-5.10.5.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-engine\5.10.5\junit-jupiter-engine-5.10.5.jar;C:\sandbox\maven\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\sandbox\maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\sandbox\maven\repository\org\xmlunit\xmlunit-core\2.10.0\xmlunit-core-2.10.0.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-web\6.2.8\spring-security-web-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-core\6.2.8\spring-security-core-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-crypto\6.3.8\spring-security-crypto-6.3.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-test\6.2.8\spring-security-test-6.2.8.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-launcher\1.10.5\junit-platform-launcher-1.10.5.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-engine\1.10.5\junit-platform-engine-1.10.5.jar;C:\sandbox\maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-jose\6.2.8\spring-security-oauth2-jose-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-core\6.2.8\spring-security-oauth2-core-6.2.8.jar;C:\sandbox\maven\repository\com\nimbusds\nimbus-jose-jwt\9.37.2\nimbus-jose-jwt-9.37.2.jar;C:\sandbox\maven\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter-test\15.0.0\graphql-spring-boot-starter-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test-autoconfigure\15.0.0\graphql-spring-boot-test-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test\15.0.0\graphql-spring-boot-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter\15.0.0\graphql-spring-boot-starter-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-autoconfigure\15.0.0\graphql-spring-boot-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-support\15.0.0\graphql-kickstart-spring-support-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-webflux\15.0.0\graphql-kickstart-spring-webflux-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java-extended-scalars\19.1\graphql-java-extended-scalars-19.1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-kickstart\15.0.0\graphql-java-kickstart-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-servlet\15.0.0\graphql-java-servlet-15.0.0.jar;C:\sandbox\maven\repository\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-api\2.1.1\jakarta.websocket-api-2.1.1.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-client-api\2.1.1\jakarta.websocket-client-api-2.1.1.jar;C:\sandbox\maven\repository\io\github\graphql-java\graphql-java-annotations\9.1\graphql-java-annotations-9.1.jar;C:\sandbox\maven\repository\javax\validation\validation-api\1.1.0.Final\validation-api-1.1.0.Final.jar;C:\sandbox\maven\repository\org\reflections\reflections\0.10.2\reflections-0.10.2.jar;C:\sandbox\maven\repository\org\javassist\javassist\3.28.0-GA\javassist-3.28.0-GA.jar;C:\sandbox\maven\repository\org\springframework\spring-webflux\6.1.15\spring-webflux-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-websocket\3.2.12\spring-boot-starter-websocket-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-websocket\6.1.15\spring-websocket-6.1.15.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-tools\13.0.2\graphql-java-tools-13.0.2.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-reflect\1.9.25\kotlin-reflect-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-jdk8\1.7.3\kotlinx-coroutines-jdk8-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core-jvm\1.7.3\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core\1.7.3\kotlinx-coroutines-core-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-reactive\1.7.3\kotlinx-coroutines-reactive-1.7.3.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java\21.5\graphql-java-21.5.jar;C:\sandbox\maven\repository\com\graphql-java\java-dataloader\3.2.1\java-dataloader-3.2.1.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.15.4\jackson-module-kotlin-2.15.4.jar;C:\sandbox\maven\repository\org\mockito\mockito-junit-jupiter\5.2.0\mockito-junit-jupiter-5.2.0.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-api\5.10.5\junit-jupiter-api-5.10.5.jar;C:\sandbox\maven\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-commons\1.10.5\junit-platform-commons-1.10.5.jar;C:\sandbox\maven\repository\org\springframework\spring-test\6.1.8\spring-test-6.1.8.jar;C:\sandbox\maven\repository\org\flywaydb\flyway-core\9.22.3\flyway-core-9.22.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-toml\2.15.4\jackson-dataformat-toml-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;C:\sandbox\maven\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jasypt\target\pipeline-common-jasypt-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\com\github\ulisesbocchio\jasypt-spring-boot-starter\3.0.4\jasypt-spring-boot-starter-3.0.4.jar;C:\sandbox\maven\repository\com\github\ulisesbocchio\jasypt-spring-boot\3.0.4\jasypt-spring-boot-3.0.4.jar;C:\sandbox\maven\repository\org\jasypt\jasypt\1.9.3\jasypt-1.9.3.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-spring-kafka\target\pipeline-common-spring-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\kafka\spring-kafka\3.1.10\spring-kafka-3.1.10.jar;C:\sandbox\maven\repository\org\springframework\spring-messaging\6.1.15\spring-messaging-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-tx\6.1.15\spring-tx-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\retry\spring-retry\2.0.10\spring-retry-2.0.10.jar;C:\sandbox\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-clients\3.7.2\kafka-clients-3.7.2.jar;C:\sandbox\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;C:\sandbox\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;C:\sandbox\maven\repository\org\xerial\snappy\snappy-java\********\snappy-java-********.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\target\pipeline-common-test-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\integration\spring-integration-kafka\6.3.2\spring-integration-kafka-6.3.2.jar;C:\sandbox\maven\repository\org\springframework\integration\spring-integration-core\6.3.2\spring-integration-core-6.3.2.jar;C:\sandbox\maven\repository\io\projectreactor\reactor-core\3.6.12\reactor-core-3.6.12.jar;C:\sandbox\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\sandbox\maven\repository\org\springframework\kafka\spring-kafka-test\3.1.10\spring-kafka-test-3.1.10.jar;C:\sandbox\maven\repository\org\apache\zookeeper\zookeeper\3.8.4\zookeeper-3.8.4.jar;C:\sandbox\maven\repository\org\apache\zookeeper\zookeeper-jute\3.8.4\zookeeper-jute-3.8.4.jar;C:\sandbox\maven\repository\org\apache\yetus\audience-annotations\0.12.0\audience-annotations-0.12.0.jar;C:\sandbox\maven\repository\io\netty\netty-transport-native-epoll\4.1.115.Final\netty-transport-native-epoll-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport-classes-epoll\4.1.115.Final\netty-transport-classes-epoll-4.1.115.Final.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-core\1.5.16\logback-core-1.5.16.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-classic\1.5.16\logback-classic-1.5.16.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-clients\3.7.2\kafka-clients-3.7.2-test.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-metadata\3.7.2\kafka-metadata-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-server-common\3.7.2\kafka-server-common-3.7.2.jar;C:\sandbox\maven\repository\org\pcollections\pcollections\4.0.1\pcollections-4.0.1.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-server-common\3.7.2\kafka-server-common-3.7.2-test.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-streams-test-utils\3.7.2\kafka-streams-test-utils-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka_2.13\3.7.2\kafka_2.13-3.7.2-test.jar;C:\sandbox\maven\repository\org\mockito\mockito-core\5.2.0\mockito-core-5.2.0.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;C:\sandbox\maven\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\sandbox\maven\repository\org\assertj\assertj-core\3.25.3\assertj-core-3.25.3.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-k8s-client\target\pipeline-common-k8s-client-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\kubernetes\client-java\22.0.0\client-java-22.0.0.jar;C:\sandbox\maven\repository\io\kubernetes\client-java-api\22.0.0\client-java-api-22.0.0.jar;C:\sandbox\maven\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\sandbox\maven\repository\io\swagger\swagger-annotations\1.6.14\swagger-annotations-1.6.14.jar;C:\sandbox\maven\repository\com\squareup\okhttp3\okhttp\4.9.2\okhttp-4.9.2.jar;C:\sandbox\maven\repository\com\squareup\okhttp3\logging-interceptor\4.12.0\logging-interceptor-4.12.0.jar;C:\sandbox\maven\repository\io\gsonfire\gson-fire\1.9.0\gson-fire-1.9.0.jar;C:\sandbox\maven\repository\jakarta\ws\rs\jakarta.ws.rs-api\3.1.0\jakarta.ws.rs-api-3.1.0.jar;C:\sandbox\maven\repository\io\kubernetes\client-java-proto\22.0.0\client-java-proto-22.0.0.jar;C:\sandbox\maven\repository\commons-codec\commons-codec\1.16.1\commons-codec-1.16.1.jar;C:\sandbox\maven\repository\org\bouncycastle\bcpkix-jdk18on\1.79\bcpkix-jdk18on-1.79.jar;C:\sandbox\maven\repository\org\bouncycastle\bcprov-jdk18on\1.79\bcprov-jdk18on-1.79.jar;C:\sandbox\maven\repository\org\bouncycastle\bcutil-jdk18on\1.79\bcutil-jdk18on-1.79.jar;C:\sandbox\maven\repository\org\bitbucket\b_c\jose4j\0.9.4\jose4j-0.9.4.jar;C:\sandbox\maven\repository\com\google\protobuf\protobuf-java\3.25.5\protobuf-java-3.25.5.jar;C:\sandbox\maven\repository\com\squareup\okio\okio\3.5.0\okio-3.5.0.jar;C:\sandbox\maven\repository\com\squareup\okio\okio-jvm\3.5.0\okio-jvm-3.5.0.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-kafka\target\pipeline-common-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-streams\3.7.2\kafka-streams-3.7.2.jar;C:\sandbox\maven\repository\org\rocksdb\rocksdbjni\7.9.2\rocksdbjni-7.9.2.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;C:\sandbox\maven\repository\org\apache\avro\avro\1.12.0\avro-1.12.0.jar;C:\sandbox\maven\repository\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;C:\sandbox\maven\repository\io\confluent\common-utils\5.3.0\common-utils-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\common-config\5.3.0\common-config-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-avro-serializer\5.3.0\kafka-avro-serializer-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-schema-registry-client\5.3.0\kafka-schema-registry-client-5.3.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-tracer-zipkin\target\pipeline-common-tracer-zipkin-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing-bridge-brave\1.2.12\micrometer-tracing-bridge-brave-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing\1.2.12\micrometer-tracing-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\sandbox\maven\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave\5.16.0\brave-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-context-slf4j\5.16.0\brave-context-slf4j-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-instrumentation-http\5.16.0\brave-instrumentation-http-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\aws\brave-propagation-aws\0.23.5\brave-propagation-aws-0.23.5.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter-brave\2.16.3\zipkin-reporter-brave-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter\2.16.3\zipkin-reporter-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\zipkin2\zipkin\2.23.2\zipkin-2.23.2.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-sender-urlconnection\2.16.3\zipkin-sender-urlconnection-2.16.3.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-kafka-test-helper\target\pipeline-common-kafka-test-helper-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka_2.13\3.7.2\kafka_2.13-3.7.2.jar;C:\sandbox\maven\repository\org\scala-lang\scala-library\2.13.9\scala-library-2.13.9.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-group-coordinator\3.7.2\kafka-group-coordinator-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-storage-api\3.7.2\kafka-storage-api-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-tools-api\3.7.2\kafka-tools-api-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-raft\3.7.2\kafka-raft-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-storage\3.7.2\kafka-storage-3.7.2.jar;C:\sandbox\maven\repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;C:\sandbox\maven\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-server\3.7.2\kafka-server-3.7.2.jar;C:\sandbox\maven\repository\net\sourceforge\argparse4j\argparse4j\0.7.0\argparse4j-0.7.0.jar;C:\sandbox\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;C:\sandbox\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;C:\sandbox\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-scala_2.13\2.15.4\jackson-module-scala_2.13-2.15.4.jar;C:\sandbox\maven\repository\com\thoughtworks\paranamer\paranamer\2.8\paranamer-2.8.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-csv\2.15.4\jackson-dataformat-csv-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;C:\sandbox\maven\repository\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;C:\sandbox\maven\repository\com\yammer\metrics\metrics-core\2.2.0\metrics-core-2.2.0.jar;C:\sandbox\maven\repository\org\scala-lang\modules\scala-collection-compat_2.13\2.10.0\scala-collection-compat_2.13-2.10.0.jar;C:\sandbox\maven\repository\org\scala-lang\modules\scala-java8-compat_2.13\1.0.2\scala-java8-compat_2.13-1.0.2.jar;C:\sandbox\maven\repository\org\scala-lang\scala-reflect\2.13.12\scala-reflect-2.13.12.jar;C:\sandbox\maven\repository\com\typesafe\scala-logging\scala-logging_2.13\3.9.4\scala-logging_2.13-3.9.4.jar;C:\sandbox\maven\repository\io\dropwizard\metrics\metrics-core\4.2.28\metrics-core-4.2.28.jar;C:\sandbox\maven\repository\commons-cli\commons-cli\1.4\commons-cli-1.4.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-simple\2.0.16\slf4j-simple-2.0.16.jar;"/>
    <property name="java.vm.vendor" value="Eclipse Adoptium"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="org.jboss.logging.provider" value="slf4j"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="C:\sandbox\devtools\java\jdk-21.0.4+7\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire8652559685221895030\surefirebooter-20250718035642510_33.jar C:\Users\<USER>\AppData\Local\Temp\surefire8652559685221895030 2025-07-18T03-55-35_673-jvmRun1 surefire-20250718035642510_31tmp surefire_6-20250718035642510_32tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\eddiewtchoi\workspace\ide\git\spring\cache\target\test-classes;C:\eddiewtchoi\workspace\ide\git\spring\cache\target\classes;C:\sandbox\maven\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\target\pipeline-common-jpa-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-data-jpa\3.2.12\spring-boot-starter-data-jpa-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-aop\3.2.12\spring-boot-starter-aop-3.2.12.jar;C:\sandbox\maven\repository\org\aspectj\aspectjweaver\********\aspectjweaver-********.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.12\spring-boot-starter-jdbc-3.2.12.jar;C:\sandbox\maven\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\sandbox\maven\repository\org\springframework\spring-jdbc\6.1.15\spring-jdbc-6.1.15.jar;C:\sandbox\maven\repository\org\hibernate\orm\hibernate-core\6.4.10.Final\hibernate-core-6.4.10.Final.jar;C:\sandbox\maven\repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;C:\sandbox\maven\repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;C:\sandbox\maven\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\sandbox\maven\repository\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;C:\sandbox\maven\repository\io\smallrye\jandex\3.1.2\jandex-3.1.2.jar;C:\sandbox\maven\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\sandbox\maven\repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;C:\sandbox\maven\repository\org\springframework\data\spring-data-jpa\3.2.12\spring-data-jpa-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\data\spring-data-commons\3.2.12\spring-data-commons-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-orm\6.1.15\spring-orm-6.1.15.jar;C:\sandbox\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\sandbox\maven\repository\org\springframework\spring-aspects\6.1.13\spring-aspects-6.1.13.jar;C:\sandbox\maven\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.2.12\spring-boot-starter-validation-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter\3.2.12\spring-boot-starter-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot\3.2.12\spring-boot-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.12\spring-boot-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.2.12\spring-boot-starter-logging-3.2.12.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\sandbox\maven\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.33\tomcat-embed-el-10.1.33.jar;C:\sandbox\maven\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\target\pipeline-common-core-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\sandbox\maven\repository\io\vertx\vertx-core\4.5.14\vertx-core-4.5.14.jar;C:\sandbox\maven\repository\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler\4.1.119.Final\netty-handler-4.1.119.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler-proxy\4.1.115.Final\netty-handler-proxy-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-socks\4.1.115.Final\netty-codec-socks-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http\4.1.115.Final\netty-codec-http-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http2\4.1.115.Final\netty-codec-http2-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver-dns\4.1.115.Final\netty-resolver-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-dns\4.1.115.Final\netty-codec-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\sandbox\maven\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\sandbox\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-text\1.10.0\commons-text-1.10.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-json\3.2.12\spring-boot-starter-json-3.2.12.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.15.4\jackson-dataformat-xml-2.15.4.jar;C:\sandbox\maven\repository\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;C:\sandbox\maven\repository\com\fasterxml\woodstox\woodstox-core\6.5.1\woodstox-core-6.5.1.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.4\jackson-dataformat-yaml-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-properties\2.15.4\jackson-dataformat-properties-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-jaxb-annotations\2.15.4\jackson-module-jaxb-annotations-2.15.4.jar;C:\sandbox\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\sandbox\maven\repository\org\owasp\esapi\esapi\2.6.0.0\esapi-2.6.0.0.jar;C:\sandbox\maven\repository\xom\xom\1.3.9\xom-1.3.9.jar;C:\sandbox\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;C:\sandbox\maven\repository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\sandbox\maven\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\sandbox\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\sandbox\maven\repository\org\apache-extras\beanshell\bsh\2.0b6\bsh-2.0b6.jar;C:\sandbox\maven\repository\org\owasp\antisamy\antisamy\1.7.7\antisamy-1.7.7.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.5\httpcore5-h2-5.2.5.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5\5.2.5\httpcore5-5.2.5.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-css\1.18\batik-css-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-shared-resources\1.18\batik-shared-resources-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-util\1.18\batik-util-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-constants\1.18\batik-constants-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-i18n\1.18\batik-i18n-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\xmlgraphics-commons\2.10\xmlgraphics-commons-2.10.jar;C:\sandbox\maven\repository\org\htmlunit\neko-htmlunit\4.6.0\neko-htmlunit-4.6.0.jar;C:\sandbox\maven\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\sandbox\maven\repository\xml-apis\xml-apis-ext\1.3.04\xml-apis-ext-1.3.04.jar;C:\sandbox\maven\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\sandbox\maven\repository\net\logstash\logback\logstash-logback-encoder\6.1\logstash-logback-encoder-6.1.jar;C:\sandbox\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\sandbox\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\jaxb-runtime\2.3.1\jaxb-runtime-2.3.1.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\sandbox\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\sandbox\maven\repository\org\jvnet\staxex\stax-ex\2.1.0\stax-ex-2.1.0.jar;C:\sandbox\maven\repository\com\sun\xml\fastinfoset\FastInfoset\2.1.1\FastInfoset-2.1.1.jar;C:\sandbox\maven\repository\org\postgresql\postgresql\42.4.5\postgresql-42.4.5.jar;C:\sandbox\maven\repository\org\checkerframework\checker-qual\3.5.0\checker-qual-3.5.0.jar;C:\sandbox\maven\repository\io\hypersistence\hypersistence-utils-hibernate-63\3.9.0\hypersistence-utils-hibernate-63-3.9.0.jar;C:\sandbox\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\sandbox\maven\repository\io\hypersistence\hypersistence-tsid\2.1.1\hypersistence-tsid-2.1.1.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-web\target\pipeline-common-web-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-web\3.2.12\spring-boot-starter-web-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.12\spring-boot-starter-tomcat-3.2.12.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.33\tomcat-embed-websocket-10.1.33.jar;C:\sandbox\maven\repository\org\springframework\spring-web\6.1.15\spring-web-6.1.15.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.35\tomcat-embed-core-10.1.35.jar;C:\sandbox\maven\repository\org\apache\tomcat\tomcat-annotations-api\10.1.33\tomcat-annotations-api-10.1.33.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.12\spring-boot-starter-actuator-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.12\spring-boot-actuator-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator\3.2.12\spring-boot-actuator-3.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-observation\1.12.13\micrometer-observation-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-commons\1.12.13\micrometer-commons-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-jakarta9\1.12.13\micrometer-jakarta9-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-core\1.12.13\micrometer-core-1.12.13.jar;C:\sandbox\maven\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\sandbox\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\sandbox\maven\repository\org\springframework\spring-webmvc\6.1.15\spring-webmvc-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-aop\6.1.15\spring-aop-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-beans\6.1.15\spring-beans-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-context\6.1.15\spring-context-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-core\6.1.15\spring-core-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-jcl\6.1.15\spring-jcl-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-expression\6.1.15\spring-expression-6.1.15.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-prometheus\target\pipeline-common-prometheus-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-registry-prometheus\1.12.13\micrometer-registry-prometheus-1.12.13.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\sandbox\maven\repository\io\swagger\core\v3\swagger-annotations\2.1.9\swagger-annotations-2.1.9.jar;C:\sandbox\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-local-h2\target\pipeline-common-local-h2-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\com\h2database\h2\2.2.224\h2-2.2.224.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\target\pipeline-common-test-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-test\3.2.12\spring-boot-starter-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test\3.2.12\spring-boot-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.12\spring-boot-test-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\sandbox\maven\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\sandbox\maven\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\sandbox\maven\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\sandbox\maven\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\sandbox\maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter\5.10.2\junit-jupiter-5.10.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-params\5.10.5\junit-jupiter-params-5.10.5.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-engine\5.10.5\junit-jupiter-engine-5.10.5.jar;C:\sandbox\maven\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\sandbox\maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\sandbox\maven\repository\org\xmlunit\xmlunit-core\2.10.0\xmlunit-core-2.10.0.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-web\6.2.8\spring-security-web-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-core\6.2.8\spring-security-core-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-crypto\6.3.8\spring-security-crypto-6.3.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-test\6.2.8\spring-security-test-6.2.8.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-launcher\1.10.5\junit-platform-launcher-1.10.5.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-engine\1.10.5\junit-platform-engine-1.10.5.jar;C:\sandbox\maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-jose\6.2.8\spring-security-oauth2-jose-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-core\6.2.8\spring-security-oauth2-core-6.2.8.jar;C:\sandbox\maven\repository\com\nimbusds\nimbus-jose-jwt\9.37.2\nimbus-jose-jwt-9.37.2.jar;C:\sandbox\maven\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter-test\15.0.0\graphql-spring-boot-starter-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test-autoconfigure\15.0.0\graphql-spring-boot-test-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test\15.0.0\graphql-spring-boot-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter\15.0.0\graphql-spring-boot-starter-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-autoconfigure\15.0.0\graphql-spring-boot-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-support\15.0.0\graphql-kickstart-spring-support-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-webflux\15.0.0\graphql-kickstart-spring-webflux-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java-extended-scalars\19.1\graphql-java-extended-scalars-19.1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-kickstart\15.0.0\graphql-java-kickstart-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-servlet\15.0.0\graphql-java-servlet-15.0.0.jar;C:\sandbox\maven\repository\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-api\2.1.1\jakarta.websocket-api-2.1.1.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-client-api\2.1.1\jakarta.websocket-client-api-2.1.1.jar;C:\sandbox\maven\repository\io\github\graphql-java\graphql-java-annotations\9.1\graphql-java-annotations-9.1.jar;C:\sandbox\maven\repository\javax\validation\validation-api\1.1.0.Final\validation-api-1.1.0.Final.jar;C:\sandbox\maven\repository\org\reflections\reflections\0.10.2\reflections-0.10.2.jar;C:\sandbox\maven\repository\org\javassist\javassist\3.28.0-GA\javassist-3.28.0-GA.jar;C:\sandbox\maven\repository\org\springframework\spring-webflux\6.1.15\spring-webflux-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-websocket\3.2.12\spring-boot-starter-websocket-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-websocket\6.1.15\spring-websocket-6.1.15.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-tools\13.0.2\graphql-java-tools-13.0.2.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-reflect\1.9.25\kotlin-reflect-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-jdk8\1.7.3\kotlinx-coroutines-jdk8-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core-jvm\1.7.3\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core\1.7.3\kotlinx-coroutines-core-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-reactive\1.7.3\kotlinx-coroutines-reactive-1.7.3.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java\21.5\graphql-java-21.5.jar;C:\sandbox\maven\repository\com\graphql-java\java-dataloader\3.2.1\java-dataloader-3.2.1.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.15.4\jackson-module-kotlin-2.15.4.jar;C:\sandbox\maven\repository\org\mockito\mockito-junit-jupiter\5.2.0\mockito-junit-jupiter-5.2.0.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-api\5.10.5\junit-jupiter-api-5.10.5.jar;C:\sandbox\maven\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-commons\1.10.5\junit-platform-commons-1.10.5.jar;C:\sandbox\maven\repository\org\springframework\spring-test\6.1.8\spring-test-6.1.8.jar;C:\sandbox\maven\repository\org\flywaydb\flyway-core\9.22.3\flyway-core-9.22.3.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-toml\2.15.4\jackson-dataformat-toml-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;C:\sandbox\maven\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jasypt\target\pipeline-common-jasypt-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\com\github\ulisesbocchio\jasypt-spring-boot-starter\3.0.4\jasypt-spring-boot-starter-3.0.4.jar;C:\sandbox\maven\repository\com\github\ulisesbocchio\jasypt-spring-boot\3.0.4\jasypt-spring-boot-3.0.4.jar;C:\sandbox\maven\repository\org\jasypt\jasypt\1.9.3\jasypt-1.9.3.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-spring-kafka\target\pipeline-common-spring-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\kafka\spring-kafka\3.1.10\spring-kafka-3.1.10.jar;C:\sandbox\maven\repository\org\springframework\spring-messaging\6.1.15\spring-messaging-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-tx\6.1.15\spring-tx-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\retry\spring-retry\2.0.10\spring-retry-2.0.10.jar;C:\sandbox\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-clients\3.7.2\kafka-clients-3.7.2.jar;C:\sandbox\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;C:\sandbox\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;C:\sandbox\maven\repository\org\xerial\snappy\snappy-java\********\snappy-java-********.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\target\pipeline-common-test-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\integration\spring-integration-kafka\6.3.2\spring-integration-kafka-6.3.2.jar;C:\sandbox\maven\repository\org\springframework\integration\spring-integration-core\6.3.2\spring-integration-core-6.3.2.jar;C:\sandbox\maven\repository\io\projectreactor\reactor-core\3.6.12\reactor-core-3.6.12.jar;C:\sandbox\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\sandbox\maven\repository\org\springframework\kafka\spring-kafka-test\3.1.10\spring-kafka-test-3.1.10.jar;C:\sandbox\maven\repository\org\apache\zookeeper\zookeeper\3.8.4\zookeeper-3.8.4.jar;C:\sandbox\maven\repository\org\apache\zookeeper\zookeeper-jute\3.8.4\zookeeper-jute-3.8.4.jar;C:\sandbox\maven\repository\org\apache\yetus\audience-annotations\0.12.0\audience-annotations-0.12.0.jar;C:\sandbox\maven\repository\io\netty\netty-transport-native-epoll\4.1.115.Final\netty-transport-native-epoll-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport-classes-epoll\4.1.115.Final\netty-transport-classes-epoll-4.1.115.Final.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-core\1.5.16\logback-core-1.5.16.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-classic\1.5.16\logback-classic-1.5.16.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-clients\3.7.2\kafka-clients-3.7.2-test.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-metadata\3.7.2\kafka-metadata-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-server-common\3.7.2\kafka-server-common-3.7.2.jar;C:\sandbox\maven\repository\org\pcollections\pcollections\4.0.1\pcollections-4.0.1.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-server-common\3.7.2\kafka-server-common-3.7.2-test.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-streams-test-utils\3.7.2\kafka-streams-test-utils-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka_2.13\3.7.2\kafka_2.13-3.7.2-test.jar;C:\sandbox\maven\repository\org\mockito\mockito-core\5.2.0\mockito-core-5.2.0.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;C:\sandbox\maven\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\sandbox\maven\repository\org\assertj\assertj-core\3.25.3\assertj-core-3.25.3.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-k8s-client\target\pipeline-common-k8s-client-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\kubernetes\client-java\22.0.0\client-java-22.0.0.jar;C:\sandbox\maven\repository\io\kubernetes\client-java-api\22.0.0\client-java-api-22.0.0.jar;C:\sandbox\maven\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\sandbox\maven\repository\io\swagger\swagger-annotations\1.6.14\swagger-annotations-1.6.14.jar;C:\sandbox\maven\repository\com\squareup\okhttp3\okhttp\4.9.2\okhttp-4.9.2.jar;C:\sandbox\maven\repository\com\squareup\okhttp3\logging-interceptor\4.12.0\logging-interceptor-4.12.0.jar;C:\sandbox\maven\repository\io\gsonfire\gson-fire\1.9.0\gson-fire-1.9.0.jar;C:\sandbox\maven\repository\jakarta\ws\rs\jakarta.ws.rs-api\3.1.0\jakarta.ws.rs-api-3.1.0.jar;C:\sandbox\maven\repository\io\kubernetes\client-java-proto\22.0.0\client-java-proto-22.0.0.jar;C:\sandbox\maven\repository\commons-codec\commons-codec\1.16.1\commons-codec-1.16.1.jar;C:\sandbox\maven\repository\org\bouncycastle\bcpkix-jdk18on\1.79\bcpkix-jdk18on-1.79.jar;C:\sandbox\maven\repository\org\bouncycastle\bcprov-jdk18on\1.79\bcprov-jdk18on-1.79.jar;C:\sandbox\maven\repository\org\bouncycastle\bcutil-jdk18on\1.79\bcutil-jdk18on-1.79.jar;C:\sandbox\maven\repository\org\bitbucket\b_c\jose4j\0.9.4\jose4j-0.9.4.jar;C:\sandbox\maven\repository\com\google\protobuf\protobuf-java\3.25.5\protobuf-java-3.25.5.jar;C:\sandbox\maven\repository\com\squareup\okio\okio\3.5.0\okio-3.5.0.jar;C:\sandbox\maven\repository\com\squareup\okio\okio-jvm\3.5.0\okio-jvm-3.5.0.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-kafka\target\pipeline-common-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-streams\3.7.2\kafka-streams-3.7.2.jar;C:\sandbox\maven\repository\org\rocksdb\rocksdbjni\7.9.2\rocksdbjni-7.9.2.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;C:\sandbox\maven\repository\org\apache\avro\avro\1.12.0\avro-1.12.0.jar;C:\sandbox\maven\repository\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;C:\sandbox\maven\repository\io\confluent\common-utils\5.3.0\common-utils-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\common-config\5.3.0\common-config-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-avro-serializer\5.3.0\kafka-avro-serializer-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-schema-registry-client\5.3.0\kafka-schema-registry-client-5.3.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-tracer-zipkin\target\pipeline-common-tracer-zipkin-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing-bridge-brave\1.2.12\micrometer-tracing-bridge-brave-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing\1.2.12\micrometer-tracing-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\sandbox\maven\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave\5.16.0\brave-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-context-slf4j\5.16.0\brave-context-slf4j-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-instrumentation-http\5.16.0\brave-instrumentation-http-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\aws\brave-propagation-aws\0.23.5\brave-propagation-aws-0.23.5.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter-brave\2.16.3\zipkin-reporter-brave-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter\2.16.3\zipkin-reporter-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\zipkin2\zipkin\2.23.2\zipkin-2.23.2.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-sender-urlconnection\2.16.3\zipkin-sender-urlconnection-2.16.3.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-kafka-test-helper\target\pipeline-common-kafka-test-helper-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka_2.13\3.7.2\kafka_2.13-3.7.2.jar;C:\sandbox\maven\repository\org\scala-lang\scala-library\2.13.9\scala-library-2.13.9.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-group-coordinator\3.7.2\kafka-group-coordinator-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-storage-api\3.7.2\kafka-storage-api-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-tools-api\3.7.2\kafka-tools-api-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-raft\3.7.2\kafka-raft-3.7.2.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-storage\3.7.2\kafka-storage-3.7.2.jar;C:\sandbox\maven\repository\com\github\ben-manes\caffeine\caffeine\3.1.8\caffeine-3.1.8.jar;C:\sandbox\maven\repository\com\google\errorprone\error_prone_annotations\2.21.1\error_prone_annotations-2.21.1.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-server\3.7.2\kafka-server-3.7.2.jar;C:\sandbox\maven\repository\net\sourceforge\argparse4j\argparse4j\0.7.0\argparse4j-0.7.0.jar;C:\sandbox\maven\repository\commons-validator\commons-validator\1.7\commons-validator-1.7.jar;C:\sandbox\maven\repository\commons-digester\commons-digester\2.1\commons-digester-2.1.jar;C:\sandbox\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-scala_2.13\2.15.4\jackson-module-scala_2.13-2.15.4.jar;C:\sandbox\maven\repository\com\thoughtworks\paranamer\paranamer\2.8\paranamer-2.8.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-csv\2.15.4\jackson-dataformat-csv-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;C:\sandbox\maven\repository\net\sf\jopt-simple\jopt-simple\5.0.4\jopt-simple-5.0.4.jar;C:\sandbox\maven\repository\com\yammer\metrics\metrics-core\2.2.0\metrics-core-2.2.0.jar;C:\sandbox\maven\repository\org\scala-lang\modules\scala-collection-compat_2.13\2.10.0\scala-collection-compat_2.13-2.10.0.jar;C:\sandbox\maven\repository\org\scala-lang\modules\scala-java8-compat_2.13\1.0.2\scala-java8-compat_2.13-1.0.2.jar;C:\sandbox\maven\repository\org\scala-lang\scala-reflect\2.13.12\scala-reflect-2.13.12.jar;C:\sandbox\maven\repository\com\typesafe\scala-logging\scala-logging_2.13\3.9.4\scala-logging_2.13-3.9.4.jar;C:\sandbox\maven\repository\io\dropwizard\metrics\metrics-core\4.2.28\metrics-core-4.2.28.jar;C:\sandbox\maven\repository\commons-cli\commons-cli\1.4\commons-cli-1.4.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-simple\2.0.16\slf4j-simple-2.0.16.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\sandbox\devtools\java\jdk-21.0.4+7"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\eddiewtchoi\workspace\ide\git\spring\cache"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="FILE_LOG_CHARSET" value="UTF-8"/>
    <property name="java.awt.headless" value="true"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire8652559685221895030\surefirebooter-20250718035642510_33.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.4+7-LTS"/>
    <property name="user.name" value="43402948"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Temurin-21.0.4+7"/>
    <property name="localRepository" value="C:\sandbox\maven\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.4"/>
    <property name="user.dir" value="C:\eddiewtchoi\workspace\ide\git\spring\cache"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="PID" value="14432"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="CONSOLE_LOG_CHARSET" value="UTF-8"/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\sandbox\devtools\java\jdk-21.0.4+7\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Windows\CCM\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;c:\Program Files (x86)\Sennheiser\SenncomSDK\;C:\Program Files (x86)\SafeCom\SafeComPrintClient;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Adaptiva\AdaptivaClient\bin\x32;C:\Program Files (x86)\Adaptiva\AdaptivaClient\bin\x64;C:\Program Files\Git-2.41.0\cmd;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\Adaptiva\AdaptivaClient\bin\x32;C:\Program Files\Adaptiva\AdaptivaClient\bin\x64;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\go\bin;C:\sandbox\devtools\golang\1.20\go\bin;C:\sandbox\devtools\google-cloud-sdk\239.0.0\google-cloud-sdk\bin;C:\sandbox\devtools\node\node-v15.14.0-win-x64\;C:\sandbox\devtools\lein;C:\sandbox\devtools\gradle\gradle-4.6\bin;C:\sandbox\devtools\maven\apache-maven-3.9.8\bin;C:\sandbox\devtools\git\2.44.0\usr\bin;C:\sandbox\devtools\git\2.44.0\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\sandbox\devtools\postgresql\14.2-1\pgsql\bin;;%USERPROFILE%\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Eclipse Adoptium"/>
    <property name="java.vm.version" value="21.0.4+7-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
    <property name="LOGGED_APPLICATION_NAME" value="[cache] "/>
  </properties>
  <testcase name="shouldGetCacheNameSuccess" classname="com.poc.hss.fasttrack.CacheControllerTest" time="3.154">
    <system-out><![CDATA[03:56:45.026 [main] INFO org.springframework.test.context.support.AnnotationConfigContextLoaderUtils -- Could not detect default configuration classes for test class [com.poc.hss.fasttrack.CacheControllerTest]: CacheControllerTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
03:56:45.248 [main] INFO org.springframework.boot.test.context.SpringBootTestContextBootstrapper -- Found @SpringBootConfiguration com.poc.hss.fasttrack.CacheApplication for test class com.poc.hss.fasttrack.CacheControllerTest
03:56:45,827 |-INFO in ConfigurationWatchList(mainURL=jar:file:/C:/eddiewtchoi/workspace/ide/git/spring/pipeline-common-core/target/pipeline-common-core-2.8.6-SNAPSHOT.jar!/logback-spring.xml, fileWatchList={}, urlWatchList=[}) - URL [jar:file:/C:/eddiewtchoi/workspace/ide/git/spring/pipeline-common-core/target/pipeline-common-core-2.8.6-SNAPSHOT.jar!/logback-spring.xml] is not of type file
03:56:45,992 |-WARN in org.springframework.boot.logging.logback.SpringProfileIfNestedWithinSecondPhaseElementSanityChecker@531ec2ca - <springProfile> elements cannot be nested within an <appender>, <logger> or <root> element
03:56:45,993 |-WARN in org.springframework.boot.logging.logback.SpringProfileIfNestedWithinSecondPhaseElementSanityChecker@531ec2ca - Element <appender> at line 4 contains a nested <springProfile> element at line 21
03:56:46,070 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - Processing appender named [STDOUT]
03:56:46,071 |-INFO in ch.qos.logback.core.model.processor.AppenderModelHandler - About to instantiate appender of type [com.poc.hss.fasttrack.logging.SplitLoggingAppender]
03:56:46,139 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [net.logstash.logback.fieldnames.LogstashFieldNames] for [fieldNames] property
03:56:46,911 |-INFO in ch.qos.logback.core.model.processor.ImplicitModelHandler - Assuming default type [ch.qos.logback.classic.encoder.PatternLayoutEncoder] for [encoder] property
03:56:46,914 |-INFO in com.poc.hss.fasttrack.logging.SplitLoggingAppender[STDOUT] - BEWARE: Writing to the console can be very slow. Avoid logging to the 
03:56:46,915 |-INFO in com.poc.hss.fasttrack.logging.SplitLoggingAppender[STDOUT] - console in production environments, especially in high volume systems.
03:56:46,915 |-INFO in com.poc.hss.fasttrack.logging.SplitLoggingAppender[STDOUT] - See also https://logback.qos.ch/codes.html#slowConsole
03:56:46,915 |-INFO in ch.qos.logback.classic.model.processor.RootLoggerModelHandler - Setting level of ROOT logger to INFO
03:56:46,915 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@186e254 - Propagating INFO level on Logger[ROOT] onto the JUL framework
03:56:46,916 |-INFO in ch.qos.logback.core.model.processor.AppenderRefModelHandler - Attaching appender named [STDOUT] to Logger[ROOT]
03:56:46,916 |-INFO in ch.qos.logback.core.model.processor.DefaultProcessor@7f69c4c0 - End of configuration.
03:56:46,916 |-INFO in org.springframework.boot.logging.logback.SpringBootJoranConfigurator@1cdb290c - Registering current configuration as safe fallback point
03:56:46,920 |-INFO in ch.qos.logback.classic.jul.LevelChangePropagator@186e254 - Propagating DEBUG level on Logger[com.poc.hss.fasttrack] onto the JUL framework

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::               (v3.2.12)

2025-07-18 03:56:46.959 INFO  --- [background-preinit]  o.h.validator.internal.util.Version : HV000001: Hibernate Validator 8.0.1.Final
2025-07-18 03:56:47.137 INFO  --- [main]  c.p.h.fasttrack.CacheControllerTest : Starting CacheControllerTest using Java 21.0.4 with PID 14432 (started by 43402948 in C:\eddiewtchoi\workspace\ide\git\spring\cache)
2025-07-18 03:56:47.138 DEBUG --- [main]  c.p.h.fasttrack.CacheControllerTest : Running with Spring Boot v3.2.12, Spring v6.1.15
2025-07-18 03:56:47.138 INFO  --- [main]  c.p.h.fasttrack.CacheControllerTest : The following 1 profile is active: "test"
2025-07-18 03:56:48.962 INFO  --- [main]  o.s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-07-18 03:56:49.203 INFO  --- [main]  o.s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 233 ms. Found 3 JPA repository interfaces.
2025-07-18 03:56:49.321 INFO  --- [main]  o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-07-18 03:56:49.333 INFO  --- [main]  o.s.i.c.DefaultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-07-18 03:56:49.642 INFO  --- [main]  c.u.j.c.EnableEncryptablePropertiesBeanFactoryPostProcessor : Post-processing PropertySource instances
2025-07-18 03:56:49.645 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Annotations [org.springframework.boot.test.autoconfigure.properties.AnnotationsPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-18 03:56:49.645 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-07-18 03:56:49.645 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource test [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-18 03:56:49.645 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Inlined Test Properties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-18 03:56:49.645 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource active-test-profiles [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-18 03:56:49.646 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-07-18 03:56:49.646 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-18 03:56:49.646 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-18 03:56:49.646 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-07-18 03:56:49.646 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-07-18 03:56:49.646 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [application-test.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-18 03:56:49.647 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-07-18 03:56:49.647 INFO  --- [main]  c.u.j.EncryptablePropertySourceConverter : Converting PropertySource logCorrelation [org.springframework.boot.actuate.autoconfigure.tracing.LogCorrelationEnvironmentPostProcessor$LogCorrelationPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-07-18 03:56:50.097 INFO  --- [main]  c.u.j.f.DefaultLazyPropertyFilter : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-07-18 03:56:50.114 INFO  --- [main]  c.u.j.r.DefaultLazyPropertyResolver : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-07-18 03:56:50.117 INFO  --- [main]  c.u.j.d.DefaultLazyPropertyDetector : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-07-18 03:56:50.219 INFO  --- [main]  com.zaxxer.hikari.HikariDataSource : cache-connection-pool - Starting...
2025-07-18 03:56:50.575 INFO  --- [main]  com.zaxxer.hikari.pool.HikariPool : cache-connection-pool - Added connection conn0: url=jdbc:h2:mem:testdb user=SA
2025-07-18 03:56:50.579 INFO  --- [main]  com.zaxxer.hikari.HikariDataSource : cache-connection-pool - Start completed.
2025-07-18 03:56:50.710 INFO  --- [main]  o.f.c.i.license.VersionPrinter : Flyway Community Edition 9.22.3 by Redgate
2025-07-18 03:56:50.710 INFO  --- [main]  o.f.c.i.license.VersionPrinter : See release notes here: https://rd.gt/416ObMi
2025-07-18 03:56:50.710 INFO  --- [main]  o.f.c.i.license.VersionPrinter : 
2025-07-18 03:56:50.813 INFO  --- [main]  org.flywaydb.core.FlywayExecutor : Database: jdbc:h2:mem:testdb (H2 2.2)
2025-07-18 03:56:50.822 WARN  --- [main]  o.f.c.i.database.base.Database : Flyway upgrade recommended: H2 2.2.224 is newer than this version of Flyway and support has not been tested. The latest supported version of H2 is 2.2.220.
2025-07-18 03:56:50.869 INFO  --- [main]  o.f.c.i.s.JdbcTableSchemaHistory : Schema history table "PUBLIC"."flyway_schema_history" does not exist yet
2025-07-18 03:56:50.874 INFO  --- [main]  o.f.core.internal.command.DbValidate : Successfully validated 12 migrations (execution time 00:00.033s)
2025-07-18 03:56:50.884 INFO  --- [main]  o.f.c.i.s.JdbcTableSchemaHistory : Creating Schema History table "PUBLIC"."flyway_schema_history" ...
2025-07-18 03:56:50.918 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Current version of schema "PUBLIC": << Empty Schema >>
2025-07-18 03:56:50.924 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.01.01.0000 - h2 base"
2025-07-18 03:56:50.937 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.03.30.1725 - initialize"
2025-07-18 03:56:50.946 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.03.30.1726 - cache missing columns"
2025-07-18 03:56:50.970 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.03.30.1727 - cache type"
2025-07-18 03:56:50.983 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.08.02.1730 - cache component group"
2025-07-18 03:56:51.007 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.08.02.1731 - cache drop constraint"
2025-07-18 03:56:51.014 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.08.02.1735 - cache add constraint"
2025-07-18 03:56:51.025 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.08.02.1738 - kafka offsets"
2025-07-18 03:56:51.033 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.08.11.1131 - cache add constraint"
2025-07-18 03:56:51.041 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.09.27.0934 - batch"
2025-07-18 03:56:51.051 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.11.03.1144 - batch"
2025-07-18 03:56:51.063 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Migrating schema "PUBLIC" to version "2022.11.03.1252 - batch patch"
2025-07-18 03:56:51.072 INFO  --- [main]  o.f.core.internal.command.DbMigrate : Successfully applied 12 migrations to schema "PUBLIC", now at version v2022.11.03.1252 (execution time 00:00.073s)
2025-07-18 03:56:51.240 INFO  --- [main]  o.h.jpa.internal.util.LogHelper : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-07-18 03:56:51.353 INFO  --- [main]  org.hibernate.Version : HHH000412: Hibernate ORM core version 6.4.10.Final
2025-07-18 03:56:51.407 INFO  --- [main]  o.h.c.i.RegionFactoryInitiator : HHH000026: Second-level cache disabled
2025-07-18 03:56:52.017 INFO  --- [main]  o.s.o.j.p.SpringPersistenceUnitInfo : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-07-18 03:56:53.933 INFO  --- [main]  o.h.e.t.j.p.i.JtaPlatformInitiator : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-07-18 03:56:53.938 INFO  --- [main]  o.s.o.j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-07-18 03:56:54.624 INFO  --- [main]  o.s.d.j.r.query.QueryEnhancerFactory : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-07-18 03:56:56.538 WARN  --- [main]  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-18 03:56:57.739 INFO  --- [main]  o.s.b.t.m.w.SpringBootMockServletContext : Initializing Spring TestDispatcherServlet ''
2025-07-18 03:56:57.739 INFO  --- [main]  o.s.t.w.s.TestDispatcherServlet : Initializing Servlet ''
2025-07-18 03:56:57.754 INFO  --- [main]  o.s.b.a.e.web.EndpointLinksResolver : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-07-18 03:56:57.776 INFO  --- [main]  o.s.t.w.s.TestDispatcherServlet : Completed initialization in 37 ms
2025-07-18 03:56:58.169 INFO  --- [main]  o.s.i.endpoint.EventDrivenConsumer : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-07-18 03:56:58.169 INFO  --- [main]  o.s.i.c.PublishSubscribeChannel : Channel 'cache.errorChannel' has 1 subscriber(s).
2025-07-18 03:56:58.170 INFO  --- [main]  o.s.i.endpoint.EventDrivenConsumer : started bean '_org.springframework.integration.errorLogger'
2025-07-18 03:56:58.244 INFO  --- [main]  o.a.k.c.consumer.ConsumerConfig : ConsumerConfig values: 
	allow.auto.create.topics = true
	auto.commit.interval.ms = 5000
	auto.include.jmx.reporter = true
	auto.offset.reset = earliest
	bootstrap.servers = [kafka-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:443]
	check.crcs = true
	client.dns.lookup = use_all_dns_ips
	client.id = consumer-test-group-id-1
	client.rack = 
	connections.max.idle.ms = 540000
	default.api.timeout.ms = 60000
	enable.auto.commit = false
	enable.metrics.push = true
	exclude.internal.topics = true
	fetch.max.bytes = 52428800
	fetch.max.wait.ms = 500
	fetch.min.bytes = 1
	group.id = test-group-id
	group.instance.id = null
	group.protocol = classic
	group.remote.assignor = null
	heartbeat.interval.ms = 3000
	interceptor.classes = []
	internal.leave.group.on.close = true
	internal.throw.on.fetch.stable.offset.unsupported = false
	isolation.level = read_committed
	key.deserializer = class org.apache.kafka.common.serialization.StringDeserializer
	max.partition.fetch.bytes = 1048576
	max.poll.interval.ms = 300000
	max.poll.records = 10000
	metadata.max.age.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partition.assignment.strategy = [class org.apache.kafka.clients.consumer.RangeAssignor, class org.apache.kafka.clients.consumer.CooperativeStickyAssignor]
	receive.buffer.bytes = 65536
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 30000
	retry.backoff.max.ms = 1000
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = null
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = GSSAPI
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	session.timeout.ms = 45000
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = https
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	value.deserializer = class org.apache.kafka.common.serialization.StringDeserializer

2025-07-18 03:56:58.359 INFO  --- [main]  o.a.k.c.t.i.KafkaMetricsCollector : initializing Kafka metrics collector
2025-07-18 03:56:58.616 INFO  --- [main]  o.a.kafka.common.utils.AppInfoParser : Kafka version: 3.7.2
2025-07-18 03:56:58.616 INFO  --- [main]  o.a.kafka.common.utils.AppInfoParser : Kafka commitId: 79a8f2b5f44f9d5a
2025-07-18 03:56:58.616 INFO  --- [main]  o.a.kafka.common.utils.AppInfoParser : Kafka startTimeMs: 1752782218614
2025-07-18 03:56:58.625 INFO  --- [main]  o.a.k.c.c.i.LegacyKafkaConsumer : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Subscribed to topic(s): unity2-test-topic
2025-07-18 03:56:58.681 INFO  --- [main]  c.p.h.fasttrack.CacheControllerTest : Started CacheControllerTest in 13.154 seconds (process running for 15.137)
2025-07-18 03:57:01.144 INFO  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Node -1 disconnected.
2025-07-18 03:57:01.153 INFO  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Cancelled in-flight API_VERSIONS request with correlation id 1 due to node -1 being disconnected (elapsed time since creation: 2145ms, elapsed time since send: 2145ms, request timeout: 30000ms)
2025-07-18 03:57:01.153 WARN  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Bootstrap broker kafka-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:443 (id: -1 rack: null) disconnected
2025-07-18 03:57:01.372 INFO  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Node -1 disconnected.
2025-07-18 03:57:01.372 INFO  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Cancelled in-flight API_VERSIONS request with correlation id 2 due to node -1 being disconnected (elapsed time since creation: 87ms, elapsed time since send: 87ms, request timeout: 30000ms)
2025-07-18 03:57:01.372 WARN  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Bootstrap broker kafka-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:443 (id: -1 rack: null) disconnected
2025-07-18 03:57:01.470 DEBUG --- [main]  c.p.h.fasttrack.service.CacheService : putCache: CacheCompositeKeyDTO(group=test-group, component=test-component, key=test-key, batch=test-batch) CacheUpdateDTO(value={stringField=stringValue, integerField=1024, mapField={mapKey2=mapValue2, mapKey1=mapValue1, mapKey3=mapValue3}}, type=NORMAL, operation=SET)
2025-07-18 03:57:01.687 INFO  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Node -1 disconnected.
2025-07-18 03:57:01.687 INFO  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Cancelled in-flight API_VERSIONS request with correlation id 3 due to node -1 being disconnected (elapsed time since creation: 193ms, elapsed time since send: 193ms, request timeout: 30000ms)
2025-07-18 03:57:01.688 WARN  --- [concurrentMessageListenerContainer-0-C-1]  o.apache.kafka.clients.NetworkClient : [Consumer clientId=consumer-test-group-id-1, groupId=test-group-id] Bootstrap broker kafka-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:443 (id: -1 rack: null) disconnected
]]></system-out>
    <system-err><![CDATA[Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
Jul 18, 2025 3:56:44 AM org.junit.platform.launcher.core.LauncherConfigurationParameters loadClasspathResource
WARNING: Discovered 2 'junit-platform.properties' configuration files in the classpath; only the first will be used.
SLF4J(W): Class path contains multiple SLF4J providers.
SLF4J(W): Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@44040454]
SLF4J(W): Found provider [org.slf4j.simple.SimpleServiceProvider@65fe9e33]
SLF4J(W): See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J(I): Actual provider is of type [ch.qos.logback.classic.spi.LogbackServiceProvider@44040454]
WARNING: A Java agent has been loaded dynamically (C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar)
WARNING: If a serviceability tool is in use, please run with -XX:+EnableDynamicAgentLoading to hide this warning
WARNING: If a serviceability tool is not in use, please run with -Djdk.instrument.traceUsage for more information
WARNING: Dynamic loading of agents will be disallowed by default in a future release
]]></system-err>
  </testcase>
  <testcase name="shouldClearAllCacheSuccess" classname="com.poc.hss.fasttrack.CacheControllerTest" time="0.029">
    <system-out><![CDATA[2025-07-18 03:57:01.863 DEBUG --- [main]  c.p.h.fasttrack.service.CacheService : putCache: CacheCompositeKeyDTO(group=test-group, component=test-component, key=test-key, batch=test-batch) CacheUpdateDTO(value={stringField=stringValue, integerField=1024, mapField={mapKey2=mapValue2, mapKey1=mapValue1, mapKey3=mapValue3}}, type=NORMAL, operation=SET)
]]></system-out>
  </testcase>
  <testcase name="shouldObjectCacheSucceed" classname="com.poc.hss.fasttrack.CacheControllerTest" time="0.113">
    <system-out><![CDATA[2025-07-18 03:57:01.892 DEBUG --- [main]  c.p.h.fasttrack.service.CacheService : putCache: CacheCompositeKeyDTO(group=test-group, component=test-component, key=test-key, batch=test-batch) CacheUpdateDTO(value={stringField=stringValue, integerField=1024, mapField={mapKey2=mapValue2, mapKey1=mapValue1, mapKey3=mapValue3}}, type=NORMAL, operation=SET)
2025-07-18 03:57:01.944 DEBUG --- [main]  c.p.h.fasttrack.service.CacheService : putCache: CacheCompositeKeyDTO(group=test-group, component=test-component, key=test-key, batch=test-batch) CacheUpdateDTO(value={stringField=updatedValue, integerField=30, mapField={mapKey2=mapValue2, mapKey1=mapValue1, mapKey3=mapValue3}}, type=NORMAL, operation=SET)
]]></system-out>
  </testcase>
</testsuite>