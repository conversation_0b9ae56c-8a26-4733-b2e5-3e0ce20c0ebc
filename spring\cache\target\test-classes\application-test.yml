spring:
  flyway:
    locations: classpath:db/migration/h2,classpath:db/migration/common
  datasource:
    url: jdbc:h2:mem:testdb;CASE_INSENSITIVE_IDENTIFIERS=true
    username: sa
    password:
    initialization-mode: never
kafkaConfig:
  bootstrap.servers: kafka-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc:443
  group.id: test-group-id
  auto.offset.reset: earliest
cache:
  topic: unity2-test-topic
logging:
  level:
    com.poc.hss.fasttrack: DEBUG

kubernetes:
  mock:
    enabled: true
