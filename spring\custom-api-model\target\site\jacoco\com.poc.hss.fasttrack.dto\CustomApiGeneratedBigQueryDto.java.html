<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomApiGeneratedBigQueryDto.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_source">CustomApiGeneratedBigQueryDto.java</span></div><h1>CustomApiGeneratedBigQueryDto.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dto;

import com.google.cloud.bigquery.QueryParameterValue;
import com.poc.hss.fasttrack.jdbc.sqlbuilder.JdbcSQLBuilder;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

<span class="pc bpc" id="L10" title="21 of 38 branches missed.">@Data</span>
<span class="pc" id="L11">@Builder</span>
public class CustomApiGeneratedBigQueryDto {
<span class="fc" id="L13">    private JdbcSQLBuilder sqlBuilder;</span>
<span class="fc" id="L14">    private String sql;</span>
<span class="fc" id="L15">    private String dataSetName;</span>
<span class="fc" id="L16">    Map&lt;String, QueryParameterValue&gt; queryParameterValueMap;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>