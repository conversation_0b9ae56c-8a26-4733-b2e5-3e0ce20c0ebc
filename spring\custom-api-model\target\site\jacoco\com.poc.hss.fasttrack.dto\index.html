<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">106 of 710</td><td class="ctr2">85%</td><td class="bar">47 of 94</td><td class="ctr2">50%</td><td class="ctr1">43</td><td class="ctr2">115</td><td class="ctr1">0</td><td class="ctr2">19</td><td class="ctr1">4</td><td class="ctr2">68</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a3"><a href="CustomApiGeneratedBigQueryDto.html" class="el_class">CustomApiGeneratedBigQueryDto</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="30" alt="30"/><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="183" alt="183"/></td><td class="ctr2" id="c5">85%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="17" alt="17"/></td><td class="ctr2" id="e3">44%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">33</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">6</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k0">14</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a5"><a href="CustomApiGeneratedQueryDto.html" class="el_class">CustomApiGeneratedQueryDto</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="20" alt="20"/><img src="../jacoco-resources/greenbar.gif" width="92" height="10" title="164" alt="164"/></td><td class="ctr2" id="c3">89%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="13" alt="13"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f1">11</td><td class="ctr2" id="g1">27</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k1">14</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a6"><a href="CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder.html" class="el_class">CustomApiGeneratedQueryDto.CustomApiGeneratedQueryDtoBuilder</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="35" alt="35"/></td><td class="ctr2" id="c7">72%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k4">7</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder.html" class="el_class">CustomApiGeneratedBigQueryDto.CustomApiGeneratedBigQueryDtoBuilder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="35" alt="35"/></td><td class="ctr2" id="c6">74%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">7</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k5">7</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a0"><a href="BigQueryWhereClauseParsingDTO.html" class="el_class">BigQueryWhereClauseParsingDTO</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="81" alt="81"/></td><td class="ctr2" id="c4">88%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">56%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">16</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k2">8</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a7"><a href="WhereClauseParsingDTO.html" class="el_class">WhereClauseParsingDTO</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="9" alt="9"/><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="74" alt="74"/></td><td class="ctr2" id="c2">89%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">57%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g3">15</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k3">8</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a1"><a href="BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilder.html" class="el_class">BigQueryWhereClauseParsingDTO.BigQueryWhereClauseParsingDTOBuilder</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c9">56%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k6">3</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a8"><a href="WhereClauseParsingDTO$WhereClauseParsingDTOBuilder.html" class="el_class">WhereClauseParsingDTO.WhereClauseParsingDTOBuilder</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="9" alt="9"/></td><td class="ctr2" id="c8">69%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k7">3</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a2"><a href="BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilderImpl.html" class="el_class">BigQueryWhereClauseParsingDTO.BigQueryWhereClauseParsingDTOBuilderImpl</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">2</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a9"><a href="WhereClauseParsingDTO$WhereClauseParsingDTOBuilderImpl.html" class="el_class">WhereClauseParsingDTO.WhereClauseParsingDTOBuilderImpl</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">2</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>