<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">106 of 710</td><td class="ctr2">85%</td><td class="bar">47 of 94</td><td class="ctr2">50%</td><td class="ctr1">43</td><td class="ctr2">115</td><td class="ctr1">0</td><td class="ctr2">19</td><td class="ctr1">4</td><td class="ctr2">68</td><td class="ctr1">0</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a1"><a href="CustomApiGeneratedBigQueryDto.java.html" class="el_source">CustomApiGeneratedBigQueryDto.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="42" alt="42"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="218" alt="218"/></td><td class="ctr2" id="c3">83%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="21" alt="21"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="17" alt="17"/></td><td class="ctr2" id="e3">44%</td><td class="ctr1" id="f0">18</td><td class="ctr2" id="g0">40</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">6</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">21</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m2">2</td></tr><tr><td id="a2"><a href="CustomApiGeneratedQueryDto.java.html" class="el_source">CustomApiGeneratedQueryDto.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="33" alt="33"/><img src="../jacoco-resources/greenbar.gif" width="91" height="10" title="199" alt="199"/></td><td class="ctr2" id="c1">85%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="13" alt="13"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g1">34</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m3">2</td></tr><tr><td id="a0"><a href="BigQueryWhereClauseParsingDTO.java.html" class="el_source">BigQueryWhereClauseParsingDTO.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="97" alt="97"/></td><td class="ctr2" id="c2">84%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">56%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">21</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a3"><a href="WhereClauseParsingDTO.java.html" class="el_source">WhereClauseParsingDTO.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="41" height="10" title="90" alt="90"/></td><td class="ctr2" id="c0">87%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">57%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">20</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">13</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m1">3</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>