<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JoinOperator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.enums</a> &gt; <span class="el_source">JoinOperator.java</span></div><h1>JoinOperator.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.enums;

<span class="fc" id="L3">public enum JoinOperator {</span>

<span class="fc" id="L5">    INNER_JOIN(&quot;INNER_JOIN&quot;, &quot;INNER JOIN&quot;),</span>
<span class="fc" id="L6">    LEFT_JOIN(&quot;LEFT_JOIN&quot;, &quot;LEFT JOIN&quot;),</span>
<span class="fc" id="L7">    RIGHT_JOIN(&quot;RIGHT_JOIN&quot;, &quot;RIGHT JOIN&quot;),</span>
<span class="fc" id="L8">    FULL_OUTER_JOIN(&quot;FULL_OUTER_JOIN&quot;, &quot;FULL OUTER JOIN&quot;);</span>

    private final String value;
    private final String sqlValue;

<span class="fc" id="L13">    JoinOperator(String value, String sqlValue) {</span>
<span class="fc" id="L14">        this.value = value;</span>
<span class="fc" id="L15">        this.sqlValue = sqlValue;</span>
<span class="fc" id="L16">    }</span>

    public static JoinOperator fromValue(String text) {
<span class="fc bfc" id="L19" title="All 2 branches covered.">        for (JoinOperator j : JoinOperator.values()) {</span>
<span class="fc bfc" id="L20" title="All 2 branches covered.">            if (String.valueOf(j.value).equals(text)) {</span>
<span class="fc" id="L21">                return j;</span>
            }
        }
<span class="fc" id="L24">        return null;</span>
    }

    public String getValue() {
<span class="fc" id="L28">        return value;</span>
    }

    public String getSqlValue() {
<span class="fc" id="L32">        return sqlValue;</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>