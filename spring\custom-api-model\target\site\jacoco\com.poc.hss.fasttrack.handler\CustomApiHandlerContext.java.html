<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomApiHandlerContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.handler</a> &gt; <span class="el_source">CustomApiHandlerContext.java</span></div><h1>CustomApiHandlerContext.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.handler;

import io.micrometer.core.instrument.MeterRegistry;
import lombok.Builder;
import lombok.Getter;
import org.springframework.web.client.RestTemplate;
import org.springframework.core.env.Environment;
import org.togglz.core.manager.FeatureManager;

import java.util.List;
import java.util.Map;

@Getter
<span class="pc" id="L14">@Builder</span>
public class CustomApiHandlerContext {
<span class="fc" id="L16">    private final List&lt;String&gt; authorizationScopes;</span>
<span class="fc" id="L17">    private final Map&lt;String, Object&gt; queryParams;</span>
<span class="fc" id="L18">    private final Map&lt;String, String&gt; headers;</span>
<span class="fc" id="L19">    private final RestTemplate restTemplate;</span>
<span class="fc" id="L20">    private final Integer page;</span>
<span class="fc" id="L21">    private final Environment env;</span>
<span class="fc" id="L22">    private final MeterRegistry meterRegistry;</span>
<span class="fc" id="L23">    private final FeatureManager togglzManager;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>