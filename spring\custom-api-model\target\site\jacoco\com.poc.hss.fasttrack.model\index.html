<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <span class="el_package">com.poc.hss.fasttrack.model</span></div><h1>com.poc.hss.fasttrack.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">77 of 632</td><td class="ctr2">87%</td><td class="bar">35 of 78</td><td class="ctr2">55%</td><td class="ctr1">32</td><td class="ctr2">100</td><td class="ctr1">0</td><td class="ctr2">20</td><td class="ctr1">2</td><td class="ctr2">61</td><td class="ctr1">0</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a2"><a href="CustomQueryResponse.html" class="el_class">CustomQueryResponse</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="111" alt="111"/></td><td class="ctr2" id="c2">87%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="11" alt="11"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g1">21</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k1">10</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CustomQueryRequest.html" class="el_class">CustomQueryRequest</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="100" alt="100"/></td><td class="ctr2" id="c3">86%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="11" alt="11"/></td><td class="ctr2" id="e3">50%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g2">20</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">9</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a5"><a href="CustomQueryResponse$PageInfo$PageInfoBuilder.html" class="el_class">CustomQueryResponse.PageInfo.PageInfoBuilder</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="29" height="10" title="49" alt="49"/></td><td class="ctr2" id="c4">77%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">9</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k3">9</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="CustomQueryResponse$PageInfo.html" class="el_class">CustomQueryResponse.PageInfo</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="112" height="10" title="186" alt="186"/></td><td class="ctr2" id="c0">93%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">66%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g0">28</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i0">10</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k0">19</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a0"><a href="CustomHttpStatusResponse.html" class="el_class">CustomHttpStatusResponse</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="53" height="10" title="88" alt="88"/></td><td class="ctr2" id="c1">88%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">56%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">17</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">9</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a3"><a href="CustomQueryResponse$CustomQueryResponseBuilder.html" class="el_class">CustomQueryResponse.CustomQueryResponseBuilder</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="21" alt="21"/></td><td class="ctr2" id="c5">72%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k5">5</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>