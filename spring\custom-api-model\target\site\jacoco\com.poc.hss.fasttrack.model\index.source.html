<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">custom-api-model</a> &gt; <span class="el_package">com.poc.hss.fasttrack.model</span></div><h1>com.poc.hss.fasttrack.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">77 of 632</td><td class="ctr2">87%</td><td class="bar">35 of 78</td><td class="ctr2">55%</td><td class="ctr1">32</td><td class="ctr2">100</td><td class="ctr1">0</td><td class="ctr2">20</td><td class="ctr1">2</td><td class="ctr2">61</td><td class="ctr1">0</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a2"><a href="CustomQueryResponse.java.html" class="el_source">CustomQueryResponse.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="50" alt="50"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="367" alt="367"/></td><td class="ctr2" id="c1">88%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="69" height="10" title="23" alt="23"/></td><td class="ctr2" id="e0">57%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">63</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">14</td><td class="ctr1" id="j0">2</td><td class="ctr2" id="k0">43</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">4</td></tr><tr><td id="a1"><a href="CustomQueryRequest.java.html" class="el_source">CustomQueryRequest.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="28" height="10" title="100" alt="100"/></td><td class="ctr2" id="c2">86%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="11" alt="11"/></td><td class="ctr2" id="e2">50%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">20</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">3</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="CustomHttpStatusResponse.java.html" class="el_source">CustomHttpStatusResponse.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="88" alt="88"/></td><td class="ctr2" id="c0">88%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">56%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">17</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">3</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">9</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>