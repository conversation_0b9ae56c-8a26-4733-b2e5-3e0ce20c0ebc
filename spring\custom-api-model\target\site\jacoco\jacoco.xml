<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="custom-api-model"><sessioninfo id="H344L0L63UFKL6Z-f9016c0c" start="1752767481054" dump="1752767484568"/><package name="com/poc/hss/fasttrack/dao"><class name="com/poc/hss/fasttrack/dao/CustomApiDao" sourcefilename="CustomApiDao.java"/><sourcefile name="CustomApiDao.java"/></package><package name="com/poc/hss/fasttrack/model"><class name="com/poc/hss/fasttrack/model/CustomHttpStatusResponse" sourcefilename="CustomHttpStatusResponse.java"><method name="&lt;init&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getHttpStatus" desc="()I" line="10"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getBody" desc="()Ljava/lang/Object;" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setHttpStatus" desc="(I)V" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setBody" desc="(Ljava/lang/Object;)V" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="9" covered="36"/><counter type="BRANCH" missed="6" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="5" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="7"><counter type="INSTRUCTION" missed="2" covered="25"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="7"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="11" covered="88"/><counter type="BRANCH" missed="7" covered="9"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="6" covered="11"/><counter type="METHOD" missed="0" covered="9"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder" sourcefilename="CustomQueryResponse.java"><method name="&lt;init&gt;" desc="()V" line="19"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="nextPageIdx" desc="(I)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="prePageIdx" desc="(I)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="currentPageIdx" desc="(I)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="pageSize" desc="(I)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="totalNumberOfRecords" desc="(I)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="totalPage" desc="(I)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo;" line="19"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="19"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="14" covered="49"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="8"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/CustomQueryRequest" sourcefilename="CustomQueryRequest.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPage" desc="()Ljava/lang/Integer;" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getParameters" desc="()Ljava/util/Map;" line="12"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setPage" desc="(Ljava/lang/Integer;)V" line="8"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setParameters" desc="(Ljava/util/Map;)V" line="8"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="12" covered="43"/><counter type="BRANCH" missed="9" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="7" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="8"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="8"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="16" covered="100"/><counter type="BRANCH" missed="11" covered="11"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="9" covered="11"/><counter type="METHOD" missed="0" covered="9"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/CustomQueryResponse" sourcefilename="CustomQueryResponse.java"><method name="&lt;init&gt;" desc="(Ljava/util/List;Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/model/CustomQueryResponse$CustomQueryResponseBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getResultMessages" desc="()Ljava/util/List;" line="15"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPageInfo" desc="()Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo;" line="16"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setResultMessages" desc="(Ljava/util/List;)V" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setPageInfo" desc="(Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo;)V" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="11"><counter type="INSTRUCTION" missed="12" covered="43"/><counter type="BRANCH" missed="9" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="7" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="11"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="16" covered="111"/><counter type="BRANCH" missed="11" covered="11"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="9" covered="12"/><counter type="METHOD" missed="0" covered="10"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/CustomQueryResponse$CustomQueryResponseBuilder" sourcefilename="CustomQueryResponse.java"><method name="&lt;init&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="resultMessages" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$CustomQueryResponseBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="pageInfo" desc="(Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo;)Lcom/poc/hss/fasttrack/model/CustomQueryResponse$CustomQueryResponseBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/model/CustomQueryResponse;" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo" sourcefilename="CustomQueryResponse.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getNextPageIdx" desc="()I" line="23"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPrePageIdx" desc="()I" line="24"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getCurrentPageIdx" desc="()I" line="25"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPageSize" desc="()I" line="26"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getTotalNumberOfRecords" desc="()I" line="27"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getTotalPage" desc="()I" line="28"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setNextPageIdx" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setPrePageIdx" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setCurrentPageIdx" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setPageSize" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setTotalNumberOfRecords" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setTotalPage" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="18"><counter type="INSTRUCTION" missed="12" covered="51"/><counter type="BRANCH" missed="6" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="6" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="18"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="18"><counter type="INSTRUCTION" missed="0" covered="48"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="18"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(IIIIII)V" line="20"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="21"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="12" covered="186"/><counter type="BRANCH" missed="6" covered="12"/><counter type="LINE" missed="0" covered="10"/><counter type="COMPLEXITY" missed="6" covered="22"/><counter type="METHOD" missed="0" covered="19"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="CustomQueryRequest.java"><line nr="8" mi="16" ci="94" mb="11" cb="11"/><line nr="11" mi="0" ci="3" mb="0" cb="0"/><line nr="12" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="100"/><counter type="BRANCH" missed="11" covered="11"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="9" covered="11"/><counter type="METHOD" missed="0" covered="9"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CustomQueryResponse.java"><line nr="11" mi="16" ci="92" mb="11" cb="11"/><line nr="12" mi="8" ci="34" mb="0" cb="0"/><line nr="15" mi="0" ci="3" mb="0" cb="0"/><line nr="16" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="12" ci="140" mb="6" cb="12"/><line nr="19" mi="14" ci="53" mb="0" cb="0"/><line nr="20" mi="0" ci="21" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="0" ci="3" mb="0" cb="0"/><line nr="24" mi="0" ci="3" mb="0" cb="0"/><line nr="25" mi="0" ci="3" mb="0" cb="0"/><line nr="26" mi="0" ci="3" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="50" covered="367"/><counter type="BRANCH" missed="17" covered="23"/><counter type="LINE" missed="0" covered="14"/><counter type="COMPLEXITY" missed="17" covered="46"/><counter type="METHOD" missed="2" covered="41"/><counter type="CLASS" missed="0" covered="4"/></sourcefile><sourcefile name="CustomHttpStatusResponse.java"><line nr="7" mi="11" ci="82" mb="7" cb="9"/><line nr="10" mi="0" ci="3" mb="0" cb="0"/><line nr="11" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="11" covered="88"/><counter type="BRANCH" missed="7" covered="9"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="6" covered="11"/><counter type="METHOD" missed="0" covered="9"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="77" covered="555"/><counter type="BRANCH" missed="35" covered="43"/><counter type="LINE" missed="0" covered="20"/><counter type="COMPLEXITY" missed="32" covered="68"/><counter type="METHOD" missed="2" covered="59"/><counter type="CLASS" missed="0" covered="6"/></package><package name="com/poc/hss/fasttrack/handler"><class name="com/poc/hss/fasttrack/handler/CustomApiHandlerContext" sourcefilename="CustomApiHandlerContext.java"><method name="&lt;init&gt;" desc="(Ljava/util/List;Ljava/util/Map;Ljava/util/Map;Lorg/springframework/web/client/RestTemplate;Ljava/lang/Integer;Lorg/springframework/core/env/Environment;Lio/micrometer/core/instrument/MeterRegistry;Lorg/togglz/core/manager/FeatureManager;)V" line="14"><counter type="INSTRUCTION" missed="0" covered="27"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getAuthorizationScopes" desc="()Ljava/util/List;" line="16"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getQueryParams" desc="()Ljava/util/Map;" line="17"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getHeaders" desc="()Ljava/util/Map;" line="18"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getRestTemplate" desc="()Lorg/springframework/web/client/RestTemplate;" line="19"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPage" desc="()Ljava/lang/Integer;" line="20"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getEnv" desc="()Lorg/springframework/core/env/Environment;" line="21"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getMeterRegistry" desc="()Lio/micrometer/core/instrument/MeterRegistry;" line="22"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getTogglzManager" desc="()Lorg/togglz/core/manager/FeatureManager;" line="23"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="55"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="0" covered="10"/><counter type="METHOD" missed="0" covered="10"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder" sourcefilename="CustomApiHandlerContext.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="authorizationScopes" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="queryParams" desc="(Ljava/util/Map;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="headers" desc="(Ljava/util/Map;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="restTemplate" desc="(Lorg/springframework/web/client/RestTemplate;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="page" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="env" desc="(Lorg/springframework/core/env/Environment;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="meterRegistry" desc="(Lio/micrometer/core/instrument/MeterRegistry;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="togglzManager" desc="(Lorg/togglz/core/manager/FeatureManager;)Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext$CustomApiHandlerContextBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/handler/CustomApiHandlerContext;" line="14"><counter type="INSTRUCTION" missed="0" covered="20"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="25" covered="63"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="10"/><counter type="METHOD" missed="1" covered="10"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="CustomApiHandlerContext.java"><line nr="14" mi="25" ci="94" mb="0" cb="0"/><line nr="16" mi="0" ci="3" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="0" ci="3" mb="0" cb="0"/><line nr="19" mi="0" ci="3" mb="0" cb="0"/><line nr="20" mi="0" ci="3" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="25" covered="118"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="1" covered="20"/><counter type="METHOD" missed="1" covered="20"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="25" covered="118"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="1" covered="20"/><counter type="METHOD" missed="1" covered="20"/><counter type="CLASS" missed="0" covered="2"/></package><package name="com/poc/hss/fasttrack/enums"><class name="com/poc/hss/fasttrack/enums/AggregateFunction" sourcefilename="AggregateFunction.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/AggregateFunction;" line="17"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getValue" desc="()Ljava/lang/String;" line="26"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="0" covered="31"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="68"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="13"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/enums/JoinOperator" sourcefilename="JoinOperator.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V" line="13"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/JoinOperator;" line="19"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getValue" desc="()Ljava/lang/String;" line="28"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getSqlValue" desc="()Ljava/lang/String;" line="32"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="0" covered="35"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="78"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="7"/><counter type="METHOD" missed="0" covered="5"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="JoinOperator.java"><line nr="3" mi="0" ci="3" mb="0" cb="0"/><line nr="5" mi="0" ci="8" mb="0" cb="0"/><line nr="6" mi="0" ci="8" mb="0" cb="0"/><line nr="7" mi="0" ci="8" mb="0" cb="0"/><line nr="8" mi="0" ci="8" mb="0" cb="0"/><line nr="13" mi="0" ci="4" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="3" mb="0" cb="0"/><line nr="16" mi="0" ci="1" mb="0" cb="0"/><line nr="19" mi="0" ci="16" mb="0" cb="2"/><line nr="20" mi="0" ci="6" mb="0" cb="2"/><line nr="21" mi="0" ci="2" mb="0" cb="0"/><line nr="24" mi="0" ci="2" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="0" cb="0"/><line nr="32" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="78"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="7"/><counter type="METHOD" missed="0" covered="5"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="AggregateFunction.java"><line nr="3" mi="0" ci="3" mb="0" cb="0"/><line nr="5" mi="0" ci="7" mb="0" cb="0"/><line nr="6" mi="0" ci="7" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="12" mi="0" ci="4" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="1" mb="0" cb="0"/><line nr="17" mi="0" ci="16" mb="0" cb="2"/><line nr="18" mi="0" ci="6" mb="0" cb="2"/><line nr="19" mi="0" ci="2" mb="0" cb="0"/><line nr="22" mi="0" ci="2" mb="0" cb="0"/><line nr="26" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="68"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="13"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="0" covered="146"/><counter type="BRANCH" missed="0" covered="8"/><counter type="LINE" missed="0" covered="28"/><counter type="COMPLEXITY" missed="0" covered="13"/><counter type="METHOD" missed="0" covered="9"/><counter type="CLASS" missed="0" covered="2"/></package><package name="com/poc/hss/fasttrack/dto"><class name="com/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder" sourcefilename="CustomApiGeneratedBigQueryDto.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sqlBuilder" desc="(Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sql" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="dataSetName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="queryParameterValueMap" desc="(Ljava/util/Map;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto;" line="11"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="12" covered="35"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="6"/><counter type="METHOD" missed="1" covered="6"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO" sourcefilename="BigQueryWhereClauseParsingDTO.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilder;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getQueryParameterValueMap" desc="()Ljava/util/Map;" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setQueryParameterValueMap" desc="(Ljava/util/Map;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="11"><counter type="INSTRUCTION" missed="9" covered="35"/><counter type="BRANCH" missed="6" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="5" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="11"><counter type="INSTRUCTION" missed="2" covered="19"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="11" covered="81"/><counter type="BRANCH" missed="7" covered="9"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="6" covered="10"/><counter type="METHOD" missed="0" covered="8"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilderImpl" sourcefilename="BigQueryWhereClauseParsingDTO.java"><method name="self" desc="()Lcom/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilderImpl;" line="12"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO;" line="12"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto" sourcefilename="CustomApiGeneratedBigQueryDto.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V" line="11"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedBigQueryDto$CustomApiGeneratedBigQueryDtoBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getSqlBuilder" desc="()Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getSql" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getDataSetName" desc="()Ljava/lang/String;" line="15"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getQueryParameterValueMap" desc="()Ljava/util/Map;" line="16"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setSqlBuilder" desc="(Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setSql" desc="(Ljava/lang/String;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setDataSetName" desc="(Ljava/lang/String;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setQueryParameterValueMap" desc="(Ljava/util/Map;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="10"><counter type="INSTRUCTION" missed="22" covered="67"/><counter type="BRANCH" missed="17" covered="13"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="13" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="10"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="10"><counter type="INSTRUCTION" missed="8" covered="54"/><counter type="BRANCH" missed="4" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="4" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="30" covered="183"/><counter type="BRANCH" missed="21" covered="17"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="17" covered="16"/><counter type="METHOD" missed="0" covered="14"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/WhereClauseParsingDTO$WhereClauseParsingDTOBuilderImpl" sourcefilename="WhereClauseParsingDTO.java"><method name="self" desc="()Lcom/poc/hss/fasttrack/dto/WhereClauseParsingDTO$WhereClauseParsingDTOBuilderImpl;" line="7"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/WhereClauseParsingDTO;" line="7"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto" sourcefilename="CustomApiGeneratedQueryDto.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;Ljava/lang/String;[Ljava/lang/Object;[I)V" line="8"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder;" line="8"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getSqlBuilder" desc="()Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="10"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getSql" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getArgs" desc="()[Ljava/lang/Object;" line="12"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getArgTypes" desc="()[I" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setSqlBuilder" desc="(Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;)V" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setSql" desc="(Ljava/lang/String;)V" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setArgs" desc="([Ljava/lang/Object;)V" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setArgTypes" desc="([I)V" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="16" covered="55"/><counter type="BRANCH" missed="11" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="9" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="7"><counter type="INSTRUCTION" missed="4" covered="46"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="7"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="20" covered="164"/><counter type="BRANCH" missed="13" covered="13"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="11" covered="16"/><counter type="METHOD" missed="0" covered="14"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder" sourcefilename="CustomApiGeneratedQueryDto.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sqlBuilder" desc="(Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder;" line="8"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sql" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder;" line="8"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="args" desc="([Ljava/lang/Object;)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder;" line="8"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="argTypes" desc="([I)Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto$CustomApiGeneratedQueryDtoBuilder;" line="8"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/CustomApiGeneratedQueryDto;" line="8"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="8"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="13" covered="35"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="6"/><counter type="METHOD" missed="1" covered="6"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilder" sourcefilename="BigQueryWhereClauseParsingDTO.java"><method name="&lt;init&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="queryParameterValueMap" desc="(Ljava/util/Map;)Lcom/poc/hss/fasttrack/dto/BigQueryWhereClauseParsingDTO$BigQueryWhereClauseParsingDTOBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="7" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/WhereClauseParsingDTO$WhereClauseParsingDTOBuilder" sourcefilename="WhereClauseParsingDTO.java"><method name="&lt;init&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="updatedSql" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/WhereClauseParsingDTO$WhereClauseParsingDTOBuilder;" line="7"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="7"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="4" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/WhereClauseParsingDTO" sourcefilename="WhereClauseParsingDTO.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/dto/WhereClauseParsingDTO$WhereClauseParsingDTOBuilder;)V" line="7"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/WhereClauseParsingDTO$WhereClauseParsingDTOBuilder;" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getUpdatedSql" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setUpdatedSql" desc="(Ljava/lang/String;)V" line="6"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="6"><counter type="INSTRUCTION" missed="7" covered="31"/><counter type="BRANCH" missed="5" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="4" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="6"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="6"><counter type="INSTRUCTION" missed="2" covered="18"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="6"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="9" covered="74"/><counter type="BRANCH" missed="6" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="5" covered="10"/><counter type="METHOD" missed="0" covered="8"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="BigQueryWhereClauseParsingDTO.java"><line nr="10" mi="0" ci="9" mb="0" cb="0"/><line nr="11" mi="11" ci="57" mb="7" cb="9"/><line nr="12" mi="7" ci="28" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="18" covered="97"/><counter type="BRANCH" missed="7" covered="9"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="7" covered="14"/><counter type="METHOD" missed="1" covered="12"/><counter type="CLASS" missed="0" covered="3"/></sourcefile><sourcefile name="WhereClauseParsingDTO.java"><line nr="6" mi="9" ci="60" mb="6" cb="8"/><line nr="7" mi="4" ci="27" mb="0" cb="0"/><line nr="9" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="13" covered="90"/><counter type="BRANCH" missed="6" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="6" covered="14"/><counter type="METHOD" missed="1" covered="12"/><counter type="CLASS" missed="0" covered="3"/></sourcefile><sourcefile name="CustomApiGeneratedQueryDto.java"><line nr="7" mi="20" ci="133" mb="13" cb="13"/><line nr="8" mi="13" ci="54" mb="0" cb="0"/><line nr="10" mi="0" ci="3" mb="0" cb="0"/><line nr="11" mi="0" ci="3" mb="0" cb="0"/><line nr="12" mi="0" ci="3" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="33" covered="199"/><counter type="BRANCH" missed="13" covered="13"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="12" covered="22"/><counter type="METHOD" missed="1" covered="20"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><sourcefile name="CustomApiGeneratedBigQueryDto.java"><line nr="10" mi="30" ci="152" mb="21" cb="17"/><line nr="11" mi="12" ci="54" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="3" mb="0" cb="0"/><line nr="16" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="42" covered="218"/><counter type="BRANCH" missed="21" covered="17"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="18" covered="22"/><counter type="METHOD" missed="1" covered="20"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="106" covered="604"/><counter type="BRANCH" missed="47" covered="47"/><counter type="LINE" missed="0" covered="19"/><counter type="COMPLEXITY" missed="43" covered="72"/><counter type="METHOD" missed="4" covered="64"/><counter type="CLASS" missed="0" covered="10"/></package><counter type="INSTRUCTION" missed="208" covered="1423"/><counter type="BRANCH" missed="82" covered="98"/><counter type="LINE" missed="0" covered="76"/><counter type="COMPLEXITY" missed="76" covered="173"/><counter type="METHOD" missed="7" covered="152"/><counter type="CLASS" missed="0" covered="20"/></report>