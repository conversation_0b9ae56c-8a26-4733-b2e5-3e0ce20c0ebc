<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.021" tests="8" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\eddiewtchoi\workspace\ide\git\spring\custom-api-model\target\test-classes;C:\eddiewtchoi\workspace\ide\git\spring\custom-api-model\target\classes;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\target\pipeline-common-core-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter\3.2.12\spring-boot-starter-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot\3.2.12\spring-boot-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.12\spring-boot-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.2.12\spring-boot-starter-logging-3.2.12.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-classic\1.5.16\logback-classic-1.5.16.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-core\1.5.16\logback-core-1.5.16.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\sandbox\maven\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\sandbox\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\sandbox\maven\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\sandbox\maven\repository\org\xmlunit\xmlunit-core\2.10.0\xmlunit-core-2.10.0.jar;C:\sandbox\maven\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\sandbox\maven\repository\io\vertx\vertx-core\4.5.14\vertx-core-4.5.14.jar;C:\sandbox\maven\repository\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler\4.1.119.Final\netty-handler-4.1.119.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler-proxy\4.1.115.Final\netty-handler-proxy-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-socks\4.1.115.Final\netty-codec-socks-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http\4.1.115.Final\netty-codec-http-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http2\4.1.115.Final\netty-codec-http2-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver-dns\4.1.115.Final\netty-resolver-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-dns\4.1.115.Final\netty-codec-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\sandbox\maven\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\sandbox\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-text\1.10.0\commons-text-1.10.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-aop\3.2.12\spring-boot-starter-aop-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-aop\6.1.15\spring-aop-6.1.15.jar;C:\sandbox\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-json\3.2.12\spring-boot-starter-json-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-web\6.1.15\spring-web-6.1.15.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.15.4\jackson-dataformat-xml-2.15.4.jar;C:\sandbox\maven\repository\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;C:\sandbox\maven\repository\com\fasterxml\woodstox\woodstox-core\6.5.1\woodstox-core-6.5.1.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.4\jackson-dataformat-yaml-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-properties\2.15.4\jackson-dataformat-properties-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-jaxb-annotations\2.15.4\jackson-module-jaxb-annotations-2.15.4.jar;C:\sandbox\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\sandbox\maven\repository\org\owasp\esapi\esapi\2.6.0.0\esapi-2.6.0.0.jar;C:\sandbox\maven\repository\xom\xom\1.3.9\xom-1.3.9.jar;C:\sandbox\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;C:\sandbox\maven\repository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\sandbox\maven\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\sandbox\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\sandbox\maven\repository\org\apache-extras\beanshell\bsh\2.0b6\bsh-2.0b6.jar;C:\sandbox\maven\repository\org\owasp\antisamy\antisamy\1.7.7\antisamy-1.7.7.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.5\httpcore5-h2-5.2.5.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5\5.2.5\httpcore5-5.2.5.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-css\1.18\batik-css-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-shared-resources\1.18\batik-shared-resources-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-util\1.18\batik-util-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-constants\1.18\batik-constants-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-i18n\1.18\batik-i18n-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\xmlgraphics-commons\2.10\xmlgraphics-commons-2.10.jar;C:\sandbox\maven\repository\org\htmlunit\neko-htmlunit\4.6.0\neko-htmlunit-4.6.0.jar;C:\sandbox\maven\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\sandbox\maven\repository\xml-apis\xml-apis-ext\1.3.04\xml-apis-ext-1.3.04.jar;C:\sandbox\maven\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\sandbox\maven\repository\net\logstash\logback\logstash-logback-encoder\6.1\logstash-logback-encoder-6.1.jar;C:\sandbox\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\sandbox\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\jaxb-runtime\2.3.1\jaxb-runtime-2.3.1.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\sandbox\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\sandbox\maven\repository\org\jvnet\staxex\stax-ex\2.1.0\stax-ex-2.1.0.jar;C:\sandbox\maven\repository\com\sun\xml\fastinfoset\FastInfoset\2.1.1\FastInfoset-2.1.1.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-bigquery\target\pipeline-common-bigquery-2.8.6-SNAPSHOT.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jdbc\target\pipeline-common-jdbc-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.12\spring-boot-starter-jdbc-3.2.12.jar;C:\sandbox\maven\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\sandbox\maven\repository\org\springframework\spring-jdbc\6.1.15\spring-jdbc-6.1.15.jar;C:\sandbox\maven\repository\org\postgresql\postgresql\42.4.5\postgresql-42.4.5.jar;C:\sandbox\maven\repository\com\oracle\database\jdbc\ojdbc8\********\ojdbc8-********.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-bigquery\2.36.0\google-cloud-bigquery-2.36.0.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-core\2.8.20\google-cloud-core-2.8.20.jar;C:\sandbox\maven\repository\com\google\protobuf\protobuf-java-util\3.21.7\protobuf-java-util-3.21.7.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-common-protos\2.9.6\proto-google-common-protos-2.9.6.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-iam-v1\1.6.2\proto-google-iam-v1-1.6.2.jar;C:\sandbox\maven\repository\com\google\auth\google-auth-library-credentials\1.11.0\google-auth-library-credentials-1.11.0.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-core-http\2.8.20\google-cloud-core-http-2.8.20.jar;C:\sandbox\maven\repository\com\google\api-client\google-api-client\2.0.0\google-api-client-2.0.0.jar;C:\sandbox\maven\repository\com\google\oauth-client\google-oauth-client\1.34.1\google-oauth-client-1.34.1.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client-apache-v2\1.42.2\google-http-client-apache-v2-1.42.2.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client-appengine\1.42.2\google-http-client-appengine-1.42.2.jar;C:\sandbox\maven\repository\com\google\api\gax-httpjson\0.104.2\gax-httpjson-0.104.2.jar;C:\sandbox\maven\repository\io\opencensus\opencensus-api\0.31.1\opencensus-api-0.31.1.jar;C:\sandbox\maven\repository\io\opencensus\opencensus-contrib-http-util\0.31.1\opencensus-contrib-http-util-0.31.1.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client-gson\1.42.2\google-http-client-gson-1.42.2.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client\1.42.2\google-http-client-1.42.2.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\sandbox\maven\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\sandbox\maven\repository\org\checkerframework\checker-compat-qual\2.5.6\checker-compat-qual-2.5.6.jar;C:\sandbox\maven\repository\org\checkerframework\checker-qual\3.40.0\checker-qual-3.40.0.jar;C:\sandbox\maven\repository\com\google\auth\google-auth-library-oauth2-http\1.11.0\google-auth-library-oauth2-http-1.11.0.jar;C:\sandbox\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\sandbox\maven\repository\com\google\apis\google-api-services-bigquery\v2-rev20231111-2.0.0\google-api-services-bigquery-v2-rev20231111-2.0.0.jar;C:\sandbox\maven\repository\com\google\api\api-common\2.2.1\api-common-2.2.1.jar;C:\sandbox\maven\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\sandbox\maven\repository\com\google\guava\guava\32.0.0-jre\guava-32.0.0-jre.jar;C:\sandbox\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\sandbox\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\sandbox\maven\repository\com\google\api\gax\2.19.2\gax-2.19.2.jar;C:\sandbox\maven\repository\org\threeten\threetenbp\1.6.9\threetenbp-1.6.9.jar;C:\sandbox\maven\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\sandbox\maven\repository\org\threeten\threeten-extra\1.7.2\threeten-extra-1.7.2.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-bigquerystorage\2.23.1\google-cloud-bigquerystorage-2.23.1.jar;C:\sandbox\maven\repository\io\grpc\grpc-api\1.70.0\grpc-api-1.70.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-stub\1.49.2\grpc-stub-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-protobuf\1.49.2\grpc-protobuf-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-protobuf-lite\1.49.2\grpc-protobuf-lite-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-util\1.60.0\grpc-util-1.60.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-core\1.70.0\grpc-core-1.70.0.jar;C:\sandbox\maven\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\sandbox\maven\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-context\1.49.2\grpc-context-1.49.2.jar;C:\sandbox\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\sandbox\maven\repository\com\google\auto\value\auto-value\1.11.0\auto-value-1.11.0.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-cloud-bigquerystorage-v1beta1\0.147.1\proto-google-cloud-bigquerystorage-v1beta1-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-cloud-bigquerystorage-v1beta2\0.147.1\proto-google-cloud-bigquerystorage-v1beta2-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\gax-grpc\2.19.2\gax-grpc-2.19.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-inprocess\1.60.0\grpc-inprocess-1.60.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-alts\1.49.2\grpc-alts-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-grpclb\1.49.2\grpc-grpclb-1.49.2.jar;C:\sandbox\maven\repository\org\conscrypt\conscrypt-openjdk-uber\2.5.2\conscrypt-openjdk-uber-2.5.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-auth\1.49.2\grpc-auth-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-googleapis\1.49.2\grpc-googleapis-1.49.2.jar;C:\sandbox\maven\repository\org\json\json\20231013\json-20231013.jar;C:\sandbox\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\sandbox\maven\repository\commons-codec\commons-codec\1.16.1\commons-codec-1.16.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\grpc-google-cloud-bigquerystorage-v1beta1\0.147.1\grpc-google-cloud-bigquerystorage-v1beta1-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\grpc-google-cloud-bigquerystorage-v1beta2\0.147.1\grpc-google-cloud-bigquerystorage-v1beta2-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\grpc-google-cloud-bigquerystorage-v1\2.23.1\grpc-google-cloud-bigquerystorage-v1-2.23.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-cloud-bigquerystorage-v1\2.23.1\proto-google-cloud-bigquerystorage-v1-2.23.1.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-vector\14.0.2\arrow-vector-14.0.2.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-format\14.0.2\arrow-format-14.0.2.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;C:\sandbox\maven\repository\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar;C:\sandbox\maven\repository\com\google\flatbuffers\flatbuffers-java\1.12.0\flatbuffers-java-1.12.0.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-memory-core\14.0.2\arrow-memory-core-14.0.2.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-memory-netty\14.0.2\arrow-memory-netty-14.0.2.jar;C:\sandbox\maven\repository\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar;C:\sandbox\maven\repository\com\google\errorprone\error_prone_annotations\2.23.0\error_prone_annotations-2.23.0.jar;C:\sandbox\maven\repository\com\google\protobuf\protobuf-java\3.25.5\protobuf-java-3.25.5.jar;C:\sandbox\maven\repository\io\grpc\grpc-netty-shaded\1.71.0\grpc-netty-shaded-1.71.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-tracer-zipkin\target\pipeline-common-tracer-zipkin-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.12\spring-boot-starter-actuator-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.12\spring-boot-actuator-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator\3.2.12\spring-boot-actuator-3.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-observation\1.12.13\micrometer-observation-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-commons\1.12.13\micrometer-commons-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-jakarta9\1.12.13\micrometer-jakarta9-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-core\1.12.13\micrometer-core-1.12.13.jar;C:\sandbox\maven\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\sandbox\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing-bridge-brave\1.2.12\micrometer-tracing-bridge-brave-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing\1.2.12\micrometer-tracing-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\sandbox\maven\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave\5.16.0\brave-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-context-slf4j\5.16.0\brave-context-slf4j-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-instrumentation-http\5.16.0\brave-instrumentation-http-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\aws\brave-propagation-aws\0.23.5\brave-propagation-aws-0.23.5.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter-brave\2.16.3\zipkin-reporter-brave-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter\2.16.3\zipkin-reporter-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\zipkin2\zipkin\2.23.2\zipkin-2.23.2.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-sender-urlconnection\2.16.3\zipkin-sender-urlconnection-2.16.3.jar;C:\sandbox\maven\repository\org\springframework\kafka\spring-kafka\3.1.10\spring-kafka-3.1.10.jar;C:\sandbox\maven\repository\org\springframework\spring-context\6.1.15\spring-context-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-messaging\6.1.15\spring-messaging-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-tx\6.1.15\spring-tx-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\retry\spring-retry\2.0.10\spring-retry-2.0.10.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-clients\3.7.2\kafka-clients-3.7.2.jar;C:\sandbox\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;C:\sandbox\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;C:\sandbox\maven\repository\org\xerial\snappy\snappy-java\********\snappy-java-********.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-kafka\target\pipeline-common-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-streams\3.7.2\kafka-streams-3.7.2.jar;C:\sandbox\maven\repository\org\rocksdb\rocksdbjni\7.9.2\rocksdbjni-7.9.2.jar;C:\sandbox\maven\repository\org\apache\avro\avro\1.12.0\avro-1.12.0.jar;C:\sandbox\maven\repository\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;C:\sandbox\maven\repository\io\confluent\common-utils\5.3.0\common-utils-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\common-config\5.3.0\common-config-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-avro-serializer\5.3.0\kafka-avro-serializer-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-schema-registry-client\5.3.0\kafka-schema-registry-client-5.3.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-web\target\pipeline-common-web-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\spring-webmvc\6.1.15\spring-webmvc-6.1.15.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-prometheus\target\pipeline-common-prometheus-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-registry-prometheus\1.12.13\micrometer-registry-prometheus-1.12.13.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\sandbox\maven\repository\io\swagger\core\v3\swagger-annotations\2.1.9\swagger-annotations-2.1.9.jar;C:\sandbox\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\sandbox\maven\repository\org\togglz\togglz-webmvc-spring-boot-starter\4.4.0\togglz-webmvc-spring-boot-starter-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-spring-boot-autoconfigure\4.4.0\togglz-spring-boot-autoconfigure-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-spring-core\4.4.0\togglz-spring-core-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-core\4.4.0\togglz-core-4.4.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-web\3.2.12\spring-boot-starter-web-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.12\spring-boot-starter-tomcat-3.2.12.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.33\tomcat-embed-websocket-10.1.33.jar;C:\sandbox\maven\repository\org\togglz\togglz-spring-web\4.4.0\togglz-spring-web-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-servlet\4.4.0\togglz-servlet-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-console-spring-boot-starter\4.4.0\togglz-console-spring-boot-starter-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-core-spring-boot-starter\4.4.0\togglz-core-spring-boot-starter-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-console\4.4.0\togglz-console-4.4.0.jar;C:\sandbox\maven\repository\org\owasp\encoder\encoder\1.2.3\encoder-1.2.3.jar;C:\sandbox\maven\repository\com\googlecode\owasp-java-html-sanitizer\owasp-java-html-sanitizer\20220608.1\owasp-java-html-sanitizer-20220608.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.2.12\spring-boot-starter-validation-3.2.12.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.33\tomcat-embed-el-10.1.33.jar;C:\sandbox\maven\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\sandbox\maven\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\sandbox\maven\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\target\pipeline-common-test-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-web\6.2.8\spring-security-web-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-core\6.2.8\spring-security-core-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-crypto\6.3.8\spring-security-crypto-6.3.8.jar;C:\sandbox\maven\repository\org\springframework\spring-beans\6.1.15\spring-beans-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-expression\6.1.15\spring-expression-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-test\6.2.8\spring-security-test-6.2.8.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-launcher\1.10.5\junit-platform-launcher-1.10.5.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-engine\1.10.5\junit-platform-engine-1.10.5.jar;C:\sandbox\maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-jose\6.2.8\spring-security-oauth2-jose-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-core\6.2.8\spring-security-oauth2-core-6.2.8.jar;C:\sandbox\maven\repository\com\nimbusds\nimbus-jose-jwt\9.37.2\nimbus-jose-jwt-9.37.2.jar;C:\sandbox\maven\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter-test\15.0.0\graphql-spring-boot-starter-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test-autoconfigure\15.0.0\graphql-spring-boot-test-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test\15.0.0\graphql-spring-boot-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter\15.0.0\graphql-spring-boot-starter-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-autoconfigure\15.0.0\graphql-spring-boot-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-support\15.0.0\graphql-kickstart-spring-support-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-webflux\15.0.0\graphql-kickstart-spring-webflux-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java-extended-scalars\19.1\graphql-java-extended-scalars-19.1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-kickstart\15.0.0\graphql-java-kickstart-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-servlet\15.0.0\graphql-java-servlet-15.0.0.jar;C:\sandbox\maven\repository\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-api\2.1.1\jakarta.websocket-api-2.1.1.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-client-api\2.1.1\jakarta.websocket-client-api-2.1.1.jar;C:\sandbox\maven\repository\io\github\graphql-java\graphql-java-annotations\9.1\graphql-java-annotations-9.1.jar;C:\sandbox\maven\repository\javax\validation\validation-api\1.1.0.Final\validation-api-1.1.0.Final.jar;C:\sandbox\maven\repository\org\reflections\reflections\0.10.2\reflections-0.10.2.jar;C:\sandbox\maven\repository\org\javassist\javassist\3.28.0-GA\javassist-3.28.0-GA.jar;C:\sandbox\maven\repository\org\springframework\spring-webflux\6.1.15\spring-webflux-6.1.15.jar;C:\sandbox\maven\repository\io\projectreactor\reactor-core\3.6.12\reactor-core-3.6.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-websocket\3.2.12\spring-boot-starter-websocket-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-websocket\6.1.15\spring-websocket-6.1.15.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-tools\13.0.2\graphql-java-tools-13.0.2.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-reflect\1.9.25\kotlin-reflect-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-jdk8\1.7.3\kotlinx-coroutines-jdk8-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core-jvm\1.7.3\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core\1.7.3\kotlinx-coroutines-core-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-reactive\1.7.3\kotlinx-coroutines-reactive-1.7.3.jar;C:\sandbox\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java\21.5\graphql-java-21.5.jar;C:\sandbox\maven\repository\com\graphql-java\java-dataloader\3.2.1\java-dataloader-3.2.1.jar;C:\sandbox\maven\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.15.4\jackson-module-kotlin-2.15.4.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.35\tomcat-embed-core-10.1.35.jar;C:\sandbox\maven\repository\org\apache\tomcat\tomcat-annotations-api\10.1.33\tomcat-annotations-api-10.1.33.jar;C:\sandbox\maven\repository\com\h2database\h2\2.2.224\h2-2.2.224.jar;C:\sandbox\maven\repository\org\mockito\mockito-junit-jupiter\5.2.0\mockito-junit-jupiter-5.2.0.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-api\5.10.5\junit-jupiter-api-5.10.5.jar;C:\sandbox\maven\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-commons\1.10.5\junit-platform-commons-1.10.5.jar;C:\sandbox\maven\repository\org\springframework\spring-test\6.1.8\spring-test-6.1.8.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-test\3.2.12\spring-boot-starter-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test\3.2.12\spring-boot-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.12\spring-boot-test-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\sandbox\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\sandbox\maven\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\sandbox\maven\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\sandbox\maven\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\sandbox\maven\repository\org\assertj\assertj-core\3.25.3\assertj-core-3.25.3.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;C:\sandbox\maven\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\sandbox\maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter\5.10.2\junit-jupiter-5.10.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-params\5.10.5\junit-jupiter-params-5.10.5.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-engine\5.10.5\junit-jupiter-engine-5.10.5.jar;C:\sandbox\maven\repository\org\mockito\mockito-core\5.2.0\mockito-core-5.2.0.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;C:\sandbox\maven\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\sandbox\maven\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\sandbox\maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\sandbox\maven\repository\org\springframework\spring-core\6.1.15\spring-core-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-jcl\6.1.15\spring-jcl-6.1.15.jar;"/>
    <property name="java.vm.vendor" value="Eclipse Adoptium"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="C:\sandbox\devtools\java\jdk-21.0.4+7\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire2637469561737384227\surefirebooter-20250718035726850_55.jar C:\Users\<USER>\AppData\Local\Temp\surefire2637469561737384227 2025-07-18T03-55-35_673-jvmRun1 surefire-20250718035726850_53tmp surefire_9-20250718035726850_54tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\eddiewtchoi\workspace\ide\git\spring\custom-api-model\target\test-classes;C:\eddiewtchoi\workspace\ide\git\spring\custom-api-model\target\classes;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\target\pipeline-common-core-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter\3.2.12\spring-boot-starter-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot\3.2.12\spring-boot-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-autoconfigure\3.2.12\spring-boot-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-logging\3.2.12\spring-boot-starter-logging-3.2.12.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-classic\1.5.16\logback-classic-1.5.16.jar;C:\sandbox\maven\repository\ch\qos\logback\logback-core\1.5.16\logback-core-1.5.16.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;C:\sandbox\maven\repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;C:\sandbox\maven\repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar;C:\sandbox\maven\repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;C:\sandbox\maven\repository\org\yaml\snakeyaml\2.0\snakeyaml-2.0.jar;C:\sandbox\maven\repository\org\xmlunit\xmlunit-core\2.10.0\xmlunit-core-2.10.0.jar;C:\sandbox\maven\repository\org\projectlombok\lombok\1.18.30\lombok-1.18.30.jar;C:\sandbox\maven\repository\io\vertx\vertx-core\4.5.14\vertx-core-4.5.14.jar;C:\sandbox\maven\repository\io\netty\netty-transport\4.1.115.Final\netty-transport-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler\4.1.119.Final\netty-handler-4.1.119.Final.jar;C:\sandbox\maven\repository\io\netty\netty-transport-native-unix-common\4.1.115.Final\netty-transport-native-unix-common-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec\4.1.115.Final\netty-codec-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-handler-proxy\4.1.115.Final\netty-handler-proxy-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-socks\4.1.115.Final\netty-codec-socks-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http\4.1.115.Final\netty-codec-http-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-http2\4.1.115.Final\netty-codec-http2-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver\4.1.115.Final\netty-resolver-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-resolver-dns\4.1.115.Final\netty-resolver-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\io\netty\netty-codec-dns\4.1.115.Final\netty-codec-dns-4.1.115.Final.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-core\2.15.4\jackson-core-2.15.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-lang3\3.13.0\commons-lang3-3.13.0.jar;C:\sandbox\maven\repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar;C:\sandbox\maven\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;C:\sandbox\maven\repository\org\apache\commons\commons-text\1.10.0\commons-text-1.10.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-aop\3.2.12\spring-boot-starter-aop-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-aop\6.1.15\spring-aop-6.1.15.jar;C:\sandbox\maven\repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-json\3.2.12\spring-boot-starter-json-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-web\6.1.15\spring-web-6.1.15.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.4\jackson-datatype-jdk8-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.4\jackson-module-parameter-names-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-xml\2.15.4\jackson-dataformat-xml-2.15.4.jar;C:\sandbox\maven\repository\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;C:\sandbox\maven\repository\com\fasterxml\woodstox\woodstox-core\6.5.1\woodstox-core-6.5.1.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.15.4\jackson-dataformat-yaml-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\dataformat\jackson-dataformat-properties\2.15.4\jackson-dataformat-properties-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.4\jackson-datatype-jsr310-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-jaxb-annotations\2.15.4\jackson-module-jaxb-annotations-2.15.4.jar;C:\sandbox\maven\repository\jakarta\activation\jakarta.activation-api\2.1.3\jakarta.activation-api-2.1.3.jar;C:\sandbox\maven\repository\org\owasp\esapi\esapi\2.6.0.0\esapi-2.6.0.0.jar;C:\sandbox\maven\repository\xom\xom\1.3.9\xom-1.3.9.jar;C:\sandbox\maven\repository\commons-beanutils\commons-beanutils\1.10.1\commons-beanutils-1.10.1.jar;C:\sandbox\maven\repository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;C:\sandbox\maven\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\sandbox\maven\repository\commons-fileupload\commons-fileupload\1.5\commons-fileupload-1.5.jar;C:\sandbox\maven\repository\org\apache-extras\beanshell\bsh\2.0b6\bsh-2.0b6.jar;C:\sandbox\maven\repository\org\owasp\antisamy\antisamy\1.7.7\antisamy-1.7.7.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\client5\httpclient5\5.2.3\httpclient5-5.2.3.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5-h2\5.2.5\httpcore5-h2-5.2.5.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\core5\httpcore5\5.2.5\httpcore5-5.2.5.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-css\1.18\batik-css-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-shared-resources\1.18\batik-shared-resources-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-util\1.18\batik-util-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-constants\1.18\batik-constants-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\batik-i18n\1.18\batik-i18n-1.18.jar;C:\sandbox\maven\repository\org\apache\xmlgraphics\xmlgraphics-commons\2.10\xmlgraphics-commons-2.10.jar;C:\sandbox\maven\repository\org\htmlunit\neko-htmlunit\4.6.0\neko-htmlunit-4.6.0.jar;C:\sandbox\maven\repository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;C:\sandbox\maven\repository\xml-apis\xml-apis-ext\1.3.04\xml-apis-ext-1.3.04.jar;C:\sandbox\maven\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;C:\sandbox\maven\repository\net\logstash\logback\logstash-logback-encoder\6.1\logstash-logback-encoder-6.1.jar;C:\sandbox\maven\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\sandbox\maven\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\jaxb-runtime\2.3.1\jaxb-runtime-2.3.1.jar;C:\sandbox\maven\repository\org\glassfish\jaxb\txw2\4.0.5\txw2-4.0.5.jar;C:\sandbox\maven\repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;C:\sandbox\maven\repository\org\jvnet\staxex\stax-ex\2.1.0\stax-ex-2.1.0.jar;C:\sandbox\maven\repository\com\sun\xml\fastinfoset\FastInfoset\2.1.1\FastInfoset-2.1.1.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-bigquery\target\pipeline-common-bigquery-2.8.6-SNAPSHOT.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jdbc\target\pipeline-common-jdbc-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.12\spring-boot-starter-jdbc-3.2.12.jar;C:\sandbox\maven\repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;C:\sandbox\maven\repository\org\springframework\spring-jdbc\6.1.15\spring-jdbc-6.1.15.jar;C:\sandbox\maven\repository\org\postgresql\postgresql\42.4.5\postgresql-42.4.5.jar;C:\sandbox\maven\repository\com\oracle\database\jdbc\ojdbc8\********\ojdbc8-********.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-bigquery\2.36.0\google-cloud-bigquery-2.36.0.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-core\2.8.20\google-cloud-core-2.8.20.jar;C:\sandbox\maven\repository\com\google\protobuf\protobuf-java-util\3.21.7\protobuf-java-util-3.21.7.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-common-protos\2.9.6\proto-google-common-protos-2.9.6.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-iam-v1\1.6.2\proto-google-iam-v1-1.6.2.jar;C:\sandbox\maven\repository\com\google\auth\google-auth-library-credentials\1.11.0\google-auth-library-credentials-1.11.0.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-core-http\2.8.20\google-cloud-core-http-2.8.20.jar;C:\sandbox\maven\repository\com\google\api-client\google-api-client\2.0.0\google-api-client-2.0.0.jar;C:\sandbox\maven\repository\com\google\oauth-client\google-oauth-client\1.34.1\google-oauth-client-1.34.1.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client-apache-v2\1.42.2\google-http-client-apache-v2-1.42.2.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client-appengine\1.42.2\google-http-client-appengine-1.42.2.jar;C:\sandbox\maven\repository\com\google\api\gax-httpjson\0.104.2\gax-httpjson-0.104.2.jar;C:\sandbox\maven\repository\io\opencensus\opencensus-api\0.31.1\opencensus-api-0.31.1.jar;C:\sandbox\maven\repository\io\opencensus\opencensus-contrib-http-util\0.31.1\opencensus-contrib-http-util-0.31.1.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client-gson\1.42.2\google-http-client-gson-1.42.2.jar;C:\sandbox\maven\repository\com\google\http-client\google-http-client\1.42.2\google-http-client-1.42.2.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\httpclient\4.5.14\httpclient-4.5.14.jar;C:\sandbox\maven\repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;C:\sandbox\maven\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\sandbox\maven\repository\org\checkerframework\checker-compat-qual\2.5.6\checker-compat-qual-2.5.6.jar;C:\sandbox\maven\repository\org\checkerframework\checker-qual\3.40.0\checker-qual-3.40.0.jar;C:\sandbox\maven\repository\com\google\auth\google-auth-library-oauth2-http\1.11.0\google-auth-library-oauth2-http-1.11.0.jar;C:\sandbox\maven\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\sandbox\maven\repository\com\google\apis\google-api-services-bigquery\v2-rev20231111-2.0.0\google-api-services-bigquery-v2-rev20231111-2.0.0.jar;C:\sandbox\maven\repository\com\google\api\api-common\2.2.1\api-common-2.2.1.jar;C:\sandbox\maven\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\sandbox\maven\repository\com\google\guava\guava\32.0.0-jre\guava-32.0.0-jre.jar;C:\sandbox\maven\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\sandbox\maven\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\sandbox\maven\repository\com\google\api\gax\2.19.2\gax-2.19.2.jar;C:\sandbox\maven\repository\org\threeten\threetenbp\1.6.9\threetenbp-1.6.9.jar;C:\sandbox\maven\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\sandbox\maven\repository\org\threeten\threeten-extra\1.7.2\threeten-extra-1.7.2.jar;C:\sandbox\maven\repository\com\google\cloud\google-cloud-bigquerystorage\2.23.1\google-cloud-bigquerystorage-2.23.1.jar;C:\sandbox\maven\repository\io\grpc\grpc-api\1.70.0\grpc-api-1.70.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-stub\1.49.2\grpc-stub-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-protobuf\1.49.2\grpc-protobuf-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-protobuf-lite\1.49.2\grpc-protobuf-lite-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-util\1.60.0\grpc-util-1.60.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-core\1.70.0\grpc-core-1.70.0.jar;C:\sandbox\maven\repository\com\google\android\annotations\4.1.1.4\annotations-4.1.1.4.jar;C:\sandbox\maven\repository\io\perfmark\perfmark-api\0.26.0\perfmark-api-0.26.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-context\1.49.2\grpc-context-1.49.2.jar;C:\sandbox\maven\repository\org\codehaus\mojo\animal-sniffer-annotations\1.23\animal-sniffer-annotations-1.23.jar;C:\sandbox\maven\repository\com\google\auto\value\auto-value\1.11.0\auto-value-1.11.0.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-cloud-bigquerystorage-v1beta1\0.147.1\proto-google-cloud-bigquerystorage-v1beta1-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-cloud-bigquerystorage-v1beta2\0.147.1\proto-google-cloud-bigquerystorage-v1beta2-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\gax-grpc\2.19.2\gax-grpc-2.19.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-inprocess\1.60.0\grpc-inprocess-1.60.0.jar;C:\sandbox\maven\repository\io\grpc\grpc-alts\1.49.2\grpc-alts-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-grpclb\1.49.2\grpc-grpclb-1.49.2.jar;C:\sandbox\maven\repository\org\conscrypt\conscrypt-openjdk-uber\2.5.2\conscrypt-openjdk-uber-2.5.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-auth\1.49.2\grpc-auth-1.49.2.jar;C:\sandbox\maven\repository\io\grpc\grpc-googleapis\1.49.2\grpc-googleapis-1.49.2.jar;C:\sandbox\maven\repository\org\json\json\20231013\json-20231013.jar;C:\sandbox\maven\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\sandbox\maven\repository\commons-codec\commons-codec\1.16.1\commons-codec-1.16.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\grpc-google-cloud-bigquerystorage-v1beta1\0.147.1\grpc-google-cloud-bigquerystorage-v1beta1-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\grpc-google-cloud-bigquerystorage-v1beta2\0.147.1\grpc-google-cloud-bigquerystorage-v1beta2-0.147.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\grpc-google-cloud-bigquerystorage-v1\2.23.1\grpc-google-cloud-bigquerystorage-v1-2.23.1.jar;C:\sandbox\maven\repository\com\google\api\grpc\proto-google-cloud-bigquerystorage-v1\2.23.1\proto-google-cloud-bigquerystorage-v1-2.23.1.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-vector\14.0.2\arrow-vector-14.0.2.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-format\14.0.2\arrow-format-14.0.2.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.4\jackson-annotations-2.15.4.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\core\jackson-databind\2.15.4\jackson-databind-2.15.4.jar;C:\sandbox\maven\repository\io\netty\netty-common\4.1.115.Final\netty-common-4.1.115.Final.jar;C:\sandbox\maven\repository\com\google\flatbuffers\flatbuffers-java\1.12.0\flatbuffers-java-1.12.0.jar;C:\sandbox\maven\repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-memory-core\14.0.2\arrow-memory-core-14.0.2.jar;C:\sandbox\maven\repository\org\apache\arrow\arrow-memory-netty\14.0.2\arrow-memory-netty-14.0.2.jar;C:\sandbox\maven\repository\io\netty\netty-buffer\4.1.115.Final\netty-buffer-4.1.115.Final.jar;C:\sandbox\maven\repository\com\google\errorprone\error_prone_annotations\2.23.0\error_prone_annotations-2.23.0.jar;C:\sandbox\maven\repository\com\google\protobuf\protobuf-java\3.25.5\protobuf-java-3.25.5.jar;C:\sandbox\maven\repository\io\grpc\grpc-netty-shaded\1.71.0\grpc-netty-shaded-1.71.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-tracer-zipkin\target\pipeline-common-tracer-zipkin-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-actuator\3.2.12\spring-boot-starter-actuator-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\3.2.12\spring-boot-actuator-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-actuator\3.2.12\spring-boot-actuator-3.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-observation\1.12.13\micrometer-observation-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-commons\1.12.13\micrometer-commons-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-jakarta9\1.12.13\micrometer-jakarta9-1.12.13.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-core\1.12.13\micrometer-core-1.12.13.jar;C:\sandbox\maven\repository\org\hdrhistogram\HdrHistogram\2.1.12\HdrHistogram-2.1.12.jar;C:\sandbox\maven\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing-bridge-brave\1.2.12\micrometer-tracing-bridge-brave-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-tracing\1.2.12\micrometer-tracing-1.2.12.jar;C:\sandbox\maven\repository\io\micrometer\context-propagation\1.1.2\context-propagation-1.1.2.jar;C:\sandbox\maven\repository\aopalliance\aopalliance\1.0\aopalliance-1.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave\5.16.0\brave-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-context-slf4j\5.16.0\brave-context-slf4j-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\brave\brave-instrumentation-http\5.16.0\brave-instrumentation-http-5.16.0.jar;C:\sandbox\maven\repository\io\zipkin\aws\brave-propagation-aws\0.23.5\brave-propagation-aws-0.23.5.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter-brave\2.16.3\zipkin-reporter-brave-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-reporter\2.16.3\zipkin-reporter-2.16.3.jar;C:\sandbox\maven\repository\io\zipkin\zipkin2\zipkin\2.23.2\zipkin-2.23.2.jar;C:\sandbox\maven\repository\io\zipkin\reporter2\zipkin-sender-urlconnection\2.16.3\zipkin-sender-urlconnection-2.16.3.jar;C:\sandbox\maven\repository\org\springframework\kafka\spring-kafka\3.1.10\spring-kafka-3.1.10.jar;C:\sandbox\maven\repository\org\springframework\spring-context\6.1.15\spring-context-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-messaging\6.1.15\spring-messaging-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-tx\6.1.15\spring-tx-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\retry\spring-retry\2.0.10\spring-retry-2.0.10.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-clients\3.7.2\kafka-clients-3.7.2.jar;C:\sandbox\maven\repository\com\github\luben\zstd-jni\1.5.6-4\zstd-jni-1.5.6-4.jar;C:\sandbox\maven\repository\org\lz4\lz4-java\1.8.0\lz4-java-1.8.0.jar;C:\sandbox\maven\repository\org\xerial\snappy\snappy-java\********\snappy-java-********.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-kafka\target\pipeline-common-kafka-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\apache\kafka\kafka-streams\3.7.2\kafka-streams-3.7.2.jar;C:\sandbox\maven\repository\org\rocksdb\rocksdbjni\7.9.2\rocksdbjni-7.9.2.jar;C:\sandbox\maven\repository\org\apache\avro\avro\1.12.0\avro-1.12.0.jar;C:\sandbox\maven\repository\org\apache\commons\commons-compress\1.27.1\commons-compress-1.27.1.jar;C:\sandbox\maven\repository\io\confluent\common-utils\5.3.0\common-utils-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\common-config\5.3.0\common-config-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-avro-serializer\5.3.0\kafka-avro-serializer-5.3.0.jar;C:\sandbox\maven\repository\io\confluent\kafka-schema-registry-client\5.3.0\kafka-schema-registry-client-5.3.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-web\target\pipeline-common-web-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\spring-webmvc\6.1.15\spring-webmvc-6.1.15.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-prometheus\target\pipeline-common-prometheus-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\io\micrometer\micrometer-registry-prometheus\1.12.13\micrometer-registry-prometheus-1.12.13.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_common\0.16.0\simpleclient_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient\0.16.0\simpleclient-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel\0.16.0\simpleclient_tracer_otel-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_common\0.16.0\simpleclient_tracer_common-0.16.0.jar;C:\sandbox\maven\repository\io\prometheus\simpleclient_tracer_otel_agent\0.16.0\simpleclient_tracer_otel_agent-0.16.0.jar;C:\sandbox\maven\repository\io\swagger\core\v3\swagger-annotations\2.1.9\swagger-annotations-2.1.9.jar;C:\sandbox\maven\repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;C:\sandbox\maven\repository\org\togglz\togglz-webmvc-spring-boot-starter\4.4.0\togglz-webmvc-spring-boot-starter-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-spring-boot-autoconfigure\4.4.0\togglz-spring-boot-autoconfigure-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-spring-core\4.4.0\togglz-spring-core-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-core\4.4.0\togglz-core-4.4.0.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-web\3.2.12\spring-boot-starter-web-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.12\spring-boot-starter-tomcat-3.2.12.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.33\tomcat-embed-websocket-10.1.33.jar;C:\sandbox\maven\repository\org\togglz\togglz-spring-web\4.4.0\togglz-spring-web-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-servlet\4.4.0\togglz-servlet-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-console-spring-boot-starter\4.4.0\togglz-console-spring-boot-starter-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-core-spring-boot-starter\4.4.0\togglz-core-spring-boot-starter-4.4.0.jar;C:\sandbox\maven\repository\org\togglz\togglz-console\4.4.0\togglz-console-4.4.0.jar;C:\sandbox\maven\repository\org\owasp\encoder\encoder\1.2.3\encoder-1.2.3.jar;C:\sandbox\maven\repository\com\googlecode\owasp-java-html-sanitizer\owasp-java-html-sanitizer\20220608.1\owasp-java-html-sanitizer-20220608.1.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-validation\3.2.12\spring-boot-starter-validation-3.2.12.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.33\tomcat-embed-el-10.1.33.jar;C:\sandbox\maven\repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;C:\sandbox\maven\repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;C:\sandbox\maven\repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\target\pipeline-common-test-2.8.6-SNAPSHOT.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-web\6.2.8\spring-security-web-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-core\6.2.8\spring-security-core-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-crypto\6.3.8\spring-security-crypto-6.3.8.jar;C:\sandbox\maven\repository\org\springframework\spring-beans\6.1.15\spring-beans-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-expression\6.1.15\spring-expression-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-test\6.2.8\spring-security-test-6.2.8.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-launcher\1.10.5\junit-platform-launcher-1.10.5.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-engine\1.10.5\junit-platform-engine-1.10.5.jar;C:\sandbox\maven\repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-jose\6.2.8\spring-security-oauth2-jose-6.2.8.jar;C:\sandbox\maven\repository\org\springframework\security\spring-security-oauth2-core\6.2.8\spring-security-oauth2-core-6.2.8.jar;C:\sandbox\maven\repository\com\nimbusds\nimbus-jose-jwt\9.37.2\nimbus-jose-jwt-9.37.2.jar;C:\sandbox\maven\repository\com\github\stephenc\jcip\jcip-annotations\1.0-1\jcip-annotations-1.0-1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter-test\15.0.0\graphql-spring-boot-starter-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test-autoconfigure\15.0.0\graphql-spring-boot-test-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-test\15.0.0\graphql-spring-boot-test-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-starter\15.0.0\graphql-spring-boot-starter-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-spring-boot-autoconfigure\15.0.0\graphql-spring-boot-autoconfigure-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-support\15.0.0\graphql-kickstart-spring-support-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-kickstart-spring-webflux\15.0.0\graphql-kickstart-spring-webflux-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java-extended-scalars\19.1\graphql-java-extended-scalars-19.1.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-kickstart\15.0.0\graphql-java-kickstart-15.0.0.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-servlet\15.0.0\graphql-java-servlet-15.0.0.jar;C:\sandbox\maven\repository\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-api\2.1.1\jakarta.websocket-api-2.1.1.jar;C:\sandbox\maven\repository\jakarta\websocket\jakarta.websocket-client-api\2.1.1\jakarta.websocket-client-api-2.1.1.jar;C:\sandbox\maven\repository\io\github\graphql-java\graphql-java-annotations\9.1\graphql-java-annotations-9.1.jar;C:\sandbox\maven\repository\javax\validation\validation-api\1.1.0.Final\validation-api-1.1.0.Final.jar;C:\sandbox\maven\repository\org\reflections\reflections\0.10.2\reflections-0.10.2.jar;C:\sandbox\maven\repository\org\javassist\javassist\3.28.0-GA\javassist-3.28.0-GA.jar;C:\sandbox\maven\repository\org\springframework\spring-webflux\6.1.15\spring-webflux-6.1.15.jar;C:\sandbox\maven\repository\io\projectreactor\reactor-core\3.6.12\reactor-core-3.6.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-websocket\3.2.12\spring-boot-starter-websocket-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\spring-websocket\6.1.15\spring-websocket-6.1.15.jar;C:\sandbox\maven\repository\com\graphql-java-kickstart\graphql-java-tools\13.0.2\graphql-java-tools-13.0.2.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-reflect\1.9.25\kotlin-reflect-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-jdk8\1.7.3\kotlinx-coroutines-jdk8-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core-jvm\1.7.3\kotlinx-coroutines-core-jvm-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.9.25\kotlin-stdlib-common-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core\1.7.3\kotlinx-coroutines-core-1.7.3.jar;C:\sandbox\maven\repository\org\jetbrains\kotlinx\kotlinx-coroutines-reactive\1.7.3\kotlinx-coroutines-reactive-1.7.3.jar;C:\sandbox\maven\repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar;C:\sandbox\maven\repository\com\graphql-java\graphql-java\21.5\graphql-java-21.5.jar;C:\sandbox\maven\repository\com\graphql-java\java-dataloader\3.2.1\java-dataloader-3.2.1.jar;C:\sandbox\maven\repository\org\antlr\antlr4-runtime\4.13.0\antlr4-runtime-4.13.0.jar;C:\sandbox\maven\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.15.4\jackson-module-kotlin-2.15.4.jar;C:\sandbox\maven\repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.35\tomcat-embed-core-10.1.35.jar;C:\sandbox\maven\repository\org\apache\tomcat\tomcat-annotations-api\10.1.33\tomcat-annotations-api-10.1.33.jar;C:\sandbox\maven\repository\com\h2database\h2\2.2.224\h2-2.2.224.jar;C:\sandbox\maven\repository\org\mockito\mockito-junit-jupiter\5.2.0\mockito-junit-jupiter-5.2.0.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-api\5.10.5\junit-jupiter-api-5.10.5.jar;C:\sandbox\maven\repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;C:\sandbox\maven\repository\org\junit\platform\junit-platform-commons\1.10.5\junit-platform-commons-1.10.5.jar;C:\sandbox\maven\repository\org\springframework\spring-test\6.1.8\spring-test-6.1.8.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-starter-test\3.2.12\spring-boot-starter-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test\3.2.12\spring-boot-test-3.2.12.jar;C:\sandbox\maven\repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.12\spring-boot-test-autoconfigure-3.2.12.jar;C:\sandbox\maven\repository\com\jayway\jsonpath\json-path\2.9.0\json-path-2.9.0.jar;C:\sandbox\maven\repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.2\jakarta.xml.bind-api-4.0.2.jar;C:\sandbox\maven\repository\net\minidev\json-smart\2.5.2\json-smart-2.5.2.jar;C:\sandbox\maven\repository\net\minidev\accessors-smart\2.5.2\accessors-smart-2.5.2.jar;C:\sandbox\maven\repository\org\ow2\asm\asm\9.7.1\asm-9.7.1.jar;C:\sandbox\maven\repository\org\assertj\assertj-core\3.25.3\assertj-core-3.25.3.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy\1.14.19\byte-buddy-1.14.19.jar;C:\sandbox\maven\repository\org\awaitility\awaitility\4.2.2\awaitility-4.2.2.jar;C:\sandbox\maven\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter\5.10.2\junit-jupiter-5.10.2.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-params\5.10.5\junit-jupiter-params-5.10.5.jar;C:\sandbox\maven\repository\org\junit\jupiter\junit-jupiter-engine\5.10.5\junit-jupiter-engine-5.10.5.jar;C:\sandbox\maven\repository\org\mockito\mockito-core\5.2.0\mockito-core-5.2.0.jar;C:\sandbox\maven\repository\net\bytebuddy\byte-buddy-agent\1.14.19\byte-buddy-agent-1.14.19.jar;C:\sandbox\maven\repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;C:\sandbox\maven\repository\org\skyscreamer\jsonassert\1.5.3\jsonassert-1.5.3.jar;C:\sandbox\maven\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\sandbox\maven\repository\org\springframework\spring-core\6.1.15\spring-core-6.1.15.jar;C:\sandbox\maven\repository\org\springframework\spring-jcl\6.1.15\spring-jcl-6.1.15.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\sandbox\devtools\java\jdk-21.0.4+7"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\eddiewtchoi\workspace\ide\git\spring\custom-api-model"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire2637469561737384227\surefirebooter-20250718035726850_55.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.4+7-LTS"/>
    <property name="user.name" value="43402948"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Temurin-21.0.4+7"/>
    <property name="localRepository" value="C:\sandbox\maven\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.4"/>
    <property name="user.dir" value="C:\eddiewtchoi\workspace\ide\git\spring\custom-api-model"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\sandbox\devtools\java\jdk-21.0.4+7\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Windows\CCM\;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;c:\Program Files (x86)\Sennheiser\SenncomSDK\;C:\Program Files (x86)\SafeCom\SafeComPrintClient;C:\Windows\system32\config\systemprofile\AppData\Local\Microsoft\WindowsApps;C:\Program Files (x86)\Adaptiva\AdaptivaClient\bin\x32;C:\Program Files (x86)\Adaptiva\AdaptivaClient\bin\x64;C:\Program Files\Git-2.41.0\cmd;C:\Program Files\Microsoft VS Code\bin;C:\Program Files\Adaptiva\AdaptivaClient\bin\x32;C:\Program Files\Adaptiva\AdaptivaClient\bin\x64;%SystemRoot%\system32;%SystemRoot%;%SystemRoot%\System32\Wbem;%SYSTEMROOT%\System32\WindowsPowerShell\v1.0\;%SYSTEMROOT%\System32\OpenSSH\;C:\Users\<USER>\go\bin;C:\sandbox\devtools\golang\1.20\go\bin;C:\sandbox\devtools\google-cloud-sdk\239.0.0\google-cloud-sdk\bin;C:\sandbox\devtools\node\node-v15.14.0-win-x64\;C:\sandbox\devtools\lein;C:\sandbox\devtools\gradle\gradle-4.6\bin;C:\sandbox\devtools\maven\apache-maven-3.9.8\bin;C:\sandbox\devtools\git\2.44.0\usr\bin;C:\sandbox\devtools\git\2.44.0\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\sandbox\devtools\postgresql\14.2-1\pgsql\bin;;%USERPROFILE%\AppData\Local\Microsoft\WindowsApps;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Eclipse Adoptium"/>
    <property name="java.vm.version" value="21.0.4+7-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testPageInfoNoArgsConstructor" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.002"/>
  <testcase name="testPageInfoSettersAndGetters" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.001"/>
  <testcase name="testPageInfoBuilder" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.002"/>
  <testcase name="testPageInfoToString" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.011"/>
  <testcase name="testPageInfoEqualsAndHashCode" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.001"/>
  <testcase name="testPageInfoWithNegativeValues" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.0"/>
  <testcase name="testPageInfoWithZeroValues" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.0"/>
  <testcase name="testPageInfoAllArgsConstructor" classname="com.poc.hss.fasttrack.model.CustomQueryResponsePageInfoTest" time="0.001"/>
</testsuite>