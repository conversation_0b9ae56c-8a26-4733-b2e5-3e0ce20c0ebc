<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">LookupApi.java</span></div><h1>LookupApi.java</h1><pre class="source lang-java linenums">/**
 * NOTE: This class is auto generated by the swagger code generator program (3.0.27).
 * https://github.com/swagger-api/swagger-codegen
 * Do not edit the class manually.
 */
package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.model.LookupRequest;
import com.poc.hss.fasttrack.model.LookupResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.bind.annotation.CookieValue;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Validated
@RequestMapping(&quot;/api&quot;)
public interface LookupApi {

<span class="fc" id="L47">    Logger log = LoggerFactory.getLogger(LookupApi.class);</span>

    default Optional&lt;ObjectMapper&gt; getObjectMapper(){
<span class="nc" id="L50">        return Optional.empty();</span>
    }

    default Optional&lt;HttpServletRequest&gt; getRequest(){
<span class="nc" id="L54">        return Optional.empty();</span>
    }

    default Optional&lt;String&gt; getAcceptHeader() {
<span class="fc" id="L58">        return getRequest().map(r -&gt; r.getHeader(&quot;Accept&quot;));</span>
    }

    @Operation(summary = &quot;Lookup by criteria&quot;, description = &quot;&quot;, tags={ &quot;Lookup&quot; })
    @ApiResponses(value = { 
        @ApiResponse(responseCode = &quot;200&quot;, description = &quot;OK&quot;, content = @Content(mediaType = &quot;application/json&quot;, schema = @Schema(implementation = LookupResponse.class))) })
    @RequestMapping(value = &quot;/lookup/{accessSchemaName}&quot;,
        produces = { &quot;application/json&quot; }, 
        consumes = { &quot;application/json&quot; }, 
        method = RequestMethod.POST)
    default ResponseEntity&lt;LookupResponse&gt; lookup(@Parameter(in = ParameterIn.PATH, description = &quot;Access schema name&quot;, required=true, schema=@Schema()) @PathVariable(&quot;accessSchemaName&quot;) String accessSchemaName, @Parameter(in = ParameterIn.DEFAULT, description = &quot;&quot;, schema=@Schema()) @Valid @RequestBody LookupRequest body) {
<span class="fc bfc" id="L69" title="All 4 branches covered.">        if(getObjectMapper().isPresent() &amp;&amp; getAcceptHeader().isPresent()) {</span>
<span class="fc bfc" id="L70" title="All 2 branches covered.">            if (getAcceptHeader().get().contains(&quot;application/json&quot;)) {</span>
                try {
<span class="fc" id="L72">                    return new ResponseEntity&lt;&gt;(getObjectMapper().get().readValue(&quot;{\r\n  \&quot;data\&quot; : [ {\r\n    \&quot;key\&quot; : { }\r\n  }, {\r\n    \&quot;key\&quot; : { }\r\n  } ],\r\n  \&quot;errors\&quot; : [ {\r\n    \&quot;code\&quot; : \&quot;code\&quot;,\r\n    \&quot;message\&quot; : \&quot;message\&quot;\r\n  }, {\r\n    \&quot;code\&quot; : \&quot;code\&quot;,\r\n    \&quot;message\&quot; : \&quot;message\&quot;\r\n  } ]\r\n}&quot;, LookupResponse.class), HttpStatus.NOT_IMPLEMENTED);</span>
<span class="nc" id="L73">                } catch (IOException e) {</span>
<span class="nc" id="L74">                    log.error(&quot;Couldn't serialize response for content type application/json&quot;, e);</span>
<span class="nc" id="L75">                    return new ResponseEntity&lt;&gt;(HttpStatus.INTERNAL_SERVER_ERROR);</span>
                }
            }
        } else {
<span class="fc" id="L79">            log.warn(&quot;ObjectMapper or HttpServletRequest not configured in default LookupApi interface so no example is generated&quot;);</span>
        }
<span class="fc" id="L81">        return new ResponseEntity&lt;&gt;(HttpStatus.NOT_IMPLEMENTED);</span>
    }

}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>