<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupApiController.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.controller</a> &gt; <span class="el_source">LookupApiController.java</span></div><h1>LookupApiController.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.controller;

import com.poc.hss.fasttrack.constant.ErrorCodeConstant;
import com.poc.hss.fasttrack.dto.FieldAggregationDTO;
import com.poc.hss.fasttrack.dto.JdbcCriteriaDTO;
import com.poc.hss.fasttrack.model.Error;
import com.poc.hss.fasttrack.model.LookupRequest;
import com.poc.hss.fasttrack.model.LookupResponse;
import com.poc.hss.fasttrack.service.DataLookupJdbcService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
<span class="fc" id="L26">public class LookupApiController extends BaseController implements LookupApi {</span>

<span class="fc" id="L28">    private final static Logger logger = LoggerFactory.getLogger(LookupApiController.class);</span>

    @Autowired
    private DataLookupJdbcService dataLookupJdbcService;

    @Override
    public ResponseEntity&lt;LookupResponse&gt; lookup(String accessSchemaName, @Valid LookupRequest body) {
<span class="fc" id="L35">        LookupResponse response = new LookupResponse();</span>

        try {
<span class="fc" id="L38">            List&lt;Map&lt;String, Object&gt;&gt; lookupResult = dataLookupJdbcService.lookup(accessSchemaName, convert(body));</span>
<span class="fc" id="L39">            response.setData(lookupResult);</span>
<span class="fc" id="L40">        } catch (BadSqlGrammarException e) {</span>
<span class="fc" id="L41">            logger.error(&quot;Unexpected error in data lookup&quot;, e);</span>
<span class="fc" id="L42">            response.addErrorsItem(new Error()</span>
<span class="fc" id="L43">                    .code(ErrorCodeConstant.MALFORMED_SQL_ERROR)</span>
<span class="fc" id="L44">                    .message(e.getMessage())</span>
            );
<span class="fc" id="L46">        }</span>

<span class="fc bfc" id="L48" title="All 2 branches covered.">        return new ResponseEntity&lt;&gt;(response, CollectionUtils.isEmpty(response.getErrors()) ? HttpStatus.OK : HttpStatus.BAD_REQUEST);</span>
    }

    private JdbcCriteriaDTO convert(LookupRequest body) {
<span class="fc" id="L52">        return JdbcCriteriaDTO.builder()</span>
<span class="fc" id="L53">                .distinct(body.isDistinct())</span>
<span class="fc" id="L54">                .where(body.getCriteria())</span>
<span class="fc" id="L55">                .fields(body.getFields())</span>
<span class="fc" id="L56">                .fieldAggregations(CollectionUtils.emptyIfNull(body.getFieldAggregations())</span>
<span class="fc" id="L57">                        .stream()</span>
<span class="fc" id="L58">                        .map(this::convert)</span>
<span class="fc" id="L59">                        .collect(Collectors.toList())</span>
                )
<span class="fc" id="L61">                .sorts(CollectionUtils.emptyIfNull(body.getOrders())</span>
<span class="fc" id="L62">                        .stream()</span>
<span class="fc" id="L63">                        .map(order -&gt; order.split(&quot;:&quot;))</span>
<span class="fc" id="L64">                        .map(order -&gt; JdbcCriteriaDTO.Sort.builder()</span>
<span class="fc" id="L65">                                .field(order[0])</span>
<span class="fc bfc" id="L66" title="All 4 branches covered.">                                .direction(order.length &gt; 1 &amp;&amp; StringUtils.equalsIgnoreCase(order[1], &quot;desc&quot;) ? JdbcCriteriaDTO.Direction.DESC : JdbcCriteriaDTO.Direction.ASC)</span>
<span class="fc" id="L67">                                .build()</span>
                        )
<span class="fc" id="L69">                        .collect(Collectors.toList())</span>
                )
<span class="fc" id="L71">                .groups(body.getGroups())</span>
<span class="fc" id="L72">                .limit(body.getLimit())</span>
<span class="fc" id="L73">                .offset(body.getOffset())</span>
<span class="fc" id="L74">                .build();</span>
    }

    private FieldAggregationDTO convert(com.poc.hss.fasttrack.model.FieldAggregation agg) {
<span class="fc" id="L78">        return FieldAggregationDTO</span>
<span class="fc" id="L79">                .builder()</span>
<span class="fc" id="L80">                .function(agg.getFunction())</span>
<span class="fc" id="L81">                .field(agg.getField())</span>
<span class="fc" id="L82">                .rename(agg.getRename())</span>
<span class="fc" id="L83">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>