<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Error.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">Error.java</span></div><h1>Error.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * Error
 */
@Validated


<span class="fc" id="L17">public class Error   {</span>
<span class="fc" id="L18">  @JsonProperty(&quot;code&quot;)</span>
  private String code = null;

<span class="fc" id="L21">  @JsonProperty(&quot;message&quot;)</span>
  private String message = null;

  public Error code(String code) {
<span class="fc" id="L25">    this.code = code;</span>
<span class="fc" id="L26">    return this;</span>
  }

  /**
   * Get code
   * @return code
   **/
  @Schema(description = &quot;&quot;)
  
    public String getCode() {
<span class="fc" id="L36">    return code;</span>
  }

  public void setCode(String code) {
<span class="fc" id="L40">    this.code = code;</span>
<span class="fc" id="L41">  }</span>

  public Error message(String message) {
<span class="fc" id="L44">    this.message = message;</span>
<span class="fc" id="L45">    return this;</span>
  }

  /**
   * Get message
   * @return message
   **/
  @Schema(description = &quot;&quot;)
  
    public String getMessage() {
<span class="fc" id="L55">    return message;</span>
  }

  public void setMessage(String message) {
<span class="fc" id="L59">    this.message = message;</span>
<span class="fc" id="L60">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="fc bfc" id="L65" title="All 2 branches covered.">    if (this == o) {</span>
<span class="fc" id="L66">      return true;</span>
    }
<span class="fc bfc" id="L68" title="All 4 branches covered.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="fc" id="L69">      return false;</span>
    }
<span class="fc" id="L71">    Error error = (Error) o;</span>
<span class="fc bfc" id="L72" title="All 2 branches covered.">    return Objects.equals(this.code, error.code) &amp;&amp;</span>
<span class="fc bfc" id="L73" title="All 2 branches covered.">        Objects.equals(this.message, error.message);</span>
  }

  @Override
  public int hashCode() {
<span class="fc" id="L78">    return Objects.hash(code, message);</span>
  }

  @Override
  public String toString() {
<span class="fc" id="L83">    StringBuilder sb = new StringBuilder();</span>
<span class="fc" id="L84">    sb.append(&quot;class Error {\n&quot;);</span>
    
<span class="fc" id="L86">    sb.append(&quot;    code: &quot;).append(toIndentedString(code)).append(&quot;\n&quot;);</span>
<span class="fc" id="L87">    sb.append(&quot;    message: &quot;).append(toIndentedString(message)).append(&quot;\n&quot;);</span>
<span class="fc" id="L88">    sb.append(&quot;}&quot;);</span>
<span class="fc" id="L89">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="fc bfc" id="L97" title="All 2 branches covered.">    if (o == null) {</span>
<span class="fc" id="L98">      return &quot;null&quot;;</span>
    }
<span class="fc" id="L100">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>