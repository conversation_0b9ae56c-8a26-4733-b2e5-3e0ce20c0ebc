<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldAggregation.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">FieldAggregation.java</span></div><h1>FieldAggregation.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * FieldAggregation
 */
@Validated


<span class="fc" id="L17">public class FieldAggregation   {</span>
<span class="fc" id="L18">  @JsonProperty(&quot;function&quot;)</span>
  private String function = null;

<span class="fc" id="L21">  @JsonProperty(&quot;field&quot;)</span>
  private String field = null;

<span class="fc" id="L24">  @JsonProperty(&quot;rename&quot;)</span>
  private String rename = null;

  public FieldAggregation function(String function) {
<span class="fc" id="L28">    this.function = function;</span>
<span class="fc" id="L29">    return this;</span>
  }

  /**
   * Get function
   * @return function
   **/
  @Schema(description = &quot;&quot;)
  
    public String getFunction() {
<span class="fc" id="L39">    return function;</span>
  }

  public void setFunction(String function) {
<span class="fc" id="L43">    this.function = function;</span>
<span class="fc" id="L44">  }</span>

  public FieldAggregation field(String field) {
<span class="fc" id="L47">    this.field = field;</span>
<span class="fc" id="L48">    return this;</span>
  }

  /**
   * Get field
   * @return field
   **/
  @Schema(description = &quot;&quot;)
  
    public String getField() {
<span class="fc" id="L58">    return field;</span>
  }

  public void setField(String field) {
<span class="fc" id="L62">    this.field = field;</span>
<span class="fc" id="L63">  }</span>

  public FieldAggregation rename(String rename) {
<span class="fc" id="L66">    this.rename = rename;</span>
<span class="fc" id="L67">    return this;</span>
  }

  /**
   * Get rename
   * @return rename
   **/
  @Schema(description = &quot;&quot;)
  
    public String getRename() {
<span class="fc" id="L77">    return rename;</span>
  }

  public void setRename(String rename) {
<span class="fc" id="L81">    this.rename = rename;</span>
<span class="fc" id="L82">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="fc bfc" id="L87" title="All 2 branches covered.">    if (this == o) {</span>
<span class="fc" id="L88">      return true;</span>
    }
<span class="fc bfc" id="L90" title="All 4 branches covered.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="fc" id="L91">      return false;</span>
    }
<span class="fc" id="L93">    FieldAggregation fieldAggregation = (FieldAggregation) o;</span>
<span class="fc bfc" id="L94" title="All 2 branches covered.">    return Objects.equals(this.function, fieldAggregation.function) &amp;&amp;</span>
<span class="fc bfc" id="L95" title="All 2 branches covered.">        Objects.equals(this.field, fieldAggregation.field) &amp;&amp;</span>
<span class="pc bpc" id="L96" title="1 of 2 branches missed.">        Objects.equals(this.rename, fieldAggregation.rename);</span>
  }

  @Override
  public int hashCode() {
<span class="fc" id="L101">    return Objects.hash(function, field, rename);</span>
  }

  @Override
  public String toString() {
<span class="fc" id="L106">    StringBuilder sb = new StringBuilder();</span>
<span class="fc" id="L107">    sb.append(&quot;class FieldAggregation {\n&quot;);</span>
    
<span class="fc" id="L109">    sb.append(&quot;    function: &quot;).append(toIndentedString(function)).append(&quot;\n&quot;);</span>
<span class="fc" id="L110">    sb.append(&quot;    field: &quot;).append(toIndentedString(field)).append(&quot;\n&quot;);</span>
<span class="fc" id="L111">    sb.append(&quot;    rename: &quot;).append(toIndentedString(rename)).append(&quot;\n&quot;);</span>
<span class="fc" id="L112">    sb.append(&quot;}&quot;);</span>
<span class="fc" id="L113">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="fc bfc" id="L121" title="All 2 branches covered.">    if (o == null) {</span>
<span class="fc" id="L122">      return &quot;null&quot;;</span>
    }
<span class="fc" id="L124">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>