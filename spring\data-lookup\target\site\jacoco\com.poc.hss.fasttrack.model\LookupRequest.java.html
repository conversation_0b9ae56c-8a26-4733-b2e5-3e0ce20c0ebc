<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">LookupRequest.java</span></div><h1>LookupRequest.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.FieldAggregation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * LookupRequest
 */
@Validated


<span class="fc" id="L20">public class LookupRequest   {</span>
<span class="fc" id="L21">  @JsonProperty(&quot;criteria&quot;)</span>
  private String criteria = null;

<span class="fc" id="L24">  @JsonProperty(&quot;fields&quot;)</span>
  @Valid
  private List&lt;String&gt; fields = null;

<span class="fc" id="L28">  @JsonProperty(&quot;orders&quot;)</span>
  @Valid
  private List&lt;String&gt; orders = null;

<span class="fc" id="L32">  @JsonProperty(&quot;limit&quot;)</span>
  private Integer limit = null;

<span class="fc" id="L35">  @JsonProperty(&quot;offset&quot;)</span>
  private Integer offset = null;

<span class="fc" id="L38">  @JsonProperty(&quot;groups&quot;)</span>
  @Valid
  private List&lt;String&gt; groups = null;

<span class="fc" id="L42">  @JsonProperty(&quot;fieldAggregations&quot;)</span>
  @Valid
  private List&lt;FieldAggregation&gt; fieldAggregations = null;

<span class="fc" id="L46">  @JsonProperty(&quot;distinct&quot;)</span>
  private Boolean distinct = null;

  public LookupRequest criteria(String criteria) {
<span class="fc" id="L50">    this.criteria = criteria;</span>
<span class="fc" id="L51">    return this;</span>
  }

  /**
   * Get criteria
   * @return criteria
   **/
  @Schema(description = &quot;&quot;)
  
    public String getCriteria() {
<span class="fc" id="L61">    return criteria;</span>
  }

  public void setCriteria(String criteria) {
<span class="fc" id="L65">    this.criteria = criteria;</span>
<span class="fc" id="L66">  }</span>

  public LookupRequest fields(List&lt;String&gt; fields) {
<span class="fc" id="L69">    this.fields = fields;</span>
<span class="fc" id="L70">    return this;</span>
  }

  public LookupRequest addFieldsItem(String fieldsItem) {
<span class="fc bfc" id="L74" title="All 2 branches covered.">    if (this.fields == null) {</span>
<span class="fc" id="L75">      this.fields = new ArrayList&lt;&gt;();</span>
    }
<span class="fc" id="L77">    this.fields.add(fieldsItem);</span>
<span class="fc" id="L78">    return this;</span>
  }

  /**
   * Get fields
   * @return fields
   **/
  @Schema(description = &quot;&quot;)
  
    public List&lt;String&gt; getFields() {
<span class="fc" id="L88">    return fields;</span>
  }

  public void setFields(List&lt;String&gt; fields) {
<span class="fc" id="L92">    this.fields = fields;</span>
<span class="fc" id="L93">  }</span>

  public LookupRequest orders(List&lt;String&gt; orders) {
<span class="fc" id="L96">    this.orders = orders;</span>
<span class="fc" id="L97">    return this;</span>
  }

  public LookupRequest addOrdersItem(String ordersItem) {
<span class="fc bfc" id="L101" title="All 2 branches covered.">    if (this.orders == null) {</span>
<span class="fc" id="L102">      this.orders = new ArrayList&lt;&gt;();</span>
    }
<span class="fc" id="L104">    this.orders.add(ordersItem);</span>
<span class="fc" id="L105">    return this;</span>
  }

  /**
   * Fields to order by (in format of [field_name]:[asc|desc])
   * @return orders
   **/
  @Schema(example = &quot;username:asc&quot;, description = &quot;Fields to order by (in format of [field_name]:[asc|desc])&quot;)
  
    public List&lt;String&gt; getOrders() {
<span class="fc" id="L115">    return orders;</span>
  }

  public void setOrders(List&lt;String&gt; orders) {
<span class="fc" id="L119">    this.orders = orders;</span>
<span class="fc" id="L120">  }</span>

  public LookupRequest limit(Integer limit) {
<span class="fc" id="L123">    this.limit = limit;</span>
<span class="fc" id="L124">    return this;</span>
  }

  /**
   * Get limit
   * @return limit
   **/
  @Schema(description = &quot;&quot;)
  
    public Integer getLimit() {
<span class="fc" id="L134">    return limit;</span>
  }

  public void setLimit(Integer limit) {
<span class="fc" id="L138">    this.limit = limit;</span>
<span class="fc" id="L139">  }</span>

  public LookupRequest offset(Integer offset) {
<span class="fc" id="L142">    this.offset = offset;</span>
<span class="fc" id="L143">    return this;</span>
  }

  /**
   * Get offset
   * @return offset
   **/
  @Schema(description = &quot;&quot;)
  
    public Integer getOffset() {
<span class="fc" id="L153">    return offset;</span>
  }

  public void setOffset(Integer offset) {
<span class="fc" id="L157">    this.offset = offset;</span>
<span class="fc" id="L158">  }</span>

  public LookupRequest groups(List&lt;String&gt; groups) {
<span class="fc" id="L161">    this.groups = groups;</span>
<span class="fc" id="L162">    return this;</span>
  }

  public LookupRequest addGroupsItem(String groupsItem) {
<span class="fc bfc" id="L166" title="All 2 branches covered.">    if (this.groups == null) {</span>
<span class="fc" id="L167">      this.groups = new ArrayList&lt;&gt;();</span>
    }
<span class="fc" id="L169">    this.groups.add(groupsItem);</span>
<span class="fc" id="L170">    return this;</span>
  }

  /**
   * Get groups
   * @return groups
   **/
  @Schema(description = &quot;&quot;)
  
    public List&lt;String&gt; getGroups() {
<span class="fc" id="L180">    return groups;</span>
  }

  public void setGroups(List&lt;String&gt; groups) {
<span class="fc" id="L184">    this.groups = groups;</span>
<span class="fc" id="L185">  }</span>

  public LookupRequest fieldAggregations(List&lt;FieldAggregation&gt; fieldAggregations) {
<span class="fc" id="L188">    this.fieldAggregations = fieldAggregations;</span>
<span class="fc" id="L189">    return this;</span>
  }

  public LookupRequest addFieldAggregationsItem(FieldAggregation fieldAggregationsItem) {
<span class="fc bfc" id="L193" title="All 2 branches covered.">    if (this.fieldAggregations == null) {</span>
<span class="fc" id="L194">      this.fieldAggregations = new ArrayList&lt;&gt;();</span>
    }
<span class="fc" id="L196">    this.fieldAggregations.add(fieldAggregationsItem);</span>
<span class="fc" id="L197">    return this;</span>
  }

  /**
   * Take precedence over fields if name conflicts, use together with group
   * @return fieldAggregations
   **/
  @Schema(description = &quot;Take precedence over fields if name conflicts, use together with group&quot;)
      @Valid
    public List&lt;FieldAggregation&gt; getFieldAggregations() {
<span class="fc" id="L207">    return fieldAggregations;</span>
  }

  public void setFieldAggregations(List&lt;FieldAggregation&gt; fieldAggregations) {
<span class="fc" id="L211">    this.fieldAggregations = fieldAggregations;</span>
<span class="fc" id="L212">  }</span>

  public LookupRequest distinct(Boolean distinct) {
<span class="fc" id="L215">    this.distinct = distinct;</span>
<span class="fc" id="L216">    return this;</span>
  }

  /**
   * Get distinct
   * @return distinct
   **/
  @Schema(description = &quot;&quot;)
  
    public Boolean isDistinct() {
<span class="fc" id="L226">    return distinct;</span>
  }

  public void setDistinct(Boolean distinct) {
<span class="fc" id="L230">    this.distinct = distinct;</span>
<span class="fc" id="L231">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="fc bfc" id="L236" title="All 2 branches covered.">    if (this == o) {</span>
<span class="fc" id="L237">      return true;</span>
    }
<span class="fc bfc" id="L239" title="All 4 branches covered.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="fc" id="L240">      return false;</span>
    }
<span class="fc" id="L242">    LookupRequest lookupRequest = (LookupRequest) o;</span>
<span class="fc bfc" id="L243" title="All 2 branches covered.">    return Objects.equals(this.criteria, lookupRequest.criteria) &amp;&amp;</span>
<span class="pc bpc" id="L244" title="1 of 2 branches missed.">        Objects.equals(this.fields, lookupRequest.fields) &amp;&amp;</span>
<span class="pc bpc" id="L245" title="1 of 2 branches missed.">        Objects.equals(this.orders, lookupRequest.orders) &amp;&amp;</span>
<span class="pc bpc" id="L246" title="1 of 2 branches missed.">        Objects.equals(this.limit, lookupRequest.limit) &amp;&amp;</span>
<span class="pc bpc" id="L247" title="1 of 2 branches missed.">        Objects.equals(this.offset, lookupRequest.offset) &amp;&amp;</span>
<span class="pc bpc" id="L248" title="1 of 2 branches missed.">        Objects.equals(this.groups, lookupRequest.groups) &amp;&amp;</span>
<span class="pc bpc" id="L249" title="1 of 2 branches missed.">        Objects.equals(this.fieldAggregations, lookupRequest.fieldAggregations) &amp;&amp;</span>
<span class="pc bpc" id="L250" title="1 of 2 branches missed.">        Objects.equals(this.distinct, lookupRequest.distinct);</span>
  }

  @Override
  public int hashCode() {
<span class="fc" id="L255">    return Objects.hash(criteria, fields, orders, limit, offset, groups, fieldAggregations, distinct);</span>
  }

  @Override
  public String toString() {
<span class="fc" id="L260">    StringBuilder sb = new StringBuilder();</span>
<span class="fc" id="L261">    sb.append(&quot;class LookupRequest {\n&quot;);</span>
    
<span class="fc" id="L263">    sb.append(&quot;    criteria: &quot;).append(toIndentedString(criteria)).append(&quot;\n&quot;);</span>
<span class="fc" id="L264">    sb.append(&quot;    fields: &quot;).append(toIndentedString(fields)).append(&quot;\n&quot;);</span>
<span class="fc" id="L265">    sb.append(&quot;    orders: &quot;).append(toIndentedString(orders)).append(&quot;\n&quot;);</span>
<span class="fc" id="L266">    sb.append(&quot;    limit: &quot;).append(toIndentedString(limit)).append(&quot;\n&quot;);</span>
<span class="fc" id="L267">    sb.append(&quot;    offset: &quot;).append(toIndentedString(offset)).append(&quot;\n&quot;);</span>
<span class="fc" id="L268">    sb.append(&quot;    groups: &quot;).append(toIndentedString(groups)).append(&quot;\n&quot;);</span>
<span class="fc" id="L269">    sb.append(&quot;    fieldAggregations: &quot;).append(toIndentedString(fieldAggregations)).append(&quot;\n&quot;);</span>
<span class="fc" id="L270">    sb.append(&quot;    distinct: &quot;).append(toIndentedString(distinct)).append(&quot;\n&quot;);</span>
<span class="fc" id="L271">    sb.append(&quot;}&quot;);</span>
<span class="fc" id="L272">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="fc bfc" id="L280" title="All 2 branches covered.">    if (o == null) {</span>
<span class="fc" id="L281">      return &quot;null&quot;;</span>
    }
<span class="fc" id="L283">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>