<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">LookupResponse.java</span></div><h1>LookupResponse.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.poc.hss.fasttrack.model.Error;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import jakarta.validation.constraints.*;

/**
 * LookupResponse
 */
@Validated


<span class="fc" id="L21">public class LookupResponse   {</span>
<span class="fc" id="L22">  @JsonProperty(&quot;data&quot;)</span>
  @Valid
  private List&lt;Map&lt;String, Object&gt;&gt; data = null;

<span class="fc" id="L26">  @JsonProperty(&quot;errors&quot;)</span>
  @Valid
  private List&lt;Error&gt; errors = null;

  public LookupResponse data(List&lt;Map&lt;String, Object&gt;&gt; data) {
<span class="fc" id="L31">    this.data = data;</span>
<span class="fc" id="L32">    return this;</span>
  }

  public LookupResponse addDataItem(Map&lt;String, Object&gt; dataItem) {
<span class="fc bfc" id="L36" title="All 2 branches covered.">    if (this.data == null) {</span>
<span class="fc" id="L37">      this.data = new ArrayList&lt;&gt;();</span>
    }
<span class="fc" id="L39">    this.data.add(dataItem);</span>
<span class="fc" id="L40">    return this;</span>
  }

  /**
   * Get data
   * @return data
   **/
  @Schema(description = &quot;&quot;)
      @Valid
    public List&lt;Map&lt;String, Object&gt;&gt; getData() {
<span class="fc" id="L50">    return data;</span>
  }

  public void setData(List&lt;Map&lt;String, Object&gt;&gt; data) {
<span class="fc" id="L54">    this.data = data;</span>
<span class="fc" id="L55">  }</span>

  public LookupResponse errors(List&lt;Error&gt; errors) {
<span class="fc" id="L58">    this.errors = errors;</span>
<span class="fc" id="L59">    return this;</span>
  }

  public LookupResponse addErrorsItem(Error errorsItem) {
<span class="fc bfc" id="L63" title="All 2 branches covered.">    if (this.errors == null) {</span>
<span class="fc" id="L64">      this.errors = new ArrayList&lt;&gt;();</span>
    }
<span class="fc" id="L66">    this.errors.add(errorsItem);</span>
<span class="fc" id="L67">    return this;</span>
  }

  /**
   * Get errors
   * @return errors
   **/
  @Schema(description = &quot;&quot;)
      @Valid
    public List&lt;Error&gt; getErrors() {
<span class="fc" id="L77">    return errors;</span>
  }

  public void setErrors(List&lt;Error&gt; errors) {
<span class="fc" id="L81">    this.errors = errors;</span>
<span class="fc" id="L82">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="fc bfc" id="L87" title="All 2 branches covered.">    if (this == o) {</span>
<span class="fc" id="L88">      return true;</span>
    }
<span class="fc bfc" id="L90" title="All 4 branches covered.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="fc" id="L91">      return false;</span>
    }
<span class="fc" id="L93">    LookupResponse lookupResponse = (LookupResponse) o;</span>
<span class="fc bfc" id="L94" title="All 2 branches covered.">    return Objects.equals(this.data, lookupResponse.data) &amp;&amp;</span>
<span class="pc bpc" id="L95" title="1 of 2 branches missed.">        Objects.equals(this.errors, lookupResponse.errors);</span>
  }

  @Override
  public int hashCode() {
<span class="fc" id="L100">    return Objects.hash(data, errors);</span>
  }

  @Override
  public String toString() {
<span class="fc" id="L105">    StringBuilder sb = new StringBuilder();</span>
<span class="fc" id="L106">    sb.append(&quot;class LookupResponse {\n&quot;);</span>
    
<span class="fc" id="L108">    sb.append(&quot;    data: &quot;).append(toIndentedString(data)).append(&quot;\n&quot;);</span>
<span class="fc" id="L109">    sb.append(&quot;    errors: &quot;).append(toIndentedString(errors)).append(&quot;\n&quot;);</span>
<span class="fc" id="L110">    sb.append(&quot;}&quot;);</span>
<span class="fc" id="L111">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="fc bfc" id="L119" title="All 2 branches covered.">    if (o == null) {</span>
<span class="fc" id="L120">      return &quot;null&quot;;</span>
    }
<span class="fc" id="L122">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>