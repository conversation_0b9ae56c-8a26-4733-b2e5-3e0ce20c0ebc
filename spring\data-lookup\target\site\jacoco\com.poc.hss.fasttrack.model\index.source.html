<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <span class="el_package">com.poc.hss.fasttrack.model</span></div><h1>com.poc.hss.fasttrack.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">0 of 857</td><td class="ctr2">100%</td><td class="bar">9 of 74</td><td class="ctr2">87%</td><td class="ctr1">9</td><td class="ctr2">108</td><td class="ctr1">0</td><td class="ctr2">200</td><td class="ctr1">0</td><td class="ctr2">71</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a2"><a href="LookupRequest.java.html" class="el_source">LookupRequest.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="409" alt="409"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="93" height="10" title="25" alt="25"/></td><td class="ctr2" id="e3">78%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">49</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">94</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">33</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="FieldAggregation.java.html" class="el_source">FieldAggregation.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="164" alt="164"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="13" alt="13"/></td><td class="ctr2" id="e2">92%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">21</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">38</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">14</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="LookupResponse.java.html" class="el_source">LookupResponse.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="157" alt="157"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="56" height="10" title="15" alt="15"/></td><td class="ctr2" id="e1">93%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">21</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">38</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="Error.java.html" class="el_source">Error.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="37" height="10" title="127" alt="127"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">17</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">30</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">11</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>