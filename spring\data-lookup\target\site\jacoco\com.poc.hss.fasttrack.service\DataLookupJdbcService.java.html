<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DataLookupJdbcService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">DataLookupJdbcService.java</span></div><h1>DataLookupJdbcService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.dao.JdbcDao;
import com.poc.hss.fasttrack.dto.JdbcCriteriaDTO;
import com.poc.hss.fasttrack.jdbc.util.GenericJdbcUtils;
import io.vertx.core.json.JsonObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L16">public class DataLookupJdbcService {</span>

    @Value(&quot;${projectId}&quot;)
    private String projectId;

    @Autowired
    private JdbcDao jdbcDao;

    public List&lt;Map&lt;String, Object&gt;&gt; lookup(final String tableName, JdbcCriteriaDTO criteriaDTO) {
<span class="fc" id="L25">        return jdbcDao.findJsonObjectByCriteria(</span>
<span class="fc" id="L26">                GenericJdbcUtils.getDoubleQuotedSchemaName(projectId),</span>
                tableName,
                criteriaDTO
        )
<span class="fc" id="L30">                .stream()</span>
<span class="fc" id="L31">                .map(JsonObject::getMap)</span>
<span class="fc" id="L32">                .collect(Collectors.toList());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>