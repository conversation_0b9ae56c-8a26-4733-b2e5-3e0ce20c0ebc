<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.poc.hss.fasttrack</groupId>
        <artifactId>pipeline-parent</artifactId>
        <version>2.8.6-SNAPSHOT</version>
    </parent>
    <artifactId>kafka-operation-tool</artifactId>
    <version>2.8.6-SNAPSHOT</version>
    <name>kafka-operation-tool</name>
    <description>Kafka operation tool</description>
    <dependencies>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-kafka</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-metric</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-k8s-client</artifactId>
        </dependency>
		<dependency>
			<groupId>org.springframework.shell</groupId>
			<artifactId>spring-shell-starter</artifactId>
            <version>3.4.0</version>
		</dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-jasypt</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.poc.hss.fasttrack</groupId>
            <artifactId>pipeline-common-test-kafka</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <!-- Maven Surefire Plugin for Test Execution -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!-- Ensure all test classes are discovered -->
                    <includes>
                        <include>**/*Test.java</include>
                        <include>**/*Tests.java</include>
                    </includes>
                </configuration>
            </plugin>
            <!-- JaCoCo Maven Plugin for Code Coverage -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <excludes>
                        <exclude>**/config/**</exclude>
                        <exclude>**/*Application.class</exclude>
                        <exclude>**/*$Builder.class</exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>