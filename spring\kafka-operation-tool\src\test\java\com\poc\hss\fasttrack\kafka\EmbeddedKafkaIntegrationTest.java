package com.poc.hss.fasttrack.kafka;

import com.poc.hss.fasttrack.kafka.KafkaTest;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.kafka.test.EmbeddedKafkaBroker;
import org.springframework.kafka.test.utils.KafkaTestUtils;
import org.springframework.test.context.ActiveProfiles;

import java.time.Duration;
import java.util.Collections;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@KafkaTest
@ActiveProfiles("test")
public class EmbeddedKafkaIntegrationTest {

    @Autowired
    private EmbeddedKafkaBroker embeddedKafkaBroker;

    @Test
    public void testEmbeddedKafkaSetup() {
        // Verify that embedded Kafka broker is running
        assertNotNull(embeddedKafkaBroker);
        assertTrue(embeddedKafkaBroker.getBrokersAsString().contains("localhost:13333"));
        
        System.out.println("Embedded Kafka Broker: " + embeddedKafkaBroker.getBrokersAsString());
        System.out.println("Embedded Kafka Topics: " + embeddedKafkaBroker.getTopics());
    }

    @Test
    public void testProduceAndConsumeMessage() throws Exception {
        String testTopic = "dummy-topic-0";
        String testMessage = "Hello Embedded Kafka!";
        String testKey = "test-key";

        // Producer configuration
        Map<String, Object> producerProps = KafkaTestUtils.producerProps(embeddedKafkaBroker);
        producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);

        // Create producer and send message
        try (KafkaProducer<String, String> producer = new KafkaProducer<>(producerProps)) {
            ProducerRecord<String, String> record = new ProducerRecord<>(testTopic, testKey, testMessage);
            producer.send(record).get(); // Wait for send to complete
            producer.flush();
        }

        // Consumer configuration
        Map<String, Object> consumerProps = KafkaTestUtils.consumerProps("test-group", "true", embeddedKafkaBroker);
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "earliest");

        // Create consumer and consume message
        Consumer<String, String> consumer = new org.apache.kafka.clients.consumer.KafkaConsumer<>(consumerProps);
        consumer.subscribe(Collections.singletonList(testTopic));

        // Poll for messages
        ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(10));
        
        // Verify message was received
        assertFalse(records.isEmpty(), "Should have received at least one message");
        
        ConsumerRecord<String, String> receivedRecord = records.iterator().next();
        assertEquals(testKey, receivedRecord.key());
        assertEquals(testMessage, receivedRecord.value());
        assertEquals(testTopic, receivedRecord.topic());
        
        System.out.println("Successfully sent and received message: " + receivedRecord.value());
        
        consumer.close();
    }

    @Test
    public void testMultipleTopics() {
        // Verify that multiple topics are created as configured in @KafkaTest
        assertTrue(embeddedKafkaBroker.getTopics().contains("dummy-topic-0"));
        assertTrue(embeddedKafkaBroker.getTopics().contains("dummy-topic-1"));
        
        System.out.println("Available topics: " + embeddedKafkaBroker.getTopics());
    }
}
