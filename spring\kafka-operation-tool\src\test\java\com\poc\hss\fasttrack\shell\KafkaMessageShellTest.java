package com.poc.hss.fasttrack.shell;

import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.kafka.util.KafkaUtil;
import com.poc.hss.fasttrack.service.KafkaService;
import com.poc.hss.fasttrack.util.K8sUtil;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class KafkaMessageShellTest {
    @InjectMocks
    private KafkaMessageShell shell;
    @Mock
    private KafkaService kafkaService;
    @Mock
    private K8sUtil k8sUtil;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private KafkaMessageDTO createKafkaMessageDTO() {
        ConsumerRecord<String, String> record = new ConsumerRecord<>("topic", 0, 0L, "key", "value");
        KafkaMessageDTO dto = new KafkaMessageDTO();
        dto.setConsumerRecord(record);
        return dto;
    }

    @Test
    void testScanMessage_countOnly() {
        when(kafkaService.scanMessagesWithTotalCount(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(5L);
        String result = shell.scanMessage("topic", "format", null, "keyword", null, null, null, null, null, null, 10,
                true);
        assertTrue(result.contains("Count: 5"));
    }

    @Test
    void testScanMessage_result() {
        when(kafkaService.scanMessagesWithResult(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of(createKafkaMessageDTO()));
        String result = shell.scanMessage("topic", "format", null, "keyword", null, null, null, null, null, null, 10,
                false);
        assertTrue(result.contains("Result:"));
    }

    @Test
    void testScanMessage_inputError() {
        String result = shell.scanMessage("topic", "format", null, null, null, null, null, null, null, null, null,
                false);
        assertTrue(result.contains("must be specified"));
    }

    @Test
    void testListAllMessage_countOnly() {
        when(kafkaService.scanMessagesWithTotalCount(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(3L);
        String result = shell.listAllMessage("topic", "format", 24, null, null, null, null, true);
        assertTrue(result.contains("Count: 3"));
    }

    @Test
    void testListAllMessage_result() {
        when(kafkaService.scanMessagesWithResult(any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of(createKafkaMessageDTO()));
        String result = shell.listAllMessage("topic", "format", 24, null, null, null, null, false);
        assertTrue(result.contains("Result:"));
    }

    @Test
    void testReplayRangeMessages() {
        when(kafkaService.replayMessages(any(), any(), any(), any(), any(), any(), any(), any(), any(), any()))
                .thenReturn(List.of(createKafkaMessageDTO()));
        String result = shell.replayRangeMessages("src", "fmt", "tgt", null, null, null, null, null, null, null, null,
                null);
        assertEquals("replay successfully", result);
    }

    @Test
    void testPublishMessage() {
        String result = shell.publishMessage("topic", "fmt", "key", "msg");
        assertEquals("Publish successfully", result);
    }

    @Test
    void testResetOffset_inputError() {
        String result = shell.resetOffset("topic", "group", -1, -1L, false, false, false, false);
        assertTrue(result.contains("must be specified"));
    }

    @Test
    void testCheckTopicLag() {
        KafkaUtil.Metric metric = mock(KafkaUtil.Metric.class);
        when(metric.getPartition()).thenReturn(0);
        when(metric.getBeginningOffset()).thenReturn(0L);
        when(metric.getCommittedOffset()).thenReturn(1L);
        when(metric.getEndOffset()).thenReturn(2L);
        when(metric.getLag()).thenReturn(1L);
        when(kafkaService.checkOffset(any(), any())).thenReturn(List.of(metric));
        String result = shell.checkTopicLag("topic", "group");
        assertTrue(result.contains("partition: 0"));
        assertTrue(result.contains("Total lag: 1"));
    }
}