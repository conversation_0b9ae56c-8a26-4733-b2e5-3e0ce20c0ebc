kafkaConfig:
  bootstrap.servers: localhost:13333
  auto.offset.reset: earliest
  group.id: test-group-id
  schema.registry.url: https://schema-registry-0.ns-embtest-dev-apps.gke-t2-vpc3-377436.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc/subjects
  ssl.keystore.password: /hss/apps/certs/env/test/unity-microservices.jks.secret
  ssl.key.password: /hss/apps/certs/env/test/unity-microservices.jks.secret
  ssl.truststore.location: /hss/apps/certs/env/test/unity-microservices.ts
  ssl.truststore.password: /hss/apps/certs/env/test/unity-microservices.ts.secret
  ssl.rsa.private.key: /hss/apps/certs/env/test/ocustody_id_rsa.pem
  kafka.password.encryption.method: rsa
  delete.topic.enable: true
logging:
  level:
    com.poc.hss.fasttrack: INFO
    org.apache: WARN
    org.springframework: WARN
    state.change.logger: WARN
    kafka: WARN
