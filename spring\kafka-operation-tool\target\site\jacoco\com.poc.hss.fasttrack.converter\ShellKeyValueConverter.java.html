<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ShellKeyValueConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.converter</a> &gt; <span class="el_source">ShellKeyValueConverter.java</span></div><h1>ShellKeyValueConverter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.converter;

import com.poc.hss.fasttrack.model.ShellKeyValue;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
<span class="fc" id="L9">public class ShellKeyValueConverter implements Converter&lt;String, ShellKeyValue&gt; {</span>
    @Override
    public ShellKeyValue convert(String source) {
<span class="fc" id="L12">        String key = StringUtils.split(source, &quot;=&quot;)[0];</span>
<span class="fc" id="L13">        String value = StringUtils.split(source, &quot;=&quot;)[1];</span>
<span class="fc" id="L14">        return ShellKeyValue.builder()</span>
<span class="fc" id="L15">                .key(key)</span>
<span class="fc" id="L16">                .value(value)</span>
<span class="fc" id="L17">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>