<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InMemoryKafkaMessageStore.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka</a> &gt; <span class="el_source">InMemoryKafkaMessageStore.java</span></div><h1>InMemoryKafkaMessageStore.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka;

import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.kafka.reader.KafkaReader;
import com.poc.hss.fasttrack.service.GenericKafkaService;
import com.poc.hss.fasttrack.util.ConversionHelperService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

<span class="fc" id="L20">@Slf4j</span>
<span class="nc" id="L21">@AllArgsConstructor</span>
@Component
@Scope(&quot;prototype&quot;)
public class InMemoryKafkaMessageStore implements MessageStore {

    private KafkaReader&lt;Object, ConsumerRecord&gt; kafkaReader;

    private String groupId;

<span class="nc" id="L30">    public InMemoryKafkaMessageStore(KafkaReader kafkaReader, String groupId) {</span>
<span class="nc" id="L31">        this.kafkaReader = kafkaReader;</span>
<span class="nc" id="L32">        this.groupId = groupId;</span>
<span class="nc" id="L33">    }</span>

    @Autowired
    private ConversionHelperService conversionHelperService;

    @Autowired
    private GenericKafkaService genericKafkaService;

    @Override
    public void destroy() {
<span class="nc" id="L43">        kafkaReader.getConsumer().close();</span>
<span class="nc" id="L44">    }</span>

    @Override
    public Map&lt;String, Long&gt; scanAllMessagesWithCount(LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword, Function&lt;KafkaMessageDTO, String&gt; keyMappers) {
<span class="nc" id="L48">        final Consumer&lt;String, Object&gt; consumer = kafkaReader.getConsumer();</span>
<span class="nc" id="L49">        String topic = kafkaReader.getTopics().get(0);</span>
<span class="nc" id="L50">        log.info(&quot;Start scanning message, Consumer Group: {}, Topic: {}, partition: {}&quot;, kafkaReader.getProperties().getProperty(&quot;group.id&quot;), topic, partition);</span>
<span class="nc" id="L51">        return genericKafkaService.scanAllMessagesWithCount(consumer,</span>
                fromTimestamp,
                toTimestamp,
                maxRecords, partition, fromOffset, toOffset, keyword, keyMappers, topic);
    }

    @Override
    public List&lt;KafkaMessageDTO&gt; scanAllMessagesWithResult(LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword) {
<span class="nc" id="L59">        final Consumer&lt;String, Object&gt; consumer = kafkaReader.getConsumer();</span>
<span class="nc" id="L60">        String topic = kafkaReader.getTopics().get(0);</span>
<span class="nc" id="L61">        log.info(&quot;Start scanning message, Consumer Group: {}, Topic: {}, partition: {}&quot;, kafkaReader.getProperties().getProperty(&quot;group.id&quot;), topic, partition);</span>
<span class="nc" id="L62">        return genericKafkaService.scanAllMessagesWithResult(consumer,</span>
                fromTimestamp,
                toTimestamp,
                maxRecords, partition, fromOffset, toOffset, keyword, topic);
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>