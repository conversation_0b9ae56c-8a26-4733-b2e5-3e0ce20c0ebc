<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MessageStoreManager.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka</a> &gt; <span class="el_source">MessageStoreManager.java</span></div><h1>MessageStoreManager.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka;

import com.poc.hss.fasttrack.config.OperationShellConfig;
import com.poc.hss.fasttrack.dto.KafkaMessageFormat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.*;

<span class="fc" id="L12">@Slf4j</span>
@Component
<span class="fc" id="L14">public class MessageStoreManager {</span>

    @Autowired
    private ApplicationContext context;

    @Autowired
    private OperationShellConfig operationShellConfig;



<span class="fc" id="L24">    private final Map&lt;String, MessageStore&gt; messageStoreHolder = new HashMap&lt;&gt;();</span>

    public MessageStore getMessageStore(String topic, KafkaMessageFormat kafkaMessageFormat) {
<span class="fc" id="L27">        return getMessageStore(topic, kafkaMessageFormat, UUID.randomUUID().toString(), 10000, true);</span>
    }

    public MessageStore getMessageStore(String topic, KafkaMessageFormat kafkaMessageFormat, Boolean cached) {
<span class="fc" id="L31">        return getMessageStore(topic, kafkaMessageFormat, UUID.randomUUID().toString(), 10000, cached);</span>
    }

    public MessageStore getMessageStore(String topic, KafkaMessageFormat kafkaMessageFormat, String groupId, Integer maxPollRecord, Boolean cached) {
<span class="fc" id="L35">        Properties kafkaProperties = new Properties();</span>
<span class="fc" id="L36">        kafkaProperties.putAll(operationShellConfig.getKafkaConfig());</span>
<span class="fc" id="L37">        kafkaProperties.put(&quot;enable.auto.commit&quot;, false);</span>
<span class="fc" id="L38">        kafkaProperties.put(&quot;group.id&quot;, groupId);</span>
<span class="fc" id="L39">        kafkaProperties.putIfAbsent(&quot;max.poll.records&quot;, maxPollRecord);</span>
<span class="pc bpc" id="L40" title="1 of 2 branches missed.">        MessageStore messageStore = cached ? messageStoreHolder.get(topic) : null;</span>

<span class="fc bfc" id="L42" title="All 2 branches covered.">        if (messageStore != null) {</span>
<span class="fc" id="L43">            return messageStore;</span>
        }
<span class="fc bfc" id="L45" title="All 2 branches covered.">        switch (kafkaMessageFormat) {</span>
            case AVRO:
<span class="fc" id="L47">                messageStore = context.getBean(InMemoryKafkaMessageStore.class, new NoOpKafkaAvroReader(kafkaProperties, Collections.singletonList(topic)), groupId);</span>
<span class="fc" id="L48">                break;</span>
            case JSON:
            default:
<span class="fc" id="L51">                kafkaProperties.remove(&quot;schema.registry.url&quot;);</span>
<span class="fc" id="L52">                messageStore = context.getBean(InMemoryKafkaMessageStore.class, new NoOpKafkaMessageReader(kafkaProperties, Collections.singletonList(topic)), groupId);</span>
        }
<span class="pc bpc" id="L54" title="1 of 2 branches missed.">        if (cached)</span>
<span class="fc" id="L55">            messageStoreHolder.put(topic, messageStore);</span>
<span class="fc" id="L56">        return messageStore;</span>
    }

    public void remove(String topic){
<span class="fc" id="L60">        messageStoreHolder.remove(topic);</span>
<span class="fc" id="L61">    }</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>