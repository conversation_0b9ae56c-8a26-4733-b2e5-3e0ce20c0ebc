<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>NoOpKafkaMessageReader.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka</a> &gt; <span class="el_source">NoOpKafkaMessageReader.java</span></div><h1>NoOpKafkaMessageReader.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka;

import com.poc.hss.fasttrack.kafka.reader.KafkaReader;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.serialization.StringDeserializer;

import java.util.List;
import java.util.Properties;
import java.util.function.Function;

public class NoOpKafkaMessageReader extends KafkaReader&lt;String, ConsumerRecord&gt; {

    public NoOpKafkaMessageReader(Properties properties, List&lt;String&gt; topics) {
<span class="fc" id="L15">        super(properties, topics);</span>
<span class="fc" id="L16">    }</span>

    @Override
    protected Function&lt;ConsumerRecord&lt;String, String&gt;, ConsumerRecord&gt; converter() {
        // do nothing
<span class="fc" id="L21">        return c -&gt; c;</span>
    }

    @Override
    protected void propertiesCustomizer(Properties properties) {
<span class="fc" id="L26">        properties.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());</span>
<span class="fc" id="L27">        properties.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());</span>
<span class="fc" id="L28">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>