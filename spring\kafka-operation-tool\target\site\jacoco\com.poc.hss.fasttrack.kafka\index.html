<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.kafka</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <span class="el_package">com.poc.hss.fasttrack.kafka</span></div><h1>com.poc.hss.fasttrack.kafka</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">121 of 310</td><td class="ctr2">60%</td><td class="bar">2 of 8</td><td class="ctr2">75%</td><td class="ctr1">7</td><td class="ctr2">24</td><td class="ctr1">15</td><td class="ctr2">51</td><td class="ctr1">5</td><td class="ctr2">20</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a0"><a href="InMemoryKafkaMessageStore.html" class="el_class">InMemoryKafkaMessageStore</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="101" height="10" title="120" alt="120"/><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">3%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j0">5</td><td class="ctr2" id="k0">6</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="MessageStoreManager.html" class="el_class">MessageStoreManager</a></td><td class="bar" id="b1"><img src="../jacoco-resources/greenbar.gif" width="119" height="10" title="141" alt="141"/></td><td class="ctr2" id="c2">99%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">75%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">6</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="NoOpKafkaAvroReader.html" class="el_class">NoOpKafkaAvroReader</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="NoOpKafkaMessageReader.html" class="el_class">NoOpKafkaMessageReader</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="18" height="10" title="22" alt="22"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>