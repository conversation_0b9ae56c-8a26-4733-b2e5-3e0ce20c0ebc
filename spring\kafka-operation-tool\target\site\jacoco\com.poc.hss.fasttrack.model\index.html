<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <span class="el_package">com.poc.hss.fasttrack.model</span></div><h1>com.poc.hss.fasttrack.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">24 of 155</td><td class="ctr2">84%</td><td class="bar">12 of 22</td><td class="ctr2">45%</td><td class="ctr1">11</td><td class="ctr2">27</td><td class="ctr1">0</td><td class="ctr2">6</td><td class="ctr1">1</td><td class="ctr2">16</td><td class="ctr1">0</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a0"><a href="ShellKeyValue.html" class="el_class">ShellKeyValue</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="103" height="10" title="110" alt="110"/></td><td class="ctr2" id="c0">85%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="54" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">45%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">22</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">6</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">11</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="ShellKeyValue$ShellKeyValueBuilder.html" class="el_class">ShellKeyValue.ShellKeyValueBuilder</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="21" alt="21"/></td><td class="ctr2" id="c1">77%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">5</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>