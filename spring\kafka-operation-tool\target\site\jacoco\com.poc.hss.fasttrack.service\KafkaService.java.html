<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">KafkaService.java</span></div><h1>KafkaService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.config.ConsumerProvider;
import com.poc.hss.fasttrack.config.KafkaProducerManager;
import com.poc.hss.fasttrack.config.OperationShellConfig;
import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.dto.KafkaMessageFormat;
import com.poc.hss.fasttrack.kafka.MessageStore;
import com.poc.hss.fasttrack.kafka.MessageStoreManager;
import com.poc.hss.fasttrack.kafka.util.KafkaUtil;
import io.vertx.core.json.JsonObject;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

<span class="fc" id="L22">@Slf4j</span>
@Component
<span class="fc" id="L24">public class KafkaService {</span>

    @Autowired
    private OperationShellConfig operationShellConfig;

    @Autowired
    private KafkaProducerManager kafkaProducerManager;

    @Autowired
    private MessageStoreManager messageStoreManager;

    @Autowired
    private GenericKafkaService genericKafkaService;

    @Autowired
    private ConsumerProvider consumerProvider;

    @PostConstruct
    public void initProducerManager() {
<span class="nc" id="L43">        kafkaProducerManager.init(operationShellConfig.getKafkaConfig());</span>
<span class="nc" id="L44">    }</span>

    public void publish(String topic, String format, String key, String messageContent) {
<span class="fc" id="L47">        kafkaProducerManager.produceMessage(KafkaMessageFormat.fromValue(format), topic, key, new JsonObject(messageContent));</span>
<span class="fc" id="L48">    }</span>

    public void publish(String topic, String format, List&lt;KafkaMessageDTO&gt; kafkaMessages) {
<span class="fc" id="L51">        kafkaProducerManager.produceMessages(KafkaMessageFormat.fromValue(format), topic, kafkaMessages);</span>
<span class="fc" id="L52">    }</span>

    public boolean resetOffset(String topic, String consumerGroup, Integer partition, Long offset, Boolean toLatest, Boolean toBeginning, Boolean ifNull, Boolean allPartitions) {
<span class="fc" id="L55">        try (Consumer consumer = consumerProvider.getConsumer(consumerGroup)) {</span>
<span class="fc" id="L56">            return genericKafkaService.resetOffset(consumer, topic, partition, offset, toLatest, toBeginning, ifNull, allPartitions);</span>
        }
    }

    public List&lt;KafkaUtil.Metric&gt; checkOffset(String topic, String consumerGroup) {
<span class="fc" id="L61">        try (Consumer consumer = consumerProvider.getConsumer(consumerGroup)) {</span>
<span class="fc" id="L62">            return KafkaUtil.getMetrics(consumer, topic);</span>
        }
    }

    public List&lt;KafkaMessageDTO&gt; scanMessagesWithResult(String topic, String format, LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword) {
<span class="fc" id="L67">        MessageStore messageStore = messageStoreManager.getMessageStore(topic, KafkaMessageFormat.fromValue(format), false);</span>
<span class="fc" id="L68">        List&lt;KafkaMessageDTO&gt; result = messageStore.scanAllMessagesWithResult(fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword);</span>
<span class="fc" id="L69">        messageStore.destroy();</span>
<span class="fc" id="L70">        return result;</span>
    }

    public Long scanMessagesWithTotalCount(String topic, String format, LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword) {
<span class="nc" id="L74">        MessageStore messageStore = messageStoreManager.getMessageStore(topic, KafkaMessageFormat.fromValue(format), false);</span>
<span class="nc" id="L75">        Map&lt;String, Long&gt; result = messageStore.scanAllMessagesWithCount(fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword, e -&gt; &quot;ALL&quot;);</span>
<span class="nc" id="L76">        messageStore.destroy();</span>
<span class="nc" id="L77">        return result.get(&quot;ALL&quot;);</span>
    }

    public Map&lt;String, Long&gt; scanMessagesWithBatchCount(String topic, String format, LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword) {
<span class="nc" id="L81">        MessageStore messageStore = messageStoreManager.getMessageStore(topic, KafkaMessageFormat.fromValue(format), false);</span>
<span class="nc" id="L82">        Map&lt;String, Long&gt; result = messageStore.scanAllMessagesWithCount(fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword, GenericKafkaService::getBatchId);</span>
<span class="nc" id="L83">        messageStore.destroy();</span>
<span class="nc" id="L84">        return result;</span>
    }

    public List&lt;KafkaMessageDTO&gt; replayMessages(String sourceTopic, String format, String targetTopic, LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword) {
<span class="nc" id="L88">        MessageStore messageStore = messageStoreManager.getMessageStore(sourceTopic, KafkaMessageFormat.fromValue(format), false);</span>
<span class="nc" id="L89">        List&lt;KafkaMessageDTO&gt; kafkaMessages = messageStore.scanAllMessagesWithResult(fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword);</span>
<span class="nc" id="L90">        publish(targetTopic, format, kafkaMessages);</span>
<span class="nc" id="L91">        return kafkaMessages;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>