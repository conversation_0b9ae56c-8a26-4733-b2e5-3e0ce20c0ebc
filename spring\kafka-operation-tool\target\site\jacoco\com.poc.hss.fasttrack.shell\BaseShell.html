<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BaseShell</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.shell</a> &gt; <span class="el_class">BaseShell</span></div><h1>BaseShell</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4 of 55</td><td class="ctr2">92%</td><td class="bar">4 of 16</td><td class="ctr2">75%</td><td class="ctr1">4</td><td class="ctr2">11</td><td class="ctr1">2</td><td class="ctr2">17</td><td class="ctr1">0</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="BaseShell.java.html#L8" class="el_method">getToTimestamp(Integer, String, LocalDateTime)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="110" height="10" title="24" alt="24"/></td><td class="ctr2" id="c1">92%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">75%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">8</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="BaseShell.java.html#L20" class="el_method">getFromTimestamp(Integer, String, LocalDateTime)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="110" height="10" title="24" alt="24"/></td><td class="ctr2" id="c2">92%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">8</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a0"><a href="BaseShell.java.html#L6" class="el_method">BaseShell()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="13" height="10" title="3" alt="3"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>