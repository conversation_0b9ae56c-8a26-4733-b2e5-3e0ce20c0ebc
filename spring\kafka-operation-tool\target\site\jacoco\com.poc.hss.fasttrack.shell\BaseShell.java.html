<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BaseShell.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.shell</a> &gt; <span class="el_source">BaseShell.java</span></div><h1>BaseShell.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.shell;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

<span class="fc" id="L6">public class BaseShell {</span>
    protected LocalDateTime getToTimestamp(Integer toHours, String toTime, LocalDateTime defaultValue) {
<span class="fc" id="L8">        LocalDateTime toTimestamp = defaultValue;</span>
<span class="fc bfc" id="L9" title="All 2 branches covered.">        if (toHours != null)</span>
<span class="fc" id="L10">            toTimestamp = LocalDateTime.now().minusHours(toHours);</span>
<span class="fc bfc" id="L11" title="All 2 branches covered.">        if (toTime != null) {</span>
<span class="fc" id="L12">            LocalDateTime localDateTime = LocalDateTime.parse(toTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME);</span>
<span class="pc bpc" id="L13" title="2 of 4 branches missed.">            if (toTimestamp == null || localDateTime.isBefore(toTimestamp))</span>
<span class="nc" id="L14">                toTimestamp = localDateTime;</span>
        }
<span class="fc" id="L16">        return toTimestamp;</span>
    }

    protected LocalDateTime getFromTimestamp(Integer fromHours, String fromTime, LocalDateTime defaultValue) {
<span class="fc" id="L20">        LocalDateTime fromTimestamp = defaultValue;</span>
<span class="fc bfc" id="L21" title="All 2 branches covered.">        if (fromHours != null)</span>
<span class="fc" id="L22">            fromTimestamp = LocalDateTime.now().minusHours(fromHours);</span>
<span class="fc bfc" id="L23" title="All 2 branches covered.">        if (fromTime != null) {</span>
<span class="fc" id="L24">            LocalDateTime localDateTime = LocalDateTime.parse(fromTime, DateTimeFormatter.ISO_OFFSET_DATE_TIME);</span>
<span class="pc bpc" id="L25" title="2 of 4 branches missed.">            if (fromTimestamp == null || localDateTime.isAfter(fromTimestamp))</span>
<span class="nc" id="L26">                fromTimestamp = localDateTime;</span>
        }
<span class="fc" id="L28">        return fromTimestamp;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>