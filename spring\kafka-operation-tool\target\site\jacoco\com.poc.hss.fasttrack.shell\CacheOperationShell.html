<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheOperationShell</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.shell</a> &gt; <span class="el_class">CacheOperationShell</span></div><h1>CacheOperationShell</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">7 of 116</td><td class="ctr2">93%</td><td class="bar">2 of 12</td><td class="ctr2">83%</td><td class="ctr1">3</td><td class="ctr2">14</td><td class="ctr1">0</td><td class="ctr2">24</td><td class="ctr1">1</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a2"><a href="CacheOperationShell.java.html#L38" class="el_method">lambda$checkCache$0(String, CacheResult.CacheInfo)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="5" alt="5"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f0">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="CacheOperationShell.java.html#L38" class="el_method">checkCache(String, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="14" alt="14"/></td><td class="ctr2" id="c6">87%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="1" alt="1"/></td><td class="ctr2" id="e4">50%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="CacheOperationShell.java.html#L72" class="el_method">recalculateCache(String, Integer, String, String, String, Boolean, Boolean)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="33" alt="33"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">8</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="CacheOperationShell.java.html#L45" class="el_method">updateCache(String, String, String, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="101" height="10" title="28" alt="28"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i1">7</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="CacheOperationShell.java.html#L75" class="el_method">lambda$recalculateCache$1(RecalculatedBatchCacheDTO)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="54" height="10" title="15" alt="15"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="90" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">75%</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><a href="CacheOperationShell.java.html#L57" class="el_method">resetCache(String, String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="43" height="10" title="12" alt="12"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="CacheOperationShell.java.html#L19" class="el_method">static {...}</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="CacheOperationShell.java.html#L21" class="el_method">CacheOperationShell()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>