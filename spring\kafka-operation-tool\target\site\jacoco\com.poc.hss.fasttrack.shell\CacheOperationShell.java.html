<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheOperationShell.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.shell</a> &gt; <span class="el_source">CacheOperationShell.java</span></div><h1>CacheOperationShell.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.shell;

import com.poc.hss.fasttrack.dto.RecalculatedBatchCacheDTO;
import com.poc.hss.fasttrack.model.CacheResult;
import com.poc.hss.fasttrack.model.Unity2Component;
import com.poc.hss.fasttrack.service.CacheService;
import com.poc.hss.fasttrack.service.KafkaService;
import com.poc.hss.fasttrack.service.RecalculationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;
import org.springframework.shell.standard.ShellOption;

import java.time.LocalDateTime;
import java.util.List;

<span class="fc" id="L19">@Slf4j</span>
@ShellComponent
<span class="fc" id="L21">public class CacheOperationShell extends BaseShell {</span>

    @Autowired
    private CacheService cacheService;

    @Autowired
    private KafkaService kafkaService;

    @Autowired
    private RecalculationService recalculationService;

    @Value(&quot;${NAMESPACE:}&quot;)
    private String namespace;


    @ShellMethod(&quot;Check cache by batch id&quot;)
    public String checkCache(@ShellOption String batchId, @ShellOption(defaultValue = ShellOption.NULL) String pipeline) {
<span class="pc bpc" id="L38" title="1 of 2 branches missed.">        CacheResult result = cacheService.getCacheResult(batchId, pipeline == null ? null : e -&gt; e.getCacheGroup().contains(pipeline));</span>
<span class="fc" id="L39">        return &quot;BatchID: &quot; + batchId + &quot;\n&quot; +</span>
<span class="fc" id="L40">                result.toString();</span>
    }

    @ShellMethod(&quot;Update cache&quot;)
    public String updateCache(String name, String definition, String batchId, String value) {
<span class="fc" id="L45">        final String[] split = name.split(&quot;:&quot;);</span>
<span class="fc" id="L46">        String group = split[0];</span>
<span class="fc" id="L47">        String component = split[1];</span>
<span class="fc" id="L48">        boolean updated = cacheService.updateCache(group, Unity2Component.fromValue(component), definition, batchId, value);</span>
<span class="fc bfc" id="L49" title="All 2 branches covered.">        if (updated)</span>
<span class="fc" id="L50">            return &quot;Cache updated&quot;;</span>
        else
<span class="fc" id="L52">            return &quot;no changed&quot;;</span>
    }

    @ShellMethod(&quot;Reset cache&quot;)
    public String resetCache(@ShellOption String batchId, @ShellOption(defaultValue = ShellOption.NULL) String pipeline) {
<span class="fc" id="L57">        boolean reset = cacheService.resetCache(batchId, pipeline);</span>
<span class="fc bfc" id="L58" title="All 2 branches covered.">        if (reset)</span>
<span class="fc" id="L59">            return &quot;Cache reset Completed&quot;;</span>
        else
<span class="fc" id="L61">            return &quot;no changed&quot;;</span>
    }

    @ShellMethod(&quot;Recalculate cache&quot;)
    public String recalculateCache(@ShellOption(defaultValue = ShellOption.NULL) String messageTopic,
                                   @ShellOption(defaultValue = ShellOption.NULL) Integer fromHours, @ShellOption(defaultValue = ShellOption.NULL) String fromTime,
                                   @ShellOption(defaultValue = ShellOption.NULL) String batchId, @ShellOption String consumerGroup,
                                   @ShellOption(defaultValue = &quot;false&quot;) Boolean showMessageLag,
                                   @ShellOption(defaultValue = &quot;false&quot;) Boolean execute) {

        try {
<span class="fc" id="L72">            LocalDateTime fromTimestamp = getFromTimestamp(fromHours, fromTime, null);</span>
<span class="fc" id="L73">            final List&lt;RecalculatedBatchCacheDTO&gt; recalculatedBatchCacheDTOS = recalculationService.recalculateCache(messageTopic,</span>
<span class="fc" id="L74">                    batchId, consumerGroup, namespace, fromTimestamp, showMessageLag, execute);</span>
<span class="pc bpc" id="L75" title="1 of 6 branches missed.">            if (recalculatedBatchCacheDTOS.stream().anyMatch(e -&gt; e.getSourceStageRecalculationList().stream().anyMatch(RecalculatedBatchCacheDTO.SourceStageRecalculation::getUpdated) || e.getTargetStageRecalculation().getUpdated())) {</span>
<span class="fc" id="L76">                return &quot;Cache Updated&quot;;</span>
            } else {
<span class="fc" id="L78">                return &quot;no cache updated&quot;;</span>
            }
<span class="fc" id="L80">        } catch (Exception e) {</span>
<span class="fc" id="L81">            return e.getMessage();</span>
        }
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>