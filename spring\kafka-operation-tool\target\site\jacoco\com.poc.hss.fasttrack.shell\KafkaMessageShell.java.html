<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaMessageShell.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.shell</a> &gt; <span class="el_source">KafkaMessageShell.java</span></div><h1>KafkaMessageShell.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.shell;

import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.kafka.util.KafkaUtil;
import com.poc.hss.fasttrack.model.Deployment;
import com.poc.hss.fasttrack.service.KafkaService;
import com.poc.hss.fasttrack.util.K8sUtil;
import com.poc.hss.fasttrack.util.SupportAttributesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.shell.context.InteractionMode;
import org.springframework.shell.standard.ShellComponent;
import org.springframework.shell.standard.ShellMethod;
import org.springframework.shell.standard.ShellOption;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

<span class="fc" id="L26">@Slf4j</span>
@ShellComponent
<span class="fc" id="L28">public class KafkaMessageShell extends BaseShell {</span>
    @Autowired
    private KafkaService kafkaService;
    @Value(&quot;${NAMESPACE:}&quot;)
    private String namespace;
    @Autowired
    private K8sUtil k8sUtil;

    @ShellMethod(value = &quot;Scan and display a message in a given kafka topic&quot;)
    public String scanMessage(String topic, String format,
                              @ShellOption(defaultValue = ShellOption.NULL) Integer partition,
                              @ShellOption(defaultValue = ShellOption.NULL) String keyword,
                              @ShellOption(defaultValue = ShellOption.NULL) Long fromOffset, @ShellOption(defaultValue = ShellOption.NULL) Long toOffset,
                              @ShellOption(defaultValue = ShellOption.NULL) Integer fromHours, @ShellOption(defaultValue = ShellOption.NULL) Integer toHours,
                              @ShellOption(defaultValue = ShellOption.NULL) String fromTime, @ShellOption(defaultValue = ShellOption.NULL) String toTime,
                              @ShellOption(defaultValue = ShellOption.NULL) Integer maxRecords,
                              @ShellOption(defaultValue = &quot;false&quot;) Boolean countOnly) {
<span class="pc bpc" id="L45" title="1 of 2 branches missed.">        LocalDateTime fromTimestamp = getFromTimestamp(fromHours, fromTime, fromOffset == null ? LocalDateTime.now().minusDays(1) : null);</span>
<span class="fc" id="L46">        LocalDateTime toTimestamp = getToTimestamp(toHours, toTime, null);</span>
<span class="fc" id="L47">        List&lt;String&gt; errorList = validateScanMessageInput(keyword, maxRecords);</span>
<span class="fc bfc" id="L48" title="All 2 branches covered.">        if (CollectionUtils.isEmpty(errorList)) {</span>
<span class="fc bfc" id="L49" title="All 2 branches covered.">            if (countOnly) {</span>
<span class="fc" id="L50">                Long count = kafkaService.scanMessagesWithTotalCount(topic, format, fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword);</span>
<span class="fc" id="L51">                return String.format(&quot;-----------------------------------------------------\n&quot; +</span>
                        &quot;Count: %s&quot;, count);
            } else {
<span class="fc" id="L54">                List&lt;KafkaMessageDTO&gt; result = kafkaService.scanMessagesWithResult(topic, format, fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword);</span>
<span class="fc" id="L55">                return printResult(result);</span>
            }
        } else {
<span class="fc" id="L58">            return String.join(&quot;\n&quot;, errorList);</span>
        }
    }


    private List&lt;String&gt; validateScanMessageInput(String keyword, Integer maxRecords) {
<span class="fc" id="L64">        List&lt;String&gt; errorList = new ArrayList&lt;&gt;();</span>
        //For seeking the starting point
<span class="pc bpc" id="L66" title="1 of 4 branches missed.">        if (keyword == null &amp;&amp; maxRecords == null) {</span>
<span class="fc" id="L67">            errorList.add(&quot;--keyword or --max-records must be specified&quot;);</span>
        }
<span class="fc" id="L69">        return errorList;</span>
    }

    @ShellMethod(&quot;list all messages in a given kafka topic&quot;)
    public String listAllMessage(String topic, String format,
                                 @ShellOption(defaultValue = &quot;24&quot;) Integer fromHours, @ShellOption(defaultValue = ShellOption.NULL) Integer toHours,
                                 @ShellOption(defaultValue = ShellOption.NULL) String fromTime, @ShellOption(defaultValue = ShellOption.NULL) String toTime,
                                 @ShellOption(defaultValue = ShellOption.NULL) Integer maxRecords,
                                 @ShellOption(defaultValue = &quot;false&quot;) Boolean countOnly) {
<span class="fc" id="L78">        LocalDateTime fromTimestamp = getFromTimestamp(fromHours, fromTime, null);</span>
<span class="fc" id="L79">        LocalDateTime toTimestamp = getToTimestamp(toHours, toTime, null);</span>
<span class="fc bfc" id="L80" title="All 2 branches covered.">        if (countOnly) {</span>
<span class="fc" id="L81">            Long count = kafkaService.scanMessagesWithTotalCount(topic, format, fromTimestamp, toTimestamp, maxRecords, null, null, null, null);</span>
<span class="fc" id="L82">            return String.format(&quot;-----------------------------------------------------\n&quot; +</span>
                    &quot;Count: %s&quot;, count);
        } else {
<span class="fc" id="L85">            List&lt;KafkaMessageDTO&gt; result = kafkaService.scanMessagesWithResult(topic, format, fromTimestamp, toTimestamp, maxRecords, null, null, null, null);</span>
<span class="fc" id="L86">            return printResult(result);</span>
        }
    }

    private String printResult(List&lt;KafkaMessageDTO&gt; result) {
<span class="fc" id="L91">        return String.format(&quot;-----------------------------------------------------\n&quot; +</span>
                &quot;Result: \n %s\n&quot; +
<span class="fc" id="L93">                &quot;Count: %s&quot;, result.toString(), result.size());</span>
    }

    @ShellMethod(&quot;Replay a range of kafka message by the start Id and end Id&quot;)
    public String replayRangeMessages(
            String sourceTopic, String format, String targetTopic,
            @ShellOption(defaultValue = ShellOption.NULL) Integer partition,
            @ShellOption(defaultValue = ShellOption.NULL) String keyword,
            @ShellOption(defaultValue = ShellOption.NULL) Long fromOffset, @ShellOption(defaultValue = ShellOption.NULL) Long toOffset,
            @ShellOption(defaultValue = ShellOption.NULL) Integer fromHours, @ShellOption(defaultValue = ShellOption.NULL) Integer toHours,
            @ShellOption(defaultValue = ShellOption.NULL) String fromTime, @ShellOption(defaultValue = ShellOption.NULL) String toTime,
            @ShellOption(defaultValue = ShellOption.NULL) Integer maxRecords
    ) {
<span class="fc" id="L106">        LocalDateTime fromTimestamp = getFromTimestamp(fromHours, fromTime, null);</span>
<span class="fc" id="L107">        LocalDateTime toTimestamp = getToTimestamp(toHours, toTime, null);</span>
<span class="fc" id="L108">        List&lt;KafkaMessageDTO&gt; kafkaMessages = kafkaService.replayMessages(sourceTopic, format, targetTopic, fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword);</span>
<span class="fc" id="L109">        printResult(kafkaMessages);</span>
<span class="fc" id="L110">        return &quot;replay successfully&quot;;</span>
    }

    @ShellMethod(&quot;Publish a message&quot;)
    public String publishMessage(String topic, String format, String key, String messageContent) {
<span class="fc" id="L115">        kafkaService.publish(topic, format, key, messageContent);</span>
<span class="fc" id="L116">        return &quot;Publish successfully&quot;;</span>
    }

    @ShellMethod(&quot;Reset Topic partition Offset&quot;)
    public String resetOffset(String topic, @ShellOption String consumerGroup,
                              @ShellOption(defaultValue = &quot;-1&quot;) Integer partition, @ShellOption(defaultValue = &quot;-1&quot;) Long offset,
                              @ShellOption(defaultValue = &quot;false&quot;) Boolean toLatest, @ShellOption(defaultValue = &quot;false&quot;) Boolean toBeginning,
                              @ShellOption(defaultValue = &quot;false&quot;) Boolean ifNull, @ShellOption(defaultValue = &quot;false&quot;) Boolean allPartitions) {


<span class="fc" id="L126">        List&lt;String&gt; errorList = validateResetOffsetInput(topic, partition, offset, toLatest, toBeginning, ifNull, allPartitions);</span>
<span class="pc bpc" id="L127" title="1 of 2 branches missed.">        if (CollectionUtils.isEmpty(errorList)) {</span>
<span class="nc" id="L128">            Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes = k8sUtil.getConfigMap(&quot;.*\\-support-attributes&quot;, namespace);</span>
<span class="nc" id="L129">            final Map&lt;String, String&gt; appSupportAttributes = SupportAttributesUtil.lookupBySourceTopicAndConsumerGroup(topic, consumerGroup, supportAttributes);</span>
<span class="nc bnc" id="L130" title="All 4 branches missed.">            Deployment deployment = appSupportAttributes == null || !appSupportAttributes.containsKey(&quot;deployment&quot;) ? null : Deployment.builder()</span>
<span class="nc" id="L131">                    .namespace(namespace)</span>
<span class="nc" id="L132">                    .name(appSupportAttributes.get(&quot;deployment&quot;))</span>
<span class="nc" id="L133">                    .build();</span>
<span class="nc" id="L134">            boolean scaled = false;</span>
<span class="nc bnc" id="L135" title="All 2 branches missed.">            if (deployment != null)</span>
<span class="nc" id="L136">                scaled = k8sUtil.scaleDown(deployment);</span>
            try {
<span class="nc" id="L138">                boolean reset = kafkaService.resetOffset(topic, consumerGroup, partition, offset, toLatest, toBeginning, ifNull, allPartitions);</span>
<span class="nc bnc" id="L139" title="All 2 branches missed.">                return reset ? &quot;Reset successfully&quot; : &quot;Please reset after stopping the pod&quot;;</span>
            } finally {
<span class="nc bnc" id="L141" title="All 4 branches missed.">                if (deployment != null &amp;&amp; scaled)</span>
<span class="nc" id="L142">                    k8sUtil.scaleUp(deployment);</span>
            }
        } else {
<span class="fc" id="L145">            return String.join(&quot;\n&quot;, errorList);</span>
        }
    }

    private List&lt;String&gt; validateResetOffsetInput(String topic, Integer partition, Long offset, Boolean toLatest, Boolean toBeginning, Boolean ifNull, Boolean allPartitions) {
<span class="fc" id="L150">        List&lt;String&gt; errorList = new ArrayList&lt;&gt;();</span>
<span class="pc bpc" id="L151" title="2 of 4 branches missed.">        if (Stream.of(partition != -1, allPartitions).filter(e -&gt; e).count() != 1) {</span>
<span class="fc" id="L152">            errorList.add(&quot;--partition must be specified, or --all-partitions=true&quot;);</span>
        }

<span class="pc bpc" id="L155" title="2 of 4 branches missed.">        if (Stream.of(offset != -1, toLatest, toBeginning).filter(e -&gt; e).count() != 1) {</span>
<span class="fc" id="L156">            errorList.add(&quot;--offset must be specified, or --to-latest=true, or --to-beginning=true&quot;);</span>
        }

<span class="fc" id="L159">        return errorList;</span>

    }

    @ShellMethod(&quot;Check topic offset by its partition and offset&quot;)
    public String checkTopicLag(String topic, @ShellOption String consumerGroup) {
<span class="fc" id="L165">        final List&lt;KafkaUtil.Metric&gt; metrics = kafkaService.checkOffset(topic, consumerGroup);</span>
<span class="fc" id="L166">        StringBuilder result = new StringBuilder();</span>
<span class="fc" id="L167">        result.append(&quot;Consumer Group: &quot;).append(consumerGroup).append(&quot;\n&quot;)</span>
<span class="fc" id="L168">                .append(&quot;Topic: &quot;).append(topic).append(&quot;\n&quot;)</span>
<span class="fc" id="L169">                .append(metrics.stream()</span>
<span class="fc" id="L170">                        .map(e -&gt; &quot;partition: &quot; + e.getPartition()</span>
<span class="fc" id="L171">                                + &quot;\tbeginning-offset: &quot; + e.getBeginningOffset()</span>
<span class="fc" id="L172">                                + &quot;\tcommitted-offset: &quot; + e.getCommittedOffset()</span>
<span class="fc" id="L173">                                + &quot;\tend-offset: &quot; + e.getEndOffset()</span>
<span class="fc" id="L174">                                + &quot;\tlag: &quot; + e.getLag()</span>
<span class="fc" id="L175">                        ).collect(Collectors.joining(&quot;\n&quot;))).append(&quot;\n&quot;)</span>
<span class="fc" id="L176">                .append(&quot;Total lag: &quot; + metrics.stream().mapToLong(KafkaUtil.Metric::getLag).sum());</span>
<span class="fc" id="L177">        return result.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>