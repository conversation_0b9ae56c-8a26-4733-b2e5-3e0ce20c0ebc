<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.shell</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">kafka-operation-tool</a> &gt; <span class="el_package">com.poc.hss.fasttrack.shell</span></div><h1>com.poc.hss.fasttrack.shell</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">85 of 591</td><td class="ctr2">85%</td><td class="bar">25 of 62</td><td class="ctr2">59%</td><td class="ctr1">20</td><td class="ctr2">56</td><td class="ctr1">15</td><td class="ctr2">108</td><td class="ctr1">1</td><td class="ctr2">25</td><td class="ctr1">0</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a2"><a href="KafkaMessageShell.java.html" class="el_source">KafkaMessageShell.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="74" alt="74"/><img src="../jacoco-resources/greenbar.gif" width="98" height="10" title="346" alt="346"/></td><td class="ctr2" id="c2">82%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="67" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="52" height="10" title="15" alt="15"/></td><td class="ctr2" id="e2">44%</td><td class="ctr1" id="f0">13</td><td class="ctr2" id="g0">31</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">67</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k0">14</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="CacheOperationShell.java.html" class="el_source">CacheOperationShell.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="31" height="10" title="109" alt="109"/></td><td class="ctr2" id="c0">93%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">83%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g1">14</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">24</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="BaseShell.java.html" class="el_source">BaseShell.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="51" alt="51"/></td><td class="ctr2" id="c1">92%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">75%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g2">11</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">3</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>