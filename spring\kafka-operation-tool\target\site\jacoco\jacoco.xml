<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="kafka-operation-tool"><sessioninfo id="H344L0L63UFKL6Z-6c5366a0" start="1752783279863" dump="1752783285636"/><package name="com/poc/hss/fasttrack/service"><class name="com/poc/hss/fasttrack/service/KafkaService" sourcefilename="KafkaService.java"><method name="&lt;init&gt;" desc="()V" line="24"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="initProducerManager" desc="()V" line="43"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publish" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="47"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="publish" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V" line="51"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="resetOffset" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;)Z" line="55"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkOffset" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/util/List;" line="61"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="scanMessagesWithResult" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)Ljava/util/List;" line="67"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="scanMessagesWithTotalCount" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)Ljava/lang/Long;" line="74"><counter type="INSTRUCTION" missed="27" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="scanMessagesWithBatchCount" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)Ljava/util/Map;" line="81"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="replayMessages" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)Ljava/util/List;" line="88"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$scanMessagesWithTotalCount$0" desc="(Lcom/poc/hss/fasttrack/dto/KafkaMessageDTO;)Ljava/lang/String;" line="75"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="22"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="86" covered="80"/><counter type="LINE" missed="14" covered="14"/><counter type="COMPLEXITY" missed="5" covered="7"/><counter type="METHOD" missed="5" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="KafkaService.java"><line nr="22" mi="0" ci="4" mb="0" cb="0"/><line nr="24" mi="0" ci="3" mb="0" cb="0"/><line nr="43" mi="6" ci="0" mb="0" cb="0"/><line nr="44" mi="1" ci="0" mb="0" cb="0"/><line nr="47" mi="0" ci="11" mb="0" cb="0"/><line nr="48" mi="0" ci="1" mb="0" cb="0"/><line nr="51" mi="0" ci="7" mb="0" cb="0"/><line nr="52" mi="0" ci="1" mb="0" cb="0"/><line nr="55" mi="0" ci="5" mb="0" cb="0"/><line nr="56" mi="0" ci="14" mb="0" cb="0"/><line nr="61" mi="0" ci="5" mb="0" cb="0"/><line nr="62" mi="0" ci="6" mb="0" cb="0"/><line nr="67" mi="0" ci="9" mb="0" cb="0"/><line nr="68" mi="0" ci="10" mb="0" cb="0"/><line nr="69" mi="0" ci="2" mb="0" cb="0"/><line nr="70" mi="0" ci="2" mb="0" cb="0"/><line nr="74" mi="9" ci="0" mb="0" cb="0"/><line nr="75" mi="13" ci="0" mb="0" cb="0"/><line nr="76" mi="2" ci="0" mb="0" cb="0"/><line nr="77" mi="5" ci="0" mb="0" cb="0"/><line nr="81" mi="9" ci="0" mb="0" cb="0"/><line nr="82" mi="11" ci="0" mb="0" cb="0"/><line nr="83" mi="2" ci="0" mb="0" cb="0"/><line nr="84" mi="2" ci="0" mb="0" cb="0"/><line nr="88" mi="9" ci="0" mb="0" cb="0"/><line nr="89" mi="10" ci="0" mb="0" cb="0"/><line nr="90" mi="5" ci="0" mb="0" cb="0"/><line nr="91" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="86" covered="80"/><counter type="LINE" missed="14" covered="14"/><counter type="COMPLEXITY" missed="5" covered="7"/><counter type="METHOD" missed="5" covered="7"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="86" covered="80"/><counter type="LINE" missed="14" covered="14"/><counter type="COMPLEXITY" missed="5" covered="7"/><counter type="METHOD" missed="5" covered="7"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/poc/hss/fasttrack/model"><class name="com/poc/hss/fasttrack/model/ShellKeyValue$ShellKeyValueBuilder" sourcefilename="ShellKeyValue.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="key" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/ShellKeyValue$ShellKeyValueBuilder;" line="9"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="value" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/ShellKeyValue$ShellKeyValueBuilder;" line="9"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/model/ShellKeyValue;" line="9"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="6" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/ShellKeyValue" sourcefilename="ShellKeyValue.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/model/ShellKeyValue$ShellKeyValueBuilder;" line="9"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getKey" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getValue" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setKey" desc="(Ljava/lang/String;)V" line="8"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setValue" desc="(Ljava/lang/String;)V" line="8"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="14" covered="41"/><counter type="BRANCH" missed="10" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="8" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="8"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="8"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="18" covered="110"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="10" covered="12"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="ShellKeyValue.java"><line nr="8" mi="18" ci="88" mb="12" cb="10"/><line nr="9" mi="6" ci="25" mb="0" cb="0"/><line nr="10" mi="0" ci="9" mb="0" cb="0"/><line nr="11" mi="0" ci="3" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="24" covered="131"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="11" covered="16"/><counter type="METHOD" missed="1" covered="15"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="24" covered="131"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="11" covered="16"/><counter type="METHOD" missed="1" covered="15"/><counter type="CLASS" missed="0" covered="2"/></package><package name="com/poc/hss/fasttrack"><class name="com/poc/hss/fasttrack/SupportOperationShellMain" sourcefilename="SupportOperationShellMain.java"><method name="&lt;init&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="main" desc="([Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="SupportOperationShellMain.java"><line nr="7" mi="3" ci="0" mb="0" cb="0"/><line nr="9" mi="4" ci="0" mb="0" cb="0"/><line nr="10" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/poc/hss/fasttrack/converter"><class name="com/poc/hss/fasttrack/converter/ShellKeyValueConverter" sourcefilename="ShellKeyValueConverter.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="convert" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/ShellKeyValue;" line="12"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="ShellKeyValueConverter.java"><line nr="9" mi="0" ci="3" mb="0" cb="0"/><line nr="12" mi="0" ci="6" mb="0" cb="0"/><line nr="13" mi="0" ci="6" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="2" mb="0" cb="0"/><line nr="16" mi="0" ci="1" mb="0" cb="0"/><line nr="17" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/poc/hss/fasttrack/shell"><class name="com/poc/hss/fasttrack/shell/CacheOperationShell" sourcefilename="CacheOperationShell.java"><method name="&lt;init&gt;" desc="()V" line="21"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkCache" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="38"><counter type="INSTRUCTION" missed="2" covered="14"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="updateCache" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="45"><counter type="INSTRUCTION" missed="0" covered="28"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="resetCache" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="57"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="recalculateCache" desc="(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;)Ljava/lang/String;" line="72"><counter type="INSTRUCTION" missed="0" covered="33"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="8"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$recalculateCache$1" desc="(Lcom/poc/hss/fasttrack/dto/RecalculatedBatchCacheDTO;)Z" line="75"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$checkCache$0" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/model/CacheResult$CacheInfo;)Z" line="38"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="19"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="7" covered="109"/><counter type="BRANCH" missed="2" covered="10"/><counter type="LINE" missed="0" covered="24"/><counter type="COMPLEXITY" missed="3" covered="11"/><counter type="METHOD" missed="1" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/shell/KafkaMessageShell" sourcefilename="KafkaMessageShell.java"><method name="&lt;init&gt;" desc="()V" line="28"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="scanMessage" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Boolean;)Ljava/lang/String;" line="45"><counter type="INSTRUCTION" missed="1" covered="71"/><counter type="BRANCH" missed="1" covered="5"/><counter type="LINE" missed="0" covered="10"/><counter type="COMPLEXITY" missed="1" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="validateScanMessageInput" desc="(Ljava/lang/String;Ljava/lang/Integer;)Ljava/util/List;" line="64"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="listAllMessage" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Boolean;)Ljava/lang/String;" line="78"><counter type="INSTRUCTION" missed="0" covered="54"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="printResult" desc="(Ljava/util/List;)Ljava/lang/String;" line="91"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="replayRangeMessages" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;)Ljava/lang/String;" line="106"><counter type="INSTRUCTION" missed="0" covered="32"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="publishMessage" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="115"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="resetOffset" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;)Ljava/lang/String;" line="126"><counter type="INSTRUCTION" missed="69" covered="17"/><counter type="BRANCH" missed="13" covered="1"/><counter type="LINE" missed="13" covered="3"/><counter type="COMPLEXITY" missed="7" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="validateResetOffsetInput" desc="(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;)Ljava/util/List;" line="150"><counter type="INSTRUCTION" missed="4" covered="63"/><counter type="BRANCH" missed="4" covered="4"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="4" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="checkTopicLag" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="165"><counter type="INSTRUCTION" missed="0" covered="45"/><counter type="LINE" missed="0" covered="9"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$checkTopicLag$2" desc="(Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric;)Ljava/lang/String;" line="170"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$validateResetOffsetInput$1" desc="(Ljava/lang/Boolean;)Z" line="155"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$validateResetOffsetInput$0" desc="(Ljava/lang/Boolean;)Z" line="151"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="26"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="74" covered="346"/><counter type="BRANCH" missed="19" covered="15"/><counter type="LINE" missed="13" covered="54"/><counter type="COMPLEXITY" missed="13" covered="18"/><counter type="METHOD" missed="0" covered="14"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/shell/BaseShell" sourcefilename="BaseShell.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getToTimestamp" desc="(Ljava/lang/Integer;Ljava/lang/String;Ljava/time/LocalDateTime;)Ljava/time/LocalDateTime;" line="8"><counter type="INSTRUCTION" missed="2" covered="24"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="1" covered="7"/><counter type="COMPLEXITY" missed="2" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getFromTimestamp" desc="(Ljava/lang/Integer;Ljava/lang/String;Ljava/time/LocalDateTime;)Ljava/time/LocalDateTime;" line="20"><counter type="INSTRUCTION" missed="2" covered="24"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="1" covered="7"/><counter type="COMPLEXITY" missed="2" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="4" covered="51"/><counter type="BRANCH" missed="4" covered="12"/><counter type="LINE" missed="2" covered="15"/><counter type="COMPLEXITY" missed="4" covered="7"/><counter type="METHOD" missed="0" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="KafkaMessageShell.java"><line nr="26" mi="0" ci="4" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="0" cb="0"/><line nr="45" mi="1" ci="11" mb="1" cb="1"/><line nr="46" mi="0" ci="6" mb="0" cb="0"/><line nr="47" mi="0" ci="5" mb="0" cb="0"/><line nr="48" mi="0" ci="3" mb="0" cb="2"/><line nr="49" mi="0" ci="3" mb="0" cb="2"/><line nr="50" mi="0" ci="13" mb="0" cb="0"/><line nr="51" mi="0" ci="9" mb="0" cb="0"/><line nr="54" mi="0" ci="13" mb="0" cb="0"/><line nr="55" mi="0" ci="4" mb="0" cb="0"/><line nr="58" mi="0" ci="4" mb="0" cb="0"/><line nr="64" mi="0" ci="4" mb="0" cb="0"/><line nr="66" mi="0" ci="4" mb="1" cb="3"/><line nr="67" mi="0" ci="4" mb="0" cb="0"/><line nr="69" mi="0" ci="2" mb="0" cb="0"/><line nr="78" mi="0" ci="6" mb="0" cb="0"/><line nr="79" mi="0" ci="6" mb="0" cb="0"/><line nr="80" mi="0" ci="3" mb="0" cb="2"/><line nr="81" mi="0" ci="13" mb="0" cb="0"/><line nr="82" mi="0" ci="9" mb="0" cb="0"/><line nr="85" mi="0" ci="13" mb="0" cb="0"/><line nr="86" mi="0" ci="4" mb="0" cb="0"/><line nr="91" mi="0" ci="8" mb="0" cb="0"/><line nr="93" mi="0" ci="8" mb="0" cb="0"/><line nr="106" mi="0" ci="6" mb="0" cb="0"/><line nr="107" mi="0" ci="6" mb="0" cb="0"/><line nr="108" mi="0" ci="14" mb="0" cb="0"/><line nr="109" mi="0" ci="4" mb="0" cb="0"/><line nr="110" mi="0" ci="2" mb="0" cb="0"/><line nr="115" mi="0" ci="7" mb="0" cb="0"/><line nr="116" mi="0" ci="2" mb="0" cb="0"/><line nr="126" mi="0" ci="10" mb="0" cb="0"/><line nr="127" mi="0" ci="3" mb="1" cb="1"/><line nr="128" mi="7" ci="0" mb="0" cb="0"/><line nr="129" mi="5" ci="0" mb="0" cb="0"/><line nr="130" mi="11" ci="0" mb="4" cb="0"/><line nr="131" mi="3" ci="0" mb="0" cb="0"/><line nr="132" mi="3" ci="0" mb="0" cb="0"/><line nr="133" mi="2" ci="0" mb="0" cb="0"/><line nr="134" mi="2" ci="0" mb="0" cb="0"/><line nr="135" mi="2" ci="0" mb="2" cb="0"/><line nr="136" mi="5" ci="0" mb="0" cb="0"/><line nr="138" mi="12" ci="0" mb="0" cb="0"/><line nr="139" mi="8" ci="0" mb="2" cb="0"/><line nr="141" mi="4" ci="0" mb="4" cb="0"/><line nr="142" mi="5" ci="0" mb="0" cb="0"/><line nr="145" mi="0" ci="4" mb="0" cb="0"/><line nr="150" mi="0" ci="4" mb="0" cb="0"/><line nr="151" mi="2" ci="25" mb="2" cb="2"/><line nr="152" mi="0" ci="4" mb="0" cb="0"/><line nr="155" mi="2" ci="30" mb="2" cb="2"/><line nr="156" mi="0" ci="4" mb="0" cb="0"/><line nr="159" mi="0" ci="2" mb="0" cb="0"/><line nr="165" mi="0" ci="6" mb="0" cb="0"/><line nr="166" mi="0" ci="4" mb="0" cb="0"/><line nr="167" mi="0" ci="8" mb="0" cb="0"/><line nr="168" mi="0" ci="6" mb="0" cb="0"/><line nr="169" mi="0" ci="4" mb="0" cb="0"/><line nr="170" mi="0" ci="6" mb="0" cb="0"/><line nr="171" mi="0" ci="2" mb="0" cb="0"/><line nr="172" mi="0" ci="2" mb="0" cb="0"/><line nr="173" mi="0" ci="2" mb="0" cb="0"/><line nr="174" mi="0" ci="2" mb="0" cb="0"/><line nr="175" mi="0" ci="5" mb="0" cb="0"/><line nr="176" mi="0" ci="7" mb="0" cb="0"/><line nr="177" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="74" covered="346"/><counter type="BRANCH" missed="19" covered="15"/><counter type="LINE" missed="13" covered="54"/><counter type="COMPLEXITY" missed="13" covered="18"/><counter type="METHOD" missed="0" covered="14"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CacheOperationShell.java"><line nr="19" mi="0" ci="4" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="38" mi="7" ci="9" mb="1" cb="1"/><line nr="39" mi="0" ci="3" mb="0" cb="0"/><line nr="40" mi="0" ci="2" mb="0" cb="0"/><line nr="45" mi="0" ci="4" mb="0" cb="0"/><line nr="46" mi="0" ci="4" mb="0" cb="0"/><line nr="47" mi="0" ci="4" mb="0" cb="0"/><line nr="48" mi="0" ci="10" mb="0" cb="0"/><line nr="49" mi="0" ci="2" mb="0" cb="2"/><line nr="50" mi="0" ci="2" mb="0" cb="0"/><line nr="52" mi="0" ci="2" mb="0" cb="0"/><line nr="57" mi="0" ci="6" mb="0" cb="0"/><line nr="58" mi="0" ci="2" mb="0" cb="2"/><line nr="59" mi="0" ci="2" mb="0" cb="0"/><line nr="61" mi="0" ci="2" mb="0" cb="0"/><line nr="72" mi="0" ci="6" mb="0" cb="0"/><line nr="73" mi="0" ci="11" mb="0" cb="0"/><line nr="74" mi="0" ci="3" mb="0" cb="0"/><line nr="75" mi="0" ci="20" mb="1" cb="5"/><line nr="76" mi="0" ci="2" mb="0" cb="0"/><line nr="78" mi="0" ci="2" mb="0" cb="0"/><line nr="80" mi="0" ci="1" mb="0" cb="0"/><line nr="81" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="7" covered="109"/><counter type="BRANCH" missed="2" covered="10"/><counter type="LINE" missed="0" covered="24"/><counter type="COMPLEXITY" missed="3" covered="11"/><counter type="METHOD" missed="1" covered="7"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="BaseShell.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="8" mi="0" ci="2" mb="0" cb="0"/><line nr="9" mi="0" ci="2" mb="0" cb="2"/><line nr="10" mi="0" ci="6" mb="0" cb="0"/><line nr="11" mi="0" ci="2" mb="0" cb="2"/><line nr="12" mi="0" ci="4" mb="0" cb="0"/><line nr="13" mi="0" ci="6" mb="2" cb="2"/><line nr="14" mi="2" ci="0" mb="0" cb="0"/><line nr="16" mi="0" ci="2" mb="0" cb="0"/><line nr="20" mi="0" ci="2" mb="0" cb="0"/><line nr="21" mi="0" ci="2" mb="0" cb="2"/><line nr="22" mi="0" ci="6" mb="0" cb="0"/><line nr="23" mi="0" ci="2" mb="0" cb="2"/><line nr="24" mi="0" ci="4" mb="0" cb="0"/><line nr="25" mi="0" ci="6" mb="2" cb="2"/><line nr="26" mi="2" ci="0" mb="0" cb="0"/><line nr="28" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="4" covered="51"/><counter type="BRANCH" missed="4" covered="12"/><counter type="LINE" missed="2" covered="15"/><counter type="COMPLEXITY" missed="4" covered="7"/><counter type="METHOD" missed="0" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="85" covered="506"/><counter type="BRANCH" missed="25" covered="37"/><counter type="LINE" missed="15" covered="93"/><counter type="COMPLEXITY" missed="20" covered="36"/><counter type="METHOD" missed="1" covered="24"/><counter type="CLASS" missed="0" covered="3"/></package><package name="com/poc/hss/fasttrack/kafka"><class name="com/poc/hss/fasttrack/kafka/NoOpKafkaAvroReader" sourcefilename="NoOpKafkaAvroReader.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/util/List;)V" line="17"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="converter" desc="()Ljava/util/function/Function;" line="23"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="propertiesCustomizer" desc="(Ljava/util/Properties;)V" line="28"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$converter$0" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Lorg/apache/kafka/clients/consumer/ConsumerRecord;" line="23"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/NoOpKafkaMessageReader" sourcefilename="NoOpKafkaMessageReader.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/util/List;)V" line="15"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="converter" desc="()Ljava/util/function/Function;" line="21"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="propertiesCustomizer" desc="(Ljava/util/Properties;)V" line="26"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$converter$0" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Lorg/apache/kafka/clients/consumer/ConsumerRecord;" line="21"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/InMemoryKafkaMessageStore" sourcefilename="InMemoryKafkaMessageStore.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/reader/KafkaReader;Ljava/lang/String;)V" line="30"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="destroy" desc="()V" line="43"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="scanAllMessagesWithCount" desc="(Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;Ljava/util/function/Function;)Ljava/util/Map;" line="48"><counter type="INSTRUCTION" missed="46" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="scanAllMessagesWithResult" desc="(Ljava/time/LocalDateTime;Ljava/time/LocalDateTime;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/String;)Ljava/util/List;" line="59"><counter type="INSTRUCTION" missed="45" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/reader/KafkaReader;Ljava/lang/String;Lcom/poc/hss/fasttrack/util/ConversionHelperService;Lcom/poc/hss/fasttrack/service/GenericKafkaService;)V" line="21"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="20"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="120" covered="4"/><counter type="LINE" missed="15" covered="1"/><counter type="COMPLEXITY" missed="5" covered="1"/><counter type="METHOD" missed="5" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/MessageStoreManager" sourcefilename="MessageStoreManager.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getMessageStore" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/KafkaMessageFormat;)Lcom/poc/hss/fasttrack/kafka/MessageStore;" line="27"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getMessageStore" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/KafkaMessageFormat;Ljava/lang/Boolean;)Lcom/poc/hss/fasttrack/kafka/MessageStore;" line="31"><counter type="INSTRUCTION" missed="0" covered="10"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getMessageStore" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/KafkaMessageFormat;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Boolean;)Lcom/poc/hss/fasttrack/kafka/MessageStore;" line="35"><counter type="INSTRUCTION" missed="1" covered="102"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="0" covered="16"/><counter type="COMPLEXITY" missed="2" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="remove" desc="(Ljava/lang/String;)V" line="60"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="1" covered="141"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="2" covered="8"/><counter type="METHOD" missed="0" covered="6"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/MessageStore" sourcefilename="MessageStore.java"/><sourcefile name="NoOpKafkaMessageReader.java"><line nr="15" mi="0" ci="4" mb="0" cb="0"/><line nr="16" mi="0" ci="1" mb="0" cb="0"/><line nr="21" mi="0" ci="4" mb="0" cb="0"/><line nr="26" mi="0" ci="6" mb="0" cb="0"/><line nr="27" mi="0" ci="6" mb="0" cb="0"/><line nr="28" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="InMemoryKafkaMessageStore.java"><line nr="20" mi="0" ci="4" mb="0" cb="0"/><line nr="21" mi="15" ci="0" mb="0" cb="0"/><line nr="30" mi="2" ci="0" mb="0" cb="0"/><line nr="31" mi="3" ci="0" mb="0" cb="0"/><line nr="32" mi="3" ci="0" mb="0" cb="0"/><line nr="33" mi="1" ci="0" mb="0" cb="0"/><line nr="43" mi="4" ci="0" mb="0" cb="0"/><line nr="44" mi="1" ci="0" mb="0" cb="0"/><line nr="48" mi="4" ci="0" mb="0" cb="0"/><line nr="49" mi="7" ci="0" mb="0" cb="0"/><line nr="50" mi="21" ci="0" mb="0" cb="0"/><line nr="51" mi="14" ci="0" mb="0" cb="0"/><line nr="59" mi="4" ci="0" mb="0" cb="0"/><line nr="60" mi="7" ci="0" mb="0" cb="0"/><line nr="61" mi="21" ci="0" mb="0" cb="0"/><line nr="62" mi="13" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="120" covered="4"/><counter type="LINE" missed="15" covered="1"/><counter type="COMPLEXITY" missed="5" covered="1"/><counter type="METHOD" missed="5" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="MessageStoreManager.java"><line nr="12" mi="0" ci="4" mb="0" cb="0"/><line nr="14" mi="0" ci="2" mb="0" cb="0"/><line nr="24" mi="0" ci="6" mb="0" cb="0"/><line nr="27" mi="0" ci="11" mb="0" cb="0"/><line nr="31" mi="0" ci="10" mb="0" cb="0"/><line nr="35" mi="0" ci="4" mb="0" cb="0"/><line nr="36" mi="0" ci="5" mb="0" cb="0"/><line nr="37" mi="0" ci="6" mb="0" cb="0"/><line nr="38" mi="0" ci="5" mb="0" cb="0"/><line nr="39" mi="0" ci="5" mb="0" cb="0"/><line nr="40" mi="1" ci="10" mb="1" cb="1"/><line nr="42" mi="0" ci="2" mb="0" cb="2"/><line nr="43" mi="0" ci="2" mb="0" cb="0"/><line nr="45" mi="0" ci="5" mb="0" cb="2"/><line nr="47" mi="0" ci="21" mb="0" cb="0"/><line nr="48" mi="0" ci="1" mb="0" cb="0"/><line nr="51" mi="0" ci="4" mb="0" cb="0"/><line nr="52" mi="0" ci="21" mb="0" cb="0"/><line nr="54" mi="0" ci="3" mb="1" cb="1"/><line nr="55" mi="0" ci="6" mb="0" cb="0"/><line nr="56" mi="0" ci="2" mb="0" cb="0"/><line nr="60" mi="0" ci="5" mb="0" cb="0"/><line nr="61" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="1" covered="141"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="2" covered="8"/><counter type="METHOD" missed="0" covered="6"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="MessageStore.java"/><sourcefile name="NoOpKafkaAvroReader.java"><line nr="17" mi="0" ci="4" mb="0" cb="0"/><line nr="18" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="0" ci="4" mb="0" cb="0"/><line nr="28" mi="0" ci="6" mb="0" cb="0"/><line nr="29" mi="0" ci="6" mb="0" cb="0"/><line nr="30" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="22"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="121" covered="189"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="15" covered="36"/><counter type="COMPLEXITY" missed="7" covered="17"/><counter type="METHOD" missed="5" covered="15"/><counter type="CLASS" missed="0" covered="4"/></package><counter type="INSTRUCTION" missed="324" covered="928"/><counter type="BRANCH" missed="39" covered="53"/><counter type="LINE" missed="47" covered="156"/><counter type="COMPLEXITY" missed="45" covered="78"/><counter type="METHOD" missed="14" covered="63"/><counter type="CLASS" missed="1" covered="11"/></report>