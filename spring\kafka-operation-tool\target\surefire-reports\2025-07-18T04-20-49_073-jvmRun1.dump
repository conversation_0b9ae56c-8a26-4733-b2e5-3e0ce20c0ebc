# Created at 2025-07-18T04:24:27.439
Exiting self fork JV<PERSON>. Received SHUTDOWN command from <PERSON><PERSON> shutdown hook.
Thread dump before exiting the process (35540@H344L0L63UFKL6Z):
"main" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/java.lang.ProcessImpl.waitForInterruptibly(Native Method)
        at java.base@21.0.4/java.lang.ProcessImpl.waitFor(ProcessImpl.java:569)
        at app//org.jline.utils.ExecHelper.waitAndCapture(ExecHelper.java:72)
        at app//org.jline.terminal.impl.exec.ExecTerminalProvider.systemStreamName(ExecTerminalProvider.java:152)
        at app//org.jline.terminal.impl.exec.ExecTerminalProvider.isWindowsSystemStream(ExecTerminalProvider.java:128)
        at app//org.jline.terminal.impl.exec.ExecTerminalProvider.isSystemStream(ExecTerminalProvider.java:121)
        at app//org.jline.terminal.TerminalBuilder.lambda$doBuild$4(TerminalBuilder.java:474)
        at app//org.jline.terminal.TerminalBuilder$$Lambda/0x000001c1eeb3c000.test(Unknown Source)
        at java.base@21.0.4/java.util.stream.MatchOps$1MatchSink.accept(MatchOps.java:90)
        at java.base@21.0.4/java.util.ArrayList$ArrayListSpliterator.tryAdvance(ArrayList.java:1685)
        at java.base@21.0.4/java.util.stream.ReferencePipeline.forEachWithCancel(ReferencePipeline.java:129)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.copyIntoWithCancel(AbstractPipeline.java:527)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:513)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        at java.base@21.0.4/java.util.stream.MatchOps$MatchOp.evaluateSequential(MatchOps.java:230)
        at java.base@21.0.4/java.util.stream.MatchOps$MatchOp.evaluateSequential(MatchOps.java:196)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        at java.base@21.0.4/java.util.stream.ReferencePipeline.anyMatch(ReferencePipeline.java:632)
        at app//org.jline.terminal.TerminalBuilder.lambda$doBuild$5(TerminalBuilder.java:474)
        at app//org.jline.terminal.TerminalBuilder$$Lambda/0x000001c1eeb37cd0.apply(Unknown Source)
        at java.base@21.0.4/java.util.stream.Collectors.lambda$uniqKeysMapAccumulator$1(Collectors.java:180)
        at java.base@21.0.4/java.util.stream.Collectors$$Lambda/0x000001c1ee2ce920.accept(Unknown Source)
        at java.base@21.0.4/java.util.stream.ReduceOps$3ReducingSink.accept(ReduceOps.java:169)
        at java.base@21.0.4/java.util.Spliterators$ArraySpliterator.forEachRemaining(Spliterators.java:1024)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:509)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:499)
        at java.base@21.0.4/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)
        at java.base@21.0.4/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:234)
        at java.base@21.0.4/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:682)
        at app//org.jline.terminal.TerminalBuilder.doBuild(TerminalBuilder.java:473)
        at app//org.jline.terminal.TerminalBuilder.build(TerminalBuilder.java:427)
        at app//org.springframework.shell.boot.JLineShellAutoConfiguration.terminal(JLineShellAutoConfiguration.java:51)
        at java.base@21.0.4/java.lang.invoke.LambdaForm$DMH/0x000001c1ee039800.invokeVirtual(LambdaForm$DMH)
        at java.base@21.0.4/java.lang.invoke.LambdaForm$MH/0x000001c1ee010400.invoke(LambdaForm$MH)
        at java.base@21.0.4/java.lang.invoke.Invokers$Holder.invokeExact_MT(Invokers$Holder)
        at java.base@21.0.4/jdk.internal.reflect.DirectMethodHandleAccessor.invokeImpl(DirectMethodHandleAccessor.java:154)
        at java.base@21.0.4/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
        at java.base@21.0.4/java.lang.reflect.Method.invoke(Method.java:580)
        at app//org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:146)
        at app//org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:644)
        at app//org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:636)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory$$Lambda/0x000001c1ee813020.getObject(Unknown Source)
        at app//org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
        at app//org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
        at app//org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
        at app//org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
        at app//org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory$$Lambda/0x000001c1ee813020.getObject(Unknown Source)
        at app//org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
        at app//org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.addCandidateEntry(DefaultListableBeanFactory.java:1689)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.findAutowireCandidates(DefaultListableBeanFactory.java:1653)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeanCollection(DefaultListableBeanFactory.java:1543)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveMultipleBeans(DefaultListableBeanFactory.java:1511)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1392)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
        at app//org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
        at app//org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
        at app//org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory$$Lambda/0x000001c1ee813020.getObject(Unknown Source)
        at app//org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
        at app//org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1443)
        at app//org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1353)
        at app//org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:904)
        at app//org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:782)
        at app//org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:542)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1355)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1185)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:562)
        at app//org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:522)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:337)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory$$Lambda/0x000001c1ee813020.getObject(Unknown Source)
        at app//org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:335)
        at app//org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:200)
        at app//org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:254)

"Reference Handler" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/java.lang.ref.Reference.waitForReferencePendingList(Native Method)
        at java.base@21.0.4/java.lang.ref.Reference.processPendingReferences(Reference.java:246)
        at java.base@21.0.4/java.lang.ref.Reference$ReferenceHandler.run(Reference.java:208)

"Finalizer" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/java.lang.Object.wait0(Native Method)
        at java.base@21.0.4/java.lang.Object.wait(Object.java:366)
        at java.base@21.0.4/java.lang.Object.wait(Object.java:339)
        at java.base@21.0.4/java.lang.ref.NativeReferenceQueue.await(NativeReferenceQueue.java:48)
        at java.base@21.0.4/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:158)
        at java.base@21.0.4/java.lang.ref.NativeReferenceQueue.remove(NativeReferenceQueue.java:89)
        at java.base@21.0.4/java.lang.ref.Finalizer$FinalizerThread.run(Finalizer.java:173)

"Signal Dispatcher" 
   java.lang.Thread.State: RUNNABLE

"Attach Listener" 
   java.lang.Thread.State: RUNNABLE

"Common-Cleaner" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1847)
        at java.base@21.0.4/java.lang.ref.ReferenceQueue.await(ReferenceQueue.java:71)
        at java.base@21.0.4/java.lang.ref.ReferenceQueue.remove0(ReferenceQueue.java:143)
        at java.base@21.0.4/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:218)
        at java.base@21.0.4/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)
        at java.base@21.0.4/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:186)

"Notification Thread" 
   java.lang.Thread.State: RUNNABLE

"surefire-forkedjvm-stream-flusher" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"surefire-forkedjvm-command-thread" 
   java.lang.Thread.State: RUNNABLE
        at java.management@21.0.4/sun.management.ThreadImpl.getThreadInfo1(Native Method)
        at java.management@21.0.4/sun.management.ThreadImpl.getThreadInfo(ThreadImpl.java:187)
        at app//org.apache.maven.surefire.booter.ForkedBooter.generateThreadDump(ForkedBooter.java:579)
        at app//org.apache.maven.surefire.booter.ForkedBooter.access$600(ForkedBooter.java:79)
        at app//org.apache.maven.surefire.booter.ForkedBooter$4.update(ForkedBooter.java:315)
        at app//org.apache.maven.surefire.booter.CommandReader$CommandRunnable.callListeners(CommandReader.java:357)
        at app//org.apache.maven.surefire.booter.CommandReader$CommandRunnable.exitByConfiguration(CommandReader.java:367)
        at app//org.apache.maven.surefire.booter.CommandReader$CommandRunnable.run(CommandReader.java:330)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"metrics-meter-tick-thread-1" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"metrics-meter-tick-thread-2" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-admin-client-thread | adminclient-2" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.clients.admin.KafkaAdminClient$AdminClientRunnable.processRequests(KafkaAdminClient.java:1504)
        at app//org.apache.kafka.clients.admin.KafkaAdminClient$AdminClientRunnable.run(KafkaAdminClient.java:1435)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-cluster-test-kit-executor-1" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-cluster-test-kit-executor-2" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-cluster-test-kit-executor-3" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-cluster-test-kit-executor-4" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.LinkedBlockingQueue.take(LinkedBlockingQueue.java:435)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-0-raft-scheduler0" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"raft-expiration-reaper" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:300)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.raft.TimingWheelExpirationService$ExpiredOperationReaper.doWork(TimingWheelExpirationService.scala:56)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"kafka-0-raft-outbound-request-thread" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.doWork(InterBrokerSendThread.java:136)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"kafka-0-raft-io-thread" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.LinkedBlockingQueue.poll(LinkedBlockingQueue.java:460)
        at app//org.apache.kafka.raft.internals.BlockingMessageQueue.poll(BlockingMessageQueue.java:48)
        at app//org.apache.kafka.raft.KafkaRaftClient.poll(KafkaRaftClient.java:2318)
        at app//kafka.raft.KafkaRaftManager$RaftIoThread.doWork(RaftManager.scala:65)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"kafka-0-metadata-loader-event-handler" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.handleEvents(KafkaEventQueue.java:277)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.run(KafkaEventQueue.java:181)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-0-snapshot-generator-event-handler" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.handleEvents(KafkaEventQueue.java:277)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.run(KafkaEventQueue.java:181)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"quorum-controller-0-event-handler" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.handleEvents(KafkaEventQueue.java:285)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.run(KafkaEventQueue.java:181)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"controller-0-ThrottledChannelReaper-Fetch" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"controller-0-ThrottledChannelReaper-Produce" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"controller-0-ThrottledChannelReaper-Request" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-ThrottledChannelReaper-Fetch" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"controller-0-ThrottledChannelReaper-ControllerMutation" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-ThrottledChannelReaper-Produce" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-ThrottledChannelReaper-Request" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-AlterAcls" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-ThrottledChannelReaper-ControllerMutation" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//kafka.server.ClientQuotaManager$ThrottledChannelReaper.doWork(ClientQuotaManager.scala:221)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"data-plane-kafka-request-handler-0" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-1" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-2" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-3" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-4" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-5" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-6" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-7" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"controller-0-registration-manager-event-handler" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.handleEvents(KafkaEventQueue.java:277)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.run(KafkaEventQueue.java:181)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-network-thread-0-ListenerName(CONTROLLER)-PLAINTEXT-0" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//kafka.network.Processor.poll(SocketServer.scala:1107)
        at app//kafka.network.Processor.run(SocketServer.scala:1011)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-network-thread-0-ListenerName(CONTROLLER)-PLAINTEXT-1" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//kafka.network.Processor.poll(SocketServer.scala:1107)
        at app//kafka.network.Processor.run(SocketServer.scala:1011)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-network-thread-0-ListenerName(CONTROLLER)-PLAINTEXT-2" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//kafka.network.Processor.poll(SocketServer.scala:1107)
        at app//kafka.network.Processor.run(SocketServer.scala:1011)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-socket-acceptor-ListenerName(CONTROLLER)-PLAINTEXT-0" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//kafka.network.Acceptor.acceptNewConnections(SocketServer.scala:743)
        at app//kafka.network.Acceptor.run(SocketServer.scala:695)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"broker-0-lifecycle-manager-event-handler" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.handleEvents(KafkaEventQueue.java:285)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.run(KafkaEventQueue.java:181)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"controller-0-to-controller-registration-channel-manager" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//kafka.server.NodeToControllerRequestThread.doWork(NodeToControllerChannelManager.scala:382)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-to-controller-forwarding-channel-manager" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//kafka.server.NodeToControllerRequestThread.doWork(NodeToControllerChannelManager.scala:382)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-to-controller-alter-partition-channel-manager" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//kafka.server.NodeToControllerRequestThread.doWork(NodeToControllerChannelManager.scala:382)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-directory-assignments-manager-event-handler" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.handleEvents(KafkaEventQueue.java:277)
        at app//org.apache.kafka.queue.KafkaEventQueue$EventHandler.run(KafkaEventQueue.java:181)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"broker-0-to-controller-directory-assignments-channel-manager" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//kafka.server.NodeToControllerRequestThread.doWork(NodeToControllerChannelManager.scala:382)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-Produce" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-Fetch" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-DeleteRecords" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-ElectLeader" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-RemoteFetch" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-Heartbeat" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-Rebalance" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"broker-0-to-controller-heartbeat-channel-manager" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//kafka.server.NodeToControllerRequestThread.doWork(NodeToControllerChannelManager.scala:382)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"ExpirationReaper-0-AlterAcls" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.DelayQueue.poll(DelayQueue.java:291)
        at app//org.apache.kafka.server.util.timer.SystemTimer.advanceClock(SystemTimer.java:87)
        at app//kafka.server.DelayedOperationPurgatory.advanceClock(DelayedOperation.scala:418)
        at app//kafka.server.DelayedOperationPurgatory$ExpiredOperationReaper.doWork(DelayedOperation.scala:444)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"data-plane-kafka-request-handler-0" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-1" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-2" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-3" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-4" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-5" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-6" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-request-handler-7" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.poll(ArrayBlockingQueue.java:435)
        at app//kafka.network.RequestChannel.receiveRequest(RequestChannel.scala:480)
        at app//kafka.server.KafkaRequestHandler.run(KafkaRequestHandler.scala:114)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-0" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-1" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-2" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-3" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-4" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-log-cleaner-thread-0" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer.acquire(AbstractQueuedSynchronizer.java:756)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer.tryAcquireSharedNanos(AbstractQueuedSynchronizer.java:1126)
        at java.base@21.0.4/java.util.concurrent.CountDownLatch.await(CountDownLatch.java:276)
        at app//org.apache.kafka.server.util.ShutdownableThread.pause(ShutdownableThread.java:121)
        at app//kafka.log.LogCleaner$CleanerThread.doWork(LogCleaner.scala:383)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"kafka-scheduler-5" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-6" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-7" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-scheduler-8" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"LogDirFailureHandler" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ArrayBlockingQueue.take(ArrayBlockingQueue.java:420)
        at app//org.apache.kafka.storage.internals.log.LogDirFailureChannel.takeNextOfflineLogDir(LogDirFailureChannel.java:75)
        at app//kafka.server.ReplicaManager$LogDirFailureHandler.doWork(ReplicaManager.scala:320)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"AddPartitionsToTxnSenderThread-0" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.doWork(InterBrokerSendThread.java:136)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"group-metadata-manager-0" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"transaction-log-manager-0" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:269)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1758)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"TxnMarkerSenderThread-0" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.pollOnce(InterBrokerSendThread.java:109)
        at app//org.apache.kafka.server.util.InterBrokerSendThread.doWork(InterBrokerSendThread.java:136)
        at app//org.apache.kafka.server.util.ShutdownableThread.run(ShutdownableThread.java:135)

"kafka-scheduler-9" 
   java.lang.Thread.State: WAITING
        at java.base@21.0.4/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base@21.0.4/java.util.concurrent.locks.LockSupport.park(LockSupport.java:371)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionNode.block(AbstractQueuedSynchronizer.java:519)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.unmanagedBlock(ForkJoinPool.java:3780)
        at java.base@21.0.4/java.util.concurrent.ForkJoinPool.managedBlock(ForkJoinPool.java:3725)
        at java.base@21.0.4/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.await(AbstractQueuedSynchronizer.java:1707)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1177)
        at java.base@21.0.4/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1070)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1130)
        at java.base@21.0.4/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-network-thread-0-ListenerName(EXTERNAL)-PLAINTEXT-0" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//kafka.network.Processor.poll(SocketServer.scala:1107)
        at app//kafka.network.Processor.run(SocketServer.scala:1011)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-network-thread-0-ListenerName(EXTERNAL)-PLAINTEXT-1" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//kafka.network.Processor.poll(SocketServer.scala:1107)
        at app//kafka.network.Processor.run(SocketServer.scala:1011)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-network-thread-0-ListenerName(EXTERNAL)-PLAINTEXT-2" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//kafka.network.Processor.poll(SocketServer.scala:1107)
        at app//kafka.network.Processor.run(SocketServer.scala:1011)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"data-plane-kafka-socket-acceptor-ListenerName(EXTERNAL)-PLAINTEXT-0" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//kafka.network.Acceptor.acceptNewConnections(SocketServer.scala:743)
        at app//kafka.network.Acceptor.run(SocketServer.scala:695)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)

"kafka-admin-client-thread | adminclient-4" 
   java.lang.Thread.State: RUNNABLE
        at java.base@21.0.4/sun.nio.ch.WEPoll.wait(Native Method)
        at java.base@21.0.4/sun.nio.ch.WEPollSelectorImpl.doSelect(WEPollSelectorImpl.java:114)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.lockAndDoSelect(SelectorImpl.java:130)
        at java.base@21.0.4/sun.nio.ch.SelectorImpl.select(SelectorImpl.java:142)
        at app//org.apache.kafka.common.network.Selector.select(Selector.java:874)
        at app//org.apache.kafka.common.network.Selector.poll(Selector.java:465)
        at app//org.apache.kafka.clients.NetworkClient.poll(NetworkClient.java:585)
        at app//org.apache.kafka.clients.admin.KafkaAdminClient$AdminClientRunnable.processRequests(KafkaAdminClient.java:1504)
        at app//org.apache.kafka.clients.admin.KafkaAdminClient$AdminClientRunnable.run(KafkaAdminClient.java:1435)
        at java.base@21.0.4/java.lang.Thread.runWith(Thread.java:1596)
        at java.base@21.0.4/java.lang.Thread.run(Thread.java:1583)



