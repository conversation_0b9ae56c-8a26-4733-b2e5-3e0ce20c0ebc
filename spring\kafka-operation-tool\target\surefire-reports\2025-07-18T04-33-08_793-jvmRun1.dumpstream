# Created at 2025-07-18T04:35:58.086
TestSet has not finished before stream error has appeared >> initializing exit by non-null configuration: EXIT
java.io.EOFException
	at org.apache.maven.surefire.api.stream.AbstractStreamDecoder.read(AbstractStreamDecoder.java:444)
	at org.apache.maven.surefire.api.stream.AbstractStreamDecoder.read(AbstractStreamDecoder.java:419)
	at org.apache.maven.surefire.api.stream.AbstractStreamDecoder.readMessageType(AbstractStreamDecoder.java:116)
	at org.apache.maven.surefire.booter.stream.CommandDecoder.decode(CommandDecoder.java:77)
	at org.apache.maven.surefire.booter.spi.CommandChannelDecoder.decode(CommandChannelDecoder.java:60)
	at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.run(CommandReader.java:290)
	at java.base/java.lang.Thread.run(Thread.java:1583)


