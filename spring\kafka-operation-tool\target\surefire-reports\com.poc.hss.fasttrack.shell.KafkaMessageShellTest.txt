-------------------------------------------------------------------------------
Test set: com.poc.hss.fasttrack.shell.KafkaMessageShellTest
-------------------------------------------------------------------------------
Tests run: 9, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 0.164 s <<< FAILURE! -- in com.poc.hss.fasttrack.shell.KafkaMessageShellTest
com.poc.hss.fasttrack.shell.KafkaMessageShellTest.testListAllMessage_countOnly -- Time elapsed: 0.072 s <<< FAILURE!
org.opentest4j.AssertionFailedError: expected: <true> but was: <false>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertionFailureBuilder.buildAndThrow(AssertionFailureBuilder.java:132)
	at org.junit.jupiter.api.AssertTrue.failNotTrue(AssertTrue.java:63)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:36)
	at org.junit.jupiter.api.AssertTrue.assertTrue(AssertTrue.java:31)
	at org.junit.jupiter.api.Assertions.assertTrue(Assertions.java:183)
	at com.poc.hss.fasttrack.shell.KafkaMessageShellTest.testListAllMessage_countOnly(KafkaMessageShellTest.java:68)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

