<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.bigquery.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-bigquery</a> &gt; <span class="el_package">com.poc.hss.fasttrack.bigquery.dto</span></div><h1>com.poc.hss.fasttrack.bigquery.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">674 of 674</td><td class="ctr2">0%</td><td class="bar">100 of 100</td><td class="ctr2">0%</td><td class="ctr1">101</td><td class="ctr2">101</td><td class="ctr1">15</td><td class="ctr2">15</td><td class="ctr1">51</td><td class="ctr2">51</td><td class="ctr1">4</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a0"><a href="BigQueryInsertionDTO.java.html" class="el_source">BigQueryInsertionDTO.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="416" alt="416"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="62" alt="62"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">61</td><td class="ctr2" id="g0">61</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i0">9</td><td class="ctr1" id="j0">30</td><td class="ctr2" id="k0">30</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a1"><a href="BigQueryQueryDTO.java.html" class="el_source">BigQueryQueryDTO.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="74" height="10" title="258" alt="258"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="38" alt="38"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">40</td><td class="ctr2" id="g1">40</td><td class="ctr1" id="h1">6</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j1">21</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m1">2</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>