<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BigQueryDataType.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-bigquery</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.bigquery.enums</a> &gt; <span class="el_source">BigQueryDataType.java</span></div><h1>BigQueryDataType.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.bigquery.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

<span class="fc" id="L6">public enum BigQueryDataType {</span>

<span class="fc" id="L8">    BOOL(&quot;BOOL&quot;), </span>
<span class="fc" id="L9">    INT64(&quot;INT64&quot;), </span>
<span class="fc" id="L10">    FLOAT64(&quot;FLOAT64&quot;), </span>
<span class="fc" id="L11">    NUMERIC(&quot;NUMERIC&quot;), </span>
<span class="fc" id="L12">    BIGNUMERIC(&quot;BIGNUMERIC&quot;), </span>
<span class="fc" id="L13">    STRING(&quot;STRING&quot;),</span>
<span class="fc" id="L14">    BYTES(&quot;BYTES&quot;), </span>
<span class="fc" id="L15">    STRUCT(&quot;STRUCT&quot;), </span>
<span class="fc" id="L16">    ARRAY(&quot;ARRAY&quot;), </span>
<span class="fc" id="L17">    TIMESTAMP(&quot;TIMESTAMP&quot;), </span>
<span class="fc" id="L18">    DATE(&quot;DATE&quot;), </span>
<span class="fc" id="L19">    TIME(&quot;TIME&quot;),</span>
<span class="fc" id="L20">    DATETIME(&quot;DATETIME&quot;), </span>
<span class="fc" id="L21">    GEOGRAPHY(&quot;GEOGRAPHY&quot;);</span>

    private String value;

<span class="fc" id="L25">    BigQueryDataType(String value) {</span>
<span class="fc" id="L26">        this.value = value;</span>
<span class="fc" id="L27">      }</span>

    @Override
    @JsonValue
    public String toString() {
<span class="fc" id="L32">        return String.valueOf(value);</span>
    }

    @JsonCreator
    public static BigQueryDataType fromValue(String text) {
<span class="fc bfc" id="L37" title="All 2 branches covered.">        for (BigQueryDataType b : BigQueryDataType.values()) {</span>
<span class="fc bfc" id="L38" title="All 2 branches covered.">            if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L39">                return b;</span>
            }
        }
<span class="fc" id="L42">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>