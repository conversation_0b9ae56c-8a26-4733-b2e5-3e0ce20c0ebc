<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BigQueryInsertionUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-bigquery</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.bigquery.utils</a> &gt; <span class="el_source">BigQueryInsertionUtils.java</span></div><h1>BigQueryInsertionUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.bigquery.utils;

import com.google.cloud.bigquery.*;
import com.poc.hss.fasttrack.bigquery.dto.BigQueryInsertionDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

<span class="nc" id="L12">@Slf4j</span>
<span class="nc" id="L13">public class BigQueryInsertionUtils {</span>

    public static void createDatasetAndTableIfNotExist(BigQuery bigQuery, BigQueryInsertionDTO insertionDTO) {
<span class="nc" id="L16">        DatasetId datasetId = DatasetId.of(insertionDTO.getProjectId(), insertionDTO.getDatasetName());</span>
<span class="nc" id="L17">        Dataset dataset = bigQuery.getDataset(datasetId);</span>
<span class="nc bnc" id="L18" title="All 2 branches missed.">        if (Objects.isNull(dataset)) {</span>
<span class="nc" id="L19">            createDataset(bigQuery, datasetId, insertionDTO.getKmsKeyName(), insertionDTO.getLocation());</span>
        }
<span class="nc" id="L21">        TableId tableId = getTableId(insertionDTO);</span>
<span class="nc" id="L22">        Table table = bigQuery.getTable(tableId);</span>
<span class="nc bnc" id="L23" title="All 2 branches missed.">        if (Objects.isNull(table)) {</span>
<span class="nc" id="L24">            createTable(bigQuery, tableId, insertionDTO.getFields(), insertionDTO.getKmsKeyName());</span>
        }
<span class="nc" id="L26">    }</span>

    private static void createDataset(BigQuery bigQuery, DatasetId datasetName, String kmsKeyName, String location) {
<span class="nc" id="L29">        EncryptionConfiguration encryptionConfiguration = EncryptionConfiguration.newBuilder()</span>
<span class="nc" id="L30">                .setKmsKeyName(kmsKeyName)</span>
<span class="nc" id="L31">                .build();</span>
<span class="nc" id="L32">        DatasetInfo datasetInfo = DatasetInfo.newBuilder(datasetName)</span>
<span class="nc" id="L33">                .setDefaultEncryptionConfiguration(encryptionConfiguration)</span>
<span class="nc" id="L34">                .setLocation(location)</span>
<span class="nc" id="L35">                .build();</span>
<span class="nc" id="L36">        Dataset newDataset = bigQuery.create(datasetInfo);</span>
<span class="nc" id="L37">        log.info(&quot;Dataset {} ({}) created successfully&quot;, newDataset, location);</span>
<span class="nc" id="L38">    }</span>

    private static void createTable(BigQuery bigQuery, TableId tableId, List&lt;Field&gt; fieldList, String kmsKeyName) {
        try {
<span class="nc" id="L42">            TableDefinition tableDefinition = StandardTableDefinition.of(Schema.of(fieldList));</span>
<span class="nc" id="L43">            TableInfo tableInfo = TableInfo.newBuilder(tableId, tableDefinition)</span>
<span class="nc" id="L44">                    .setEncryptionConfiguration(BigQueryUtils.getEncryptionConfiguration(kmsKeyName))</span>
<span class="nc" id="L45">                    .build();</span>
<span class="nc" id="L46">            bigQuery.create(tableInfo);</span>
<span class="nc" id="L47">            log.info(&quot;Table {} created successfully&quot;, tableId);</span>
<span class="nc" id="L48">        } catch (BigQueryException e) {</span>
<span class="nc" id="L49">            log.error(&quot;Table was not created&quot;, e);</span>
<span class="nc" id="L50">            throw e;</span>
<span class="nc" id="L51">        }</span>
<span class="nc" id="L52">    }</span>

    public static InsertAllResponse tableInsertRows(BigQuery bigQuery, BigQueryInsertionDTO insertionDTO) {
        try {
<span class="nc" id="L56">            List&lt;InsertAllRequest.RowToInsert&gt; rows = insertionDTO.getValueMaps().stream()</span>
<span class="nc" id="L57">                    .map(valueMap -&gt; convertMapToRowToInsert(valueMap))</span>
<span class="nc" id="L58">                    .collect(Collectors.toList());</span>
<span class="nc" id="L59">            return bigQuery.insertAll(InsertAllRequest.newBuilder(getTableId(insertionDTO))</span>
<span class="nc" id="L60">                    .setRows(rows)</span>
<span class="nc" id="L61">                    .build());</span>
<span class="nc" id="L62">        } catch (BigQueryException e) {</span>
<span class="nc" id="L63">            log.error(&quot;Insert operation not performed&quot;, e);</span>
<span class="nc" id="L64">            throw e;</span>
        }
    }

    private static InsertAllRequest.RowToInsert convertMapToRowToInsert(Map&lt;String, Object&gt; valueMap) {
<span class="nc bnc" id="L69" title="All 2 branches missed.">        if (Objects.isNull(valueMap)) {</span>
<span class="nc" id="L70">            return null;</span>
        }
<span class="nc" id="L72">        return InsertAllRequest.RowToInsert.of(valueMap);</span>
    }

    public static TableId getTableId(BigQueryInsertionDTO insertionDTO) {
<span class="nc" id="L76">        return TableId.of(insertionDTO.getProjectId(), insertionDTO.getDatasetName(), insertionDTO.getTableName());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>