<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BigQueryQueryUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-bigquery</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.bigquery.utils</a> &gt; <span class="el_source">BigQueryQueryUtils.java</span></div><h1>BigQueryQueryUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.bigquery.utils;

import com.google.cloud.bigquery.*;
import com.poc.hss.fasttrack.bigquery.dto.BigQueryQueryDTO;
import com.poc.hss.fasttrack.bigquery.enums.BigQueryDataType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

<span class="nc" id="L16">@Slf4j</span>
<span class="nc" id="L17">public class BigQueryQueryUtils {</span>

    public static List&lt;Map&lt;String, Object&gt;&gt; query(BigQuery bigQuery, BigQueryQueryDTO queryDTO) throws Exception {
<span class="nc" id="L20">        QueryJobConfiguration queryConfig = getQueryJobConfiguration(queryDTO);</span>
<span class="nc" id="L21">        Job queryJob = bigQuery.create(JobInfo.newBuilder(queryConfig).build());</span>
<span class="nc" id="L22">        queryJob = queryJob.waitFor();</span>
<span class="nc bnc" id="L23" title="All 2 branches missed.">        if (queryJob == null) {</span>
<span class="nc" id="L24">            throw new Exception(&quot;Query job no longer exists&quot;);</span>
        }
<span class="nc bnc" id="L26" title="All 2 branches missed.">        if (queryJob.getStatus().getError() != null) {</span>
<span class="nc" id="L27">            throw new Exception(queryJob.getStatus().getError().toString());</span>
        }
<span class="nc" id="L29">        return extractResultSet(queryJob.getQueryResults());</span>
    }

    public static void query(BigQuery bigQuery, BigQueryQueryDTO queryDTO, Consumer&lt;Map&lt;String, Object&gt;&gt; rowProcessor) throws Exception {
<span class="nc" id="L33">        QueryJobConfiguration queryConfig = getQueryJobConfiguration(queryDTO);</span>
<span class="nc" id="L34">        Job queryJob = bigQuery.create(JobInfo.newBuilder(queryConfig).build());</span>
<span class="nc" id="L35">        queryJob = queryJob.waitFor();</span>
<span class="nc bnc" id="L36" title="All 2 branches missed.">        if (queryJob == null) {</span>
<span class="nc" id="L37">            throw new Exception(&quot;Query job no longer exists&quot;);</span>
        }
<span class="nc bnc" id="L39" title="All 2 branches missed.">        if (queryJob.getStatus().getError() != null) {</span>
<span class="nc" id="L40">            throw new Exception(queryJob.getStatus().getError().toString());</span>
        }
<span class="nc" id="L42">        extractResultSet(queryJob.getQueryResults(), rowProcessor);</span>
<span class="nc" id="L43">    }</span>

    private static QueryJobConfiguration getQueryJobConfiguration(BigQueryQueryDTO queryDTO) {
<span class="nc" id="L46">        QueryJobConfiguration queryConfig = QueryJobConfiguration.newBuilder(queryDTO.getQuery()).build();</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">        if (StringUtils.isNotEmpty(queryDTO.getKmsKeyName())) {</span>
<span class="nc" id="L48">            EncryptionConfiguration encryptionConfiguration = BigQueryUtils.getEncryptionConfiguration(queryDTO.getKmsKeyName());</span>
<span class="nc" id="L49">            queryConfig = queryConfig.toBuilder()</span>
<span class="nc" id="L50">                    .setDestinationEncryptionConfiguration(encryptionConfiguration)</span>
<span class="nc" id="L51">                    .build();</span>
        }
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (StringUtils.isNotEmpty(queryDTO.getDataSetName())) {</span>
<span class="nc" id="L54">            queryConfig = queryConfig.toBuilder()</span>
<span class="nc" id="L55">                    .setDefaultDataset(queryDTO.getDataSetName())</span>
<span class="nc" id="L56">                    .build();</span>
        }
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (MapUtils.isNotEmpty(queryDTO.getQueryParameterValueMap())) {</span>
<span class="nc" id="L59">            queryConfig = queryConfig.toBuilder()</span>
<span class="nc" id="L60">                    .setNamedParameters(queryDTO.getQueryParameterValueMap())</span>
<span class="nc" id="L61">                    .build();</span>
        }
<span class="nc" id="L63">        return queryConfig;</span>
    }

    private static void extractResultSet(TableResult queryResult, Consumer&lt;Map&lt;String, Object&gt;&gt; rowProcessor) {
<span class="nc" id="L67">        List&lt;Field&gt; fields = queryResult.getSchema().getFields();</span>
<span class="nc bnc" id="L68" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(fields)) {</span>
<span class="nc" id="L69">            return;</span>
        }
<span class="nc" id="L71">        queryResult.iterateAll().forEach(row -&gt; {</span>
<span class="nc" id="L72">            Map&lt;String, Object&gt; rowMap = convertRowToMap(fields, row);</span>
<span class="nc" id="L73">            rowProcessor.accept(rowMap);</span>
<span class="nc" id="L74">        });</span>
<span class="nc" id="L75">    }</span>

    private static List&lt;Map&lt;String, Object&gt;&gt; extractResultSet(TableResult queryResult) {
<span class="nc" id="L78">            List&lt;Field&gt; fields = queryResult.getSchema().getFields();</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">            if (CollectionUtils.isEmpty(fields)) {</span>
<span class="nc" id="L80">                return null;</span>
            }
<span class="nc" id="L82">            List&lt;Map&lt;String, Object&gt;&gt; results = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L83">            queryResult.iterateAll().forEach(row -&gt; results.add(convertRowToMap(fields, row)));</span>
<span class="nc" id="L84">            return results;</span>
        }

    private static Map&lt;String, Object&gt; convertRowToMap(List&lt;Field&gt; fields, FieldValueList row) {
<span class="nc" id="L88">        return fields.stream()</span>
<span class="nc" id="L89">                .collect(Collectors.toMap(Field::getName, field -&gt; {</span>
<span class="nc" id="L90">                    log.info(&quot;Field name: {}&quot;, field.getName());</span>
<span class="nc" id="L91">                    FieldValue fieldValue = row.get(field.getName());</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">                    return fieldValue.getValue() != null ? convertField(field, fieldValue) : Optional.empty();</span>
                }));
    }

    private static Object convertField(Field field, FieldValue fieldValue) {
<span class="nc bnc" id="L97" title="All 3 branches missed.">        switch (fieldValue.getAttribute()) {</span>
            case REPEATED:
<span class="nc" id="L99">                return fieldValue.getRepeatedValue().stream()</span>
<span class="nc" id="L100">                        .map(value -&gt; convertField(field, value))</span>
<span class="nc" id="L101">                        .toList();</span>
            case RECORD:
<span class="nc" id="L103">                Map&lt;String, Object&gt; recordMap = new HashMap&lt;&gt;();</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">                for (int i = 0; i &lt; field.getSubFields().size(); i++) {</span>
<span class="nc" id="L105">                    Field subField = field.getSubFields().get(i);</span>
<span class="nc" id="L106">                    recordMap.put(subField.getName(), convertField(subField, fieldValue.getRecordValue().get(i)));</span>
                }
<span class="nc" id="L108">                return recordMap;</span>
            default:
<span class="nc" id="L110">                return fieldValue.getValue();</span>
        }
    }

    public static QueryParameterValue getQueryParameterValue(String value, BigQueryDataType dataType) {
<span class="nc bnc" id="L115" title="All 10 branches missed.">        switch (dataType) {</span>
            case BOOL:
<span class="nc" id="L117">                return QueryParameterValue.bool(Boolean.valueOf(value));</span>
            case INT64:
<span class="nc" id="L119">                return QueryParameterValue.int64(Integer.valueOf(value));</span>
            case FLOAT64:
<span class="nc" id="L121">                return QueryParameterValue.float64(Double.valueOf(value));</span>
            case NUMERIC:
<span class="nc" id="L123">                return QueryParameterValue.numeric(new BigDecimal(value));</span>
            case BIGNUMERIC:
<span class="nc" id="L125">                return QueryParameterValue.bigNumeric(new BigDecimal(value));</span>
            case TIMESTAMP:
<span class="nc" id="L127">                return QueryParameterValue.timestamp(value);</span>
            case DATE:
<span class="nc" id="L129">                return QueryParameterValue.date(value);</span>
            case TIME:
<span class="nc" id="L131">                return QueryParameterValue.time(value);</span>
            case DATETIME:
<span class="nc" id="L133">                return QueryParameterValue.dateTime(value);</span>
            case STRING:
            default:
<span class="nc" id="L136">                return QueryParameterValue.string(value);</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>