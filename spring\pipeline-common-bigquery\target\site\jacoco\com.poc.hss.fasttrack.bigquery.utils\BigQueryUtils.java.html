<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BigQueryUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-bigquery</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.bigquery.utils</a> &gt; <span class="el_source">BigQueryUtils.java</span></div><h1>BigQueryUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.bigquery.utils;

import com.google.cloud.bigquery.EncryptionConfiguration;
import com.google.cloud.bigquery.StandardSQLTypeName;
import com.poc.hss.fasttrack.bigquery.enums.BigQueryDataType;

<span class="nc" id="L7">public class BigQueryUtils {</span>

    public static EncryptionConfiguration getEncryptionConfiguration(String kmsKeyName) {
<span class="nc" id="L10">        return EncryptionConfiguration.newBuilder().setKmsKeyName(kmsKeyName).build();</span>
    }

    public static StandardSQLTypeName convertType(BigQueryDataType type) {
<span class="nc bnc" id="L14" title="All 9 branches missed.">        switch(type) {</span>
            case BOOL:
<span class="nc" id="L16">                return StandardSQLTypeName.BOOL;</span>
            case INT64:
<span class="nc" id="L18">                return StandardSQLTypeName.INT64;</span>
            case FLOAT64:
<span class="nc" id="L20">                return StandardSQLTypeName.FLOAT64;</span>
            case NUMERIC:
<span class="nc" id="L22">                return StandardSQLTypeName.NUMERIC;</span>
            case BIGNUMERIC:
<span class="nc" id="L24">                return StandardSQLTypeName.BIGNUMERIC;</span>
            case DATE:
<span class="nc" id="L26">                return StandardSQLTypeName.DATE;</span>
            case DATETIME:
<span class="nc" id="L28">                return StandardSQLTypeName.DATETIME;</span>
            case TIMESTAMP:
<span class="nc" id="L30">                return StandardSQLTypeName.TIMESTAMP;</span>
            case STRING:
            default:
<span class="nc" id="L33">                return StandardSQLTypeName.STRING;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>