<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.bigquery.utils</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-bigquery</a> &gt; <span class="el_package">com.poc.hss.fasttrack.bigquery.utils</span></div><h1>com.poc.hss.fasttrack.bigquery.utils</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">488 of 488</td><td class="ctr2">0%</td><td class="bar">50 of 50</td><td class="ctr2">0%</td><td class="ctr1">59</td><td class="ctr2">59</td><td class="ctr1">131</td><td class="ctr2">131</td><td class="ctr1">26</td><td class="ctr2">26</td><td class="ctr1">3</td><td class="ctr2">3</td></tr></tfoot><tbody><tr><td id="a1"><a href="BigQueryQueryUtils.java.html" class="el_source">BigQueryQueryUtils.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="308" alt="308"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="35" alt="35"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">36</td><td class="ctr2" id="g0">36</td><td class="ctr1" id="h0">74</td><td class="ctr2" id="i0">74</td><td class="ctr1" id="j0">14</td><td class="ctr2" id="k0">14</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="BigQueryInsertionUtils.java.html" class="el_source">BigQueryInsertionUtils.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="58" height="10" title="149" alt="149"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">12</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h1">45</td><td class="ctr2" id="i1">45</td><td class="ctr1" id="j1">9</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="BigQueryUtils.java.html" class="el_source">BigQueryUtils.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="31" alt="31"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="9" alt="9"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">11</td><td class="ctr2" id="g2">11</td><td class="ctr1" id="h2">12</td><td class="ctr2" id="i2">12</td><td class="ctr1" id="j2">3</td><td class="ctr2" id="k2">3</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>