<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="pipeline-common-bigquery"><sessioninfo id="H344L0L63UFKL6Z-47c75793" start="1752768054525" dump="1752768055297"/><package name="com/poc/hss/fasttrack/bigquery/dto"><class name="com/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO" sourcefilename="BigQueryInsertionDTO.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V" line="10"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getProjectId" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDatasetName" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTableName" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getLocation" desc="()Ljava/lang/String;" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getKmsKeyName" desc="()Ljava/lang/String;" line="16"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getFields" desc="()Ljava/util/List;" line="17"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getValueMaps" desc="()Ljava/util/List;" line="18"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setProjectId" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDatasetName" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setTableName" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setLocation" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setKmsKeyName" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setFields" desc="(Ljava/util/List;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setValueMaps" desc="(Ljava/util/List;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="9"><counter type="INSTRUCTION" missed="140" covered="0"/><counter type="BRANCH" missed="48" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="25" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="9"><counter type="INSTRUCTION" missed="104" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="342" covered="0"/><counter type="BRANCH" missed="62" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="51" covered="0"/><counter type="METHOD" missed="20" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO" sourcefilename="BigQueryQueryDTO.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;)V" line="10"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO$BigQueryQueryDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getQuery" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getKmsKeyName" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDataSetName" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getQueryParameterValueMap" desc="()Ljava/util/Map;" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setQuery" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setKmsKeyName" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDataSetName" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setQueryParameterValueMap" desc="(Ljava/util/Map;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="9"><counter type="INSTRUCTION" missed="89" covered="0"/><counter type="BRANCH" missed="30" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="9"><counter type="INSTRUCTION" missed="62" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="212" covered="0"/><counter type="BRANCH" missed="38" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="33" covered="0"/><counter type="METHOD" missed="14" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO$BigQueryQueryDTOBuilder" sourcefilename="BigQueryQueryDTO.java"><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="query" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO$BigQueryQueryDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kmsKeyName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO$BigQueryQueryDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="dataSetName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO$BigQueryQueryDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryParameterValueMap" desc="(Ljava/util/Map;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO$BigQueryQueryDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO;" line="10"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="46" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="7" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder" sourcefilename="BigQueryInsertionDTO.java"><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="projectId" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="datasetName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="tableName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="location" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kmsKeyName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fields" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="valueMaps" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO$BigQueryInsertionDTOBuilder;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO;" line="10"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="74" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="BigQueryQueryDTO.java"><line nr="9" mi="181" ci="0" mb="38" cb="0"/><line nr="10" mi="65" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="15" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="258" covered="0"/><counter type="BRANCH" missed="38" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="40" covered="0"/><counter type="METHOD" missed="21" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><sourcefile name="BigQueryInsertionDTO.java"><line nr="9" mi="293" ci="0" mb="62" cb="0"/><line nr="10" mi="102" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="16" mi="3" ci="0" mb="0" cb="0"/><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="416" covered="0"/><counter type="BRANCH" missed="62" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="61" covered="0"/><counter type="METHOD" missed="30" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="674" covered="0"/><counter type="BRANCH" missed="100" covered="0"/><counter type="LINE" missed="15" covered="0"/><counter type="COMPLEXITY" missed="101" covered="0"/><counter type="METHOD" missed="51" covered="0"/><counter type="CLASS" missed="4" covered="0"/></package><package name="com/poc/hss/fasttrack/bigquery/enums"><class name="com/poc/hss/fasttrack/bigquery/enums/BigQueryDataType" sourcefilename="BigQueryDataType.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="25"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="32"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/bigquery/enums/BigQueryDataType;" line="37"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="101"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="139"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="BigQueryDataType.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="9" mi="0" ci="7" mb="0" cb="0"/><line nr="10" mi="0" ci="7" mb="0" cb="0"/><line nr="11" mi="0" ci="7" mb="0" cb="0"/><line nr="12" mi="0" ci="7" mb="0" cb="0"/><line nr="13" mi="0" ci="7" mb="0" cb="0"/><line nr="14" mi="0" ci="7" mb="0" cb="0"/><line nr="15" mi="0" ci="7" mb="0" cb="0"/><line nr="16" mi="0" ci="7" mb="0" cb="0"/><line nr="17" mi="0" ci="7" mb="0" cb="0"/><line nr="18" mi="0" ci="7" mb="0" cb="0"/><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="7" mb="0" cb="0"/><line nr="21" mi="0" ci="7" mb="0" cb="0"/><line nr="25" mi="0" ci="4" mb="0" cb="0"/><line nr="26" mi="0" ci="3" mb="0" cb="0"/><line nr="27" mi="0" ci="1" mb="0" cb="0"/><line nr="32" mi="0" ci="4" mb="0" cb="0"/><line nr="37" mi="0" ci="16" mb="0" cb="2"/><line nr="38" mi="0" ci="6" mb="0" cb="2"/><line nr="39" mi="0" ci="2" mb="0" cb="0"/><line nr="42" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="139"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="0" covered="139"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="23"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/poc/hss/fasttrack/bigquery/utils"><class name="com/poc/hss/fasttrack/bigquery/utils/BigQueryInsertionUtils" sourcefilename="BigQueryInsertionUtils.java"><method name="&lt;init&gt;" desc="()V" line="13"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createDatasetAndTableIfNotExist" desc="(Lcom/google/cloud/bigquery/BigQuery;Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO;)V" line="16"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createDataset" desc="(Lcom/google/cloud/bigquery/BigQuery;Lcom/google/cloud/bigquery/DatasetId;Ljava/lang/String;Ljava/lang/String;)V" line="29"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createTable" desc="(Lcom/google/cloud/bigquery/BigQuery;Lcom/google/cloud/bigquery/TableId;Ljava/util/List;Ljava/lang/String;)V" line="42"><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="tableInsertRows" desc="(Lcom/google/cloud/bigquery/BigQuery;Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO;)Lcom/google/cloud/bigquery/InsertAllResponse;" line="56"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertMapToRowToInsert" desc="(Ljava/util/Map;)Lcom/google/cloud/bigquery/InsertAllRequest$RowToInsert;" line="69"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTableId" desc="(Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryInsertionDTO;)Lcom/google/cloud/bigquery/TableId;" line="76"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$tableInsertRows$0" desc="(Ljava/util/Map;)Lcom/google/cloud/bigquery/InsertAllRequest$RowToInsert;" line="57"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="149" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="45" covered="0"/><counter type="COMPLEXITY" missed="12" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/bigquery/utils/BigQueryQueryUtils" sourcefilename="BigQueryQueryUtils.java"><method name="&lt;init&gt;" desc="()V" line="17"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="query" desc="(Lcom/google/cloud/bigquery/BigQuery;Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO;)Ljava/util/List;" line="20"><counter type="INSTRUCTION" missed="41" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="query" desc="(Lcom/google/cloud/bigquery/BigQuery;Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO;Ljava/util/function/Consumer;)V" line="33"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getQueryJobConfiguration" desc="(Lcom/poc/hss/fasttrack/bigquery/dto/BigQueryQueryDTO;)Lcom/google/cloud/bigquery/QueryJobConfiguration;" line="46"><counter type="INSTRUCTION" missed="43" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="15" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="extractResultSet" desc="(Lcom/google/cloud/bigquery/TableResult;Ljava/util/function/Consumer;)V" line="67"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="extractResultSet" desc="(Lcom/google/cloud/bigquery/TableResult;)Ljava/util/List;" line="78"><counter type="INSTRUCTION" missed="21" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertRowToMap" desc="(Ljava/util/List;Lcom/google/cloud/bigquery/FieldValueList;)Ljava/util/Map;" line="88"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertField" desc="(Lcom/google/cloud/bigquery/Field;Lcom/google/cloud/bigquery/FieldValue;)Ljava/lang/Object;" line="97"><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="BRANCH" missed="5" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getQueryParameterValue" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/bigquery/enums/BigQueryDataType;)Lcom/google/cloud/bigquery/QueryParameterValue;" line="115"><counter type="INSTRUCTION" missed="44" covered="0"/><counter type="BRANCH" missed="10" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$convertField$3" desc="(Lcom/google/cloud/bigquery/Field;Lcom/google/cloud/bigquery/FieldValue;)Ljava/lang/Object;" line="100"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$convertRowToMap$2" desc="(Lcom/google/cloud/bigquery/FieldValueList;Lcom/google/cloud/bigquery/Field;)Ljava/lang/Object;" line="90"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$extractResultSet$1" desc="(Ljava/util/List;Ljava/util/List;Lcom/google/cloud/bigquery/FieldValueList;)V" line="83"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$extractResultSet$0" desc="(Ljava/util/List;Ljava/util/function/Consumer;Lcom/google/cloud/bigquery/FieldValueList;)V" line="72"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="308" covered="0"/><counter type="BRANCH" missed="35" covered="0"/><counter type="LINE" missed="74" covered="0"/><counter type="COMPLEXITY" missed="36" covered="0"/><counter type="METHOD" missed="14" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/bigquery/utils/BigQueryUtils" sourcefilename="BigQueryUtils.java"><method name="&lt;init&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getEncryptionConfiguration" desc="(Ljava/lang/String;)Lcom/google/cloud/bigquery/EncryptionConfiguration;" line="10"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertType" desc="(Lcom/poc/hss/fasttrack/bigquery/enums/BigQueryDataType;)Lcom/google/cloud/bigquery/StandardSQLTypeName;" line="14"><counter type="INSTRUCTION" missed="23" covered="0"/><counter type="BRANCH" missed="9" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="9" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="BRANCH" missed="9" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="BigQueryUtils.java"><line nr="7" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="5" ci="0" mb="0" cb="0"/><line nr="14" mi="5" ci="0" mb="9" cb="0"/><line nr="16" mi="2" ci="0" mb="0" cb="0"/><line nr="18" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="2" ci="0" mb="0" cb="0"/><line nr="22" mi="2" ci="0" mb="0" cb="0"/><line nr="24" mi="2" ci="0" mb="0" cb="0"/><line nr="26" mi="2" ci="0" mb="0" cb="0"/><line nr="28" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="2" ci="0" mb="0" cb="0"/><line nr="33" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="BRANCH" missed="9" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="BigQueryInsertionUtils.java"><line nr="12" mi="4" ci="0" mb="0" cb="0"/><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="16" mi="6" ci="0" mb="0" cb="0"/><line nr="17" mi="6" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="2" cb="0"/><line nr="19" mi="7" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="6" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="2" cb="0"/><line nr="24" mi="7" ci="0" mb="0" cb="0"/><line nr="26" mi="1" ci="0" mb="0" cb="0"/><line nr="29" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="1" ci="0" mb="0" cb="0"/><line nr="31" mi="2" ci="0" mb="0" cb="0"/><line nr="32" mi="3" ci="0" mb="0" cb="0"/><line nr="33" mi="2" ci="0" mb="0" cb="0"/><line nr="34" mi="1" ci="0" mb="0" cb="0"/><line nr="35" mi="2" ci="0" mb="0" cb="0"/><line nr="36" mi="6" ci="0" mb="0" cb="0"/><line nr="37" mi="5" ci="0" mb="0" cb="0"/><line nr="38" mi="1" ci="0" mb="0" cb="0"/><line nr="42" mi="4" ci="0" mb="0" cb="0"/><line nr="43" mi="4" ci="0" mb="0" cb="0"/><line nr="44" mi="2" ci="0" mb="0" cb="0"/><line nr="45" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="6" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="0" cb="0"/><line nr="48" mi="1" ci="0" mb="0" cb="0"/><line nr="49" mi="4" ci="0" mb="0" cb="0"/><line nr="50" mi="2" ci="0" mb="0" cb="0"/><line nr="51" mi="1" ci="0" mb="0" cb="0"/><line nr="52" mi="1" ci="0" mb="0" cb="0"/><line nr="56" mi="4" ci="0" mb="0" cb="0"/><line nr="57" mi="4" ci="0" mb="0" cb="0"/><line nr="58" mi="4" ci="0" mb="0" cb="0"/><line nr="59" mi="7" ci="0" mb="0" cb="0"/><line nr="60" mi="1" ci="0" mb="0" cb="0"/><line nr="61" mi="1" ci="0" mb="0" cb="0"/><line nr="62" mi="1" ci="0" mb="0" cb="0"/><line nr="63" mi="4" ci="0" mb="0" cb="0"/><line nr="64" mi="2" ci="0" mb="0" cb="0"/><line nr="69" mi="3" ci="0" mb="2" cb="0"/><line nr="70" mi="2" ci="0" mb="0" cb="0"/><line nr="72" mi="3" ci="0" mb="0" cb="0"/><line nr="76" mi="8" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="149" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="45" covered="0"/><counter type="COMPLEXITY" missed="12" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="BigQueryQueryUtils.java"><line nr="16" mi="4" ci="0" mb="0" cb="0"/><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="21" mi="8" ci="0" mb="0" cb="0"/><line nr="22" mi="5" ci="0" mb="0" cb="0"/><line nr="23" mi="2" ci="0" mb="2" cb="0"/><line nr="24" mi="5" ci="0" mb="0" cb="0"/><line nr="26" mi="4" ci="0" mb="2" cb="0"/><line nr="27" mi="8" ci="0" mb="0" cb="0"/><line nr="29" mi="6" ci="0" mb="0" cb="0"/><line nr="33" mi="3" ci="0" mb="0" cb="0"/><line nr="34" mi="8" ci="0" mb="0" cb="0"/><line nr="35" mi="5" ci="0" mb="0" cb="0"/><line nr="36" mi="2" ci="0" mb="2" cb="0"/><line nr="37" mi="5" ci="0" mb="0" cb="0"/><line nr="39" mi="4" ci="0" mb="2" cb="0"/><line nr="40" mi="8" ci="0" mb="0" cb="0"/><line nr="42" mi="6" ci="0" mb="0" cb="0"/><line nr="43" mi="1" ci="0" mb="0" cb="0"/><line nr="46" mi="5" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="2" cb="0"/><line nr="48" mi="4" ci="0" mb="0" cb="0"/><line nr="49" mi="3" ci="0" mb="0" cb="0"/><line nr="50" mi="1" ci="0" mb="0" cb="0"/><line nr="51" mi="2" ci="0" mb="0" cb="0"/><line nr="53" mi="4" ci="0" mb="2" cb="0"/><line nr="54" mi="3" ci="0" mb="0" cb="0"/><line nr="55" mi="2" ci="0" mb="0" cb="0"/><line nr="56" mi="2" ci="0" mb="0" cb="0"/><line nr="58" mi="4" ci="0" mb="2" cb="0"/><line nr="59" mi="3" ci="0" mb="0" cb="0"/><line nr="60" mi="2" ci="0" mb="0" cb="0"/><line nr="61" mi="2" ci="0" mb="0" cb="0"/><line nr="63" mi="2" ci="0" mb="0" cb="0"/><line nr="67" mi="4" ci="0" mb="0" cb="0"/><line nr="68" mi="3" ci="0" mb="2" cb="0"/><line nr="69" mi="1" ci="0" mb="0" cb="0"/><line nr="71" mi="6" ci="0" mb="0" cb="0"/><line nr="72" mi="4" ci="0" mb="0" cb="0"/><line nr="73" mi="3" ci="0" mb="0" cb="0"/><line nr="74" mi="1" ci="0" mb="0" cb="0"/><line nr="75" mi="1" ci="0" mb="0" cb="0"/><line nr="78" mi="4" ci="0" mb="0" cb="0"/><line nr="79" mi="3" ci="0" mb="2" cb="0"/><line nr="80" mi="2" ci="0" mb="0" cb="0"/><line nr="82" mi="4" ci="0" mb="0" cb="0"/><line nr="83" mi="13" ci="0" mb="0" cb="0"/><line nr="84" mi="2" ci="0" mb="0" cb="0"/><line nr="88" mi="6" ci="0" mb="0" cb="0"/><line nr="89" mi="3" ci="0" mb="0" cb="0"/><line nr="90" mi="5" ci="0" mb="0" cb="0"/><line nr="91" mi="5" ci="0" mb="0" cb="0"/><line nr="92" mi="9" ci="0" mb="2" cb="0"/><line nr="97" mi="6" ci="0" mb="3" cb="0"/><line nr="99" mi="6" ci="0" mb="0" cb="0"/><line nr="100" mi="5" ci="0" mb="0" cb="0"/><line nr="101" mi="1" ci="0" mb="0" cb="0"/><line nr="103" mi="4" ci="0" mb="0" cb="0"/><line nr="104" mi="9" ci="0" mb="2" cb="0"/><line nr="105" mi="5" ci="0" mb="0" cb="0"/><line nr="106" mi="11" ci="0" mb="0" cb="0"/><line nr="108" mi="2" ci="0" mb="0" cb="0"/><line nr="110" mi="3" ci="0" mb="0" cb="0"/><line nr="115" mi="5" ci="0" mb="10" cb="0"/><line nr="117" mi="4" ci="0" mb="0" cb="0"/><line nr="119" mi="4" ci="0" mb="0" cb="0"/><line nr="121" mi="4" ci="0" mb="0" cb="0"/><line nr="123" mi="6" ci="0" mb="0" cb="0"/><line nr="125" mi="6" ci="0" mb="0" cb="0"/><line nr="127" mi="3" ci="0" mb="0" cb="0"/><line nr="129" mi="3" ci="0" mb="0" cb="0"/><line nr="131" mi="3" ci="0" mb="0" cb="0"/><line nr="133" mi="3" ci="0" mb="0" cb="0"/><line nr="136" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="308" covered="0"/><counter type="BRANCH" missed="35" covered="0"/><counter type="LINE" missed="74" covered="0"/><counter type="COMPLEXITY" missed="36" covered="0"/><counter type="METHOD" missed="14" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="488" covered="0"/><counter type="BRANCH" missed="50" covered="0"/><counter type="LINE" missed="131" covered="0"/><counter type="COMPLEXITY" missed="59" covered="0"/><counter type="METHOD" missed="26" covered="0"/><counter type="CLASS" missed="3" covered="0"/></package><package name="com/poc/hss/fasttrack/bigquery/factory"><class name="com/poc/hss/fasttrack/bigquery/factory/BigQueryClientFactory" sourcefilename="BigQueryClientFactory.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBigQueryClient" desc="(Ljava/lang/String;)Lcom/google/cloud/bigquery/BigQuery;" line="9"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="BigQueryClientFactory.java"><line nr="6" mi="3" ci="0" mb="0" cb="0"/><line nr="9" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="2" ci="0" mb="0" cb="0"/><line nr="11" mi="1" ci="0" mb="0" cb="0"/><line nr="12" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><counter type="INSTRUCTION" missed="1173" covered="139"/><counter type="BRANCH" missed="150" covered="4"/><counter type="LINE" missed="151" covered="23"/><counter type="COMPLEXITY" missed="162" covered="6"/><counter type="METHOD" missed="79" covered="4"/><counter type="CLASS" missed="8" covered="1"/></report>