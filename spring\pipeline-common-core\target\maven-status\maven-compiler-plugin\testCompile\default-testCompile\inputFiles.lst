C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\ListUtilsTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\PlaceholderUtilTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\JsonObjectSerializationTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\logging\LoggingTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\converter\ConverterContextTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\enums\CustomApiModeTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\enums\SqlExecutionModeTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\converter\FieldFetchContextTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\JsonUtilsTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\converter\FieldAwareConverterTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\dto\KeyValuePairTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\model\LoginUserTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\KeyValuePairUtilsTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\enums\DataPersistModeTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\model\AccessOperationTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\StreamUtilsTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\CopyUtilsTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\config\JsonObjectConfigTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\converter\BidirectionalConverterTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\model\Unity2ComponentTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\model\SerializationTestPojo.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\util\ValidationUtilsTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-core\src\test\java\com\poc\hss\fasttrack\constant\ReservedTableNameTest.java
