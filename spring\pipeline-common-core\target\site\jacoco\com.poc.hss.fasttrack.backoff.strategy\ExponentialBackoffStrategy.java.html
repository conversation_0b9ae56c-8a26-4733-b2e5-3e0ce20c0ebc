<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ExponentialBackoffStrategy.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.backoff.strategy</a> &gt; <span class="el_source">ExponentialBackoffStrategy.java</span></div><h1>ExponentialBackoffStrategy.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.backoff.strategy;

import org.springframework.util.backoff.BackOff;
import org.springframework.util.backoff.ExponentialBackOff;

import java.time.Duration;

<span class="nc" id="L8">public class ExponentialBackoffStrategy implements BackoffStrategy {</span>

<span class="nc" id="L10">    private static BackoffStrategy instance = null;</span>

    public static BackoffStrategy getInstance() {
<span class="nc bnc" id="L13" title="All 2 branches missed.">        if (instance == null)</span>
<span class="nc" id="L14">            instance = new ExponentialBackoffStrategy();</span>
<span class="nc" id="L15">        return instance;</span>
    }

    @Override
    public BackOff getBackoff(Duration timeout) {
<span class="nc" id="L20">        ExponentialBackOff exponentialBackOff = new ExponentialBackOff();</span>
<span class="nc" id="L21">        exponentialBackOff.setMaxElapsedTime(timeout.toMillis());</span>
<span class="nc" id="L22">        return exponentialBackOff;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>