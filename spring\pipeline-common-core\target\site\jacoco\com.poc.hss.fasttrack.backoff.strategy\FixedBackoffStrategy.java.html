<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FixedBackoffStrategy.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.backoff.strategy</a> &gt; <span class="el_source">FixedBackoffStrategy.java</span></div><h1>FixedBackoffStrategy.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.backoff.strategy;

import org.springframework.util.backoff.BackOff;
import org.springframework.util.backoff.FixedBackOff;

import java.time.Duration;

public class FixedBackoffStrategy implements BackoffStrategy {

<span class="nc" id="L10">    private static BackoffStrategy instance = null;</span>
    private final Duration interval;

    public static BackoffStrategy getInstance() {
<span class="nc bnc" id="L14" title="All 2 branches missed.">        if (instance == null)</span>
<span class="nc" id="L15">            instance = new FixedBackoffStrategy(Duration.ofMillis(FixedBackOff.DEFAULT_INTERVAL));</span>
<span class="nc" id="L16">        return instance;</span>
    }

    public static BackoffStrategy ofInterval(Duration interval) {
<span class="nc" id="L20">        return new FixedBackoffStrategy(interval);</span>
    }

<span class="nc" id="L23">    private FixedBackoffStrategy(Duration interval) {</span>
<span class="nc" id="L24">        this.interval = interval;</span>
<span class="nc" id="L25">    }</span>

    @Override
    public BackOff getBackoff(Duration timeout) {
<span class="nc" id="L29">        FixedBackOff fixedBackOff = new FixedBackOff();</span>
<span class="nc" id="L30">        fixedBackOff.setInterval(this.interval.toMillis());</span>
<span class="nc" id="L31">        fixedBackOff.setMaxAttempts(timeout.toMillis() / fixedBackOff.getInterval());</span>
<span class="nc" id="L32">        return fixedBackOff;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>