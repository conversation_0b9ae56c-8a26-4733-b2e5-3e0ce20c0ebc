<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BackoffTask.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.backoff</a> &gt; <span class="el_source">BackoffTask.java</span></div><h1>BackoffTask.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.backoff;

import com.poc.hss.fasttrack.backoff.criteria.BackoffTerminationCriteria;
import com.poc.hss.fasttrack.backoff.strategy.BackoffStrategy;
import org.springframework.util.backoff.BackOff;
import org.springframework.util.backoff.BackOffExecution;

import java.time.Duration;
import java.util.function.Supplier;

public class BackoffTask&lt;T&gt; {

    private final BackoffStrategy strategy;
    private final BackoffTerminationCriteria&lt;T&gt; terminationCriteria;
    private final Duration defaultTimeout;

    public BackoffTask(BackoffStrategy strategy, BackoffTerminationCriteria&lt;T&gt; terminationCriteria) {
<span class="nc" id="L18">        this(strategy, terminationCriteria, Duration.ofSeconds(30));</span>
<span class="nc" id="L19">    }</span>

<span class="nc" id="L21">    public BackoffTask(BackoffStrategy strategy, BackoffTerminationCriteria&lt;T&gt; terminationCriteria, Duration defaultTimeout) {</span>
<span class="nc" id="L22">        this.strategy = strategy;</span>
<span class="nc" id="L23">        this.terminationCriteria = terminationCriteria;</span>
<span class="nc" id="L24">        this.defaultTimeout = defaultTimeout;</span>
<span class="nc" id="L25">    }</span>

    public final void get() throws BackoffException {
<span class="nc" id="L28">        get(() -&gt; null);</span>
<span class="nc" id="L29">    }</span>

    public final T get(Supplier&lt;T&gt; task) throws BackoffException {
<span class="nc" id="L32">        return get(task, defaultTimeout);</span>
    }

    public final T get(Supplier&lt;T&gt; task, Duration timeout) throws BackoffException {
<span class="nc" id="L36">        BackOff backOff = this.strategy.getBackoff(timeout);</span>

<span class="nc" id="L38">        BackOffExecution backOffExecution = backOff.start();</span>

        do {
<span class="nc" id="L41">            T result = task.get();</span>
<span class="nc bnc" id="L42" title="All 2 branches missed.">            if (this.terminationCriteria.shouldTerminate(result)) {</span>
<span class="nc" id="L43">                return result;</span>
            } else {
                try {
<span class="nc" id="L46">                    long backOffInterval = backOffExecution.nextBackOff();</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">                    if (backOffInterval == -1)</span>
<span class="nc" id="L48">                        throw new BackoffException(&quot;Backoff timeout has reached.&quot;);</span>
<span class="nc" id="L49">                    Thread.sleep(backOffInterval);</span>
<span class="nc" id="L50">                } catch (InterruptedException e) {</span>
<span class="nc" id="L51">                    throw new BackoffException(e);</span>
<span class="nc" id="L52">                }</span>
            }
<span class="nc" id="L54">        } while (true);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>