<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReservedTableName.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.constant</a> &gt; <span class="el_source">ReservedTableName.java</span></div><h1>ReservedTableName.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.constant;

import java.util.ArrayList;
import java.util.List;

<span class="nc" id="L6">public class ReservedTableName {</span>
    
    // Unity 2 Postgres reserved table names
    private static final String CACHE = &quot;cache&quot;;
    private static final String KAFKA_OFFSET = &quot;kafka_offset&quot;;
    private static final String BATCH = &quot;batch&quot;;
    private static final String FLYWAY_SCHEMA_HISTORY = &quot;flyway_schema_history&quot;;

<span class="fc" id="L14">    private static final List&lt;String&gt; NAMES = new ArrayList&lt;&gt;();</span>

    static {
<span class="fc" id="L17">        NAMES.add(CACHE);</span>
<span class="fc" id="L18">        NAMES.add(FLYWAY_SCHEMA_HISTORY);</span>
<span class="fc" id="L19">        NAMES.add(KAFKA_OFFSET);</span>
<span class="fc" id="L20">        NAMES.add(BATCH);</span>
<span class="fc" id="L21">    }</span>

    public static List&lt;String&gt; getNames() {
<span class="fc" id="L24">        return NAMES;</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>