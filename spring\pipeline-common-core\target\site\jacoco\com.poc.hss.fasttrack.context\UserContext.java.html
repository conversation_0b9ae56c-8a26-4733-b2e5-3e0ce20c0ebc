<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UserContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.context</a> &gt; <span class="el_source">UserContext.java</span></div><h1>UserContext.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.context;

import com.poc.hss.fasttrack.model.LoginUser;

<span class="nc" id="L5">public class UserContext {</span>
<span class="nc" id="L6">    private final static ThreadLocal&lt;LoginUser&gt; currentUser = new ThreadLocal&lt;&gt;();</span>
    public static LoginUser getCurrentUser() {
<span class="nc" id="L8">        return currentUser.get();</span>
    }

    public static void setCurrentUser(LoginUser user) {
<span class="nc" id="L12">        currentUser.set(user);</span>
<span class="nc" id="L13">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>