<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BidirectionalConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.converter</a> &gt; <span class="el_source">BidirectionalConverter.java</span></div><h1>BidirectionalConverter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.converter;

import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.converter.GenericConverter;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashSet;
import java.util.Set;

public abstract class BidirectionalConverter&lt;S, T&gt; implements GenericConverter {

    private final Class&lt;S&gt; S;
    private final Class&lt;T&gt; T;

<span class="fc" id="L16">    protected BidirectionalConverter() {</span>
<span class="fc" id="L17">        Type[] typeParameters = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments();</span>
<span class="fc" id="L18">        this.S = (Class) typeParameters[0];</span>
<span class="fc" id="L19">        this.T = (Class) typeParameters[1];</span>
<span class="fc" id="L20">    }</span>


    @Override
    public Set&lt;ConvertiblePair&gt; getConvertibleTypes() {
<span class="fc" id="L25">        Set&lt;ConvertiblePair&gt; convertiblePairs = new HashSet&lt;&gt;();</span>
<span class="fc" id="L26">        convertiblePairs.add(new ConvertiblePair(S, T));</span>
<span class="fc" id="L27">        convertiblePairs.add(new ConvertiblePair(T, S));</span>
<span class="fc" id="L28">        return convertiblePairs;</span>
    }

    @Override
    public Object convert(Object source, TypeDescriptor sourceType, TypeDescriptor targetType) {
<span class="fc bfc" id="L33" title="All 2 branches covered.">        if (S.equals(sourceType.getType())) {</span>
<span class="fc" id="L34">            return this.convert((S) source);</span>
        } else {
<span class="fc" id="L36">            return this.convertBack((T) source);</span>
        }
    }

    protected abstract S convertBack(T source);

    protected abstract T convert(S source);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>