<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldAwareConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.converter</a> &gt; <span class="el_source">FieldAwareConverter.java</span></div><h1>FieldAwareConverter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.converter;

import org.springframework.core.ResolvableType;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.core.convert.converter.ConditionalGenericConverter;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.HashSet;
import java.util.Set;

public abstract class FieldAwareConverter&lt;S, T&gt; implements ConditionalGenericConverter {

    private final Class&lt;S&gt; S;
    private final Class&lt;T&gt; T;

<span class="fc" id="L17">    protected FieldAwareConverter() {</span>
<span class="fc" id="L18">        Type[] typeParameters = ((ParameterizedType) this.getClass().getGenericSuperclass()).getActualTypeArguments();</span>
<span class="fc" id="L19">        this.S = (Class) typeParameters[0];</span>
<span class="fc" id="L20">        this.T = (Class) typeParameters[1];</span>
<span class="fc" id="L21">    }</span>

    protected boolean acceptSuperClass() {
<span class="fc" id="L24">        return false;</span>
    }

    private boolean shouldAcceptSourceType(ResolvableType sourceType) {
<span class="fc" id="L28">        Class&lt;?&gt; rawClass = sourceType.getRawClass();</span>
<span class="fc bfc" id="L29" title="All 2 branches covered.">        if (rawClass == ConverterContext.class)</span>
<span class="fc" id="L30">            return shouldAcceptSourceType(sourceType.getGenerics()[0]);</span>
<span class="pc bpc" id="L31" title="1 of 4 branches missed.">        return acceptSuperClass() ? S.isAssignableFrom(rawClass) : rawClass == S;</span>
    }

    private boolean shouldAcceptTargetType(ResolvableType targetType) {
<span class="pc bpc" id="L35" title="1 of 2 branches missed.">        return targetType.getRawClass() == T;</span>
    }

    @Override
    public boolean matches(TypeDescriptor sourceType, TypeDescriptor targetType) {
<span class="pc bpc" id="L40" title="1 of 4 branches missed.">        return shouldAcceptSourceType(sourceType.getResolvableType()) &amp;&amp; shouldAcceptTargetType(targetType.getResolvableType());</span>
    }

    @Override
    public Set&lt;ConvertiblePair&gt; getConvertibleTypes() {
<span class="fc" id="L45">        HashSet&lt;ConvertiblePair&gt; convertiblePairs = new HashSet&lt;&gt;();</span>
<span class="fc" id="L46">        convertiblePairs.add(new ConvertiblePair(S, T));</span>
<span class="fc" id="L47">        convertiblePairs.add(new ConvertiblePair(ConverterContext.class, T));</span>
<span class="fc" id="L48">        return convertiblePairs;</span>
    }

    @Override
    public Object convert(Object source, TypeDescriptor sourceType, TypeDescriptor targetType) {
<span class="fc bfc" id="L53" title="All 2 branches covered.">        if (sourceType.getResolvableType().getRawClass() == ConverterContext.class) {</span>
<span class="fc" id="L54">            ConverterContext&lt;S&gt; context = (ConverterContext) source;</span>
<span class="fc" id="L55">            return this.convert(context.getObj(), context.getContext());</span>
        } else {
<span class="fc" id="L57">            return this.convert((S) source, FieldFetchContext.builder().fetchAll(true).build());</span>
        }
    }

    protected abstract T convert(S source, FieldFetchContext context);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>