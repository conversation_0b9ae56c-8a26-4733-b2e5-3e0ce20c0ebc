<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldFetchContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.converter</a> &gt; <span class="el_source">FieldFetchContext.java</span></div><h1>FieldFetchContext.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.converter;

import lombok.Builder;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

@Getter
<span class="pc bpc" id="L11" title="1 of 2 branches missed.">@Builder</span>
public class FieldFetchContext {
<span class="fc" id="L13">    private final boolean fetchAll;</span>
<span class="fc" id="L14">    private String name;</span>

    @Builder.Default
<span class="fc" id="L17">    private final List&lt;FieldFetchContext&gt; fields = new ArrayList&lt;&gt;();</span>

    public FieldFetchContext addField(Consumer&lt;FieldFetchContext&gt; configuration) {
<span class="fc" id="L20">        FieldFetchContext context = FieldFetchContext.builder()</span>
<span class="fc" id="L21">                .build();</span>
<span class="fc" id="L22">        configuration.accept(context);</span>
<span class="fc" id="L23">        fields.add(context);</span>
<span class="fc" id="L24">        return this;</span>
    }

    public FieldFetchContext addField(String name) {
<span class="fc" id="L28">        return addField(FieldFetchContext.builder().name(name).build());</span>
    }

    public FieldFetchContext addField(FieldFetchContext context) {
<span class="fc" id="L32">        fields.add(context);</span>
<span class="fc" id="L33">        return this;</span>
    }

    public final void ifPresent(String name, Consumer&lt;FieldFetchContext&gt; consumer) {
<span class="fc bfc" id="L37" title="All 2 branches covered.">        if (fetchAll)</span>
<span class="fc" id="L38">            consumer.accept(FieldFetchContext.builder().fetchAll(true).build());</span>
        else
<span class="fc" id="L40">            getFields().stream().filter(field -&gt; name.equals(field.getName())).findFirst().ifPresent(consumer);</span>
<span class="fc" id="L41">    }</span>

    public final void ifPresent(String name, Runnable runnable) {
<span class="fc" id="L44">        ifPresent(name, f -&gt; runnable.run());</span>
<span class="fc" id="L45">    }</span>

    public final boolean isPresent(String name) {
<span class="nc bnc" id="L48" title="All 4 branches missed.">        return fetchAll || getFields().stream().anyMatch(field -&gt; name.equals(field.getName()));</span>
    }

    public FieldFetchContext rename(String name) {
<span class="fc" id="L52">        this.name = name;</span>
<span class="fc" id="L53">        return this;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>