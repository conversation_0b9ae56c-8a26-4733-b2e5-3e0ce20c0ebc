<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">159 of 292</td><td class="ctr2">45%</td><td class="bar">27 of 38</td><td class="ctr2">28%</td><td class="ctr1">31</td><td class="ctr2">48</td><td class="ctr1">4</td><td class="ctr2">10</td><td class="ctr1">14</td><td class="ctr2">29</td><td class="ctr1">2</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a2"><a href="PageResultDTO.html" class="el_class">PageResultDTO</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="107" height="10" title="115" alt="115"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="87" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g1">17</td><td class="ctr1" id="h0">4</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j0">9</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a3"><a href="PageResultDTO$PageResultDTOBuilder.html" class="el_class">PageResultDTO.PageResultDTOBuilder</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="22" alt="22"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j1">4</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="KeyValuePair.html" class="el_class">KeyValuePair</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="16" alt="16"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="112" alt="112"/></td><td class="ctr2" id="c0">87%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="11" alt="11"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g0">22</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k0">11</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="KeyValuePair$KeyValuePairBuilder.html" class="el_class">KeyValuePair.KeyValuePairBuilder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="21" alt="21"/></td><td class="ctr2" id="c1">77%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">5</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>