<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AccessOperator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.enums</a> &gt; <span class="el_source">AccessOperator.java</span></div><h1>AccessOperator.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/***
 * first %s is column name
 * second %s is key, and will be replaced by parameter
 */
<span class="fc" id="L10">public enum AccessOperator {</span>
<span class="fc" id="L11">    INCREMENT(&quot;INCREMENT&quot;, &quot;%s + :%s&quot;),</span>
<span class="fc" id="L12">    NOT_EQUAL(&quot;NOT_EQUAL&quot;, &quot;%s &lt;&gt; :%s&quot;),</span>
<span class="fc" id="L13">    NONE(&quot;NONE&quot;, &quot;%s&quot;)</span>
    ;

    private final String name;
    private final String definition;

<span class="fc" id="L19">    AccessOperator(String name, String definition) {</span>
<span class="fc" id="L20">        this.name = name;</span>
<span class="fc" id="L21">        this.definition = definition;</span>
<span class="fc" id="L22">    }</span>

    @Override
    @JsonValue
    public String toString() {
<span class="fc" id="L27">        return String.valueOf(name);</span>
    }

    public String getDefinition(){
<span class="nc" id="L31">        return definition;</span>
    }

    @JsonCreator
    public static AccessOperator fromValue(String text) {
<span class="nc bnc" id="L36" title="All 2 branches missed.">        for (AccessOperator b : AccessOperator.values()) {</span>
<span class="nc bnc" id="L37" title="All 2 branches missed.">            if (String.valueOf(b.name).equals(text)) {</span>
<span class="nc" id="L38">                return b;</span>
            }
        }
<span class="nc" id="L41">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>