<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JsonObjectModule.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.json.module</a> &gt; <span class="el_source">JsonObjectModule.java</span></div><h1>JsonObjectModule.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.json.module;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.IOException;

public class JsonObjectModule extends SimpleModule {
    public JsonObjectModule() {
<span class="fc" id="L18">        super();</span>
<span class="fc" id="L19">        this.addSerializer(JsonObject.class, new JsonObjectSerializer());</span>
<span class="fc" id="L20">        this.addDeserializer(JsonObject.class, new JsonObjectDeserializer());</span>
<span class="fc" id="L21">        this.addSerializer(JsonArray.class, new JsonArraySerializer());</span>
<span class="fc" id="L22">        this.addDeserializer(JsonArray.class, new JsonArrayDeserializer());</span>
<span class="fc" id="L23">    }</span>

<span class="fc" id="L25">    public static class JsonObjectSerializer extends JsonSerializer&lt;JsonObject&gt; {</span>
        @Override
        public void serialize(JsonObject jsonObject, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
<span class="nc" id="L28">            jsonGenerator.writeRawValue(jsonObject.encode());</span>
<span class="nc" id="L29">        }</span>
    }

<span class="fc" id="L32">    public static class JsonObjectDeserializer extends JsonDeserializer&lt;JsonObject&gt; {</span>

        @Override
        public JsonObject deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
<span class="fc" id="L36">            TreeNode treeNode = jsonParser.readValueAsTree();</span>
<span class="fc" id="L37">            return new JsonObject(treeNode.toString());</span>
        }
    }


<span class="fc" id="L42">    public static class JsonArraySerializer extends JsonSerializer&lt;JsonArray&gt; {</span>
        @Override
        public void serialize(JsonArray jsonArray, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
<span class="nc" id="L45">            jsonGenerator.writeRawValue(jsonArray.encode());</span>
<span class="nc" id="L46">        }</span>
    }

<span class="fc" id="L49">    public static class JsonArrayDeserializer extends JsonDeserializer&lt;JsonArray&gt; {</span>

        @Override
        public JsonArray deserialize(JsonParser jsonParser, DeserializationContext deserializationContext) throws IOException {
<span class="fc" id="L53">            TreeNode treeNode = jsonParser.readValueAsTree();</span>
<span class="fc" id="L54">            return new JsonArray(treeNode.toString());</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>