<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AutoBeanDisposalListener.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.listener</a> &gt; <span class="el_source">AutoBeanDisposalListener.java</span></div><h1>AutoBeanDisposalListener.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.stereotype.Component;

import java.util.*;

<span class="nc" id="L16">@Slf4j</span>
@Component
@ConditionalOnProperty(prefix = &quot;unity2.auto-configuration&quot;, value = &quot;auto-bean-disposal&quot;, havingValue = &quot;true&quot;)
<span class="nc" id="L19">public class AutoBeanDisposalListener implements BeanFactoryAware, ApplicationListener&lt;ContextClosedEvent&gt;, BeanPostProcessor {</span>

    private BeanFactory beanFactory;
<span class="nc" id="L22">    private final List&lt;DisposableBean&gt; beans = Collections.synchronizedList(new ArrayList&lt;&gt;());</span>

    @Override
    public void onApplicationEvent(ContextClosedEvent event) {
<span class="nc" id="L26">        beans.forEach(bean -&gt; {</span>
            try {
<span class="nc" id="L28">                log.debug(&quot;Destroying bean {}&quot;, bean.getClass());</span>
<span class="nc" id="L29">                bean.destroy();</span>
<span class="nc" id="L30">            } catch (Exception e) {</span>
<span class="nc" id="L31">                log.error(&quot;Error when destroying bean {}&quot;, bean.getClass(), e);</span>
<span class="nc" id="L32">            }</span>
<span class="nc" id="L33">        });</span>
<span class="nc" id="L34">    }</span>

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
<span class="nc bnc" id="L38" title="All 4 branches missed.">        if (beanFactory.containsBean(beanName) &amp;&amp; bean instanceof DisposableBean) {</span>
<span class="nc" id="L39">            beans.add((DisposableBean) bean);</span>
        }

<span class="nc" id="L42">        return bean;</span>
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
<span class="nc" id="L47">        this.beanFactory = beanFactory;</span>
<span class="nc" id="L48">    }</span>
}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>