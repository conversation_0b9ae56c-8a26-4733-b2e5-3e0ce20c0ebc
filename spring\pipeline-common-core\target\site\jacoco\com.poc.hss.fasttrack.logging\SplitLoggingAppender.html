<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SplitLoggingAppender</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.logging</a> &gt; <span class="el_class">SplitLoggingAppender</span></div><h1>SplitLoggingAppender</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">87 of 106</td><td class="ctr2">17%</td><td class="bar">3 of 4</td><td class="ctr2">25%</td><td class="ctr1">6</td><td class="ctr2">9</td><td class="ctr1">19</td><td class="ctr2">25</td><td class="ctr1">4</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a1"><a href="SplitLoggingAppender.java.html#L31" class="el_method">cloneLoggingEvent(LoggingEvent, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="52" alt="52"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">2</td><td class="ctr2" id="g0">2</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="SplitLoggingAppender.java.html#L15" class="el_method">append(LoggingEvent)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="12" alt="12"/></td><td class="ctr2" id="c2">41%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="60" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">5</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="SplitLoggingAppender.java.html#L21" class="el_method">lambda$append$0(LoggingEvent, List, int)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="8" alt="8"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="SplitLoggingAppender.java.html#L27" class="el_method">split(String, int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="6" alt="6"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="SplitLoggingAppender.java.html#L22" class="el_method">lambda$append$1(Object)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="SplitLoggingAppender.java.html#L11" class="el_method">static {...}</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a5"><a href="SplitLoggingAppender.java.html#L10" class="el_method">SplitLoggingAppender()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>