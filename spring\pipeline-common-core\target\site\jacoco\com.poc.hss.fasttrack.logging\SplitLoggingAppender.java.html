<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SplitLoggingAppender.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.logging</a> &gt; <span class="el_source">SplitLoggingAppender.java</span></div><h1>SplitLoggingAppender.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.logging;

import ch.qos.logback.classic.spi.LoggingEvent;
import ch.qos.logback.core.ConsoleAppender;

import java.util.Arrays;
import java.util.List;
import java.util.stream.IntStream;

<span class="fc" id="L10">public class SplitLoggingAppender extends ConsoleAppender&lt;LoggingEvent&gt; {</span>
<span class="fc" id="L11">    private static final Integer MAX_LENGTH = 10240;</span>

    @Override
    protected void append(LoggingEvent eventObject) {
<span class="fc" id="L15">        final String formattedMessage = eventObject.getFormattedMessage();</span>
<span class="fc" id="L16">        super.append(eventObject);</span>
<span class="pc bpc" id="L17" title="1 of 2 branches missed.">        if (formattedMessage.length() &gt; MAX_LENGTH) {</span>
<span class="nc" id="L18">            List&lt;String&gt; splitMessages = split(formattedMessage, MAX_LENGTH);</span>
<span class="nc" id="L19">            IntStream</span>
<span class="nc" id="L20">                    .range(1, splitMessages.size())</span>
<span class="nc" id="L21">                    .mapToObj(i -&gt; cloneLoggingEvent(eventObject, splitMessages.get(i)))</span>
<span class="nc" id="L22">                    .forEach(super::append);</span>
        }
<span class="fc" id="L24">    }</span>

    private static List&lt;String&gt; split(String input, int maxLength) {
<span class="nc" id="L27">        return Arrays.asList(input.split(&quot;(?&lt;=\\G.{&quot; + maxLength + &quot;})&quot;));</span>
    }

    private LoggingEvent cloneLoggingEvent(LoggingEvent sourceEvent, String msg) {
<span class="nc" id="L31">        final LoggingEvent clonedEvent = new LoggingEvent();</span>
<span class="nc" id="L32">        clonedEvent.setArgumentArray(sourceEvent.getArgumentArray());</span>
<span class="nc" id="L33">        clonedEvent.setLevel(sourceEvent.getLevel());</span>
<span class="nc" id="L34">        clonedEvent.setLoggerName(sourceEvent.getLoggerName());</span>
<span class="nc" id="L35">        clonedEvent.setThreadName(sourceEvent.getThreadName());</span>
<span class="nc" id="L36">        clonedEvent.setTimeStamp(sourceEvent.getTimeStamp());</span>
<span class="nc" id="L37">        clonedEvent.setCallerData(sourceEvent.getCallerData());</span>
<span class="nc bnc" id="L38" title="All 2 branches missed.">        if (sourceEvent.getMarkerList() != null)</span>
<span class="nc" id="L39">            sourceEvent.getMarkerList().forEach(clonedEvent::addMarker);</span>
<span class="nc" id="L40">        clonedEvent.setMDCPropertyMap(sourceEvent.getMDCPropertyMap());</span>
<span class="nc" id="L41">        clonedEvent.setLoggerContextRemoteView(sourceEvent.getLoggerContextVO());</span>

<span class="nc" id="L43">        clonedEvent.setMessage(msg);</span>
<span class="nc" id="L44">        return clonedEvent;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>