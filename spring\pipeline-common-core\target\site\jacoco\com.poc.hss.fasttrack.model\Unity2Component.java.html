<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2Component.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">Unity2Component.java</span></div><h1>Unity2Component.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

<span class="fc" id="L6">public enum Unity2Component {</span>
<span class="fc" id="L7">    SOURCE_ADAPTOR(&quot;SOURCE_ADAPTOR&quot;),</span>
<span class="fc" id="L8">    TRANSFORM(&quot;TRANSFORM&quot;),</span>
<span class="fc" id="L9">    ACCESS(&quot;ACCESS&quot;),</span>
<span class="fc" id="L10">    CONSUMER_ADAPTOR(&quot;CONSUMER_ADAPTOR&quot;),</span>
<span class="fc" id="L11">    BLEND(&quot;BLEND&quot;),</span>
<span class="fc" id="L12">    FUSION(&quot;FUSION&quot;);</span>

    private final String value;

<span class="fc" id="L16">    Unity2Component(String value) {</span>
<span class="fc" id="L17">        this.value = value;</span>
<span class="fc" id="L18">    }</span>

    @Override
    @JsonValue
    public String toString() {
<span class="fc" id="L23">        return String.valueOf(value);</span>
    }

    @JsonCreator
    public static Unity2Component fromValue(String text) {
<span class="fc bfc" id="L28" title="All 2 branches covered.">        for (Unity2Component b : Unity2Component.values()) {</span>
<span class="fc bfc" id="L29" title="All 2 branches covered.">            if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L30">                return b;</span>
            }
        }
<span class="fc" id="L33">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>