<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <span class="el_package">com.poc.hss.fasttrack.model</span></div><h1>com.poc.hss.fasttrack.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">50 of 397</td><td class="ctr2">87%</td><td class="bar">24 of 48</td><td class="ctr2">50%</td><td class="ctr1">22</td><td class="ctr2">60</td><td class="ctr1">0</td><td class="ctr2">27</td><td class="ctr1">2</td><td class="ctr2">36</td><td class="ctr1">0</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a0"><a href="AccessOperation.java.html" class="el_source">AccessOperation.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="26" alt="26"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="133" alt="133"/></td><td class="ctr2" id="c2">83%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="54" height="10" title="10" alt="10"/></td><td class="ctr2" id="e1">45%</td><td class="ctr1" id="f0">11</td><td class="ctr2" id="g0">27</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i1">6</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">16</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a1"><a href="LoginUser.java.html" class="el_source">LoginUser.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="24" alt="24"/><img src="../jacoco-resources/greenbar.gif" width="98" height="10" title="131" alt="131"/></td><td class="ctr2" id="c1">84%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="65" height="10" title="12" alt="12"/><img src="../jacoco-resources/greenbar.gif" width="54" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">45%</td><td class="ctr1" id="f1">11</td><td class="ctr2" id="g1">27</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">16</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a2"><a href="Unity2Component.java.html" class="el_source">Unity2Component.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="62" height="10" title="83" alt="83"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>