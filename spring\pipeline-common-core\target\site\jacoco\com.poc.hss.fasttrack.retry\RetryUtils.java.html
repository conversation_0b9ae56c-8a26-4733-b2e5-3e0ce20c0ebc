<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RetryUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.retry</a> &gt; <span class="el_source">RetryUtils.java</span></div><h1>RetryUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.retry;

import com.poc.hss.fasttrack.backoff.BackoffException;
import com.poc.hss.fasttrack.backoff.BackoffTask;
import com.poc.hss.fasttrack.backoff.BackoffTaskFactory;
import com.poc.hss.fasttrack.backoff.strategy.ExponentialBackoffStrategy;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.function.Supplier;

<span class="nc" id="L14">@Slf4j</span>
<span class="nc" id="L15">public class RetryUtils {</span>

    private static &lt;T&gt; ExecutionContext&lt;T&gt; executeTask(Supplier&lt;T&gt; task) {
        try {
<span class="nc" id="L19">            T data = task.get();</span>
<span class="nc" id="L20">            return ExecutionContext.&lt;T&gt;builder()</span>
<span class="nc" id="L21">                    .data(data)</span>
<span class="nc" id="L22">                    .build();</span>
<span class="nc" id="L23">        } catch (Exception e) {</span>
<span class="nc" id="L24">            log.warn(&quot;Exception in retryable execution: &quot;, e);</span>
<span class="nc" id="L25">            return ExecutionContext.&lt;T&gt;builder()</span>
<span class="nc" id="L26">                    .exception(e)</span>
<span class="nc" id="L27">                    .build();</span>
        }
    }

    public static &lt;T&gt; T run(Supplier&lt;T&gt; task, Duration duration) throws BackoffException {
<span class="nc bnc" id="L32" title="All 2 branches missed.">        BackoffTask&lt;ExecutionContext&lt;T&gt;&gt; backoff = BackoffTaskFactory.create(ExponentialBackoffStrategy.getInstance(), e -&gt; e.exception == null);</span>
<span class="nc" id="L33">        ExecutionContext&lt;T&gt; result = backoff.get(</span>
<span class="nc" id="L34">                () -&gt; executeTask(task),</span>
                duration
        );
<span class="nc" id="L37">        return result.data;</span>
    }

<span class="nc bnc" id="L40" title="All 22 branches missed.">    @Data</span>
<span class="nc" id="L41">    @Builder</span>
    private static class ExecutionContext&lt;T&gt; {
<span class="nc" id="L43">        private T data;</span>
<span class="nc" id="L44">        private Exception exception;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>