<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConcurrentStep.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.step</a> &gt; <span class="el_source">ConcurrentStep.java</span></div><h1>ConcurrentStep.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.step;

import com.poc.hss.fasttrack.context.UserContext;
import com.poc.hss.fasttrack.model.LoginUser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

class ConcurrentStep&lt;T&gt; implements Step&lt;T&gt; {

<span class="nc" id="L16">    private static final Logger logger = LoggerFactory.getLogger(ConcurrentStep.class);</span>
    private final List&lt;? extends Step&lt;T&gt;&gt; steps;
    private final ThreadPoolTaskExecutor threadPoolTaskExecutor;

<span class="nc" id="L20">    public ConcurrentStep(ThreadPoolTaskExecutor threadPoolTaskExecutor, List&lt;? extends Step&lt;T&gt;&gt; steps) {</span>
<span class="nc" id="L21">        this.steps = steps;</span>
<span class="nc" id="L22">        this.threadPoolTaskExecutor = threadPoolTaskExecutor;</span>
<span class="nc" id="L23">    }</span>

    @Override
    public T run(T context) throws Exception {
<span class="nc" id="L27">        logger.info(&quot;Running steps concurrently: {}&quot;, steps.stream().map(s -&gt; s.getClass().getName()).collect(Collectors.joining(&quot;, &quot;)));</span>
<span class="nc bnc" id="L28" title="All 2 branches missed.">        final LoginUser currentUser = context instanceof AuditInfoContext ? ((AuditInfoContext) context).getCurrentUser() : null;</span>
<span class="nc" id="L29">        List&lt;Future&lt;T&gt;&gt; results = steps.stream()</span>
<span class="nc" id="L30">                .map(step -&gt; this.threadPoolTaskExecutor.submit(() -&gt; {</span>
                    try {
<span class="nc" id="L32">                        UserContext.setCurrentUser(currentUser);</span>
<span class="nc bnc" id="L33" title="All 2 branches missed.">                        if (step.shouldRun(context))</span>
<span class="nc" id="L34">                            return step.run(context);</span>
                        else
<span class="nc" id="L36">                            return context;</span>
<span class="nc" id="L37">                    } catch (StepExecutionException e) {</span>
<span class="nc" id="L38">                        throw e;</span>
<span class="nc" id="L39">                    } catch (ExecutionException e) {</span>
<span class="nc bnc" id="L40" title="All 4 branches missed.">                        if (e.getCause() != null &amp;&amp; e.getCause() instanceof StepExecutionException) {</span>
<span class="nc" id="L41">                            throw (StepExecutionException) e.getCause();</span>
                        } else
<span class="nc" id="L43">                            throw e;</span>
<span class="nc" id="L44">                    } catch (Exception e) {</span>
<span class="nc" id="L45">                        throw new StepExecutionException(step, e);</span>
                    } finally {
<span class="nc" id="L47">                        UserContext.setCurrentUser(null);</span>
                    }
                }))
<span class="nc" id="L50">                .collect(Collectors.toList());</span>

<span class="nc" id="L52">        T resultContext = context;</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">        for (Future&lt;T&gt; result : results) {</span>
<span class="nc" id="L54">            resultContext = result.get();</span>
<span class="nc" id="L55">        }</span>

<span class="nc" id="L57">        return resultContext;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>