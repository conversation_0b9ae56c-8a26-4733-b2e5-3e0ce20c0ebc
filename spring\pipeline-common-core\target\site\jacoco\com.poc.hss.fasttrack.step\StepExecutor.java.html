<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StepExecutor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.step</a> &gt; <span class="el_source">StepExecutor.java</span></div><h1>StepExecutor.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.step;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.ExecutionException;

@Component
<span class="nc" id="L12">public class StepExecutor&lt;T&gt; {</span>

<span class="nc" id="L14">    public static final Logger logger = LoggerFactory.getLogger(StepExecutor.class);</span>

    @Autowired
    private ConcurrentStepFactory&lt;T&gt; concurrentStepFactory;

    public final T run(List&lt;? extends Step&lt;T&gt;&gt; steps, T context) throws Exception {
<span class="nc bnc" id="L20" title="All 2 branches missed.">        for (Step&lt;T&gt; step : steps) {</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">            if (step.shouldRun(context)) {</span>
<span class="nc" id="L22">                long start = System.currentTimeMillis();</span>
<span class="nc bnc" id="L23" title="All 2 branches missed.">                if (logger.isDebugEnabled())</span>
<span class="nc" id="L24">                    logger.debug(&quot;Running step: {}&quot;, step.getClass().getName());</span>
                try {
<span class="nc" id="L26">                    context = step.run(context);</span>
<span class="nc" id="L27">                } catch (StepExecutionException e) {</span>
<span class="nc" id="L28">                    throw e;</span>
<span class="nc" id="L29">                } catch (ExecutionException e) {</span>
<span class="nc bnc" id="L30" title="All 4 branches missed.">                    if (e.getCause() != null &amp;&amp; e.getCause() instanceof StepExecutionException) {</span>
<span class="nc" id="L31">                        throw (StepExecutionException) e.getCause();</span>
                    } else
<span class="nc" id="L33">                        throw e;</span>
<span class="nc" id="L34">                } catch (Exception e) {</span>
<span class="nc" id="L35">                    throw new StepExecutionException(step, e);</span>
<span class="nc" id="L36">                }</span>
<span class="nc bnc" id="L37" title="All 2 branches missed.">                if (logger.isDebugEnabled())</span>
<span class="nc" id="L38">                    logger.debug(&quot;step: {} takes {} ms&quot;, step.getClass().getName(), System.currentTimeMillis() - start);</span>
            }
<span class="nc" id="L40">        }</span>

<span class="nc" id="L42">        return context;</span>
    }

    public final T runAsync(List&lt;? extends Step&lt;T&gt;&gt; steps, T context) throws Exception {
<span class="nc" id="L46">        return concurrentStepFactory.getInstance(steps).run(context);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>