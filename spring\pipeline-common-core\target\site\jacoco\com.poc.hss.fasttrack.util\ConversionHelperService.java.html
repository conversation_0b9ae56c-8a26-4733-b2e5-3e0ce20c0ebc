<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ConversionHelperService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">ConversionHelperService.java</span></div><h1>ConversionHelperService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import com.poc.hss.fasttrack.converter.ConverterContext;
import com.poc.hss.fasttrack.converter.FieldFetchContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ResolvableType;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.convert.TypeDescriptor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
<span class="nc" id="L24">public class ConversionHelperService {</span>

<span class="nc" id="L26">    private static final Logger logger = LoggerFactory.getLogger(ConversionHelperService.class);</span>

    @Value(&quot;${spring.conversion.implicit:true}&quot;)
    private boolean allowImplicitConversion;

    @Autowired
    private ConversionService conversionService;

    public &lt;T&gt; Stream&lt;T&gt; convertStream(Stream&lt;?&gt; input, Class&lt;T&gt; to) {
<span class="nc" id="L35">        return convertStream(input, to, null);</span>
    }

    public &lt;T&gt; Stream&lt;T&gt; convertStream(Stream&lt;?&gt; input, Class&lt;T&gt; to, FieldFetchContext fieldFetchContext) {
<span class="nc bnc" id="L39" title="All 2 branches missed.">        if (input == null)</span>
<span class="nc" id="L40">            return null;</span>
<span class="nc" id="L41">        AtomicBoolean warn = new AtomicBoolean(true);</span>
<span class="nc" id="L42">        return input.map(x -&gt; convert(x, to, fieldFetchContext, warn.getAndSet(false)));</span>
    }

    public &lt;T&gt; List&lt;T&gt; convertList(List&lt;?&gt; input, Class&lt;T&gt; to) {
<span class="nc" id="L46">        return convertList(input, to, null);</span>
    }

    public &lt;T&gt; List&lt;T&gt; convertList(List&lt;?&gt; input, Class&lt;T&gt; to, FieldFetchContext fieldFetchContext) {
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (input == null)</span>
<span class="nc" id="L51">            return null;</span>
<span class="nc" id="L52">        return convertStream(input.stream(), to, fieldFetchContext).collect(Collectors.toList());</span>
    }

    public &lt;K, V, T&gt; Map&lt;K, T&gt; convertMapValue(Map&lt;K, V&gt; input, Class&lt;T&gt; to, FieldFetchContext fieldFetchContext) {
<span class="nc bnc" id="L56" title="All 2 branches missed.">        if (input == null)</span>
<span class="nc" id="L57">            return null;</span>
<span class="nc" id="L58">        AtomicBoolean warn = new AtomicBoolean(true);</span>
<span class="nc" id="L59">        return input.entrySet().stream()</span>
<span class="nc" id="L60">                .collect(</span>
<span class="nc" id="L61">                        Collectors.toMap(</span>
                                Map.Entry::getKey,
<span class="nc" id="L63">                                x -&gt; convert(x.getValue(), to, fieldFetchContext, warn.getAndSet(false))</span>
                        )
                );
    }

    public &lt;K, V, T&gt; Map&lt;K, T&gt; convertMapValue(Map&lt;K, V&gt; input, Class&lt;T&gt; to) {
<span class="nc" id="L69">        return convertMapValue(input, to, null);</span>
    }

    public &lt;T&gt; T convert(Object obj, Class&lt;T&gt; to) {
<span class="nc" id="L73">        return convert(obj, to, null, true);</span>
    }

    public &lt;T&gt; T convert(Object obj, Class&lt;T&gt; to, FieldFetchContext fieldFetchContext) {
<span class="nc" id="L77">        return convert(obj, to, fieldFetchContext, true);</span>
    }

    private &lt;T&gt; T convert(Object obj, Class&lt;T&gt; to, FieldFetchContext fieldFetchContext, boolean warn) {
<span class="nc bnc" id="L81" title="All 2 branches missed.">        if (obj == null)</span>
<span class="nc" id="L82">            return null;</span>

<span class="nc bnc" id="L84" title="All 2 branches missed.">        if (fieldFetchContext != null) {</span>
<span class="nc" id="L85">            ResolvableType resolvableType = ResolvableType.forClassWithGenerics(ConverterContext.class, obj.getClass());</span>
<span class="nc" id="L86">            TypeDescriptor typeDescriptor = new TypeDescriptor(resolvableType, resolvableType.getRawClass(), new Annotation[]{});</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">            if (conversionService.canConvert(typeDescriptor, TypeDescriptor.valueOf(to)))</span>
<span class="nc" id="L88">                return convert(ConverterContext.builder().obj(obj).context(fieldFetchContext).build(), typeDescriptor, TypeDescriptor.valueOf(to));</span>
        }

<span class="nc bnc" id="L91" title="All 2 branches missed.">        if (conversionService.canConvert(obj.getClass(), to))</span>
<span class="nc" id="L92">            return conversionService.convert(obj, to);</span>

<span class="nc bnc" id="L94" title="All 2 branches missed.">        if (!allowImplicitConversion) {</span>
<span class="nc" id="L95">            throw new UnsupportedOperationException(&quot;Conversion from &quot; + obj.getClass() + &quot; to &quot; + to + &quot; is not allowed&quot;);</span>
        }

<span class="nc bnc" id="L98" title="All 4 branches missed.">        if (warn &amp;&amp; logger.isDebugEnabled())</span>
<span class="nc" id="L99">            logger.warn(&quot;Implicit conversion from {} to {}&quot;, obj.getClass(), to);</span>

<span class="nc" id="L101">        return JsonUtils.deserialize(JsonUtils.serialize(obj), to);</span>
    }

    private &lt;T&gt; T convert(@Nullable Object source, @Nullable TypeDescriptor sourceType, TypeDescriptor targetType) {
<span class="nc" id="L105">        return (T) conversionService.convert(source, sourceType, targetType);</span>
    }

    public &lt;T&gt; T merge(Object source, T entity) {
<span class="nc" id="L109">        BeanUtils.copyProperties(source, entity);</span>
<span class="nc" id="L110">        return entity;</span>
    }

    public &lt;T&gt; T merge(Object source, T entity, String... ignoreProperties) {
<span class="nc" id="L114">        BeanUtils.copyProperties(source, entity, ignoreProperties);</span>
<span class="nc" id="L115">        return entity;</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>