<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CopyUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">CopyUtils.java</span></div><h1>CopyUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

public class CopyUtils {
    private CopyUtils() {
    }

    public static &lt;T&gt; T deepCopy(T obj) {
<span class="fc" id="L12">        Class&lt;T&gt; clazz = (Class&lt;T&gt;) obj.getClass();</span>
<span class="fc" id="L13">        return JsonUtils.deserialize(JsonUtils.serialize(obj), clazz);</span>
    }
    
    public static &lt;K,V&gt; Map&lt;K, V&gt; deepCopy(Map&lt;K, V&gt; map) {
<span class="fc" id="L17">        Map&lt;K,V&gt; clonedMap = new LinkedHashMap&lt;K, V&gt;();</span>
<span class="fc bfc" id="L18" title="All 2 branches covered.">        for (Entry&lt;K, V&gt; entry : map.entrySet()) {</span>
<span class="fc" id="L19">            clonedMap.put(entry.getKey(), entry.getValue());</span>
<span class="fc" id="L20">        }</span>
<span class="fc" id="L21">        return clonedMap;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>