<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>EsapiUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">EsapiUtils.java</span></div><h1>EsapiUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.codecs.OracleCodec;

<span class="nc" id="L6">public class EsapiUtils {</span>

<span class="nc" id="L8">    public static class PostgresCodec extends OracleCodec {</span>
        @Override
        public String encodeCharacter(char[] immune, Character c) {
<span class="nc" id="L11">            return &quot;&quot; + c;</span>
        }
    }

    public static String encode(String input) {
<span class="nc" id="L16">        return ESAPI.encoder().encodeForSQL(new PostgresCodec(), input);</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>