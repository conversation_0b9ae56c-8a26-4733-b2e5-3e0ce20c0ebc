<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GraphUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">GraphUtil.java</span></div><h1>GraphUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

<span class="nc" id="L14">public class GraphUtil {</span>
<span class="nc bnc" id="L15" title="All 14 branches missed.">    @Data</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">    @SuperBuilder</span>
<span class="nc" id="L17">    @NoArgsConstructor</span>
<span class="nc" id="L18">    @AllArgsConstructor</span>
    public static class Graph&lt;T&gt; {
        @Builder.Default
<span class="nc" id="L21">        private Map&lt;T, Node&lt;T&gt;&gt; nodeMap = new HashMap&lt;&gt;();</span>

        public void addEdge(T from, T to) {
<span class="nc bnc" id="L24" title="All 2 branches missed.">            if (!nodeMap.containsKey(from)) {</span>
<span class="nc" id="L25">                nodeMap.put(from, Node.&lt;T&gt;builder().value(from).build());</span>
            }
<span class="nc bnc" id="L27" title="All 2 branches missed.">            if (!nodeMap.containsKey(to)) {</span>
<span class="nc" id="L28">                nodeMap.put(to, Node.&lt;T&gt;builder().value(to).build());</span>
            }
<span class="nc" id="L30">            nodeMap.get(from).getNodes().add(nodeMap.get(to));</span>
<span class="nc" id="L31">        }</span>
    }

<span class="nc bnc" id="L34" title="All 30 branches missed.">    @Data</span>
<span class="nc bnc" id="L35" title="All 6 branches missed.">    @SuperBuilder</span>
<span class="nc" id="L36">    @NoArgsConstructor</span>
<span class="nc" id="L37">    @AllArgsConstructor</span>
    public static class Node&lt;T&gt; {
<span class="nc" id="L39">        private T value;</span>
        @Builder.Default
<span class="nc" id="L41">        private boolean isOriginal = false;</span>
        @Builder.Default
<span class="nc" id="L43">        private boolean checked = false;</span>
        @Builder.Default
<span class="nc" id="L45">        private Set&lt;Node&lt;T&gt;&gt; nodes = new HashSet&lt;&gt;();</span>
    }

    public static boolean hasCycle(Graph&lt;?&gt; graph) {
<span class="nc bnc" id="L49" title="All 2 branches missed.">        for (Node&lt;?&gt; node : graph.getNodeMap().values()) {</span>
<span class="nc bnc" id="L50" title="All 4 branches missed.">            if (!node.isChecked() &amp;&amp; hasCycle(node)) {</span>
<span class="nc" id="L51">                return true;</span>
            }
<span class="nc" id="L53">        }</span>
<span class="nc" id="L54">        return false;</span>
    }

    public static boolean hasCycle(Node&lt;?&gt; originalNode) {
<span class="nc" id="L58">        originalNode.setOriginal(true);</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">        for (Node&lt;?&gt; node : originalNode.getNodes()) {</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">            if (node.isOriginal()) {</span>
<span class="nc" id="L61">                return true;</span>
<span class="nc bnc" id="L62" title="All 4 branches missed.">            } else if (!node.isChecked() &amp;&amp; hasCycle(node)) {</span>
<span class="nc" id="L63">                return true;</span>
            }
<span class="nc" id="L65">        }</span>
<span class="nc" id="L66">        originalNode.setOriginal(false);</span>
<span class="nc" id="L67">        originalNode.setChecked(true);</span>
<span class="nc" id="L68">        return false;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>