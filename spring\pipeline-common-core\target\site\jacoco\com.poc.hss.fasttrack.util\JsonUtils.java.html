<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JsonUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">JsonUtils.java</span></div><h1>JsonUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.jaxb.JaxbAnnotationModule;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.poc.hss.fasttrack.json.module.JsonObjectModule;
import io.vertx.core.json.DecodeException;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.jackson.JsonComponentModule;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
<span class="nc" id="L27">public class JsonUtils {</span>

<span class="nc" id="L29">    @Getter</span>
<span class="fc" id="L30">    private static final ObjectMapper objectMapper = new ObjectMapper();</span>
<span class="fc" id="L31">    private static final Logger logger = LoggerFactory.getLogger(JsonUtils.class);</span>

    static {
<span class="fc" id="L34">        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);</span>
<span class="fc" id="L35">        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);</span>
<span class="fc" id="L36">        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);</span>

<span class="fc" id="L38">        objectMapper.registerModule(new JavaTimeModule());</span>
<span class="fc" id="L39">        objectMapper.registerModule(new JaxbAnnotationModule());</span>
<span class="fc" id="L40">        objectMapper.registerModule(new ParameterNamesModule());</span>
<span class="fc" id="L41">        objectMapper.registerModule(new Jdk8Module());</span>
<span class="fc" id="L42">        objectMapper.registerModule(new JsonComponentModule());</span>
<span class="fc" id="L43">        objectMapper.registerModule(new JsonObjectModule());</span>
<span class="fc" id="L44">    }</span>

    public static &lt;T&gt; T deserialize(String s, Class&lt;T&gt; to) {
<span class="fc bfc" id="L47" title="All 2 branches covered.">        if (s == null)</span>
<span class="fc" id="L48">            return null;</span>

        try {
<span class="fc" id="L51">            return objectMapper.readValue(s, to);</span>
<span class="fc" id="L52">        } catch (IOException e) {</span>
<span class="fc" id="L53">            logger.error(&quot;Unexpected deserialization error&quot;, e);</span>
<span class="fc" id="L54">            throw new RuntimeException(e);</span>
        }
    }

    public static &lt;T&gt; T deserialize(String s, TypeReference&lt;T&gt; to) {
<span class="pc bpc" id="L59" title="1 of 2 branches missed.">        if (s == null)</span>
<span class="nc" id="L60">            return null;</span>

        try {
<span class="fc" id="L63">            return objectMapper.readValue(s, to);</span>
<span class="nc" id="L64">        } catch (IOException e) {</span>
<span class="nc" id="L65">            logger.error(&quot;Unexpected deserialization error&quot;, e);</span>
<span class="nc" id="L66">            throw new RuntimeException(e);</span>
        }
    }

    public static String serialize(Object obj) {
<span class="fc bfc" id="L71" title="All 2 branches covered.">        if (obj == null)</span>
<span class="fc" id="L72">            return null;</span>

        try {
<span class="fc" id="L75">            return objectMapper.writeValueAsString(obj);</span>
<span class="nc" id="L76">        } catch (IOException e) {</span>
<span class="nc" id="L77">            logger.error(&quot;Unexpected serialization error&quot;, e);</span>
<span class="nc" id="L78">            throw new RuntimeException(e);</span>
        }
    }

    public static String beautifySerialize(Object obj) {
<span class="fc bfc" id="L83" title="All 2 branches covered.">        if (obj == null)</span>
<span class="fc" id="L84">            return null;</span>

        try {
<span class="fc" id="L87">            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(obj);</span>
<span class="nc" id="L88">        } catch (IOException e) {</span>
<span class="nc" id="L89">            logger.error(&quot;Unexpected serialization error&quot;, e);</span>
<span class="nc" id="L90">            throw new RuntimeException(e);</span>
        }
    }

    public static &lt;T&gt; T convert(Object s, Class&lt;T&gt; clazz) {
<span class="fc" id="L95">        return objectMapper.convertValue(s, clazz);</span>
    }

    public static &lt;T&gt; T convert(Object s, TypeReference&lt;T&gt; typeReference) {
<span class="fc" id="L99">        return objectMapper.convertValue(s, typeReference);</span>
    }

    public static boolean isJsonObject(String s) {
        try {
<span class="fc" id="L104">            new JsonObject(s);</span>
<span class="fc" id="L105">        } catch (DecodeException e) {</span>
<span class="fc" id="L106">            return false;</span>
<span class="fc" id="L107">        }</span>
<span class="fc" id="L108">        return true;</span>
    }

    public static boolean isJsonArray(String s) {
        try {
<span class="fc" id="L113">            new JsonArray(s);</span>
<span class="fc" id="L114">        } catch (DecodeException e) {</span>
<span class="fc" id="L115">            return false;</span>
<span class="fc" id="L116">        }</span>
<span class="fc" id="L117">        return true;</span>
    }

    public static JsonArray toJsonArray(Object s) {
<span class="fc bfc" id="L121" title="All 2 branches covered.">        if (s == null) return null;</span>

<span class="fc" id="L123">        String sourceString = JsonUtils.serialize(s);</span>
<span class="fc bfc" id="L124" title="All 2 branches covered.">        if (JsonUtils.isJsonObject(sourceString)) {</span>
<span class="fc" id="L125">            JsonArray resultJsonArray = new JsonArray();</span>
<span class="fc" id="L126">            resultJsonArray.add(JsonUtils.deserialize(sourceString, JsonObject.class));</span>

<span class="fc" id="L128">            return resultJsonArray;</span>
        }

<span class="fc" id="L131">        return JsonUtils.deserialize(sourceString, JsonArray.class);</span>
    }

    public static List&lt;Map&lt;String, Object&gt;&gt; toJsonArray(String s) {
<span class="fc bfc" id="L135" title="All 2 branches covered.">        if (s == null) return null;</span>

<span class="fc bfc" id="L137" title="All 2 branches covered.">        if (JsonUtils.isJsonObject(s)) {</span>
<span class="fc" id="L138">            List&lt;Map&lt;String, Object&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L139">            result.add(JsonUtils.deserialize(s, new TypeReference&lt;Map&lt;String, Object&gt;&gt;() {}));</span>

<span class="fc" id="L141">            return result;</span>
        }

<span class="fc" id="L144">        return JsonUtils.deserialize(s, new TypeReference&lt;List&lt;Map&lt;String, Object&gt;&gt;&gt;() {});</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>