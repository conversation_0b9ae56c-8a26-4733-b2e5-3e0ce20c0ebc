<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ListUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">ListUtils.java</span></div><h1>ListUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.function.BiPredicate;
import java.util.function.Function;

<span class="nc" id="L10">public class ListUtils {</span>

    private static &lt;T&gt; int getHash(T prev, Function&lt;T, ?&gt;[] keyMappers) {
<span class="fc" id="L13">        return Objects.hash(</span>
<span class="fc" id="L14">                Arrays.stream(keyMappers)</span>
<span class="fc" id="L15">                        .map(mapper -&gt; mapper.apply(prev))</span>
<span class="fc" id="L16">                        .toArray()</span>
        );
    }

    @SafeVarargs
    public static &lt;T&gt; List&lt;List&lt;T&gt;&gt; partitionBy(List&lt;T&gt; list, Function&lt;T, ?&gt;... keyMappers) {
<span class="fc bfc" id="L22" title="All 2 branches covered.">        return ListUtils.partitionBy(list, (partition, current) -&gt; getHash(partition.get(0), keyMappers) != getHash(current, keyMappers));</span>
    }

    public static &lt;T&gt; List&lt;List&lt;T&gt;&gt; partitionBy(List&lt;T&gt; list, BiPredicate&lt;List&lt;T&gt;, T&gt; shouldSplit) {
<span class="fc" id="L26">        return partitionBy(list, shouldSplit, -1);</span>
    }

    public static &lt;T&gt; List&lt;List&lt;T&gt;&gt; partitionBy(List&lt;T&gt; list, BiPredicate&lt;List&lt;T&gt;, T&gt; shouldSplit, Integer partitionSize) {
<span class="fc" id="L30">        List&lt;List&lt;T&gt;&gt; partitions = new ArrayList&lt;&gt;();</span>

<span class="fc bfc" id="L32" title="All 2 branches covered.">        for (T item : list) {</span>
<span class="fc bfc" id="L33" title="All 6 branches covered.">            if (partitions.isEmpty() || shouldSplit.test(partitions.get(partitions.size() - 1), item) || toPartitionBySize(partitions.get(partitions.size() - 1), partitionSize))</span>
<span class="fc" id="L34">                partitions.add(new ArrayList&lt;&gt;());</span>

<span class="fc" id="L36">            partitions.get(partitions.size() - 1).add(item);</span>
<span class="fc" id="L37">        }</span>

<span class="fc" id="L39">        return partitions;</span>
    }

    private static boolean toPartitionBySize(List partition, Integer partitionSize) {
<span class="fc bfc" id="L43" title="All 4 branches covered.">        return partitionSize &gt; 0 &amp;&amp; partition.size() &gt;= partitionSize;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>