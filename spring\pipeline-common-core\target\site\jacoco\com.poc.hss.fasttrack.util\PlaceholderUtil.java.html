<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PlaceholderUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">PlaceholderUtil.java</span></div><h1>PlaceholderUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import lombok.SneakyThrows;
import org.apache.commons.io.FileUtils;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

<span class="nc" id="L15">public class PlaceholderUtil {</span>

<span class="nc" id="L17">    public enum Purpose {</span>
<span class="nc" id="L18">        SOURCE_ADAPTOR_BATCH_ID,</span>
<span class="nc" id="L19">        SOURCE_ADAPTOR_SFTP_ARCHIVE_FOLDER_NAME,</span>
<span class="nc" id="L20">        CONSUMER_ADAPTOR_BATCH_ID,</span>
<span class="nc" id="L21">        CONSUMER_ADAPTOR_OUTPUT_FILENAME,</span>
<span class="nc" id="L22">        TEST_FILE</span>
    }

<span class="nc" id="L25">    @SneakyThrows</span>
    public static void fill(File file, Purpose purpose) {
<span class="nc" id="L27">        final String content = FileUtils.readFileToString(file, StandardCharsets.UTF_8);</span>
<span class="nc" id="L28">        final String updatedContent = fillInternal(content, getDefaultParam(purpose));</span>
<span class="nc" id="L29">        FileUtils.write(file, updatedContent, StandardCharsets.UTF_8);</span>
<span class="nc" id="L30">    }</span>

    public static String fill(String template, Purpose purpose) {
<span class="nc" id="L33">        return fillInternal(template, getDefaultParam(purpose));</span>
    }

    public static String fill(String template, Purpose purpose, Map&lt;String, String&gt; inputParam) {
<span class="nc" id="L37">        Map&lt;String, String&gt; param = getDefaultParam(purpose);</span>
<span class="nc bnc" id="L38" title="All 2 branches missed.">        if (inputParam != null)</span>
<span class="nc" id="L39">            param.putAll(inputParam);</span>
<span class="nc" id="L40">        return fillInternal(template, param);</span>
    }

    public static Map&lt;String, Object&gt; fill(Map&lt;String, Object&gt; map, Purpose purpose) {
<span class="nc bnc" id="L44" title="All 2 branches missed.">        if (map == null)</span>
<span class="nc" id="L45">            return null;</span>
<span class="nc" id="L46">        final Map&lt;String, String&gt; param = getDefaultParam(purpose);</span>
<span class="nc" id="L47">        Map&lt;String, Object&gt; resultMap = new HashMap&lt;&gt;();</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">        for (String key : map.keySet()) {</span>
<span class="nc" id="L49">            Object value = map.get(key);</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">            if (value instanceof String) {</span>
<span class="nc" id="L51">                resultMap.put(key, fillInternal((String) value, param));</span>
            } else {
<span class="nc" id="L53">                resultMap.put(key, value);</span>
            }
<span class="nc" id="L55">        }</span>
<span class="nc" id="L56">        return resultMap;</span>
    }

    private static String fillInternal(String template, Map&lt;String, String&gt; param) {
<span class="nc" id="L60">        String result = template;</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">        for (String key : param.keySet()) {</span>
<span class="nc" id="L62">            result = result.replace(&quot;{&quot; + key + &quot;}&quot;, param.get(key));</span>
<span class="nc" id="L63">        }</span>
<span class="nc" id="L64">        return result;</span>
    }

    private static Map&lt;String, String&gt; getDefaultParam(Purpose purpose) {
<span class="nc" id="L68">        Map&lt;String, String&gt; param = new HashMap&lt;&gt;();</span>
<span class="nc" id="L69">        param.put(&quot;yyyyMMdd&quot;, LocalDateTime.now().format(DateTimeFormatter.ofPattern(&quot;yyyyMMdd&quot;)));</span>
<span class="nc" id="L70">        param.put(&quot;yyyyMMdd-1&quot;, LocalDateTime.now()</span>
<span class="nc" id="L71">                .minus(1, ChronoUnit.DAYS).format(DateTimeFormatter.ofPattern(&quot;yyyyMMdd&quot;)));</span>

<span class="nc bnc" id="L73" title="All 3 branches missed.">        switch (purpose) {</span>
            case SOURCE_ADAPTOR_BATCH_ID:
<span class="nc" id="L75">                param.put(&quot;uuid&quot;, UUID.randomUUID().toString());</span>
<span class="nc" id="L76">                break;</span>
            case CONSUMER_ADAPTOR_OUTPUT_FILENAME:
<span class="nc" id="L78">                param.put(&quot;timestamp&quot;, LocalDateTime.now().format(DateTimeFormatter.ofPattern(&quot;yyyyMMddHHmmss&quot;)));</span>
<span class="nc" id="L79">                param.put(&quot;timestamp-with-ms&quot;, LocalDateTime.now().format(DateTimeFormatter.ofPattern(&quot;yyyyMMddHHmmssSSS&quot;)));</span>
<span class="nc" id="L80">                break;</span>
            case TEST_FILE:
            case SOURCE_ADAPTOR_SFTP_ARCHIVE_FOLDER_NAME:
            case CONSUMER_ADAPTOR_BATCH_ID:
            default:
        }
<span class="nc" id="L86">        return param;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.************</span></div></body></html>