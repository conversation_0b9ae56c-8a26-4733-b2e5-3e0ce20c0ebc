<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>StreamUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-core</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">StreamUtils.java</span></div><h1>StreamUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collector;

<span class="nc" id="L11">public class StreamUtils {</span>
    public static &lt;T&gt; Predicate&lt;T&gt; distinctBy(Function&lt;? super T, ?&gt; function) {
<span class="fc" id="L13">        Set&lt;Object&gt; seen = ConcurrentHashMap.newKeySet();</span>
<span class="fc" id="L14">        return t -&gt; seen.add(function.apply(t));</span>
    }

    public static &lt;T, K, U&gt; Collector&lt;T, ?, Map&lt;K, U&gt;&gt; toNullableMap(
            Function&lt;? super T, ? extends K&gt; keyMapper,
            Function&lt;? super T, ? extends U&gt; valueMapper
    ) {
<span class="fc" id="L21">        return Collector.of(</span>
                HashMap::new,
<span class="fc" id="L23">                (map, obj) -&gt; map.put(keyMapper.apply(obj), valueMapper.apply(obj)),</span>
                (m1, m2) -&gt; {
<span class="nc" id="L25">                    m1.putAll(m2);</span>
<span class="nc" id="L26">                    return m1;</span>
                }
        );
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>