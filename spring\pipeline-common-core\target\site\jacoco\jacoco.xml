<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="pipeline-common-core"><sessioninfo id="H344L0L63UFKL6Z-ce5eeb73" start="1752767960247" dump="1752767962330"/><package name="com/poc/hss/fasttrack/listener"><class name="com/poc/hss/fasttrack/listener/AutoBeanDisposalListener" sourcefilename="AutoBeanDisposalListener.java"><method name="&lt;init&gt;" desc="()V" line="19"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="onApplicationEvent" desc="(Lorg/springframework/context/event/ContextClosedEvent;)V" line="26"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="postProcessAfterInitialization" desc="(Ljava/lang/Object;Ljava/lang/String;)Ljava/lang/Object;" line="38"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setBeanFactory" desc="(Lorg/springframework/beans/factory/BeanFactory;)V" line="47"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$onApplicationEvent$0" desc="(Lorg/springframework/beans/factory/DisposableBean;)V" line="28"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="54" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="AutoBeanDisposalListener.java"><line nr="16" mi="4" ci="0" mb="0" cb="0"/><line nr="19" mi="2" ci="0" mb="0" cb="0"/><line nr="22" mi="7" ci="0" mb="0" cb="0"/><line nr="26" mi="4" ci="0" mb="0" cb="0"/><line nr="28" mi="5" ci="0" mb="0" cb="0"/><line nr="29" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="1" ci="0" mb="0" cb="0"/><line nr="31" mi="6" ci="0" mb="0" cb="0"/><line nr="32" mi="1" ci="0" mb="0" cb="0"/><line nr="33" mi="1" ci="0" mb="0" cb="0"/><line nr="34" mi="1" ci="0" mb="0" cb="0"/><line nr="38" mi="8" ci="0" mb="4" cb="0"/><line nr="39" mi="6" ci="0" mb="0" cb="0"/><line nr="42" mi="2" ci="0" mb="0" cb="0"/><line nr="47" mi="3" ci="0" mb="0" cb="0"/><line nr="48" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="54" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="54" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/poc/hss/fasttrack/constant"><class name="com/poc/hss/fasttrack/constant/ReservedTableName" sourcefilename="ReservedTableName.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getNames" desc="()Ljava/util/List;" line="24"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="3" covered="23"/><counter type="LINE" missed="1" covered="7"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/constant/ErrorCodeConstant" sourcefilename="ErrorCodeConstant.java"/><class name="com/poc/hss/fasttrack/constant/Constants" sourcefilename="Constants.java"/><sourcefile name="ReservedTableName.java"><line nr="6" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="0" ci="4" mb="0" cb="0"/><line nr="17" mi="0" ci="4" mb="0" cb="0"/><line nr="18" mi="0" ci="4" mb="0" cb="0"/><line nr="19" mi="0" ci="4" mb="0" cb="0"/><line nr="20" mi="0" ci="4" mb="0" cb="0"/><line nr="21" mi="0" ci="1" mb="0" cb="0"/><line nr="24" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="3" covered="23"/><counter type="LINE" missed="1" covered="7"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="Constants.java"/><sourcefile name="ErrorCodeConstant.java"/><counter type="INSTRUCTION" missed="3" covered="23"/><counter type="LINE" missed="1" covered="7"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="2"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/poc/hss/fasttrack/util"><class name="com/poc/hss/fasttrack/util/ConversionHelperService" sourcefilename="ConversionHelperService.java"><method name="&lt;init&gt;" desc="()V" line="24"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertStream" desc="(Ljava/util/stream/Stream;Ljava/lang/Class;)Ljava/util/stream/Stream;" line="35"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertStream" desc="(Ljava/util/stream/Stream;Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Ljava/util/stream/Stream;" line="39"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertList" desc="(Ljava/util/List;Ljava/lang/Class;)Ljava/util/List;" line="46"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertList" desc="(Ljava/util/List;Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Ljava/util/List;" line="50"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertMapValue" desc="(Ljava/util/Map;Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Ljava/util/Map;" line="56"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertMapValue" desc="(Ljava/util/Map;Ljava/lang/Class;)Ljava/util/Map;" line="69"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convert" desc="(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;" line="73"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convert" desc="(Ljava/lang/Object;Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Ljava/lang/Object;" line="77"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convert" desc="(Ljava/lang/Object;Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;Z)Ljava/lang/Object;" line="81"><counter type="INSTRUCTION" missed="86" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="14" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convert" desc="(Ljava/lang/Object;Lorg/springframework/core/convert/TypeDescriptor;Lorg/springframework/core/convert/TypeDescriptor;)Ljava/lang/Object;" line="105"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="merge" desc="(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;" line="109"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="merge" desc="(Ljava/lang/Object;Ljava/lang/Object;[Ljava/lang/String;)Ljava/lang/Object;" line="114"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$convertMapValue$1" desc="(Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;Ljava/util/concurrent/atomic/AtomicBoolean;Ljava/util/Map$Entry;)Ljava/lang/Object;" line="63"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$convertStream$0" desc="(Ljava/lang/Class;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;Ljava/util/concurrent/atomic/AtomicBoolean;Ljava/lang/Object;)Ljava/lang/Object;" line="42"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="26"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="215" covered="0"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="40" covered="0"/><counter type="COMPLEXITY" missed="26" covered="0"/><counter type="METHOD" missed="16" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/ListUtils" sourcefilename="ListUtils.java"><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getHash" desc="(Ljava/lang/Object;[Ljava/util/function/Function;)I" line="13"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="partitionBy" desc="(Ljava/util/List;[Ljava/util/function/Function;)Ljava/util/List;" line="22"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="partitionBy" desc="(Ljava/util/List;Ljava/util/function/BiPredicate;)Ljava/util/List;" line="26"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="partitionBy" desc="(Ljava/util/List;Ljava/util/function/BiPredicate;Ljava/lang/Integer;)Ljava/util/List;" line="30"><counter type="INSTRUCTION" missed="0" covered="56"/><counter type="BRANCH" missed="0" covered="8"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="5"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toPartitionBySize" desc="(Ljava/util/List;Ljava/lang/Integer;)Z" line="43"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$partitionBy$1" desc="([Ljava/util/function/Function;Ljava/util/List;Ljava/lang/Object;)Z" line="22"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$getHash$0" desc="(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;" line="15"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="3" covered="104"/><counter type="BRANCH" missed="0" covered="14"/><counter type="LINE" missed="1" covered="14"/><counter type="COMPLEXITY" missed="1" covered="14"/><counter type="METHOD" missed="1" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil$Graph" sourcefilename="GraphUtil.java"><method name="addEdge" desc="(Ljava/lang/Object;Ljava/lang/Object;)V" line="24"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="$default$nodeMap" desc="()Ljava/util/Map;" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/util/GraphUtil$Graph$GraphBuilder;)V" line="16"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/util/GraphUtil$Graph$GraphBuilder;" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getNodeMap" desc="()Ljava/util/Map;" line="21"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setNodeMap" desc="(Ljava/util/Map;)V" line="15"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="15"><counter type="INSTRUCTION" missed="38" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="15"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="15"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="()V" line="17"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/util/Map;)V" line="18"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="149" covered="0"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="22" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/JsonUtils$2" sourcefilename="JsonUtils.java"><method name="&lt;init&gt;" desc="()V" line="144"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/JsonUtils$1" sourcefilename="JsonUtils.java"><method name="&lt;init&gt;" desc="()V" line="139"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/PlaceholderUtil" sourcefilename="PlaceholderUtil.java"><method name="&lt;init&gt;" desc="()V" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fill" desc="(Ljava/io/File;Lcom/poc/hss/fasttrack/util/PlaceholderUtil$Purpose;)V" line="25"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fill" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/util/PlaceholderUtil$Purpose;)Ljava/lang/String;" line="33"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fill" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/util/PlaceholderUtil$Purpose;Ljava/util/Map;)Ljava/lang/String;" line="37"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fill" desc="(Ljava/util/Map;Lcom/poc/hss/fasttrack/util/PlaceholderUtil$Purpose;)Ljava/util/Map;" line="44"><counter type="INSTRUCTION" missed="46" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fillInternal" desc="(Ljava/lang/String;Ljava/util/Map;)Ljava/lang/String;" line="60"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDefaultParam" desc="(Lcom/poc/hss/fasttrack/util/PlaceholderUtil$Purpose;)Ljava/util/Map;" line="68"><counter type="INSTRUCTION" missed="52" covered="0"/><counter type="BRANCH" missed="3" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="161" covered="0"/><counter type="BRANCH" missed="13" covered="0"/><counter type="LINE" missed="38" covered="0"/><counter type="COMPLEXITY" missed="14" covered="0"/><counter type="METHOD" missed="7" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil$Graph$GraphBuilderImpl" sourcefilename="GraphUtil.java"><method name="self" desc="()Lcom/poc/hss/fasttrack/util/GraphUtil$Graph$GraphBuilderImpl;" line="16"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/util/GraphUtil$Graph;" line="16"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/StreamUtils" sourcefilename="StreamUtils.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="distinctBy" desc="(Ljava/util/function/Function;)Ljava/util/function/Predicate;" line="13"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toNullableMap" desc="(Ljava/util/function/Function;Ljava/util/function/Function;)Ljava/util/stream/Collector;" line="21"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$toNullableMap$2" desc="(Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;" line="25"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$toNullableMap$1" desc="(Ljava/util/function/Function;Ljava/util/function/Function;Ljava/util/Map;Ljava/lang/Object;)V" line="23"><counter type="INSTRUCTION" missed="0" covered="10"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$distinctBy$0" desc="(Ljava/util/Set;Ljava/util/function/Function;Ljava/lang/Object;)Z" line="14"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="8" covered="31"/><counter type="LINE" missed="3" covered="4"/><counter type="COMPLEXITY" missed="2" covered="4"/><counter type="METHOD" missed="2" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil$Node" sourcefilename="GraphUtil.java"><method name="$default$isOriginal" desc="()Z" line="35"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="$default$checked" desc="()Z" line="35"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="$default$nodes" desc="()Ljava/util/Set;" line="35"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder;)V" line="35"><counter type="INSTRUCTION" missed="40" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder;" line="35"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getValue" desc="()Ljava/lang/Object;" line="39"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="isOriginal" desc="()Z" line="41"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="isChecked" desc="()Z" line="43"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getNodes" desc="()Ljava/util/Set;" line="45"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setValue" desc="(Ljava/lang/Object;)V" line="34"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setOriginal" desc="(Z)V" line="34"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setChecked" desc="(Z)V" line="34"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setNodes" desc="(Ljava/util/Set;)V" line="34"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="34"><counter type="INSTRUCTION" missed="69" covered="0"/><counter type="BRANCH" missed="22" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="12" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="34"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="34"><counter type="INSTRUCTION" missed="56" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="34"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="()V" line="36"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/Object;ZZLjava/util/Set;)V" line="37"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="247" covered="0"/><counter type="BRANCH" missed="36" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="37" covered="0"/><counter type="METHOD" missed="19" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilderImpl" sourcefilename="GraphUtil.java"><method name="self" desc="()Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilderImpl;" line="35"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/util/GraphUtil$Node;" line="35"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/CopyUtils" sourcefilename="CopyUtils.java"><method name="deepCopy" desc="(Ljava/lang/Object;)Ljava/lang/Object;" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="deepCopy" desc="(Ljava/util/Map;)Ljava/util/Map;" line="17"><counter type="INSTRUCTION" missed="0" covered="25"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="33"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/ValidationUtils" sourcefilename="ValidationUtils.java"><method name="isValidStrAgainstRegex" desc="(Ljava/lang/String;Ljava/lang/String;)Z" line="13"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil$Graph$GraphBuilder" sourcefilename="GraphUtil.java"><method name="&lt;init&gt;" desc="()V" line="16"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="nodeMap" desc="(Ljava/util/Map;)Lcom/poc/hss/fasttrack/util/GraphUtil$Graph$GraphBuilder;" line="16"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="16"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/EsapiUtils$PostgresCodec" sourcefilename="EsapiUtils.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="encodeCharacter" desc="([CLjava/lang/Character;)Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/EsapiUtils" sourcefilename="EsapiUtils.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="encode" desc="(Ljava/lang/String;)Ljava/lang/String;" line="16"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil" sourcefilename="GraphUtil.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hasCycle" desc="(Lcom/poc/hss/fasttrack/util/GraphUtil$Graph;)Z" line="49"><counter type="INSTRUCTION" missed="23" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hasCycle" desc="(Lcom/poc/hss/fasttrack/util/GraphUtil$Node;)Z" line="58"><counter type="INSTRUCTION" missed="36" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="62" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/KeyValuePairUtils" sourcefilename="KeyValuePairUtils.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toMap" desc="(Ljava/util/List;)Ljava/util/Map;" line="12"><counter type="INSTRUCTION" missed="0" covered="24"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="3" covered="24"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="1" covered="5"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/util/PlaceholderUtil$Purpose" sourcefilename="PlaceholderUtil.java"><method name="&lt;clinit&gt;" desc="()V" line="17"><counter type="INSTRUCTION" missed="33" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="33" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder" sourcefilename="GraphUtil.java"><method name="&lt;init&gt;" desc="()V" line="35"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="value" desc="(Ljava/lang/Object;)Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder;" line="35"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="isOriginal" desc="(Z)Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder;" line="35"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="checked" desc="(Z)Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder;" line="35"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="nodes" desc="(Ljava/util/Set;)Lcom/poc/hss/fasttrack/util/GraphUtil$Node$NodeBuilder;" line="35"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="35"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/util/JsonUtils" sourcefilename="JsonUtils.java"><method name="&lt;init&gt;" desc="()V" line="27"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="deserialize" desc="(Ljava/lang/String;Ljava/lang/Class;)Ljava/lang/Object;" line="47"><counter type="INSTRUCTION" missed="0" covered="19"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="deserialize" desc="(Ljava/lang/String;Lcom/fasterxml/jackson/core/type/TypeReference;)Ljava/lang/Object;" line="59"><counter type="INSTRUCTION" missed="12" covered="7"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="4" covered="2"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="serialize" desc="(Ljava/lang/Object;)Ljava/lang/String;" line="71"><counter type="INSTRUCTION" missed="10" covered="8"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="3" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="beautifySerialize" desc="(Ljava/lang/Object;)Ljava/lang/String;" line="83"><counter type="INSTRUCTION" missed="10" covered="9"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="3" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="convert" desc="(Ljava/lang/Object;Ljava/lang/Class;)Ljava/lang/Object;" line="95"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="convert" desc="(Ljava/lang/Object;Lcom/fasterxml/jackson/core/type/TypeReference;)Ljava/lang/Object;" line="99"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="isJsonObject" desc="(Ljava/lang/String;)Z" line="104"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="isJsonArray" desc="(Ljava/lang/String;)Z" line="113"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toJsonArray" desc="(Ljava/lang/Object;)Lio/vertx/core/json/JsonArray;" line="121"><counter type="INSTRUCTION" missed="0" covered="27"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toJsonArray" desc="(Ljava/lang/String;)Ljava/util/List;" line="135"><counter type="INSTRUCTION" missed="0" covered="29"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getObjectMapper" desc="()Lcom/fasterxml/jackson/databind/ObjectMapper;" line="29"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="30"><counter type="INSTRUCTION" missed="0" covered="59"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="37" covered="190"/><counter type="BRANCH" missed="1" covered="15"/><counter type="LINE" missed="12" covered="51"/><counter type="COMPLEXITY" missed="3" covered="18"/><counter type="METHOD" missed="2" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="KeyValuePairUtils.java"><line nr="9" mi="3" ci="0" mb="0" cb="0"/><line nr="12" mi="0" ci="4" mb="0" cb="0"/><line nr="13" mi="0" ci="10" mb="0" cb="2"/><line nr="14" mi="0" ci="7" mb="0" cb="0"/><line nr="15" mi="0" ci="1" mb="0" cb="0"/><line nr="16" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="3" covered="24"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="1" covered="5"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="1" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="JsonUtils.java"><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="29" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="0" ci="4" mb="0" cb="0"/><line nr="31" mi="0" ci="3" mb="0" cb="0"/><line nr="34" mi="0" ci="5" mb="0" cb="0"/><line nr="35" mi="0" ci="5" mb="0" cb="0"/><line nr="36" mi="0" ci="5" mb="0" cb="0"/><line nr="38" mi="0" ci="6" mb="0" cb="0"/><line nr="39" mi="0" ci="6" mb="0" cb="0"/><line nr="40" mi="0" ci="6" mb="0" cb="0"/><line nr="41" mi="0" ci="6" mb="0" cb="0"/><line nr="42" mi="0" ci="6" mb="0" cb="0"/><line nr="43" mi="0" ci="6" mb="0" cb="0"/><line nr="44" mi="0" ci="1" mb="0" cb="0"/><line nr="47" mi="0" ci="2" mb="0" cb="2"/><line nr="48" mi="0" ci="2" mb="0" cb="0"/><line nr="51" mi="0" ci="5" mb="0" cb="0"/><line nr="52" mi="0" ci="1" mb="0" cb="0"/><line nr="53" mi="0" ci="4" mb="0" cb="0"/><line nr="54" mi="0" ci="5" mb="0" cb="0"/><line nr="59" mi="0" ci="2" mb="1" cb="1"/><line nr="60" mi="2" ci="0" mb="0" cb="0"/><line nr="63" mi="0" ci="5" mb="0" cb="0"/><line nr="64" mi="1" ci="0" mb="0" cb="0"/><line nr="65" mi="4" ci="0" mb="0" cb="0"/><line nr="66" mi="5" ci="0" mb="0" cb="0"/><line nr="71" mi="0" ci="2" mb="0" cb="2"/><line nr="72" mi="0" ci="2" mb="0" cb="0"/><line nr="75" mi="0" ci="4" mb="0" cb="0"/><line nr="76" mi="1" ci="0" mb="0" cb="0"/><line nr="77" mi="4" ci="0" mb="0" cb="0"/><line nr="78" mi="5" ci="0" mb="0" cb="0"/><line nr="83" mi="0" ci="2" mb="0" cb="2"/><line nr="84" mi="0" ci="2" mb="0" cb="0"/><line nr="87" mi="0" ci="5" mb="0" cb="0"/><line nr="88" mi="1" ci="0" mb="0" cb="0"/><line nr="89" mi="4" ci="0" mb="0" cb="0"/><line nr="90" mi="5" ci="0" mb="0" cb="0"/><line nr="95" mi="0" ci="5" mb="0" cb="0"/><line nr="99" mi="0" ci="5" mb="0" cb="0"/><line nr="104" mi="0" ci="5" mb="0" cb="0"/><line nr="105" mi="0" ci="1" mb="0" cb="0"/><line nr="106" mi="0" ci="2" mb="0" cb="0"/><line nr="107" mi="0" ci="1" mb="0" cb="0"/><line nr="108" mi="0" ci="2" mb="0" cb="0"/><line nr="113" mi="0" ci="5" mb="0" cb="0"/><line nr="114" mi="0" ci="1" mb="0" cb="0"/><line nr="115" mi="0" ci="2" mb="0" cb="0"/><line nr="116" mi="0" ci="1" mb="0" cb="0"/><line nr="117" mi="0" ci="2" mb="0" cb="0"/><line nr="121" mi="0" ci="4" mb="0" cb="2"/><line nr="123" mi="0" ci="3" mb="0" cb="0"/><line nr="124" mi="0" ci="3" mb="0" cb="2"/><line nr="125" mi="0" ci="4" mb="0" cb="0"/><line nr="126" mi="0" ci="6" mb="0" cb="0"/><line nr="128" mi="0" ci="2" mb="0" cb="0"/><line nr="131" mi="0" ci="5" mb="0" cb="0"/><line nr="135" mi="0" ci="4" mb="0" cb="2"/><line nr="137" mi="0" ci="3" mb="0" cb="2"/><line nr="138" mi="0" ci="4" mb="0" cb="0"/><line nr="139" mi="0" ci="12" mb="0" cb="0"/><line nr="141" mi="0" ci="2" mb="0" cb="0"/><line nr="144" mi="0" ci="10" mb="0" cb="0"/><counter type="INSTRUCTION" missed="37" covered="196"/><counter type="BRANCH" missed="1" covered="15"/><counter type="LINE" missed="12" covered="51"/><counter type="COMPLEXITY" missed="3" covered="20"/><counter type="METHOD" missed="2" covered="13"/><counter type="CLASS" missed="0" covered="3"/></sourcefile><sourcefile name="ConversionHelperService.java"><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="4" ci="0" mb="0" cb="0"/><line nr="35" mi="6" ci="0" mb="0" cb="0"/><line nr="39" mi="2" ci="0" mb="2" cb="0"/><line nr="40" mi="2" ci="0" mb="0" cb="0"/><line nr="41" mi="5" ci="0" mb="0" cb="0"/><line nr="42" mi="17" ci="0" mb="0" cb="0"/><line nr="46" mi="6" ci="0" mb="0" cb="0"/><line nr="50" mi="2" ci="0" mb="2" cb="0"/><line nr="51" mi="2" ci="0" mb="0" cb="0"/><line nr="52" mi="10" ci="0" mb="0" cb="0"/><line nr="56" mi="2" ci="0" mb="2" cb="0"/><line nr="57" mi="2" ci="0" mb="0" cb="0"/><line nr="58" mi="5" ci="0" mb="0" cb="0"/><line nr="59" mi="10" ci="0" mb="0" cb="0"/><line nr="60" mi="2" ci="0" mb="0" cb="0"/><line nr="61" mi="1" ci="0" mb="0" cb="0"/><line nr="63" mi="10" ci="0" mb="0" cb="0"/><line nr="69" mi="6" ci="0" mb="0" cb="0"/><line nr="73" mi="7" ci="0" mb="0" cb="0"/><line nr="77" mi="7" ci="0" mb="0" cb="0"/><line nr="81" mi="2" ci="0" mb="2" cb="0"/><line nr="82" mi="2" ci="0" mb="0" cb="0"/><line nr="84" mi="2" ci="0" mb="2" cb="0"/><line nr="85" mi="10" ci="0" mb="0" cb="0"/><line nr="86" mi="9" ci="0" mb="0" cb="0"/><line nr="87" mi="7" ci="0" mb="2" cb="0"/><line nr="88" mi="12" ci="0" mb="0" cb="0"/><line nr="91" mi="7" ci="0" mb="2" cb="0"/><line nr="92" mi="6" ci="0" mb="0" cb="0"/><line nr="94" mi="3" ci="0" mb="2" cb="0"/><line nr="95" mi="10" ci="0" mb="0" cb="0"/><line nr="98" mi="5" ci="0" mb="4" cb="0"/><line nr="99" mi="6" ci="0" mb="0" cb="0"/><line nr="101" mi="5" ci="0" mb="0" cb="0"/><line nr="105" mi="7" ci="0" mb="0" cb="0"/><line nr="109" mi="3" ci="0" mb="0" cb="0"/><line nr="110" mi="2" ci="0" mb="0" cb="0"/><line nr="114" mi="4" ci="0" mb="0" cb="0"/><line nr="115" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="215" covered="0"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="40" covered="0"/><counter type="COMPLEXITY" missed="26" covered="0"/><counter type="METHOD" missed="16" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="EsapiUtils.java"><line nr="6" mi="3" ci="0" mb="0" cb="0"/><line nr="8" mi="3" ci="0" mb="0" cb="0"/><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="16" mi="7" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><sourcefile name="GraphUtil.java"><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="15" mi="70" ci="0" mb="14" cb="0"/><line nr="16" mi="46" ci="0" mb="2" cb="0"/><line nr="17" mi="6" ci="0" mb="0" cb="0"/><line nr="18" mi="6" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="5" ci="0" mb="2" cb="0"/><line nr="25" mi="9" ci="0" mb="0" cb="0"/><line nr="27" mi="5" ci="0" mb="2" cb="0"/><line nr="28" mi="9" ci="0" mb="0" cb="0"/><line nr="30" mi="13" ci="0" mb="0" cb="0"/><line nr="31" mi="1" ci="0" mb="0" cb="0"/><line nr="34" mi="156" ci="0" mb="30" cb="0"/><line nr="35" mi="107" ci="0" mb="6" cb="0"/><line nr="36" mi="12" ci="0" mb="0" cb="0"/><line nr="37" mi="15" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="0" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="49" mi="12" ci="0" mb="2" cb="0"/><line nr="50" mi="6" ci="0" mb="4" cb="0"/><line nr="51" mi="2" ci="0" mb="0" cb="0"/><line nr="53" mi="1" ci="0" mb="0" cb="0"/><line nr="54" mi="2" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="0" cb="0"/><line nr="59" mi="11" ci="0" mb="2" cb="0"/><line nr="60" mi="3" ci="0" mb="2" cb="0"/><line nr="61" mi="2" ci="0" mb="0" cb="0"/><line nr="62" mi="6" ci="0" mb="4" cb="0"/><line nr="63" mi="2" ci="0" mb="0" cb="0"/><line nr="65" mi="1" ci="0" mb="0" cb="0"/><line nr="66" mi="3" ci="0" mb="0" cb="0"/><line nr="67" mi="3" ci="0" mb="0" cb="0"/><line nr="68" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="537" covered="0"/><counter type="BRANCH" missed="70" covered="0"/><counter type="LINE" missed="35" covered="0"/><counter type="COMPLEXITY" missed="82" covered="0"/><counter type="METHOD" missed="47" covered="0"/><counter type="CLASS" missed="7" covered="0"/></sourcefile><sourcefile name="CopyUtils.java"><line nr="12" mi="0" ci="3" mb="0" cb="0"/><line nr="13" mi="0" ci="5" mb="0" cb="0"/><line nr="17" mi="0" ci="4" mb="0" cb="0"/><line nr="18" mi="0" ci="11" mb="0" cb="2"/><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="21" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="33"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="ListUtils.java"><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="5" mb="0" cb="0"/><line nr="16" mi="0" ci="1" mb="0" cb="0"/><line nr="22" mi="0" ci="18" mb="0" cb="2"/><line nr="26" mi="0" ci="6" mb="0" cb="0"/><line nr="30" mi="0" ci="4" mb="0" cb="0"/><line nr="32" mi="0" ci="9" mb="0" cb="2"/><line nr="33" mi="0" ci="24" mb="0" cb="6"/><line nr="34" mi="0" ci="6" mb="0" cb="0"/><line nr="36" mi="0" ci="10" mb="0" cb="0"/><line nr="37" mi="0" ci="1" mb="0" cb="0"/><line nr="39" mi="0" ci="2" mb="0" cb="0"/><line nr="43" mi="0" ci="12" mb="0" cb="4"/><counter type="INSTRUCTION" missed="3" covered="104"/><counter type="BRANCH" missed="0" covered="14"/><counter type="LINE" missed="1" covered="14"/><counter type="COMPLEXITY" missed="1" covered="14"/><counter type="METHOD" missed="1" covered="7"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="PlaceholderUtil.java"><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="6" ci="0" mb="0" cb="0"/><line nr="19" mi="6" ci="0" mb="0" cb="0"/><line nr="20" mi="6" ci="0" mb="0" cb="0"/><line nr="21" mi="6" ci="0" mb="0" cb="0"/><line nr="22" mi="6" ci="0" mb="0" cb="0"/><line nr="25" mi="4" ci="0" mb="0" cb="0"/><line nr="27" mi="4" ci="0" mb="0" cb="0"/><line nr="28" mi="5" ci="0" mb="0" cb="0"/><line nr="29" mi="4" ci="0" mb="0" cb="0"/><line nr="30" mi="1" ci="0" mb="0" cb="0"/><line nr="33" mi="5" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="2" ci="0" mb="2" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="4" ci="0" mb="0" cb="0"/><line nr="44" mi="2" ci="0" mb="2" cb="0"/><line nr="45" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="3" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="0" cb="0"/><line nr="48" mi="11" ci="0" mb="2" cb="0"/><line nr="49" mi="4" ci="0" mb="0" cb="0"/><line nr="50" mi="3" ci="0" mb="2" cb="0"/><line nr="51" mi="9" ci="0" mb="0" cb="0"/><line nr="53" mi="5" ci="0" mb="0" cb="0"/><line nr="55" mi="1" ci="0" mb="0" cb="0"/><line nr="56" mi="2" ci="0" mb="0" cb="0"/><line nr="60" mi="2" ci="0" mb="0" cb="0"/><line nr="61" mi="11" ci="0" mb="2" cb="0"/><line nr="62" mi="9" ci="0" mb="0" cb="0"/><line nr="63" mi="1" ci="0" mb="0" cb="0"/><line nr="64" mi="2" ci="0" mb="0" cb="0"/><line nr="68" mi="4" ci="0" mb="0" cb="0"/><line nr="69" mi="8" ci="0" mb="0" cb="0"/><line nr="70" mi="7" ci="0" mb="0" cb="0"/><line nr="71" mi="4" ci="0" mb="0" cb="0"/><line nr="73" mi="3" ci="0" mb="3" cb="0"/><line nr="75" mi="6" ci="0" mb="0" cb="0"/><line nr="76" mi="1" ci="0" mb="0" cb="0"/><line nr="78" mi="8" ci="0" mb="0" cb="0"/><line nr="79" mi="8" ci="0" mb="0" cb="0"/><line nr="80" mi="1" ci="0" mb="0" cb="0"/><line nr="86" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="194" covered="0"/><counter type="BRANCH" missed="13" covered="0"/><counter type="LINE" missed="44" covered="0"/><counter type="COMPLEXITY" missed="15" covered="0"/><counter type="METHOD" missed="8" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><sourcefile name="StreamUtils.java"><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="0" ci="2" mb="0" cb="0"/><line nr="14" mi="0" ci="10" mb="0" cb="0"/><line nr="21" mi="0" ci="9" mb="0" cb="0"/><line nr="23" mi="0" ci="10" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="31"/><counter type="LINE" missed="3" covered="4"/><counter type="COMPLEXITY" missed="2" covered="4"/><counter type="METHOD" missed="2" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="ValidationUtils.java"><line nr="13" mi="0" ci="6" mb="0" cb="4"/><line nr="14" mi="0" ci="2" mb="0" cb="0"/><line nr="16" mi="0" ci="4" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="1013" covered="400"/><counter type="BRANCH" missed="104" covered="37"/><counter type="LINE" missed="140" covered="84"/><counter type="COMPLEXITY" missed="134" covered="46"/><counter type="METHOD" missed="81" covered="28"/><counter type="CLASS" missed="12" covered="8"/></package><package name="com/poc/hss/fasttrack/json/module"><class name="com/poc/hss/fasttrack/json/module/JsonObjectModule$JsonObjectDeserializer" sourcefilename="JsonObjectModule.java"><method name="&lt;init&gt;" desc="()V" line="32"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="deserialize" desc="(Lcom/fasterxml/jackson/core/JsonParser;Lcom/fasterxml/jackson/databind/DeserializationContext;)Lio/vertx/core/json/JsonObject;" line="36"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/json/module/JsonObjectModule" sourcefilename="JsonObjectModule.java"><method name="&lt;init&gt;" desc="()V" line="18"><counter type="INSTRUCTION" missed="0" covered="31"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="31"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/json/module/JsonObjectModule$JsonArrayDeserializer" sourcefilename="JsonObjectModule.java"><method name="&lt;init&gt;" desc="()V" line="49"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="deserialize" desc="(Lcom/fasterxml/jackson/core/JsonParser;Lcom/fasterxml/jackson/databind/DeserializationContext;)Lio/vertx/core/json/JsonArray;" line="53"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/json/module/JsonObjectModule$JsonArraySerializer" sourcefilename="JsonObjectModule.java"><method name="&lt;init&gt;" desc="()V" line="42"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="serialize" desc="(Lio/vertx/core/json/JsonArray;Lcom/fasterxml/jackson/core/JsonGenerator;Lcom/fasterxml/jackson/databind/SerializerProvider;)V" line="45"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="5" covered="3"/><counter type="LINE" missed="2" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="1" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/json/module/JsonObjectModule$JsonObjectSerializer" sourcefilename="JsonObjectModule.java"><method name="&lt;init&gt;" desc="()V" line="25"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="serialize" desc="(Lio/vertx/core/json/JsonObject;Lcom/fasterxml/jackson/core/JsonGenerator;Lcom/fasterxml/jackson/databind/SerializerProvider;)V" line="28"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="5" covered="3"/><counter type="LINE" missed="2" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="1" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="JsonObjectModule.java"><line nr="18" mi="0" ci="2" mb="0" cb="0"/><line nr="19" mi="0" ci="7" mb="0" cb="0"/><line nr="20" mi="0" ci="7" mb="0" cb="0"/><line nr="21" mi="0" ci="7" mb="0" cb="0"/><line nr="22" mi="0" ci="7" mb="0" cb="0"/><line nr="23" mi="0" ci="1" mb="0" cb="0"/><line nr="25" mi="0" ci="3" mb="0" cb="0"/><line nr="28" mi="4" ci="0" mb="0" cb="0"/><line nr="29" mi="1" ci="0" mb="0" cb="0"/><line nr="32" mi="0" ci="3" mb="0" cb="0"/><line nr="36" mi="0" ci="3" mb="0" cb="0"/><line nr="37" mi="0" ci="6" mb="0" cb="0"/><line nr="42" mi="0" ci="3" mb="0" cb="0"/><line nr="45" mi="4" ci="0" mb="0" cb="0"/><line nr="46" mi="1" ci="0" mb="0" cb="0"/><line nr="49" mi="0" ci="3" mb="0" cb="0"/><line nr="53" mi="0" ci="3" mb="0" cb="0"/><line nr="54" mi="0" ci="6" mb="0" cb="0"/><counter type="INSTRUCTION" missed="10" covered="61"/><counter type="LINE" missed="4" covered="14"/><counter type="COMPLEXITY" missed="2" covered="7"/><counter type="METHOD" missed="2" covered="7"/><counter type="CLASS" missed="0" covered="5"/></sourcefile><counter type="INSTRUCTION" missed="10" covered="61"/><counter type="LINE" missed="4" covered="14"/><counter type="COMPLEXITY" missed="2" covered="7"/><counter type="METHOD" missed="2" covered="7"/><counter type="CLASS" missed="0" covered="5"/></package><package name="com/poc/hss/fasttrack/step"><class name="com/poc/hss/fasttrack/step/AuditInfoContext$AuditInfoContextBuilderImpl" sourcefilename="AuditInfoContext.java"><method name="self" desc="()Lcom/poc/hss/fasttrack/step/AuditInfoContext$AuditInfoContextBuilderImpl;" line="8"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/step/AuditInfoContext;" line="8"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/ConcurrentStep" sourcefilename="ConcurrentStep.java"><method name="&lt;init&gt;" desc="(Lorg/springframework/scheduling/concurrent/ThreadPoolTaskExecutor;Ljava/util/List;)V" line="20"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="run" desc="(Ljava/lang/Object;)Ljava/lang/Object;" line="27"><counter type="INSTRUCTION" missed="50" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$run$2" desc="(Lcom/poc/hss/fasttrack/model/LoginUser;Ljava/lang/Object;Lcom/poc/hss/fasttrack/step/Step;)Ljava/util/concurrent/Future;" line="30"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$run$1" desc="(Lcom/poc/hss/fasttrack/model/LoginUser;Lcom/poc/hss/fasttrack/step/Step;Ljava/lang/Object;)Ljava/lang/Object;" line="32"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="13" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$run$0" desc="(Lcom/poc/hss/fasttrack/step/Step;)Ljava/lang/String;" line="27"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="117" covered="0"/><counter type="BRANCH" missed="10" covered="0"/><counter type="LINE" missed="28" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/StepExecutionException" sourcefilename="StepExecutionException.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/step/Step;Ljava/lang/Exception;)V" line="5"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/Step" sourcefilename="Step.java"><method name="shouldRun" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/AuditInfoContext" sourcefilename="AuditInfoContext.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/step/AuditInfoContext$AuditInfoContextBuilder;)V" line="8"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/step/AuditInfoContext$AuditInfoContextBuilder;" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getCurrentUser" desc="()Lcom/poc/hss/fasttrack/model/LoginUser;" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setCurrentUser" desc="(Lcom/poc/hss/fasttrack/model/LoginUser;)V" line="7"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="38" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="7"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="7"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="7"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="84" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="15" covered="0"/><counter type="METHOD" missed="8" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/ConcurrentStepFactory" sourcefilename="ConcurrentStepFactory.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getInstance" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/step/Step;" line="17"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/AuditInfoContext$AuditInfoContextBuilder" sourcefilename="AuditInfoContext.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="currentUser" desc="(Lcom/poc/hss/fasttrack/model/LoginUser;)Lcom/poc/hss/fasttrack/step/AuditInfoContext$AuditInfoContextBuilder;" line="8"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="8"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/step/StepExecutor" sourcefilename="StepExecutor.java"><method name="&lt;init&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="run" desc="(Ljava/util/List;Ljava/lang/Object;)Ljava/lang/Object;" line="20"><counter type="INSTRUCTION" missed="70" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="19" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="runAsync" desc="(Ljava/util/List;Ljava/lang/Object;)Ljava/lang/Object;" line="46"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="84" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="22" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="AuditInfoContext.java"><line nr="7" mi="70" ci="0" mb="14" cb="0"/><line nr="8" mi="32" ci="0" mb="0" cb="0"/><line nr="10" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="105" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="20" covered="0"/><counter type="METHOD" missed="13" covered="0"/><counter type="CLASS" missed="3" covered="0"/></sourcefile><sourcefile name="StepExecutionException.java"><line nr="5" mi="7" ci="0" mb="0" cb="0"/><line nr="6" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="ConcurrentStep.java"><line nr="16" mi="4" ci="0" mb="0" cb="0"/><line nr="20" mi="2" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="1" ci="0" mb="0" cb="0"/><line nr="27" mi="15" ci="0" mb="0" cb="0"/><line nr="28" mi="9" ci="0" mb="2" cb="0"/><line nr="29" mi="7" ci="0" mb="0" cb="0"/><line nr="30" mi="9" ci="0" mb="0" cb="0"/><line nr="32" mi="2" ci="0" mb="0" cb="0"/><line nr="33" mi="4" ci="0" mb="2" cb="0"/><line nr="34" mi="6" ci="0" mb="0" cb="0"/><line nr="36" mi="4" ci="0" mb="0" cb="0"/><line nr="37" mi="1" ci="0" mb="0" cb="0"/><line nr="38" mi="2" ci="0" mb="0" cb="0"/><line nr="39" mi="1" ci="0" mb="0" cb="0"/><line nr="40" mi="7" ci="0" mb="4" cb="0"/><line nr="41" mi="4" ci="0" mb="0" cb="0"/><line nr="43" mi="2" ci="0" mb="0" cb="0"/><line nr="44" mi="1" ci="0" mb="0" cb="0"/><line nr="45" mi="6" ci="0" mb="0" cb="0"/><line nr="47" mi="2" ci="0" mb="0" cb="0"/><line nr="50" mi="4" ci="0" mb="0" cb="0"/><line nr="52" mi="2" ci="0" mb="0" cb="0"/><line nr="53" mi="10" ci="0" mb="2" cb="0"/><line nr="54" mi="3" ci="0" mb="0" cb="0"/><line nr="55" mi="1" ci="0" mb="0" cb="0"/><line nr="57" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="117" covered="0"/><counter type="BRANCH" missed="10" covered="0"/><counter type="LINE" missed="28" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="StepExecutor.java"><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="4" ci="0" mb="0" cb="0"/><line nr="20" mi="10" ci="0" mb="2" cb="0"/><line nr="21" mi="4" ci="0" mb="2" cb="0"/><line nr="22" mi="2" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="2" cb="0"/><line nr="24" mi="6" ci="0" mb="0" cb="0"/><line nr="26" mi="4" ci="0" mb="0" cb="0"/><line nr="27" mi="1" ci="0" mb="0" cb="0"/><line nr="28" mi="2" ci="0" mb="0" cb="0"/><line nr="29" mi="1" ci="0" mb="0" cb="0"/><line nr="30" mi="7" ci="0" mb="4" cb="0"/><line nr="31" mi="4" ci="0" mb="0" cb="0"/><line nr="33" mi="2" ci="0" mb="0" cb="0"/><line nr="34" mi="1" ci="0" mb="0" cb="0"/><line nr="35" mi="6" ci="0" mb="0" cb="0"/><line nr="36" mi="1" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="2" cb="0"/><line nr="38" mi="10" ci="0" mb="0" cb="0"/><line nr="40" mi="1" ci="0" mb="0" cb="0"/><line nr="42" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="7" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="84" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="22" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="ConcurrentStepFactory.java"><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="17" mi="9" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Step.java"><line nr="7" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="328" covered="0"/><counter type="BRANCH" missed="36" covered="0"/><counter type="LINE" missed="58" covered="0"/><counter type="COMPLEXITY" missed="45" covered="0"/><counter type="METHOD" missed="27" covered="0"/><counter type="CLASS" missed="8" covered="0"/></package><package name="com/poc/hss/fasttrack/converter"><class name="com/poc/hss/fasttrack/converter/ConverterContext$ConverterContextBuilder" sourcefilename="ConverterContext.java"><method name="&lt;init&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="obj" desc="(Ljava/lang/Object;)Lcom/poc/hss/fasttrack/converter/ConverterContext$ConverterContextBuilder;" line="7"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="context" desc="(Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Lcom/poc/hss/fasttrack/converter/ConverterContext$ConverterContextBuilder;" line="7"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/converter/ConverterContext;" line="7"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="7"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/converter/FieldFetchContext$FieldFetchContextBuilder" sourcefilename="FieldFetchContext.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fetchAll" desc="(Z)Lcom/poc/hss/fasttrack/converter/FieldFetchContext$FieldFetchContextBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="name" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/converter/FieldFetchContext$FieldFetchContextBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fields" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/converter/FieldFetchContext$FieldFetchContextBuilder;" line="11"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/converter/FieldFetchContext;" line="11"><counter type="INSTRUCTION" missed="0" covered="17"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="17" covered="30"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="3" covered="4"/><counter type="METHOD" missed="2" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/converter/ConverterContext" sourcefilename="ConverterContext.java"><method name="getObj" desc="()Ljava/lang/Object;" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getContext" desc="()Lcom/poc/hss/fasttrack/converter/FieldFetchContext;" line="17"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/Object;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)V" line="7"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/converter/ConverterContext$ConverterContextBuilder;" line="7"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setObj" desc="(Ljava/lang/Object;)V" line="6"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setContext" desc="(Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)V" line="6"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="6"><counter type="INSTRUCTION" missed="16" covered="39"/><counter type="BRANCH" missed="11" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="9" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="6"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="6"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="6"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="28" covered="99"/><counter type="BRANCH" missed="13" covered="9"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="13" covered="8"/><counter type="METHOD" missed="2" covered="8"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/converter/FieldAwareConverter" sourcefilename="FieldAwareConverter.java"><method name="&lt;init&gt;" desc="()V" line="17"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="acceptSuperClass" desc="()Z" line="24"><counter type="INSTRUCTION" missed="0" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="shouldAcceptSourceType" desc="(Lorg/springframework/core/ResolvableType;)Z" line="28"><counter type="INSTRUCTION" missed="5" covered="24"/><counter type="BRANCH" missed="1" covered="5"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="1" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="shouldAcceptTargetType" desc="(Lorg/springframework/core/ResolvableType;)Z" line="35"><counter type="INSTRUCTION" missed="1" covered="8"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="matches" desc="(Lorg/springframework/core/convert/TypeDescriptor;Lorg/springframework/core/convert/TypeDescriptor;)Z" line="40"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="BRANCH" missed="1" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getConvertibleTypes" desc="()Ljava/util/Set;" line="45"><counter type="INSTRUCTION" missed="0" covered="25"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="convert" desc="(Ljava/lang/Object;Lorg/springframework/core/convert/TypeDescriptor;Lorg/springframework/core/convert/TypeDescriptor;)Ljava/lang/Object;" line="53"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="6" covered="117"/><counter type="BRANCH" missed="3" covered="11"/><counter type="LINE" missed="0" covered="20"/><counter type="COMPLEXITY" missed="3" covered="11"/><counter type="METHOD" missed="0" covered="7"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/converter/FieldFetchContext" sourcefilename="FieldFetchContext.java"><method name="addField" desc="(Ljava/util/function/Consumer;)Lcom/poc/hss/fasttrack/converter/FieldFetchContext;" line="20"><counter type="INSTRUCTION" missed="0" covered="13"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="addField" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/converter/FieldFetchContext;" line="28"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="addField" desc="(Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Lcom/poc/hss/fasttrack/converter/FieldFetchContext;" line="32"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="ifPresent" desc="(Ljava/lang/String;Ljava/util/function/Consumer;)V" line="37"><counter type="INSTRUCTION" missed="0" covered="20"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="ifPresent" desc="(Ljava/lang/String;Ljava/lang/Runnable;)V" line="44"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="isPresent" desc="(Ljava/lang/String;)Z" line="48"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="rename" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/converter/FieldFetchContext;" line="52"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="$default$fields" desc="()Ljava/util/List;" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(ZLjava/lang/String;Ljava/util/List;)V" line="11"><counter type="INSTRUCTION" missed="0" covered="12"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/converter/FieldFetchContext$FieldFetchContextBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="isFetchAll" desc="()Z" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getName" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getFields" desc="()Ljava/util/List;" line="17"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$isPresent$2" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Z" line="48"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$ifPresent$1" desc="(Ljava/lang/Runnable;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)V" line="44"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$ifPresent$0" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/converter/FieldFetchContext;)Z" line="40"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="19" covered="95"/><counter type="BRANCH" missed="4" covered="2"/><counter type="LINE" missed="1" covered="20"/><counter type="COMPLEXITY" missed="4" covered="15"/><counter type="METHOD" missed="2" covered="14"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/converter/BidirectionalConverter" sourcefilename="BidirectionalConverter.java"><method name="&lt;init&gt;" desc="()V" line="16"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getConvertibleTypes" desc="()Ljava/util/Set;" line="25"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="convert" desc="(Ljava/lang/Object;Lorg/springframework/core/convert/TypeDescriptor;Lorg/springframework/core/convert/TypeDescriptor;)Ljava/lang/Object;" line="33"><counter type="INSTRUCTION" missed="0" covered="14"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="61"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="BidirectionalConverter.java"><line nr="16" mi="0" ci="2" mb="0" cb="0"/><line nr="17" mi="0" ci="6" mb="0" cb="0"/><line nr="18" mi="0" ci="6" mb="0" cb="0"/><line nr="19" mi="0" ci="6" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="25" mi="0" ci="4" mb="0" cb="0"/><line nr="26" mi="0" ci="10" mb="0" cb="0"/><line nr="27" mi="0" ci="10" mb="0" cb="0"/><line nr="28" mi="0" ci="2" mb="0" cb="0"/><line nr="33" mi="0" ci="6" mb="0" cb="2"/><line nr="34" mi="0" ci="4" mb="0" cb="0"/><line nr="36" mi="0" ci="4" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="61"/><counter type="BRANCH" missed="0" covered="2"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="4"/><counter type="METHOD" missed="0" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="ConverterContext.java"><line nr="6" mi="28" ci="80" mb="13" cb="9"/><line nr="7" mi="8" ci="34" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="36" covered="120"/><counter type="BRANCH" missed="13" covered="9"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="14" covered="12"/><counter type="METHOD" missed="3" covered="12"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><sourcefile name="FieldFetchContext.java"><line nr="11" mi="17" ci="50" mb="1" cb="1"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="20" mi="0" ci="1" mb="0" cb="0"/><line nr="21" mi="0" ci="2" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="0" ci="5" mb="0" cb="0"/><line nr="24" mi="0" ci="2" mb="0" cb="0"/><line nr="28" mi="0" ci="7" mb="0" cb="0"/><line nr="32" mi="0" ci="5" mb="0" cb="0"/><line nr="33" mi="0" ci="2" mb="0" cb="0"/><line nr="37" mi="0" ci="3" mb="0" cb="2"/><line nr="38" mi="0" ci="7" mb="0" cb="0"/><line nr="40" mi="0" ci="14" mb="0" cb="0"/><line nr="41" mi="0" ci="1" mb="0" cb="0"/><line nr="44" mi="0" ci="8" mb="0" cb="0"/><line nr="45" mi="0" ci="1" mb="0" cb="0"/><line nr="48" mi="19" ci="0" mb="4" cb="0"/><line nr="52" mi="0" ci="3" mb="0" cb="0"/><line nr="53" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="36" covered="125"/><counter type="BRANCH" missed="5" covered="3"/><counter type="LINE" missed="1" covered="20"/><counter type="COMPLEXITY" missed="7" covered="19"/><counter type="METHOD" missed="4" covered="18"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><sourcefile name="FieldAwareConverter.java"><line nr="17" mi="0" ci="2" mb="0" cb="0"/><line nr="18" mi="0" ci="6" mb="0" cb="0"/><line nr="19" mi="0" ci="6" mb="0" cb="0"/><line nr="20" mi="0" ci="6" mb="0" cb="0"/><line nr="21" mi="0" ci="1" mb="0" cb="0"/><line nr="24" mi="0" ci="2" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="0" cb="0"/><line nr="29" mi="0" ci="3" mb="0" cb="2"/><line nr="30" mi="0" ci="7" mb="0" cb="0"/><line nr="31" mi="5" ci="11" mb="1" cb="3"/><line nr="35" mi="1" ci="8" mb="1" cb="1"/><line nr="40" mi="0" ci="14" mb="1" cb="3"/><line nr="45" mi="0" ci="4" mb="0" cb="0"/><line nr="46" mi="0" ci="10" mb="0" cb="0"/><line nr="47" mi="0" ci="9" mb="0" cb="0"/><line nr="48" mi="0" ci="2" mb="0" cb="0"/><line nr="53" mi="0" ci="5" mb="0" cb="2"/><line nr="54" mi="0" ci="3" mb="0" cb="0"/><line nr="55" mi="0" ci="7" mb="0" cb="0"/><line nr="57" mi="0" ci="8" mb="0" cb="0"/><counter type="INSTRUCTION" missed="6" covered="117"/><counter type="BRANCH" missed="3" covered="11"/><counter type="LINE" missed="0" covered="20"/><counter type="COMPLEXITY" missed="3" covered="11"/><counter type="METHOD" missed="0" covered="7"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="78" covered="423"/><counter type="BRANCH" missed="21" covered="25"/><counter type="LINE" missed="1" covered="56"/><counter type="COMPLEXITY" missed="24" covered="46"/><counter type="METHOD" missed="7" covered="40"/><counter type="CLASS" missed="0" covered="6"/></package><package name="com/poc/hss/fasttrack/dto"><class name="com/poc/hss/fasttrack/dto/PageResultDTO" sourcefilename="PageResultDTO.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/dto/PageResultDTO$PageResultDTOBuilder;)V" line="9"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getData" desc="()Ljava/util/List;" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTotal" desc="()J" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setData" desc="(Ljava/util/List;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setTotal" desc="(J)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="46" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="8"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="8"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="115" covered="0"/><counter type="BRANCH" missed="16" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="17" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/PageResultDTO$PageResultDTOBuilder" sourcefilename="PageResultDTO.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="data" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/dto/PageResultDTO$PageResultDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="total" desc="(J)Lcom/poc/hss/fasttrack/dto/PageResultDTO$PageResultDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/KeyValuePair$KeyValuePairBuilder" sourcefilename="KeyValuePair.java"><method name="&lt;init&gt;" desc="()V" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="key" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/KeyValuePair$KeyValuePairBuilder;" line="13"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="value" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/KeyValuePair$KeyValuePairBuilder;" line="13"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/KeyValuePair;" line="13"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="6" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/dto/KeyValuePair" sourcefilename="KeyValuePair.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/KeyValuePair$KeyValuePairBuilder;" line="13"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="11"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getKey" desc="()Ljava/lang/String;" line="15"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getValue" desc="()Ljava/lang/String;" line="16"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setKey" desc="(Ljava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setValue" desc="(Ljava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="12"><counter type="INSTRUCTION" missed="12" covered="43"/><counter type="BRANCH" missed="9" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="7" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="12"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="12"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="16" covered="112"/><counter type="BRANCH" missed="11" covered="11"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="9" covered="13"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="KeyValuePair.java"><line nr="10" mi="0" ci="3" mb="0" cb="0"/><line nr="11" mi="0" ci="9" mb="0" cb="0"/><line nr="12" mi="16" ci="90" mb="11" cb="11"/><line nr="13" mi="6" ci="25" mb="0" cb="0"/><line nr="15" mi="0" ci="3" mb="0" cb="0"/><line nr="16" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="22" covered="133"/><counter type="BRANCH" missed="11" covered="11"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="10" covered="17"/><counter type="METHOD" missed="1" covered="15"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><sourcefile name="PageResultDTO.java"><line nr="8" mi="98" ci="0" mb="16" cb="0"/><line nr="9" mi="33" ci="0" mb="0" cb="0"/><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="137" covered="0"/><counter type="BRANCH" missed="16" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="21" covered="0"/><counter type="METHOD" missed="13" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="159" covered="133"/><counter type="BRANCH" missed="27" covered="11"/><counter type="LINE" missed="4" covered="6"/><counter type="COMPLEXITY" missed="31" covered="17"/><counter type="METHOD" missed="14" covered="15"/><counter type="CLASS" missed="2" covered="2"/></package><package name="com/poc/hss/fasttrack/service"><class name="com/poc/hss/fasttrack/service/FieldAwareCrudService" sourcefilename="FieldAwareCrudService.java"><method name="&lt;init&gt;" desc="()V" line="5"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fromEntity" desc="(Ljava/lang/Object;)Ljava/lang/Object;" line="8"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/service/BaseCrudService" sourcefilename="BaseCrudService.java"><method name="&lt;init&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="FieldAwareCrudService.java"><line nr="5" mi="3" ci="0" mb="0" cb="0"/><line nr="8" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="BaseCrudService.java"><line nr="3" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="2" covered="0"/></package><package name="com/poc/hss/fasttrack/context"><class name="com/poc/hss/fasttrack/context/UserContext" sourcefilename="UserContext.java"><method name="&lt;init&gt;" desc="()V" line="5"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getCurrentUser" desc="()Lcom/poc/hss/fasttrack/model/LoginUser;" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setCurrentUser" desc="(Lcom/poc/hss/fasttrack/model/LoginUser;)V" line="12"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="UserContext.java"><line nr="5" mi="3" ci="0" mb="0" cb="0"/><line nr="6" mi="5" ci="0" mb="0" cb="0"/><line nr="8" mi="4" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/poc/hss/fasttrack/model"><class name="com/poc/hss/fasttrack/model/AccessOperation" sourcefilename="AccessOperation.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/model/AccessOperation$AccessOperationBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/enums/AccessOperator;Ljava/util/List;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getOperator" desc="()Lcom/poc/hss/fasttrack/enums/AccessOperator;" line="16"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getFields" desc="()Ljava/util/List;" line="17"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setOperator" desc="(Lcom/poc/hss/fasttrack/enums/AccessOperator;)V" line="13"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setFields" desc="(Ljava/util/List;)V" line="13"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="13"><counter type="INSTRUCTION" missed="14" covered="41"/><counter type="BRANCH" missed="10" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="8" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="13"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="18" covered="112"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="10" covered="12"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/Unity2Component" sourcefilename="Unity2Component.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="16"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="23"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/Unity2Component;" line="28"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="45"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="83"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/LoginUser$LoginUserBuilder" sourcefilename="LoginUser.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="staffId" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/LoginUser$LoginUserBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="displayName" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/LoginUser$LoginUserBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/model/LoginUser;" line="11"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="6" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/LoginUser" sourcefilename="LoginUser.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/model/LoginUser$LoginUserBuilder;" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getStaffId" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getDisplayName" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setStaffId" desc="(Ljava/lang/String;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setDisplayName" desc="(Ljava/lang/String;)V" line="10"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="10"><counter type="INSTRUCTION" missed="14" covered="41"/><counter type="BRANCH" missed="10" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="8" covered="2"/><counter type="METHOD" missed="0" covered="1"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="10"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="hashCode" desc="()I" line="10"><counter type="INSTRUCTION" missed="4" covered="30"/><counter type="BRANCH" missed="2" covered="2"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="2" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="18" covered="110"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="10" covered="12"/><counter type="METHOD" missed="0" covered="11"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/AccessOperation$AccessOperationBuilder" sourcefilename="AccessOperation.java"><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="operator" desc="(Lcom/poc/hss/fasttrack/enums/AccessOperator;)Lcom/poc/hss/fasttrack/model/AccessOperation$AccessOperationBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fields" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/model/AccessOperation$AccessOperationBuilder;" line="14"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/model/AccessOperation;" line="14"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="14"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="AccessOperation.java"><line nr="11" mi="0" ci="3" mb="0" cb="0"/><line nr="12" mi="0" ci="9" mb="0" cb="0"/><line nr="13" mi="18" ci="90" mb="12" cb="10"/><line nr="14" mi="8" ci="25" mb="0" cb="0"/><line nr="16" mi="0" ci="3" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="26" covered="133"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="11" covered="16"/><counter type="METHOD" missed="1" covered="15"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><sourcefile name="Unity2Component.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="9" mi="0" ci="7" mb="0" cb="0"/><line nr="10" mi="0" ci="7" mb="0" cb="0"/><line nr="11" mi="0" ci="7" mb="0" cb="0"/><line nr="12" mi="0" ci="7" mb="0" cb="0"/><line nr="16" mi="0" ci="4" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="0" ci="4" mb="0" cb="0"/><line nr="28" mi="0" ci="16" mb="0" cb="2"/><line nr="29" mi="0" ci="6" mb="0" cb="2"/><line nr="30" mi="0" ci="2" mb="0" cb="0"/><line nr="33" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="83"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="LoginUser.java"><line nr="8" mi="0" ci="3" mb="0" cb="0"/><line nr="9" mi="0" ci="9" mb="0" cb="0"/><line nr="10" mi="18" ci="88" mb="12" cb="10"/><line nr="11" mi="6" ci="25" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="24" covered="131"/><counter type="BRANCH" missed="12" covered="10"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="11" covered="16"/><counter type="METHOD" missed="1" covered="15"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="50" covered="347"/><counter type="BRANCH" missed="24" covered="24"/><counter type="LINE" missed="0" covered="27"/><counter type="COMPLEXITY" missed="22" covered="38"/><counter type="METHOD" missed="2" covered="34"/><counter type="CLASS" missed="0" covered="5"/></package><package name="com/poc/hss/fasttrack/logging"><class name="com/poc/hss/fasttrack/logging/SplitLoggingAppender" sourcefilename="SplitLoggingAppender.java"><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="append" desc="(Lch/qos/logback/classic/spi/LoggingEvent;)V" line="15"><counter type="INSTRUCTION" missed="17" covered="12"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="5" covered="4"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="split" desc="(Ljava/lang/String;I)Ljava/util/List;" line="27"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="cloneLoggingEvent" desc="(Lch/qos/logback/classic/spi/LoggingEvent;Ljava/lang/String;)Lch/qos/logback/classic/spi/LoggingEvent;" line="31"><counter type="INSTRUCTION" missed="52" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="13" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$append$1" desc="(Ljava/lang/Object;)V" line="22"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$append$0" desc="(Lch/qos/logback/classic/spi/LoggingEvent;Ljava/util/List;I)Lch/qos/logback/classic/spi/LoggingEvent;" line="21"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="87" covered="19"/><counter type="BRANCH" missed="3" covered="1"/><counter type="LINE" missed="19" covered="6"/><counter type="COMPLEXITY" missed="6" covered="3"/><counter type="METHOD" missed="4" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="SplitLoggingAppender.java"><line nr="10" mi="0" ci="3" mb="0" cb="0"/><line nr="11" mi="0" ci="4" mb="0" cb="0"/><line nr="15" mi="0" ci="3" mb="0" cb="0"/><line nr="16" mi="0" ci="3" mb="0" cb="0"/><line nr="17" mi="0" ci="5" mb="1" cb="1"/><line nr="18" mi="5" ci="0" mb="0" cb="0"/><line nr="19" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="6" ci="0" mb="0" cb="0"/><line nr="21" mi="11" ci="0" mb="0" cb="0"/><line nr="22" mi="5" ci="0" mb="0" cb="0"/><line nr="24" mi="0" ci="1" mb="0" cb="0"/><line nr="27" mi="6" ci="0" mb="0" cb="0"/><line nr="31" mi="4" ci="0" mb="0" cb="0"/><line nr="32" mi="4" ci="0" mb="0" cb="0"/><line nr="33" mi="4" ci="0" mb="0" cb="0"/><line nr="34" mi="4" ci="0" mb="0" cb="0"/><line nr="35" mi="4" ci="0" mb="0" cb="0"/><line nr="36" mi="4" ci="0" mb="0" cb="0"/><line nr="37" mi="4" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="2" cb="0"/><line nr="39" mi="8" ci="0" mb="0" cb="0"/><line nr="40" mi="4" ci="0" mb="0" cb="0"/><line nr="41" mi="4" ci="0" mb="0" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="44" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="87" covered="19"/><counter type="BRANCH" missed="3" covered="1"/><counter type="LINE" missed="19" covered="6"/><counter type="COMPLEXITY" missed="6" covered="3"/><counter type="METHOD" missed="4" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="87" covered="19"/><counter type="BRANCH" missed="3" covered="1"/><counter type="LINE" missed="19" covered="6"/><counter type="COMPLEXITY" missed="6" covered="3"/><counter type="METHOD" missed="4" covered="3"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/poc/hss/fasttrack/retry"><class name="com/poc/hss/fasttrack/retry/RetryUtils" sourcefilename="RetryUtils.java"><method name="&lt;init&gt;" desc="()V" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="executeTask" desc="(Ljava/util/function/Supplier;)Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext;" line="19"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="run" desc="(Ljava/util/function/Supplier;Ljava/time/Duration;)Ljava/lang/Object;" line="32"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$run$1" desc="(Ljava/util/function/Supplier;)Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext;" line="34"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$run$0" desc="(Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext;)Z" line="32"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="49" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="15" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext" sourcefilename="RetryUtils.java"><method name="&lt;init&gt;" desc="(Ljava/lang/Object;Ljava/lang/Exception;)V" line="41"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext$ExecutionContextBuilder;" line="41"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getData" desc="()Ljava/lang/Object;" line="43"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getException" desc="()Ljava/lang/Exception;" line="44"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setData" desc="(Ljava/lang/Object;)V" line="40"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setException" desc="(Ljava/lang/Exception;)V" line="40"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="40"><counter type="INSTRUCTION" missed="55" covered="0"/><counter type="BRANCH" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="40"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="40"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="40"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="127" covered="0"/><counter type="BRANCH" missed="22" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="21" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext$ExecutionContextBuilder" sourcefilename="RetryUtils.java"><method name="&lt;init&gt;" desc="()V" line="41"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="data" desc="(Ljava/lang/Object;)Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext$ExecutionContextBuilder;" line="41"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="exception" desc="(Ljava/lang/Exception;)Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext$ExecutionContextBuilder;" line="41"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/retry/RetryUtils$ExecutionContext;" line="41"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="41"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="29" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="5" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="RetryUtils.java"><line nr="14" mi="4" ci="0" mb="0" cb="0"/><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="19" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="21" mi="1" ci="0" mb="0" cb="0"/><line nr="22" mi="1" ci="0" mb="0" cb="0"/><line nr="23" mi="1" ci="0" mb="0" cb="0"/><line nr="24" mi="4" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="1" ci="0" mb="0" cb="0"/><line nr="27" mi="1" ci="0" mb="0" cb="0"/><line nr="32" mi="11" ci="0" mb="2" cb="0"/><line nr="33" mi="7" ci="0" mb="0" cb="0"/><line nr="34" mi="3" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="108" ci="0" mb="22" cb="0"/><line nr="41" mi="42" ci="0" mb="0" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="44" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="205" covered="0"/><counter type="BRANCH" missed="24" covered="0"/><counter type="LINE" missed="19" covered="0"/><counter type="COMPLEXITY" missed="33" covered="0"/><counter type="METHOD" missed="21" covered="0"/><counter type="CLASS" missed="3" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="205" covered="0"/><counter type="BRANCH" missed="24" covered="0"/><counter type="LINE" missed="19" covered="0"/><counter type="COMPLEXITY" missed="33" covered="0"/><counter type="METHOD" missed="21" covered="0"/><counter type="CLASS" missed="3" covered="0"/></package><package name="com/poc/hss/fasttrack/backoff/criteria"><class name="com/poc/hss/fasttrack/backoff/criteria/BackoffTerminationCriteria" sourcefilename="BackoffTerminationCriteria.java"/><sourcefile name="BackoffTerminationCriteria.java"/></package><package name="com/poc/hss/fasttrack/enums"><class name="com/poc/hss/fasttrack/enums/SqlExecutionMode" sourcefilename="SqlExecutionMode.java"><method name="&lt;clinit&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/enums/CustomApiMode" sourcefilename="CustomApiMode.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="16"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="23"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/CustomApiMode;" line="28"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="45"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="83"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/enums/DataPersistMode" sourcefilename="DataPersistMode.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="19"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/DataPersistMode;" line="24"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="17"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="55"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="11"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/enums/AccessOperator" sourcefilename="AccessOperator.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="27"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getDefinition" desc="()Ljava/lang/String;" line="31"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/AccessOperator;" line="36"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="0" covered="27"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="29" covered="42"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="5" covered="9"/><counter type="COMPLEXITY" missed="4" covered="3"/><counter type="METHOD" missed="2" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="SqlExecutionMode.java"><line nr="3" mi="0" ci="3" mb="0" cb="0"/><line nr="4" mi="0" ci="6" mb="0" cb="0"/><line nr="5" mi="0" ci="6" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="DataPersistMode.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="12" mi="0" ci="4" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="1" mb="0" cb="0"/><line nr="19" mi="0" ci="4" mb="0" cb="0"/><line nr="24" mi="0" ci="16" mb="0" cb="2"/><line nr="25" mi="0" ci="6" mb="0" cb="2"/><line nr="26" mi="0" ci="2" mb="0" cb="0"/><line nr="29" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="55"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="11"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="AccessOperator.java"><line nr="10" mi="0" ci="3" mb="0" cb="0"/><line nr="11" mi="0" ci="8" mb="0" cb="0"/><line nr="12" mi="0" ci="8" mb="0" cb="0"/><line nr="13" mi="0" ci="8" mb="0" cb="0"/><line nr="19" mi="0" ci="4" mb="0" cb="0"/><line nr="20" mi="0" ci="3" mb="0" cb="0"/><line nr="21" mi="0" ci="3" mb="0" cb="0"/><line nr="22" mi="0" ci="1" mb="0" cb="0"/><line nr="27" mi="0" ci="4" mb="0" cb="0"/><line nr="31" mi="3" ci="0" mb="0" cb="0"/><line nr="36" mi="16" ci="0" mb="2" cb="0"/><line nr="37" mi="6" ci="0" mb="2" cb="0"/><line nr="38" mi="2" ci="0" mb="0" cb="0"/><line nr="41" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="29" covered="42"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="5" covered="9"/><counter type="COMPLEXITY" missed="4" covered="3"/><counter type="METHOD" missed="2" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="CustomApiMode.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="9" mi="0" ci="7" mb="0" cb="0"/><line nr="10" mi="0" ci="7" mb="0" cb="0"/><line nr="11" mi="0" ci="7" mb="0" cb="0"/><line nr="12" mi="0" ci="7" mb="0" cb="0"/><line nr="16" mi="0" ci="4" mb="0" cb="0"/><line nr="17" mi="0" ci="3" mb="0" cb="0"/><line nr="18" mi="0" ci="1" mb="0" cb="0"/><line nr="23" mi="0" ci="4" mb="0" cb="0"/><line nr="28" mi="0" ci="16" mb="0" cb="2"/><line nr="29" mi="0" ci="6" mb="0" cb="2"/><line nr="30" mi="0" ci="2" mb="0" cb="0"/><line nr="33" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="83"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="15"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="29" covered="195"/><counter type="BRANCH" missed="4" covered="8"/><counter type="LINE" missed="5" covered="38"/><counter type="COMPLEXITY" missed="4" covered="16"/><counter type="METHOD" missed="2" covered="12"/><counter type="CLASS" missed="0" covered="4"/></package><package name="com/poc/hss/fasttrack/backoff"><class name="com/poc/hss/fasttrack/backoff/BackoffTaskFactory" sourcefilename="BackoffTaskFactory.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="create" desc="(Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;Lcom/poc/hss/fasttrack/backoff/criteria/BackoffTerminationCriteria;)Lcom/poc/hss/fasttrack/backoff/BackoffTask;" line="10"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="create" desc="(Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;Lcom/poc/hss/fasttrack/backoff/criteria/BackoffTerminationCriteria;Ljava/time/Duration;)Lcom/poc/hss/fasttrack/backoff/BackoffTask;" line="14"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/backoff/BackoffTask" sourcefilename="BackoffTask.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;Lcom/poc/hss/fasttrack/backoff/criteria/BackoffTerminationCriteria;)V" line="18"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;Lcom/poc/hss/fasttrack/backoff/criteria/BackoffTerminationCriteria;Ljava/time/Duration;)V" line="21"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="get" desc="()V" line="28"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="get" desc="(Ljava/util/function/Supplier;)Ljava/lang/Object;" line="32"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="get" desc="(Ljava/util/function/Supplier;Ljava/time/Duration;)Ljava/lang/Object;" line="36"><counter type="INSTRUCTION" missed="40" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="13" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$get$0" desc="()Ljava/lang/Object;" line="28"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="72" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="23" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/backoff/BackoffException" sourcefilename="BackoffException.java"><method name="&lt;init&gt;" desc="(Ljava/lang/Throwable;)V" line="5"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="BackoffTaskFactory.java"><line nr="8" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="6" ci="0" mb="0" cb="0"/><line nr="14" mi="7" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="BackoffException.java"><line nr="5" mi="3" ci="0" mb="0" cb="0"/><line nr="6" mi="1" ci="0" mb="0" cb="0"/><line nr="9" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="BackoffTask.java"><line nr="18" mi="6" ci="0" mb="0" cb="0"/><line nr="19" mi="1" ci="0" mb="0" cb="0"/><line nr="21" mi="2" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="25" mi="1" ci="0" mb="0" cb="0"/><line nr="28" mi="6" ci="0" mb="0" cb="0"/><line nr="29" mi="1" ci="0" mb="0" cb="0"/><line nr="32" mi="6" ci="0" mb="0" cb="0"/><line nr="36" mi="5" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="0" cb="0"/><line nr="42" mi="5" ci="0" mb="2" cb="0"/><line nr="43" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="3" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="2" cb="0"/><line nr="48" mi="5" ci="0" mb="0" cb="0"/><line nr="49" mi="2" ci="0" mb="0" cb="0"/><line nr="50" mi="1" ci="0" mb="0" cb="0"/><line nr="51" mi="5" ci="0" mb="0" cb="0"/><line nr="52" mi="1" ci="0" mb="0" cb="0"/><line nr="54" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="72" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="23" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="96" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="30" covered="0"/><counter type="COMPLEXITY" missed="13" covered="0"/><counter type="METHOD" missed="11" covered="0"/><counter type="CLASS" missed="3" covered="0"/></package><package name="com/poc/hss/fasttrack/backoff/strategy"><class name="com/poc/hss/fasttrack/backoff/strategy/FixedBackoffStrategy" sourcefilename="FixedBackoffStrategy.java"><method name="getInstance" desc="()Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;" line="14"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="ofInterval" desc="(Ljava/time/Duration;)Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;" line="20"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/time/Duration;)V" line="23"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getBackoff" desc="(Ljava/time/Duration;)Lorg/springframework/util/backoff/BackOff;" line="29"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="5" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/backoff/strategy/BackoffStrategy" sourcefilename="BackoffStrategy.java"/><class name="com/poc/hss/fasttrack/backoff/strategy/ExponentialBackoffStrategy" sourcefilename="ExponentialBackoffStrategy.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getInstance" desc="()Lcom/poc/hss/fasttrack/backoff/strategy/BackoffStrategy;" line="13"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getBackoff" desc="(Ljava/time/Duration;)Lorg/springframework/util/backoff/BackOff;" line="20"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="ExponentialBackoffStrategy.java"><line nr="8" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="2" ci="0" mb="2" cb="0"/><line nr="14" mi="4" ci="0" mb="0" cb="0"/><line nr="15" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="4" ci="0" mb="0" cb="0"/><line nr="21" mi="4" ci="0" mb="0" cb="0"/><line nr="22" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="FixedBackoffStrategy.java"><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="2" ci="0" mb="2" cb="0"/><line nr="15" mi="6" ci="0" mb="0" cb="0"/><line nr="16" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="5" ci="0" mb="0" cb="0"/><line nr="23" mi="2" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="25" mi="1" ci="0" mb="0" cb="0"/><line nr="29" mi="4" ci="0" mb="0" cb="0"/><line nr="30" mi="5" ci="0" mb="0" cb="0"/><line nr="31" mi="7" ci="0" mb="0" cb="0"/><line nr="32" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="5" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="BackoffStrategy.java"/><counter type="INSTRUCTION" missed="66" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="20" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="2" covered="0"/></package><counter type="INSTRUCTION" missed="2205" covered="1601"/><counter type="BRANCH" missed="255" covered="106"/><counter type="LINE" missed="325" covered="238"/><counter type="COMPLEXITY" missed="341" covered="175"/><counter type="METHOD" missed="194" covered="141"/><counter type="CLASS" missed="34" covered="32"/></report>