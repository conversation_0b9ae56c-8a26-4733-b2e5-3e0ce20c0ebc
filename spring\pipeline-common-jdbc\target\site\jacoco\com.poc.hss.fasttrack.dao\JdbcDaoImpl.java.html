<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JdbcDaoImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dao</a> &gt; <span class="el_source">JdbcDaoImpl.java</span></div><h1>JdbcDaoImpl.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dao;

import com.poc.hss.fasttrack.constant.Constants;
import com.poc.hss.fasttrack.dto.JdbcCriteriaDTO;
import com.poc.hss.fasttrack.enums.AccessOperator;
import com.poc.hss.fasttrack.enums.DataPersistMode;
import com.poc.hss.fasttrack.enums.PGDataType;
import com.poc.hss.fasttrack.jdbc.util.GenericJdbcUtils;
import com.poc.hss.fasttrack.model.AccessOperation;
import com.poc.hss.fasttrack.model.DbColumn;
import com.poc.hss.fasttrack.util.ConversionHelperService;
import com.poc.hss.fasttrack.util.ListUtils;
import com.poc.hss.fasttrack.util.StreamUtils;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.IncorrectResultSizeDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.SqlParameterValue;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.jdbc.core.namedparam.SqlParameterSource;
import org.springframework.jdbc.core.namedparam.SqlParameterSourceUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.sql.Types;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

import static com.poc.hss.fasttrack.constant.Constants.*;

<span class="nc" id="L44">@AllArgsConstructor</span>
public class JdbcDaoImpl implements JdbcDao {
<span class="nc" id="L46">    private static final Logger logger = LoggerFactory.getLogger(JdbcDaoImpl.class);</span>

    private final JdbcTemplate jdbcTemplate;
    private final ConversionHelperService conversionHelperService;
    private final NamedParameterJdbcTemplate namedParameterJdbcTemplate;

    @Override
    public void execute(String sql) {
<span class="nc" id="L54">        logger.debug(&quot;execute sql: &quot; + sql);</span>
<span class="nc" id="L55">        jdbcTemplate.execute(GenericJdbcUtils.secureSQLStatement(sql));</span>
<span class="nc" id="L56">    }</span>

    @Override
    public void createSchemaIfNotExist(String schema) {
<span class="nc" id="L60">        String sql = String.format(&quot;create schema if not exists %s&quot;, schema);</span>
<span class="nc" id="L61">        logger.info(&quot;createSchema: &quot; + sql);</span>
<span class="nc" id="L62">        execute(sql);</span>
<span class="nc" id="L63">    }</span>

    @Override
    public void createTableIfNotExist(String schema, String table, String transformedColumns, String primaryKey) {
<span class="nc" id="L67">        String sql = String.format(&quot;create table if not exists %s.%s (%s, Primary Key(%s))&quot;, schema, table, transformedColumns, GenericJdbcUtils.toDoubleQuoteStr(primaryKey));</span>
<span class="nc" id="L68">        logger.info(&quot;createTable: &quot; + sql);</span>
<span class="nc" id="L69">        execute(sql);</span>
<span class="nc" id="L70">    }</span>

    @Override
    public void truncateTable(String schema, String table) {
<span class="nc" id="L74">        String sql = String.format(&quot;truncate table %s.%s&quot;, schema, table);</span>
<span class="nc" id="L75">        logger.info(&quot;truncateTable: &quot; + sql);</span>
<span class="nc" id="L76">        execute(sql);</span>
<span class="nc" id="L77">    }</span>

    @Override
    public void createIndexIfNotExist(String schema, String table, String column, String index) {
<span class="nc" id="L81">        String sql = String.format(&quot;create index if not exists %s on %s.%s (%s)&quot;, index, schema, table, column);</span>
<span class="nc" id="L82">        logger.debug(&quot;createIndex: &quot; + sql);</span>
<span class="nc" id="L83">        execute(sql);</span>
<span class="nc" id="L84">    }</span>

    @Override
    public void dropIndexIfExist(String schema, String index) {
<span class="nc" id="L88">        String sql = String.format(&quot;drop index if exists %s.%s&quot;, schema, index);</span>
<span class="nc" id="L89">        logger.debug(&quot;dropIndex: &quot; + sql);</span>
<span class="nc" id="L90">        execute(sql);</span>
<span class="nc" id="L91">    }</span>

    @Override
    public void updateDefaultValue(String schema, String table, String column, String defaultValue) {
<span class="nc" id="L95">        String sql = String.format(&quot;alter table %s.%s alter column %s set default '%s'&quot;, schema, table, GenericJdbcUtils.toDoubleQuoteStr(column), defaultValue);</span>
<span class="nc" id="L96">        logger.debug(&quot;updateDefaultValue: {}&quot;, sql);</span>
<span class="nc" id="L97">        execute(sql);</span>
<span class="nc" id="L98">    }</span>

    @Override
    public void dropDefaultValue(String schema, String table, String column) {
<span class="nc" id="L102">        String sql = String.format(&quot;alter table %s.%s alter column %s drop default&quot;, schema, table, GenericJdbcUtils.toDoubleQuoteStr(column));</span>
<span class="nc" id="L103">        logger.debug(&quot;dropDefaultValue: {}&quot;, sql);</span>
<span class="nc" id="L104">        execute(sql);</span>
<span class="nc" id="L105">    }</span>

    @Override
    public List&lt;Map&lt;String, Object&gt;&gt; getColumns(String schema, String table) {
<span class="nc" id="L109">        String sql = String.format(&quot;select column_name, udt_name from information_schema.columns where table_schema=%s and table_name=%s&quot;,</span>
<span class="nc" id="L110">                schema.replace(&quot;\&quot;&quot;, &quot;'&quot;),</span>
<span class="nc" id="L111">                table.replace(&quot;\&quot;&quot;, &quot;'&quot;));</span>
<span class="nc" id="L112">        logger.debug(&quot;getColumns: &quot; + sql);</span>
<span class="nc" id="L113">        return jdbcTemplate.queryForList(sql);</span>
    }

    @Override
    public boolean isTableExist(String schema, String table) {
<span class="nc" id="L118">        String sql = String.format(&quot;select table_name from information_schema.tables where table_schema=%s and table_name=%s&quot;,</span>
<span class="nc" id="L119">                schema.replace(&quot;\&quot;&quot;, &quot;'&quot;),</span>
<span class="nc" id="L120">                table.replace(&quot;\&quot;&quot;, &quot;'&quot;));</span>
<span class="nc" id="L121">        logger.debug(&quot;isTableExist: &quot; + sql);</span>
<span class="nc" id="L122">        return CollectionUtils.isNotEmpty(jdbcTemplate.queryForList(sql, String.class));</span>
    }

    @Override
    public void addColumn(String schema, String table, List&lt;DbColumn&gt; columns) {
<span class="nc bnc" id="L127" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(columns))</span>
<span class="nc" id="L128">            return;</span>

<span class="nc" id="L130">        String alterSql = String.format(&quot;alter table %s.%s %s&quot;, schema, table,</span>
<span class="nc" id="L131">                columns.stream()</span>
<span class="nc" id="L132">                        .map(col -&gt; String.format(&quot;add column if not exists %s&quot;,</span>
<span class="nc" id="L133">                                GenericJdbcUtils.transformColumns(Collections.singletonList(col))))</span>
<span class="nc" id="L134">                        .collect(Collectors.joining(&quot;, &quot;))</span>
        );
<span class="nc" id="L136">        logger.info(&quot;alterSql: &quot; + alterSql);</span>
<span class="nc" id="L137">        execute(alterSql);</span>
<span class="nc" id="L138">    }</span>

    @Override
    public void alterDataType(String schema, String table, List&lt;DbColumn&gt; columns) {
<span class="nc bnc" id="L142" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(columns))</span>
<span class="nc" id="L143">            return;</span>
<span class="nc" id="L144">        String alterSql = String.format(&quot;alter table %s.%s %s&quot;, schema, table,</span>
<span class="nc" id="L145">                columns.stream()</span>
<span class="nc" id="L146">                        .map(col -&gt; String.format(&quot;alter column %s type %s using %s::%s&quot;,</span>
<span class="nc" id="L147">                                GenericJdbcUtils.toDoubleQuoteStr(col.getName()),</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">                                BooleanUtils.isTrue(col.getIsMultiple()) ? PGDataType.JSONB : col.getDataType(),</span>
<span class="nc" id="L149">                                GenericJdbcUtils.toDoubleQuoteStr(col.getName()),</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">                                BooleanUtils.isTrue(col.getIsMultiple()) ? PGDataType.JSONB : col.getDataType()))</span>
<span class="nc" id="L151">                        .collect(Collectors.joining(&quot;, &quot;))</span>
        );
<span class="nc" id="L153">        logger.info(&quot;alterSql: &quot; + alterSql);</span>
<span class="nc" id="L154">        execute(alterSql);</span>
<span class="nc" id="L155">    }</span>

    @Override
    public String delete(String schema, String table, String id) {
<span class="nc" id="L159">        String sql = String.format(&quot;delete from %s.%s where \&quot;_id\&quot; = '%s'&quot;, schema, table, id);</span>
<span class="nc" id="L160">        logger.debug(&quot;delete: &quot; + sql);</span>
<span class="nc" id="L161">        return String.format(&quot;No of deleted documents %d&quot;, jdbcTemplate.update(sql));</span>
    }

    @Override
    public String batchSoftDelete(String schema, String table, List&lt;String&gt; ids, Timestamp currentTime) {
<span class="nc" id="L166">        String sql = GenericJdbcUtils.genBatchSoftDeleteStatement(schema, table, ids.size());</span>
<span class="nc" id="L167">        logger.debug(&quot;batchSoftDelete sql: &quot; + sql);</span>
<span class="nc" id="L168">        Map&lt;String, String&gt; valuesMap = GenericJdbcUtils.generateInParameterValueMap(ids);</span>
<span class="nc" id="L169">        final MapSqlParameterSource mapSqlParameterSource = new MapSqlParameterSource(valuesMap);</span>
<span class="nc" id="L170">        mapSqlParameterSource.addValue(DELETED_TIME_STAMP, new SqlParameterValue(conversionHelperService.convert(PGDataType.TIMESTAMP, Integer.class), currentTime));</span>
        try {
<span class="nc" id="L172">            namedParameterJdbcTemplate.update(sql, mapSqlParameterSource);</span>
<span class="nc" id="L173">        } catch (DataAccessException e) {</span>
<span class="nc" id="L174">            logger.error(ExceptionUtils.getMessage(e));</span>
<span class="nc" id="L175">            throw e;</span>
<span class="nc" id="L176">        }</span>
<span class="nc" id="L177">        return null;</span>
    }

    @Override
    public void batchUpsert(String schema, String table, List&lt;DbColumn&gt; columns, List&lt;Map&lt;String, Object&gt;&gt; recordList, DataPersistMode dataPersistMode, Map&lt;String, AccessOperation&gt; operationMap, Timestamp currentTime) {
<span class="nc" id="L182">        batchUpsert(schema, table, Constants.ID, columns, recordList, dataPersistMode, operationMap, currentTime);</span>
<span class="nc" id="L183">    }</span>

    @Override
    public void batchUpsert(String schema, String table, String pk, List&lt;DbColumn&gt; columns, List&lt;Map&lt;String, Object&gt;&gt; recordList) {
<span class="nc" id="L187">        Timestamp currentTime = Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC));</span>
<span class="nc" id="L188">        batchUpsert(schema, table, pk, columns, recordList, DataPersistMode.OVERWRITE, new HashMap&lt;&gt;(), currentTime);</span>
<span class="nc" id="L189">    }</span>

    @Override
    public void batchUpsert(String schema, String table, String pk, List&lt;DbColumn&gt; columns, List&lt;Map&lt;String, Object&gt;&gt; recordList, DataPersistMode dataPersistMode, Map&lt;String, AccessOperation&gt; operationMap, Timestamp currentTime) {
<span class="nc bnc" id="L193" title="All 2 branches missed.">        if (dataPersistMode == DataPersistMode.PATCH) {</span>
<span class="nc" id="L194">            ListUtils.partitionBy(recordList, Map::keySet)</span>
<span class="nc" id="L195">                    .forEach(partition -&gt; {</span>
<span class="nc" id="L196">                        List&lt;DbColumn&gt; sqlColumns = columns.stream()</span>
<span class="nc" id="L197">                                .filter(col -&gt; partition.get(0).containsKey(col.getName()))</span>
<span class="nc" id="L198">                                .collect(Collectors.toList());</span>

<span class="nc" id="L200">                        batchUpsert(schema, table, pk, sqlColumns, partition, DataPersistMode.OVERWRITE, operationMap, currentTime);</span>
<span class="nc" id="L201">                    });</span>
        } else {
<span class="nc" id="L203">            String sql = GenericJdbcUtils.genUpsertStatement(schema, table, pk, columns, operationMap);</span>
<span class="nc" id="L204">            logger.debug(&quot;batchUpsert sql: {}&quot;, sql);</span>
<span class="nc" id="L205">            batchUpdate(sql, columns, recordList, currentTime);</span>
        }
<span class="nc" id="L207">    }</span>

    @Override
    public void batchInsert(String schema, String table, List&lt;DbColumn&gt; columns, List&lt;Map&lt;String, Object&gt;&gt; recordList, DataPersistMode dataPersistMode) {
<span class="nc" id="L211">        Timestamp currentTime = Timestamp.valueOf(LocalDateTime.now(ZoneOffset.UTC));</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">        if (dataPersistMode == DataPersistMode.PATCH) {</span>
<span class="nc" id="L213">            ListUtils.partitionBy(recordList, Map::keySet)</span>
<span class="nc" id="L214">                    .forEach(partition -&gt; {</span>
<span class="nc" id="L215">                        List&lt;DbColumn&gt; sqlColumns = columns.stream()</span>
<span class="nc" id="L216">                                .filter(col -&gt; partition.get(0).containsKey(col.getName()))</span>
<span class="nc" id="L217">                                .collect(Collectors.toList());</span>

<span class="nc" id="L219">                        batchInsert(schema, table, sqlColumns, partition, DataPersistMode.OVERWRITE);</span>
<span class="nc" id="L220">                    });</span>
        } else {
<span class="nc" id="L222">            String sql = GenericJdbcUtils.genInsertStatement(schema, table, columns);</span>
<span class="nc" id="L223">            logger.debug(&quot;batchInsert sql: &quot; + sql);</span>
<span class="nc" id="L224">            batchUpdate(sql, columns, recordList, currentTime);</span>
        }
<span class="nc" id="L226">    }</span>

    private void batchUpdate(String sql, List&lt;DbColumn&gt; columns, List&lt;Map&lt;String, Object&gt;&gt; recordList, Timestamp currentTime) {
<span class="nc" id="L229">        List&lt;Map&lt;String, SqlParameterValue&gt;&gt; recordListWithSqlType = recordList.stream()</span>
<span class="nc" id="L230">                .map(record -&gt; columns.stream()</span>
<span class="nc bnc" id="L231" title="All 4 branches missed.">                        .filter(column -&gt; !column.getName().equalsIgnoreCase(CREATED_TIME_STAMP) &amp;&amp; !column.getName().equalsIgnoreCase(CREATED_BY)</span>
<span class="nc bnc" id="L232" title="All 4 branches missed.">                                &amp;&amp; !column.getName().equalsIgnoreCase(UPDATED_TIME_STAMP) &amp;&amp; !column.getName().equalsIgnoreCase(UPDATED_BY))</span>
<span class="nc" id="L233">                        .collect(Collectors.toMap(DbColumn::getName, column -&gt; getSqlParameterValue(column, record))))</span>
<span class="nc" id="L234">                .peek(record -&gt; {</span>
<span class="nc" id="L235">                    record.put(CREATED_TIME_STAMP, new SqlParameterValue(conversionHelperService.convert(PGDataType.TIMESTAMP, Integer.class), currentTime));</span>
<span class="nc" id="L236">                    record.put(UPDATED_TIME_STAMP, new SqlParameterValue(conversionHelperService.convert(PGDataType.TIMESTAMP, Integer.class), currentTime));</span>
<span class="nc" id="L237">                })</span>
<span class="nc" id="L238">                .collect(Collectors.toList());</span>

<span class="nc" id="L240">        SqlParameterSource[] src = SqlParameterSourceUtils.createBatch(recordListWithSqlType);</span>
        try {
<span class="nc" id="L242">            namedParameterJdbcTemplate.batchUpdate(sql, src);</span>
<span class="nc" id="L243">        } catch (DataAccessException e) {</span>
<span class="nc" id="L244">            logger.error(ExceptionUtils.getMessage(e));</span>
<span class="nc" id="L245">            throw e;</span>
<span class="nc" id="L246">        }</span>
<span class="nc" id="L247">    }</span>

    private List&lt;Map&lt;String, Object&gt;&gt; findByCriteria(String schema, String table, JdbcCriteriaDTO criteria, Collection&lt;?&gt; param) {
<span class="nc" id="L250">        StringBuilder sb = new StringBuilder(&quot;select &quot;);</span>

<span class="nc" id="L252">        Map&lt;String, String&gt; fieldNameExpressionMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L253">        CollectionUtils.emptyIfNull(criteria.getFields())</span>
<span class="nc" id="L254">                .forEach(fieldName -&gt; fieldNameExpressionMap.put(GenericJdbcUtils.toDoubleQuoteStr(fieldName), GenericJdbcUtils.toDoubleQuoteStr(fieldName)));</span>
<span class="nc" id="L255">        CollectionUtils.emptyIfNull(criteria.getFieldAggregations())</span>
<span class="nc" id="L256">                .forEach(agg -&gt; fieldNameExpressionMap.put(</span>
<span class="nc" id="L257">                        GenericJdbcUtils.toDoubleQuoteStr(agg.getRename()),</span>
<span class="nc" id="L258">                        String.format(&quot;%s(%s)&quot;, agg.getFunction(), GenericJdbcUtils.toDoubleQuoteStr(agg.getField()))</span>
                ));

<span class="nc bnc" id="L261" title="All 2 branches missed.">        if (BooleanUtils.isTrue(criteria.getDistinct()))</span>
<span class="nc" id="L262">            sb.append(&quot;distinct &quot;);</span>

<span class="nc" id="L264">        sb.append(StringUtils.defaultIfBlank(</span>
                fieldNameExpressionMap
<span class="nc" id="L266">                        .entrySet()</span>
<span class="nc" id="L267">                        .stream()</span>
<span class="nc" id="L268">                        .map(expr -&gt; String.format(&quot;%s as %s&quot;, expr.getValue(), expr.getKey()))</span>
<span class="nc" id="L269">                        .collect(Collectors.joining(&quot;, &quot;)),</span>
                &quot;*&quot;
        ))
<span class="nc" id="L272">                .append(&quot; from &quot;)</span>
<span class="nc" id="L273">                .append(schema)</span>
<span class="nc" id="L274">                .append(&quot;.&quot;)</span>
<span class="nc" id="L275">                .append(GenericJdbcUtils.toDoubleQuoteStr(table));</span>


<span class="nc bnc" id="L278" title="All 2 branches missed.">        if (criteria.getWhere() != null)</span>
<span class="nc" id="L279">            sb.append(&quot; where &quot;).append(criteria.getWhere());</span>

<span class="nc bnc" id="L281" title="All 2 branches missed.">        if (CollectionUtils.isNotEmpty(criteria.getGroups()))</span>
<span class="nc" id="L282">            sb.append(&quot; group by &quot;).append(</span>
<span class="nc" id="L283">                    criteria.getGroups()</span>
<span class="nc" id="L284">                            .stream()</span>
<span class="nc" id="L285">                            .map(GenericJdbcUtils::toDoubleQuoteStr)</span>
<span class="nc" id="L286">                            .collect(Collectors.joining(&quot;, &quot;))</span>
            );

<span class="nc bnc" id="L289" title="All 2 branches missed.">        if (CollectionUtils.isNotEmpty(criteria.getSorts()))</span>
<span class="nc" id="L290">            sb.append(&quot; order by &quot;).append(</span>
<span class="nc" id="L291">                    criteria.getSorts().stream()</span>
<span class="nc" id="L292">                            .map(s -&gt; String.format(&quot;%s %s&quot;, GenericJdbcUtils.toDoubleQuoteStr(s.getField()), Optional.ofNullable(s.getDirection()).orElse(JdbcCriteriaDTO.Direction.ASC)))</span>
<span class="nc" id="L293">                            .collect(Collectors.joining(&quot;, &quot;))</span>
            );

<span class="nc bnc" id="L296" title="All 2 branches missed.">        if (criteria.getLimit() != null)</span>
<span class="nc" id="L297">            sb.append(&quot; limit &quot;).append(criteria.getLimit());</span>

<span class="nc bnc" id="L299" title="All 2 branches missed.">        if (criteria.getOffset() != null)</span>
<span class="nc" id="L300">            sb.append(&quot; offset &quot;).append(criteria.getOffset());</span>

<span class="nc" id="L302">        String encodedSql = GenericJdbcUtils.secureSQLStatement(sb.toString());</span>
<span class="nc" id="L303">        logger.debug(&quot;findByCriteria: &quot; + encodedSql);</span>
        try {
<span class="nc bnc" id="L305" title="All 2 branches missed.">            if (CollectionUtils.isEmpty(param))</span>
<span class="nc" id="L306">                return jdbcTemplate.queryForList(encodedSql);</span>
            else
<span class="nc" id="L308">                return jdbcTemplate.queryForList(encodedSql, param.toArray());</span>
<span class="nc" id="L309">        } catch (IncorrectResultSizeDataAccessException exception) {</span>
<span class="nc" id="L310">            return Collections.emptyList();</span>
        }
    }

    @Override
    public List&lt;JsonObject&gt; findJsonObjectByCriteria(String schema, String table, JdbcCriteriaDTO criteria) {
<span class="nc" id="L316">        return findJsonObjectByCriteria(schema, table, criteria, null);</span>
    }

    public List&lt;JsonObject&gt; findJsonObjectByCriteria(String schema, String table, JdbcCriteriaDTO criteria, Collection&lt;?&gt; param) {
<span class="nc" id="L320">        return findByCriteria(schema, table, criteria, param)</span>
<span class="nc" id="L321">                .stream()</span>
<span class="nc" id="L322">                .map(rec -&gt; rec.entrySet()</span>
<span class="nc" id="L323">                        .stream()</span>
<span class="nc" id="L324">                        .collect(StreamUtils.toNullableMap(</span>
                                Map.Entry::getKey,
<span class="nc" id="L326">                                entry -&gt; convertValue(entry.getValue())</span>
                        ))
                )
<span class="nc" id="L329">                .map(JsonObject::new)</span>
<span class="nc" id="L330">                .map(JsonObject::copy) // force JsonObject validation with internal API io.vertx.core.json.Json.checkAndCopy</span>
<span class="nc" id="L331">                .collect(Collectors.toList());</span>
    }

    private Object convertValue(Object obj) {
<span class="nc bnc" id="L335" title="All 2 branches missed.">        if (obj instanceof PGobject) {</span>
<span class="nc" id="L336">            PGobject pgObject = (PGobject) obj;</span>
<span class="nc" id="L337">            String rawValue = pgObject.getValue();</span>
<span class="nc bnc" id="L338" title="All 2 branches missed.">            if (rawValue == null)</span>
<span class="nc" id="L339">                return null;</span>
<span class="nc bnc" id="L340" title="All 2 branches missed.">            else if (rawValue.startsWith(&quot;[&quot;))</span>
<span class="nc" id="L341">                return new JsonArray(rawValue);</span>
<span class="nc bnc" id="L342" title="All 2 branches missed.">            else if (rawValue.startsWith(&quot;{&quot;))</span>
<span class="nc" id="L343">                return new JsonObject(rawValue);</span>
            else
<span class="nc" id="L345">                return rawValue;</span>
<span class="nc bnc" id="L346" title="All 2 branches missed.">        } else if (obj instanceof BigDecimal) {</span>
<span class="nc" id="L347">            BigDecimal bigDecimal = (BigDecimal) obj;</span>
<span class="nc" id="L348">            return bigDecimal.doubleValue();</span>
<span class="nc bnc" id="L349" title="All 2 branches missed.">        } else if (obj instanceof Date) {</span>
<span class="nc" id="L350">            return DateFormatUtils.format((Date) obj, Constants.API_DATE_FORMAT_FOR_TIMESTAMP_PATTERN, TimeZone.getDefault());</span>
        } else {
<span class="nc" id="L352">            return obj;</span>
        }
    }

    private SqlParameterValue getSqlParameterValue(DbColumn column, Map&lt;String, Object&gt; record) {
<span class="nc" id="L357">        Object value = Optional.ofNullable(record).map(r -&gt; r.get(column.getName())).orElse(column.getDefaultValue());</span>
<span class="nc bnc" id="L358" title="All 4 branches missed.">        Integer type = (column.getIsMultiple() != null &amp;&amp; column.getIsMultiple()) ? Types.OTHER : conversionHelperService.convert(column.getDataType(), Integer.class);</span>
<span class="nc" id="L359">        return new SqlParameterValue(type, value);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>