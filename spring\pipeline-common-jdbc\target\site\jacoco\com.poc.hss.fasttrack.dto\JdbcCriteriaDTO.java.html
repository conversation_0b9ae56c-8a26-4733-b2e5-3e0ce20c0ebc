<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JdbcCriteriaDTO.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_source">JdbcCriteriaDTO.java</span></div><h1>JdbcCriteriaDTO.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

<span class="nc bnc" id="L8" title="All 70 branches missed.">@Data</span>
<span class="nc" id="L9">@Builder</span>
public class JdbcCriteriaDTO {
<span class="nc" id="L11">    private List&lt;String&gt; fields;</span>
<span class="nc" id="L12">    private String where;</span>
<span class="nc" id="L13">    private Integer offset;</span>
<span class="nc" id="L14">    private Integer limit;</span>
<span class="nc" id="L15">    private List&lt;Sort&gt; sorts;</span>
<span class="nc" id="L16">    private List&lt;FieldAggregationDTO&gt; fieldAggregations;</span>
<span class="nc" id="L17">    private Boolean distinct;</span>
<span class="nc" id="L18">    private List&lt;String&gt; groups;</span>

<span class="nc bnc" id="L20" title="All 22 branches missed.">    @Data</span>
<span class="nc" id="L21">    @Builder</span>
    public static class Sort {
<span class="nc" id="L23">        private String field;</span>
<span class="nc" id="L24">        private Direction direction;</span>
    }

<span class="nc" id="L27">    public enum Direction {</span>
<span class="nc" id="L28">        ASC,</span>
<span class="nc" id="L29">        DESC</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>