<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">845 of 845</td><td class="ctr2">0%</td><td class="bar">122 of 122</td><td class="ctr2">0%</td><td class="ctr1">128</td><td class="ctr2">128</td><td class="ctr1">22</td><td class="ctr2">22</td><td class="ctr1">67</td><td class="ctr2">67</td><td class="ctr1">7</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a2"><a href="JdbcCriteriaDTO.html" class="el_class">JdbcCriteriaDTO</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="387" alt="387"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="70" alt="70"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">57</td><td class="ctr2" id="g0">57</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">10</td><td class="ctr1" id="j0">22</td><td class="ctr2" id="k0">22</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="FieldAggregationDTO.html" class="el_class">FieldAggregationDTO</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="168" alt="168"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="30" alt="30"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">27</td><td class="ctr2" id="g1">27</td><td class="ctr1" id="h1">5</td><td class="ctr2" id="i1">5</td><td class="ctr1" id="j1">12</td><td class="ctr2" id="k1">12</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a5"><a href="JdbcCriteriaDTO$Sort.html" class="el_class">JdbcCriteriaDTO.Sort</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="126" alt="126"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="22" alt="22"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">21</td><td class="ctr2" id="g2">21</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j3">10</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="JdbcCriteriaDTO$JdbcCriteriaDTOBuilder.html" class="el_class">JdbcCriteriaDTO.JdbcCriteriaDTOBuilder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="85" alt="85"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">11</td><td class="ctr2" id="g3">11</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j2">11</td><td class="ctr2" id="k2">11</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a1"><a href="FieldAggregationDTO$FieldAggregationDTOBuilder.html" class="el_class">FieldAggregationDTO.FieldAggregationDTOBuilder</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="36" alt="36"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j4">6</td><td class="ctr2" id="k4">6</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a6"><a href="JdbcCriteriaDTO$Sort$SortBuilder.html" class="el_class">JdbcCriteriaDTO.Sort.SortBuilder</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="28" alt="28"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j5">5</td><td class="ctr2" id="k5">5</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a3"><a href="JdbcCriteriaDTO$Direction.html" class="el_class">JdbcCriteriaDTO.Direction</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="15" alt="15"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>