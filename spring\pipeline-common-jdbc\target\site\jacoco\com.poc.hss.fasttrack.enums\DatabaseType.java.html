<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DatabaseType.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.enums</a> &gt; <span class="el_source">DatabaseType.java</span></div><h1>DatabaseType.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

<span class="fc" id="L6">public enum DatabaseType {</span>
<span class="fc" id="L7">    ORACLE(&quot;ORACLE&quot;),</span>
<span class="fc" id="L8">    POSTGRES(&quot;POSTGRES&quot;);</span>

    private String value;

<span class="fc" id="L12">    DatabaseType(String value) {</span>
<span class="fc" id="L13">        this.value = value;</span>
<span class="fc" id="L14">    }</span>

    @Override
    @JsonValue
    public String toString() {
<span class="fc" id="L19">        return String.valueOf(value);</span>
    }

    @JsonCreator
    public static DatabaseType fromValue(String text) {
<span class="fc bfc" id="L24" title="All 2 branches covered.">        for (DatabaseType b : DatabaseType.values()) {</span>
<span class="fc bfc" id="L25" title="All 2 branches covered.">            if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L26">                return b;</span>
            }
        }
<span class="fc" id="L29">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>