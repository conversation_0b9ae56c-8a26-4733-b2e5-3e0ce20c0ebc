<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PGDataType.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.enums</a> &gt; <span class="el_source">PGDataType.java</span></div><h1>PGDataType.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.enums;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

<span class="fc" id="L8">public enum PGDataType {</span>

<span class="fc" id="L10">    TEXT(&quot;text&quot;,&quot;text&quot;),</span>
<span class="fc" id="L11">    NUMERIC(&quot;numeric&quot;,&quot;number&quot;),</span>
<span class="fc" id="L12">    DATE(&quot;date&quot;,&quot;date&quot;),</span>
<span class="fc" id="L13">    TIMESTAMP(&quot;timestamp&quot;,&quot;datetime&quot;),</span>
<span class="fc" id="L14">    JSONB(&quot;jsonb&quot;,&quot;jsonb&quot;),</span>
<span class="fc" id="L15">    BOOLEAN(&quot;boolean&quot;, &quot;boolean&quot;);</span>

    private String value;

    private String displayValue;
    
<span class="nc" id="L21">    PGDataType(String value) {</span>
<span class="nc" id="L22">        this.value = value;</span>
<span class="nc" id="L23">    }</span>

<span class="fc" id="L25">    PGDataType(String value, String displayValue) {</span>
<span class="fc" id="L26">        this.value = value;</span>
<span class="fc" id="L27">        this.displayValue = displayValue;</span>
<span class="fc" id="L28">    }</span>
    
    public String getValue() {
<span class="nc" id="L31">        return value;</span>
    }

    public String getDisplayValue() {
<span class="nc" id="L35">        return displayValue;</span>
    }
    
    @Override
    @JsonValue
    public String toString() {
<span class="fc" id="L41">        return String.valueOf(value);</span>
    }

    @JsonCreator
    public static PGDataType fromValue(String text) {
<span class="nc bnc" id="L46" title="All 2 branches missed.">        for (PGDataType b : PGDataType.values()) {</span>
<span class="nc bnc" id="L47" title="All 4 branches missed.">            if (StringUtils.equals(b.value, text) || StringUtils.equals(b.displayValue, text)) {</span>
<span class="nc" id="L48">                return b;</span>
            }
        }
<span class="nc" id="L51">        return null;</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>