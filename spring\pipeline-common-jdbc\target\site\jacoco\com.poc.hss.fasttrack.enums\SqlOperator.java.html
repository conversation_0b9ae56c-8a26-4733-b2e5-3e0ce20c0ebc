<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SqlOperator.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.enums</a> &gt; <span class="el_source">SqlOperator.java</span></div><h1>SqlOperator.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.enums;

<span class="nc" id="L3">public enum SqlOperator {</span>

<span class="nc" id="L5">    EQUAL(&quot;=&quot;),</span>
<span class="nc" id="L6">    EQUALITY(&quot;==&quot;),</span>
<span class="nc" id="L7">    NOT_EQUAL(&quot;!=&quot;),</span>
<span class="nc" id="L8">    GREATER(&quot;&gt;&quot;),</span>
<span class="nc" id="L9">    LESS(&quot;&lt;&quot;),</span>
<span class="nc" id="L10">    GREATER_EQUAL(&quot;&gt;=&quot;),</span>
<span class="nc" id="L11">    LESS_EQUAL(&quot;&lt;=&quot;),</span>
<span class="nc" id="L12">    LIKE(&quot;LIKE&quot;),</span>
<span class="nc" id="L13">    IN(&quot;IN&quot;),</span>
<span class="nc" id="L14">    NOT_IN(&quot;NOT IN&quot;),</span>
<span class="nc" id="L15">    AND(&quot;AND&quot;),</span>
<span class="nc" id="L16">    OR(&quot;OR&quot;);</span>
    ;

    private final String value;

<span class="nc" id="L21">    SqlOperator(String value) {</span>
<span class="nc" id="L22">        this.value = value;</span>
<span class="nc" id="L23">    }</span>

    public static SqlOperator fromValue(String text) {
<span class="nc bnc" id="L26" title="All 2 branches missed.">        for (SqlOperator j : SqlOperator.values()) {</span>
<span class="nc bnc" id="L27" title="All 2 branches missed.">            if (String.valueOf(j.value).equals(text)) {</span>
<span class="nc" id="L28">                return j;</span>
            }
        }
<span class="nc" id="L31">        return null;</span>
    }

    public String getValue() {
<span class="nc" id="L35">        return value;</span>
    }


}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>