<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JdbcLocalDateConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.converter</a> &gt; <span class="el_source">JdbcLocalDateConverter.java</span></div><h1>JdbcLocalDateConverter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.converter;

import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Component
<span class="nc" id="L10">public class JdbcLocalDateConverter implements Converter&lt;String, LocalDate&gt; {</span>

	@Override
	public LocalDate convert(String source) {
<span class="nc bnc" id="L14" title="All 4 branches missed.">		return (source.length() == 10 &amp;&amp; source.contains(&quot;-&quot;)) </span>
<span class="nc" id="L15">				? LocalDate.parse(source) </span>
<span class="nc" id="L16">				: LocalDate.parse(source, DateTimeFormatter.BASIC_ISO_DATE);</span>
	}
	
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>