<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PGToSqlTypeConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.converter</a> &gt; <span class="el_source">PGToSqlTypeConverter.java</span></div><h1>PGToSqlTypeConverter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.converter;

import com.poc.hss.fasttrack.enums.PGDataType;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.sql.Types;

@Component
<span class="nc" id="L10">public class PGToSqlTypeConverter implements Converter&lt;PGDataType, Integer&gt;{</span>
    
    @Override
    public Integer convert(PGDataType pgDataType) {
<span class="nc bnc" id="L14" title="All 7 branches missed.">        switch(pgDataType) {</span>
            case TEXT:
<span class="nc" id="L16">                return Types.VARCHAR;</span>
            case NUMERIC:
<span class="nc" id="L18">                return Types.NUMERIC;</span>
            case DATE:
<span class="nc" id="L20">                return Types.DATE;</span>
            case TIMESTAMP:
<span class="nc" id="L22">                return Types.TIMESTAMP;</span>
            case BOOLEAN:
<span class="nc" id="L24">                return Types.BOOLEAN;</span>
            case JSONB:
<span class="nc" id="L26">                return Types.OTHER;</span>
            default:
<span class="nc" id="L28">                return Types.NULL;</span>
        }
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>