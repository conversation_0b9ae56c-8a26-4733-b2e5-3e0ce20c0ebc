<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JdbcSQLBuilder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.jdbc.sqlbuilder</a> &gt; <span class="el_class">JdbcSQLBuilder</span></div><h1>JdbcSQLBuilder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">266 of 266</td><td class="ctr2">0%</td><td class="bar">26 of 26</td><td class="ctr2">0%</td><td class="ctr1">25</td><td class="ctr2">25</td><td class="ctr1">69</td><td class="ctr2">69</td><td class="ctr1">12</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a3"><a href="JdbcSQLBuilder.java.html#L9" class="el_method">JdbcSQLBuilder()</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="43" alt="43"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h1">9</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="JdbcSQLBuilder.java.html#L108" class="el_method">toString()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="29" alt="29"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="JdbcSQLBuilder.java.html#L47" class="el_method">where(Boolean, List, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="27" alt="27"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h4">6</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="JdbcSQLBuilder.java.html#L85" class="el_method">limit(Integer)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="24" alt="24"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="JdbcSQLBuilder.java.html#L96" class="el_method">offset(Integer)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="24" alt="24"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h3">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a8"><a href="JdbcSQLBuilder.java.html#L21" class="el_method">select(List)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="22" alt="22"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h5">5</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="JdbcSQLBuilder.java.html#L30" class="el_method">from(List)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="61" height="10" title="22" alt="22"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a11"><a href="JdbcSQLBuilder.java.html#L58" class="el_method">where(List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="19" alt="19"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a7"><a href="JdbcSQLBuilder.java.html#L67" class="el_method">orderBy(List)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="19" alt="19"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="JdbcSQLBuilder.java.html#L76" class="el_method">groupBy(List)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="19" alt="19"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a4"><a href="JdbcSQLBuilder.java.html#L39" class="el_method">join(List)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="39" height="10" title="14" alt="14"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a1"><a href="JdbcSQLBuilder.java.html#L121" class="el_method">getWhereClause()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>