<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JdbcSQLBuilder.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.sqlbuilder</a> &gt; <span class="el_source">JdbcSQLBuilder.java</span></div><h1>JdbcSQLBuilder.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.sqlbuilder;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

<span class="nc" id="L9">public abstract class JdbcSQLBuilder{</span>

<span class="nc" id="L11">    protected StringBuilder selectClause = new StringBuilder();</span>
<span class="nc" id="L12">    protected StringBuilder fromClause = new StringBuilder();</span>
<span class="nc" id="L13">    protected StringBuilder joinClause = new StringBuilder();</span>
<span class="nc" id="L14">    protected StringBuilder whereClause = new StringBuilder();</span>
<span class="nc" id="L15">    protected StringBuilder orderByClause = new StringBuilder();</span>
<span class="nc" id="L16">    protected StringBuilder groupByClause = new StringBuilder();</span>
<span class="nc" id="L17">    protected StringBuilder limitClause = new StringBuilder();</span>
<span class="nc" id="L18">    protected StringBuilder offsetClause = new StringBuilder();</span>

    public JdbcSQLBuilder select(List&lt;String&gt; columns) {
<span class="nc bnc" id="L21" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(columns)) {</span>
<span class="nc" id="L22">            throw new IllegalArgumentException(&quot;Empty select condition&quot;);</span>
        }
<span class="nc" id="L24">        selectClause.append(&quot;select &quot;);</span>
<span class="nc" id="L25">        selectClause.append(String.join(&quot;, &quot;, columns));</span>
<span class="nc" id="L26">        return this;</span>
    }

    public JdbcSQLBuilder from(List&lt;String&gt; tables) {
<span class="nc bnc" id="L30" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(tables)) {</span>
<span class="nc" id="L31">            throw new IllegalArgumentException(&quot;Empty from condition&quot;);</span>
        }
<span class="nc" id="L33">        fromClause.append(&quot; from &quot;);</span>
<span class="nc" id="L34">        fromClause.append(String.join(&quot;, &quot;, tables));</span>
<span class="nc" id="L35">        return this;</span>
    }

    public JdbcSQLBuilder join(List&lt;String&gt; joinCondition) {
<span class="nc bnc" id="L39" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(joinCondition)) {</span>
<span class="nc" id="L40">           return this;</span>
        }
<span class="nc" id="L42">        joinClause.append(String.join(&quot; &quot;, joinCondition));</span>
<span class="nc" id="L43">        return this;</span>
    }

    public JdbcSQLBuilder where(Boolean enableWhereClause, List&lt;String&gt; predicates, String customWhereClause) {
<span class="nc bnc" id="L47" title="All 4 branches missed.">        if (CollectionUtils.isEmpty(predicates) &amp;&amp; StringUtils.isEmpty(customWhereClause)) {</span>
<span class="nc" id="L48">            return this;</span>
        }
<span class="nc" id="L50">        whereClause.append(&quot; where &quot;);</span>
<span class="nc bnc" id="L51" title="All 2 branches missed.">        whereClause.append(BooleanUtils.isTrue(enableWhereClause) ?</span>
<span class="nc" id="L52">                customWhereClause : String.join(&quot; and &quot;, predicates));</span>
<span class="nc" id="L53">        return this;</span>
    }

    @Deprecated
    public JdbcSQLBuilder where(List&lt;String&gt; predicates) {
<span class="nc bnc" id="L58" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(predicates)) {</span>
<span class="nc" id="L59">            return this;</span>
        }
<span class="nc" id="L61">        whereClause.append(&quot; where &quot;);</span>
<span class="nc" id="L62">        whereClause.append(String.join(&quot; and &quot;, predicates));</span>
<span class="nc" id="L63">        return this;</span>
    }

    public JdbcSQLBuilder orderBy(List&lt;String&gt; columns) {
<span class="nc bnc" id="L67" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(columns)) {</span>
<span class="nc" id="L68">            return this;</span>
        }
<span class="nc" id="L70">        orderByClause.append(&quot; order by &quot;);</span>
<span class="nc" id="L71">        orderByClause.append(String.join(&quot;, &quot;, columns));</span>
<span class="nc" id="L72">        return this;</span>
    }

    public JdbcSQLBuilder groupBy(List&lt;String&gt; columns) {
<span class="nc bnc" id="L76" title="All 2 branches missed.">        if (CollectionUtils.isEmpty(columns)) {</span>
<span class="nc" id="L77">            return this;</span>
        }
<span class="nc" id="L79">        groupByClause.append(&quot; group by &quot;);</span>
<span class="nc" id="L80">        groupByClause.append(String.join(&quot;, &quot;, columns));</span>
<span class="nc" id="L81">        return this;</span>
    }

    public JdbcSQLBuilder limit(Integer limit) {
<span class="nc bnc" id="L85" title="All 2 branches missed.">        if (limit == null)</span>
<span class="nc" id="L86">            return this;</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">        if (limit &lt; 0) {</span>
<span class="nc" id="L88">            throw new IllegalArgumentException(&quot;Limit must be positive&quot;);</span>
        }
<span class="nc" id="L90">        limitClause.append(&quot; limit &quot;);</span>
<span class="nc" id="L91">        limitClause.append(limit);</span>
<span class="nc" id="L92">        return this;</span>
    }

    public JdbcSQLBuilder offset(Integer offset) {
<span class="nc bnc" id="L96" title="All 2 branches missed.">        if (offset == null)</span>
<span class="nc" id="L97">            return this;</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">        if (offset &lt; 0) {</span>
<span class="nc" id="L99">            throw new IllegalArgumentException(&quot;Offset must be positive&quot;);</span>
        }
<span class="nc" id="L101">        offsetClause.append(&quot; offset &quot;);</span>
<span class="nc" id="L102">        offsetClause.append(offset);</span>
<span class="nc" id="L103">        return this;</span>
    }

    @Override
    public String toString() {
<span class="nc" id="L108">        return new StringBuilder()</span>
<span class="nc" id="L109">                .append(selectClause)</span>
<span class="nc" id="L110">                .append(fromClause)</span>
<span class="nc" id="L111">                .append(joinClause)</span>
<span class="nc" id="L112">                .append(whereClause)</span>
<span class="nc" id="L113">                .append(groupByClause)</span>
<span class="nc" id="L114">                .append(orderByClause)</span>
<span class="nc" id="L115">                .append(limitClause)</span>
<span class="nc" id="L116">                .append(offsetClause)</span>
<span class="nc" id="L117">                .toString();</span>
    }

    public String getWhereClause() {
<span class="nc" id="L121">        return whereClause.toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>