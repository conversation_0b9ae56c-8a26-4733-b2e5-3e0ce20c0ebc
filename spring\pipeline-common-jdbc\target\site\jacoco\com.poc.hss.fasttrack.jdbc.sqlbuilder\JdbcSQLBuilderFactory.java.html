<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>JdbcSQLBuilderFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.sqlbuilder</a> &gt; <span class="el_source">JdbcSQLBuilderFactory.java</span></div><h1>JdbcSQLBuilderFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.sqlbuilder;

import com.poc.hss.fasttrack.enums.CustomApiMode;

<span class="nc" id="L5">public class JdbcSQLBuilderFactory {</span>
    public static JdbcSQLBuilder getInstance(CustomApiMode mode){
<span class="nc bnc" id="L7" title="All 2 branches missed.">        switch (mode){</span>
            case ORACLE:
<span class="nc" id="L9">                return new OracleJdbcSQLBuilder();</span>
            case UNITY2_POSTGRES, POSTGRES, CLOUD_POSTGRES, DENODO:
            default:
<span class="nc" id="L12">                return new PostgresJdbcSQLBuilder();</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>