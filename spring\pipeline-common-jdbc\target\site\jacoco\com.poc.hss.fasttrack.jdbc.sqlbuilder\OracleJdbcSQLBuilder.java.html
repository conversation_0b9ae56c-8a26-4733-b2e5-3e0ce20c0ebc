<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>OracleJdbcSQLBuilder.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.sqlbuilder</a> &gt; <span class="el_source">OracleJdbcSQLBuilder.java</span></div><h1>OracleJdbcSQLBuilder.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.sqlbuilder;

<span class="nc" id="L3">public class OracleJdbcSQLBuilder extends JdbcSQLBuilder {</span>
    public JdbcSQLBuilder limit(Integer limit) {
<span class="nc bnc" id="L5" title="All 2 branches missed.">        if (limit == null)</span>
<span class="nc" id="L6">            return this;</span>
<span class="nc bnc" id="L7" title="All 2 branches missed.">        if (limit &lt; 0) {</span>
<span class="nc" id="L8">            throw new IllegalArgumentException(&quot;Limit must be positive&quot;);</span>
        }
<span class="nc bnc" id="L10" title="All 2 branches missed.">        if (limit &gt; 0) {</span>
<span class="nc" id="L11">            limitClause.append(&quot; FETCH NEXT &quot;);</span>
<span class="nc" id="L12">            limitClause.append(limit);</span>
<span class="nc" id="L13">            limitClause.append(&quot; ROWS ONLY &quot;);</span>
        }
<span class="nc" id="L15">        return this;</span>
    }

    public JdbcSQLBuilder offset(Integer offset) {
<span class="nc bnc" id="L19" title="All 2 branches missed.">        if (offset == null)</span>
<span class="nc" id="L20">            return this;</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">        if (offset &lt; 0) {</span>
<span class="nc" id="L22">            throw new IllegalArgumentException(&quot;Offset must be positive&quot;);</span>
        }
<span class="nc bnc" id="L24" title="All 2 branches missed.">        if (offset &gt; 0) {</span>
<span class="nc" id="L25">            offsetClause.append(&quot; OFFSET &quot;);</span>
<span class="nc" id="L26">            offsetClause.append(offset);</span>
<span class="nc" id="L27">            offsetClause.append(&quot; ROWS &quot;);</span>
        }
<span class="nc" id="L29">        return this;</span>
    }
    @Override
    public String toString() {
<span class="nc" id="L33">        return new StringBuilder()</span>
<span class="nc" id="L34">                .append(selectClause)</span>
<span class="nc" id="L35">                .append(fromClause)</span>
<span class="nc" id="L36">                .append(joinClause)</span>
<span class="nc" id="L37">                .append(whereClause)</span>
<span class="nc" id="L38">                .append(groupByClause)</span>
<span class="nc" id="L39">                .append(orderByClause)</span>
<span class="nc" id="L40">                .append(offsetClause)</span>
<span class="nc" id="L41">                .append(limitClause)</span>
<span class="nc" id="L42">                .toString();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>