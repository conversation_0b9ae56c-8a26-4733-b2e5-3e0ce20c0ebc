<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.jdbc.sqlbuilder</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <span class="el_package">com.poc.hss.fasttrack.jdbc.sqlbuilder</span></div><h1>com.poc.hss.fasttrack.jdbc.sqlbuilder</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">381 of 381</td><td class="ctr2">0%</td><td class="bar">40 of 40</td><td class="ctr2">0%</td><td class="ctr1">39</td><td class="ctr2">39</td><td class="ctr1">103</td><td class="ctr2">103</td><td class="ctr1">19</td><td class="ctr2">19</td><td class="ctr1">4</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a0"><a href="JdbcSQLBuilder.java.html" class="el_source">JdbcSQLBuilder.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="266" alt="266"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="26" alt="26"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">25</td><td class="ctr2" id="g0">25</td><td class="ctr1" id="h0">69</td><td class="ctr2" id="i0">69</td><td class="ctr1" id="j0">12</td><td class="ctr2" id="k0">12</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="OracleJdbcSQLBuilder.java.html" class="el_source">OracleJdbcSQLBuilder.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="43" height="10" title="96" alt="96"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h1">29</td><td class="ctr2" id="i1">29</td><td class="ctr1" id="j1">4</td><td class="ctr2" id="k1">4</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="JdbcSQLBuilderFactory.java.html" class="el_source">JdbcSQLBuilderFactory.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="16" alt="16"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h2">4</td><td class="ctr2" id="i2">4</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k2">2</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="PostgresJdbcSQLBuilder.java.html" class="el_source">PostgresJdbcSQLBuilder.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>