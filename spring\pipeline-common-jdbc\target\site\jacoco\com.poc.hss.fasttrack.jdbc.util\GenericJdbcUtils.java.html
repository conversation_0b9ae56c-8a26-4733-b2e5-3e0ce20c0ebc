<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GenericJdbcUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.util</a> &gt; <span class="el_source">GenericJdbcUtils.java</span></div><h1>GenericJdbcUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.util;

import com.poc.hss.fasttrack.enums.PGDataType;
import com.poc.hss.fasttrack.model.AccessOperation;
import com.poc.hss.fasttrack.model.DbColumn;
import com.poc.hss.fasttrack.util.EsapiUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.poc.hss.fasttrack.constant.Constants.*;

<span class="nc" id="L23">public class GenericJdbcUtils {</span>

<span class="fc" id="L25">    private static Map&lt;String, String&gt; cachedStatementMap = new ConcurrentHashMap&lt;&gt;();</span>

    public static String getDoubleQuotedSchemaName(String projectId) {
<span class="nc bnc" id="L28" title="All 2 branches missed.">        return projectId == null ? null : toDoubleQuoteStr(getSchemaName(projectId));</span>
    }

    public static String getSchemaName(String projectId) {
<span class="nc bnc" id="L32" title="All 2 branches missed.">        return projectId == null ? null : projectId.replace(&quot;-&quot;, &quot;_&quot;);</span>
    }

    public static String transformTableName(String tableName) {
<span class="nc" id="L36">        return toDoubleQuoteStr(tableName);</span>
    }

    public static String toDoubleQuoteStr(String source) {
<span class="fc" id="L40">        return String.format(&quot;\&quot;%s\&quot;&quot;, source);</span>
    }

    public static String removeDoubleQuoteStr(String source) {
<span class="nc" id="L44">        return source.replace(&quot;\&quot;&quot;, &quot;&quot;);</span>
    }

    public static String toSingleQuoteStr(String source) {
<span class="nc" id="L48">        return String.format(&quot;'%s'&quot;, source);</span>
    }

    public static String toParenthesesStr(String source) {
<span class="nc" id="L52">        return String.format(&quot;(%s)&quot;, source);</span>
    }

    public static String transformIdList(List&lt;String&gt; ids) {
<span class="nc" id="L56">        return ids.stream()</span>
<span class="nc" id="L57">                .map(GenericJdbcUtils::toSingleQuoteStr)</span>
<span class="nc" id="L58">                .collect(Collectors.joining(&quot;,&quot;));</span>
    }

    public static String transformColumns(List&lt;DbColumn&gt; columns) {
<span class="nc bnc" id="L62" title="All 2 branches missed.">        return CollectionUtils.isEmpty(columns) ? StringUtils.EMPTY :</span>
<span class="nc" id="L63">                columns.stream()</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">                        .map(col -&gt; String.format(&quot;%s %s&quot;, GenericJdbcUtils.toDoubleQuoteStr(col.getName()), BooleanUtils.isTrue(col.getIsMultiple()) ? PGDataType.JSONB.getValue() : col.getDataType().getValue()))</span>
<span class="nc" id="L65">                        .collect(Collectors.joining(&quot;,&quot;));</span>
    }

    public static String genUpsertStatement(String schema, String table, List&lt;DbColumn&gt; columns) {
<span class="fc" id="L69">        return genUpsertStatement(schema, table, ID, columns, Collections.EMPTY_MAP);</span>
    }

    public static String genUpsertStatement(String schema, String table, List&lt;DbColumn&gt; columns, Map&lt;String, AccessOperation&gt; operationMap) {
<span class="fc" id="L73">        return genUpsertStatement(schema, table, ID, columns, operationMap);</span>
    }

    public static String genUpsertStatement(String schema, String table, String pk, List&lt;DbColumn&gt; columns) {
<span class="nc" id="L77">        return genUpsertStatement(schema, table, pk, columns, Collections.EMPTY_MAP);</span>
    }

    public static String genUpsertStatement(String schema, String table, String pk, List&lt;DbColumn&gt; columns, Map&lt;String, AccessOperation&gt; operationMap) {
<span class="fc" id="L81">        String key = String.format(&quot;%s-%s-%s-%s-%s&quot;, schema, table, pk, columns, operationMap);</span>
<span class="pc bpc" id="L82" title="1 of 2 branches missed.">        if (cachedStatementMap.containsKey(key)) {</span>
<span class="nc" id="L83">            return cachedStatementMap.get(key);</span>
        }
<span class="fc" id="L85">        final List&lt;String&gt; filteredColumns = columns.stream().map(DbColumn::getName).filter(e -&gt;</span>
<span class="pc bpc" id="L86" title="2 of 4 branches missed.">                !e.equalsIgnoreCase(CREATED_TIME_STAMP) &amp;&amp; !e.equalsIgnoreCase(CREATED_BY) &amp;&amp;</span>
<span class="pc bpc" id="L87" title="2 of 4 branches missed.">                        !e.equalsIgnoreCase(UPDATED_TIME_STAMP) &amp;&amp; !e.equalsIgnoreCase(UPDATED_BY) &amp;&amp;</span>
<span class="pc bpc" id="L88" title="2 of 4 branches missed.">                        !e.equalsIgnoreCase(DELETED_TIME_STAMP) &amp;&amp; !e.equalsIgnoreCase(DELETED_BY)</span>
<span class="fc" id="L89">        ).collect(Collectors.toList());</span>

<span class="fc" id="L91">        String sql = String.format(&quot;insert into %s.%s (%s, %s, %s, %s, %s) values (%s, 'System', %s, 'System', %s) on conflict (%s) do update set %s = %s, %s = 'System', %s = null, %s = null, %s&quot;,</span>
                schema,
                table,
<span class="fc" id="L94">                toDoubleQuoteStr(CREATED_TIME_STAMP),</span>
<span class="fc" id="L95">                toDoubleQuoteStr(CREATED_BY),</span>
<span class="fc" id="L96">                toDoubleQuoteStr(UPDATED_TIME_STAMP),</span>
<span class="fc" id="L97">                toDoubleQuoteStr(UPDATED_BY),</span>
<span class="fc" id="L98">                filteredColumns.stream().map(GenericJdbcUtils::toDoubleQuoteStr).collect(Collectors.joining(&quot;,&quot;)),</span>
                &quot;:&quot; + CREATED_TIME_STAMP,
                &quot;:&quot; + UPDATED_TIME_STAMP,
<span class="fc" id="L101">                filteredColumns.stream().map(p -&gt; &quot;:&quot; + p).collect(Collectors.joining(&quot;,&quot;)),</span>
                pk,
<span class="fc" id="L103">                toDoubleQuoteStr(UPDATED_TIME_STAMP),</span>
                &quot;:&quot; + UPDATED_TIME_STAMP,
<span class="fc" id="L105">                toDoubleQuoteStr(UPDATED_BY),</span>
<span class="fc" id="L106">                toDoubleQuoteStr(DELETED_TIME_STAMP),</span>
<span class="fc" id="L107">                toDoubleQuoteStr(DELETED_BY),</span>
                // for update -&gt; set &quot;key&quot;='value'
<span class="fc" id="L109">                filteredColumns.stream()</span>
<span class="pc bpc" id="L110" title="1 of 2 branches missed.">                        .filter(r -&gt; !StringUtils.equalsIgnoreCase(&quot;_id&quot;, r))</span>
<span class="fc" id="L111">                        .map(r -&gt; {</span>
<span class="fc bfc" id="L112" title="All 2 branches covered.">                            if (operationMap.containsKey(r)) {</span>
<span class="fc" id="L113">                                final AccessOperation accessOperation = operationMap.get(r);</span>
<span class="fc" id="L114">                                String field1 = r;</span>
<span class="fc" id="L115">                                String field2 = r;</span>
<span class="fc bfc" id="L116" title="All 2 branches covered.">                                if (accessOperation.getFields() != null) {</span>
<span class="pc bpc" id="L117" title="1 of 2 branches missed.">                                    if (accessOperation.getFields().size() &gt; 0)</span>
<span class="fc" id="L118">                                        field1 = accessOperation.getFields().get(0);</span>
<span class="pc bpc" id="L119" title="1 of 2 branches missed.">                                    if (accessOperation.getFields().size() &gt; 1)</span>
<span class="fc" id="L120">                                        field2 = accessOperation.getFields().get(1);</span>
                                }
                                //table.column means original value, EXCLUDED.column mean new value
<span class="fc" id="L123">                                return String.format(</span>
<span class="fc" id="L124">                                        String.format(&quot;%s=%s&quot;, toDoubleQuoteStr(r), accessOperation.getOperator().getDefinition()),</span>
<span class="fc" id="L125">                                        table + &quot;.&quot; + toDoubleQuoteStr(field1), field2);</span>
                            } else {
<span class="fc" id="L127">                                return String.format(&quot;%s=:%s&quot;, toDoubleQuoteStr(r), r);</span>
                            }
                        })
<span class="fc" id="L130">                        .collect(Collectors.joining(&quot;,&quot;)));</span>
<span class="fc" id="L131">        final String securedSQL = secureSQLStatement(sql);</span>
<span class="fc" id="L132">        cachedStatementMap.put(key, securedSQL);</span>
<span class="fc" id="L133">        return securedSQL;</span>
    }

    public static String genInsertStatement(String schema, String table, List&lt;DbColumn&gt; columns) {
<span class="nc" id="L137">        String currentTime = LocalDateTime.now(ZoneOffset.UTC).toString();</span>
<span class="nc" id="L138">        final List&lt;String&gt; filteredColumns = columns.stream().map(DbColumn::getName).filter(e -&gt;</span>
<span class="nc bnc" id="L139" title="All 8 branches missed.">                !e.equalsIgnoreCase(CREATED_TIME_STAMP) &amp;&amp; !e.equalsIgnoreCase(CREATED_BY) &amp;&amp; !e.equalsIgnoreCase(UPDATED_TIME_STAMP) &amp;&amp; !e.equalsIgnoreCase(UPDATED_BY)</span>
<span class="nc" id="L140">        ).collect(Collectors.toList());</span>
<span class="nc" id="L141">        String sql = String.format(&quot;insert into %s.%s (%s, %s, %s, %s, %s) values ('%s', 'System', '%s', 'System', %s) &quot;,</span>
                schema,
                table,
<span class="nc" id="L144">                toDoubleQuoteStr(CREATED_TIME_STAMP),</span>
<span class="nc" id="L145">                toDoubleQuoteStr(CREATED_BY),</span>
<span class="nc" id="L146">                toDoubleQuoteStr(UPDATED_TIME_STAMP),</span>
<span class="nc" id="L147">                toDoubleQuoteStr(UPDATED_BY),</span>
<span class="nc" id="L148">                filteredColumns.stream().map(GenericJdbcUtils::toDoubleQuoteStr).collect(Collectors.joining(&quot;,&quot;)),</span>
                currentTime,
                currentTime,
<span class="nc" id="L151">                filteredColumns.stream().map(p -&gt; &quot;:&quot; + p).collect(Collectors.joining(&quot;,&quot;)));</span>
<span class="nc" id="L152">        return secureSQLStatement(sql);</span>
    }

    public static String genBatchSoftDeleteStatement(String schema, String table, int inParameterSize) {
<span class="nc" id="L156">        String sql = String.format(&quot;update %s.%s set %s = %s, %s = 'System' WHERE _id in ( %s )&quot;,</span>
                schema,
                table,
                DELETED_TIME_STAMP,
                &quot;:&quot; + DELETED_TIME_STAMP,
                DELETED_BY,
<span class="nc" id="L162">                genInQueryParameters(inParameterSize));</span>
<span class="nc" id="L163">        return secureSQLStatement(sql);</span>
    }

    public static &lt;T&gt; Map&lt;String, T&gt; generateInParameterValueMap(List&lt;T&gt; values) {
<span class="nc" id="L167">        Map&lt;String, T&gt; result = new HashMap&lt;&gt;();</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">        for (int i = 0; i &lt; values.size(); i++) {</span>
<span class="nc" id="L169">            result.put(&quot;arg_&quot; + i, values.get(i));</span>
        }
<span class="nc" id="L171">        return result;</span>
    }

    private static String genInQueryParameters(int inParameterSize) {
<span class="nc" id="L175">        return IntStream.range(0, inParameterSize)</span>
<span class="nc" id="L176">                .mapToObj(i -&gt; &quot;:arg_&quot; + i)</span>
<span class="nc" id="L177">                .collect(Collectors.joining(&quot;,&quot;));</span>
    }

    public static String secureSQLStatement(String sql) {
<span class="fc" id="L181">        return EsapiUtils.encode(sql);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>