<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PGUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.jdbc.util</a> &gt; <span class="el_source">PGUtils.java</span></div><h1>PGUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.jdbc.util;

import com.poc.hss.fasttrack.util.JsonUtils;
import io.vertx.core.json.DecodeException;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import lombok.SneakyThrows;
import org.apache.commons.lang.StringUtils;
import org.postgresql.util.PGobject;

import com.poc.hss.fasttrack.enums.PGDataType;

public class PGUtils {

    private PGUtils() {
    }

<span class="nc" id="L18">    @SneakyThrows</span>
    public static PGobject createPGObject(String value) {
<span class="nc" id="L20">        PGobject pgObject = new PGobject();</span>
<span class="nc" id="L21">        pgObject.setType(PGDataType.JSONB.toString());</span>
<span class="nc" id="L22">        pgObject.setValue(escapeNullCharacter(value));</span>
<span class="nc" id="L23">        return pgObject;</span>
    }
    
    public static String escapeNullCharacter(String value) {
        // handle JSONB serialization case
<span class="nc bnc" id="L28" title="All 2 branches missed.">        if (StringUtils.contains(value, &quot;\\u0000&quot;)) {</span>
<span class="nc" id="L29">            return StringUtils.replace(value, &quot;\\u0000&quot;, StringUtils.EMPTY);</span>
        }
        
<span class="nc bnc" id="L32" title="All 2 branches missed.">        if (StringUtils.contains(value, Character.MIN_VALUE)) {</span>
<span class="nc" id="L33">            return StringUtils.replace(value, String.valueOf(Character.MIN_VALUE), StringUtils.EMPTY);</span>
        } 
<span class="nc" id="L35">        return value;</span>
    }

    public static Object serializePGObject(PGobject input) {
<span class="nc" id="L39">        String value = input.getValue();</span>
        try {
<span class="nc bnc" id="L41" title="All 2 branches missed.">            if (JsonUtils.isJsonArray(value))</span>
<span class="nc" id="L42">                return new JsonArray(value);</span>
            else
<span class="nc" id="L44">                return new JsonObject(value);</span>
<span class="nc" id="L45">        } catch (DecodeException e) {</span>
<span class="nc" id="L46">            return value;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>