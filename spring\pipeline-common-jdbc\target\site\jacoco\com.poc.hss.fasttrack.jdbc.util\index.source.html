<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.jdbc.util</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <span class="el_package">com.poc.hss.fasttrack.jdbc.util</span></div><h1>com.poc.hss.fasttrack.jdbc.util</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">333 of 634</td><td class="ctr2">47%</td><td class="bar">34 of 48</td><td class="ctr2">29%</td><td class="ctr1">43</td><td class="ctr2">55</td><td class="ctr1">54</td><td class="ctr2">97</td><td class="ctr1">21</td><td class="ctr2">31</td><td class="ctr1">1</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a0"><a href="GenericJdbcUtils.java.html" class="el_source">GenericJdbcUtils.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="57" height="10" title="276" alt="276"/><img src="../jacoco-resources/greenbar.gif" width="62" height="10" title="301" alt="301"/></td><td class="ctr2" id="c0">52%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="28" alt="28"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">33%</td><td class="ctr1" id="f0">37</td><td class="ctr2" id="g0">49</td><td class="ctr1" id="h0">38</td><td class="ctr2" id="i0">81</td><td class="ctr1" id="j0">18</td><td class="ctr2" id="k0">28</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="PGUtils.java.html" class="el_source">PGUtils.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="57" alt="57"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k1">3</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>