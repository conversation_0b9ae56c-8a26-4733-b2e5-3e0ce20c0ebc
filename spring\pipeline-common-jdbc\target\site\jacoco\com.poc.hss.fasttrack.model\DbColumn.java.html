<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>DbColumn.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-jdbc</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">DbColumn.java</span></div><h1>DbColumn.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import com.poc.hss.fasttrack.enums.PGDataType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static com.poc.hss.fasttrack.constant.Constants.*;

<span class="pc bnc" id="L16" title="All 54 branches missed.">@Data</span>
<span class="pc" id="L17">@Builder</span>
<span class="nc" id="L18">@NoArgsConstructor</span>
<span class="fc" id="L19">@AllArgsConstructor</span>
public class DbColumn {

<span class="fc" id="L22">    private String name;</span>
<span class="pc" id="L23">    private PGDataType dataType = PGDataType.TEXT;</span>
<span class="pc" id="L24">    private Boolean isIndexed = false;</span>
<span class="pc" id="L25">    private Boolean isMultiple = false;</span>
<span class="pc" id="L26">    private List&lt;DbColumn&gt; contentSchema = Collections.emptyList();</span>
<span class="fc" id="L27">    private String defaultValue;</span>

<span class="fc" id="L29">    private static final List&lt;DbColumn&gt; RESERVED_COLUMN_LIST = new ArrayList&lt;&gt;();</span>

    static {
<span class="fc" id="L32">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(TRACE_ID).dataType(PGDataType.TEXT).isIndexed(false).build());</span>
<span class="fc" id="L33">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(ID).dataType(PGDataType.TEXT).isIndexed(false).build());</span>
<span class="fc" id="L34">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(CREATED_BY).dataType(PGDataType.TEXT).isIndexed(false).build());</span>
<span class="fc" id="L35">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(CREATED_TIME_STAMP).dataType(PGDataType.TIMESTAMP).isIndexed(false).build());</span>
<span class="fc" id="L36">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(DELETED_BY).dataType(PGDataType.TEXT).isIndexed(false).build());</span>
<span class="fc" id="L37">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(DELETED_TIME_STAMP).dataType(PGDataType.TIMESTAMP).isIndexed(false).build());</span>
<span class="fc" id="L38">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(UPDATED_BY).dataType(PGDataType.TEXT).isIndexed(false).build());</span>
<span class="fc" id="L39">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(UPDATED_TIME_STAMP).dataType(PGDataType.TIMESTAMP).isIndexed(false).build());</span>
<span class="fc" id="L40">        RESERVED_COLUMN_LIST.add(DbColumn.builder().name(BATCH_ID).dataType(PGDataType.TEXT).isIndexed(false).build());</span>
<span class="fc" id="L41">    }</span>

    public static List&lt;DbColumn&gt; getReservedColumns() {
<span class="nc" id="L44">        return RESERVED_COLUMN_LIST;</span>
    }

    public static List&lt;String&gt; getReservedColumnNames() {
<span class="nc" id="L48">        return getReservedColumns().stream()</span>
<span class="nc" id="L49">                .map(DbColumn::getName)</span>
<span class="nc" id="L50">                .collect(Collectors.toList());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>