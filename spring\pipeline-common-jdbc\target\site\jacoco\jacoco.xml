<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="pipeline-common-jdbc"><sessioninfo id="H344L0L63UFKL6Z-ff9f3a" start="1752767395586" dump="1752767396792"/><package name="com/poc/hss/fasttrack/dao"><class name="com/poc/hss/fasttrack/dao/JdbcDao" sourcefilename="JdbcDao.java"/><class name="com/poc/hss/fasttrack/dao/JdbcDaoImpl" sourcefilename="JdbcDaoImpl.java"><method name="execute" desc="(Ljava/lang/String;)V" line="54"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createSchemaIfNotExist" desc="(Ljava/lang/String;)V" line="60"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createTableIfNotExist" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="67"><counter type="INSTRUCTION" missed="30" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="truncateTable" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="74"><counter type="INSTRUCTION" missed="21" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createIndexIfNotExist" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="81"><counter type="INSTRUCTION" missed="29" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="dropIndexIfExist" desc="(Ljava/lang/String;Ljava/lang/String;)V" line="88"><counter type="INSTRUCTION" missed="21" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="updateDefaultValue" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="95"><counter type="INSTRUCTION" missed="30" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="dropDefaultValue" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="102"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getColumns" desc="(Ljava/lang/String;Ljava/lang/String;)Ljava/util/List;" line="109"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="isTableExist" desc="(Ljava/lang/String;Ljava/lang/String;)Z" line="118"><counter type="INSTRUCTION" missed="30" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="addColumn" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V" line="127"><counter type="INSTRUCTION" missed="35" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="alterDataType" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V" line="142"><counter type="INSTRUCTION" missed="35" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="delete" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="159"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchSoftDelete" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/sql/Timestamp;)Ljava/lang/String;" line="166"><counter type="INSTRUCTION" missed="49" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchUpsert" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lcom/poc/hss/fasttrack/enums/DataPersistMode;Ljava/util/Map;Ljava/sql/Timestamp;)V" line="182"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchUpsert" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V" line="187"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchUpsert" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lcom/poc/hss/fasttrack/enums/DataPersistMode;Ljava/util/Map;Ljava/sql/Timestamp;)V" line="193"><counter type="INSTRUCTION" missed="39" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchInsert" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lcom/poc/hss/fasttrack/enums/DataPersistMode;)V" line="211"><counter type="INSTRUCTION" missed="38" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchUpdate" desc="(Ljava/lang/String;Ljava/util/List;Ljava/util/List;Ljava/sql/Timestamp;)V" line="229"><counter type="INSTRUCTION" missed="32" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="findByCriteria" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO;Ljava/util/Collection;)Ljava/util/List;" line="250"><counter type="INSTRUCTION" missed="145" covered="0"/><counter type="BRANCH" missed="14" covered="0"/><counter type="LINE" missed="41" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="findJsonObjectByCriteria" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO;)Ljava/util/List;" line="316"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="findJsonObjectByCriteria" desc="(Ljava/lang/String;Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO;Ljava/util/Collection;)Ljava/util/List;" line="320"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convertValue" desc="(Ljava/lang/Object;)Ljava/lang/Object;" line="335"><counter type="INSTRUCTION" missed="54" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSqlParameterValue" desc="(Lcom/poc/hss/fasttrack/model/DbColumn;Ljava/util/Map;)Lorg/springframework/jdbc/core/SqlParameterValue;" line="357"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Lorg/springframework/jdbc/core/JdbcTemplate;Lcom/poc/hss/fasttrack/util/ConversionHelperService;Lorg/springframework/jdbc/core/namedparam/NamedParameterJdbcTemplate;)V" line="44"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$getSqlParameterValue$16" desc="(Lcom/poc/hss/fasttrack/model/DbColumn;Ljava/util/Map;)Ljava/lang/Object;" line="357"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findJsonObjectByCriteria$15" desc="(Ljava/util/Map;)Ljava/util/Map;" line="322"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findJsonObjectByCriteria$14" desc="(Ljava/util/Map$Entry;)Ljava/lang/Object;" line="326"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findByCriteria$13" desc="(Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort;)Ljava/lang/String;" line="292"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findByCriteria$12" desc="(Ljava/util/Map$Entry;)Ljava/lang/String;" line="268"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findByCriteria$11" desc="(Ljava/util/Map;Lcom/poc/hss/fasttrack/dto/FieldAggregationDTO;)V" line="256"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findByCriteria$10" desc="(Ljava/util/Map;Ljava/lang/String;)V" line="254"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchUpdate$9" desc="(Ljava/sql/Timestamp;Ljava/util/Map;)V" line="235"><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchUpdate$8" desc="(Ljava/util/List;Ljava/util/Map;)Ljava/util/Map;" line="230"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchUpdate$7" desc="(Ljava/util/Map;Lcom/poc/hss/fasttrack/model/DbColumn;)Lorg/springframework/jdbc/core/SqlParameterValue;" line="233"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchUpdate$6" desc="(Lcom/poc/hss/fasttrack/model/DbColumn;)Z" line="231"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchInsert$5" desc="(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)V" line="215"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchInsert$4" desc="(Ljava/util/List;Lcom/poc/hss/fasttrack/model/DbColumn;)Z" line="216"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchUpsert$3" desc="(Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/Map;Ljava/sql/Timestamp;Ljava/util/List;)V" line="196"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$batchUpsert$2" desc="(Ljava/util/List;Lcom/poc/hss/fasttrack/model/DbColumn;)Z" line="197"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$alterDataType$1" desc="(Lcom/poc/hss/fasttrack/model/DbColumn;)Ljava/lang/String;" line="146"><counter type="INSTRUCTION" missed="39" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$addColumn$0" desc="(Lcom/poc/hss/fasttrack/model/DbColumn;)Ljava/lang/String;" line="132"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="46"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="1065" covered="0"/><counter type="BRANCH" missed="50" covered="0"/><counter type="LINE" missed="199" covered="0"/><counter type="COMPLEXITY" missed="68" covered="0"/><counter type="METHOD" missed="43" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="JdbcDaoImpl.java"><line nr="44" mi="12" ci="0" mb="0" cb="0"/><line nr="46" mi="4" ci="0" mb="0" cb="0"/><line nr="54" mi="4" ci="0" mb="0" cb="0"/><line nr="55" mi="5" ci="0" mb="0" cb="0"/><line nr="56" mi="1" ci="0" mb="0" cb="0"/><line nr="60" mi="9" ci="0" mb="0" cb="0"/><line nr="61" mi="4" ci="0" mb="0" cb="0"/><line nr="62" mi="3" ci="0" mb="0" cb="0"/><line nr="63" mi="1" ci="0" mb="0" cb="0"/><line nr="67" mi="22" ci="0" mb="0" cb="0"/><line nr="68" mi="4" ci="0" mb="0" cb="0"/><line nr="69" mi="3" ci="0" mb="0" cb="0"/><line nr="70" mi="1" ci="0" mb="0" cb="0"/><line nr="74" mi="13" ci="0" mb="0" cb="0"/><line nr="75" mi="4" ci="0" mb="0" cb="0"/><line nr="76" mi="3" ci="0" mb="0" cb="0"/><line nr="77" mi="1" ci="0" mb="0" cb="0"/><line nr="81" mi="21" ci="0" mb="0" cb="0"/><line nr="82" mi="4" ci="0" mb="0" cb="0"/><line nr="83" mi="3" ci="0" mb="0" cb="0"/><line nr="84" mi="1" ci="0" mb="0" cb="0"/><line nr="88" mi="13" ci="0" mb="0" cb="0"/><line nr="89" mi="4" ci="0" mb="0" cb="0"/><line nr="90" mi="3" ci="0" mb="0" cb="0"/><line nr="91" mi="1" ci="0" mb="0" cb="0"/><line nr="95" mi="22" ci="0" mb="0" cb="0"/><line nr="96" mi="4" ci="0" mb="0" cb="0"/><line nr="97" mi="3" ci="0" mb="0" cb="0"/><line nr="98" mi="1" ci="0" mb="0" cb="0"/><line nr="102" mi="18" ci="0" mb="0" cb="0"/><line nr="103" mi="4" ci="0" mb="0" cb="0"/><line nr="104" mi="3" ci="0" mb="0" cb="0"/><line nr="105" mi="1" ci="0" mb="0" cb="0"/><line nr="109" mi="10" ci="0" mb="0" cb="0"/><line nr="110" mi="7" ci="0" mb="0" cb="0"/><line nr="111" mi="2" ci="0" mb="0" cb="0"/><line nr="112" mi="4" ci="0" mb="0" cb="0"/><line nr="113" mi="5" ci="0" mb="0" cb="0"/><line nr="118" mi="10" ci="0" mb="0" cb="0"/><line nr="119" mi="7" ci="0" mb="0" cb="0"/><line nr="120" mi="2" ci="0" mb="0" cb="0"/><line nr="121" mi="4" ci="0" mb="0" cb="0"/><line nr="122" mi="7" ci="0" mb="0" cb="0"/><line nr="127" mi="3" ci="0" mb="2" cb="0"/><line nr="128" mi="1" ci="0" mb="0" cb="0"/><line nr="130" mi="16" ci="0" mb="0" cb="0"/><line nr="131" mi="2" ci="0" mb="0" cb="0"/><line nr="132" mi="10" ci="0" mb="0" cb="0"/><line nr="133" mi="3" ci="0" mb="0" cb="0"/><line nr="134" mi="3" ci="0" mb="0" cb="0"/><line nr="136" mi="4" ci="0" mb="0" cb="0"/><line nr="137" mi="3" ci="0" mb="0" cb="0"/><line nr="138" mi="1" ci="0" mb="0" cb="0"/><line nr="142" mi="3" ci="0" mb="2" cb="0"/><line nr="143" mi="1" ci="0" mb="0" cb="0"/><line nr="144" mi="16" ci="0" mb="0" cb="0"/><line nr="145" mi="2" ci="0" mb="0" cb="0"/><line nr="146" mi="10" ci="0" mb="0" cb="0"/><line nr="147" mi="5" ci="0" mb="0" cb="0"/><line nr="148" mi="12" ci="0" mb="2" cb="0"/><line nr="149" mi="5" ci="0" mb="0" cb="0"/><line nr="150" mi="9" ci="0" mb="2" cb="0"/><line nr="151" mi="3" ci="0" mb="0" cb="0"/><line nr="153" mi="4" ci="0" mb="0" cb="0"/><line nr="154" mi="3" ci="0" mb="0" cb="0"/><line nr="155" mi="1" ci="0" mb="0" cb="0"/><line nr="159" mi="17" ci="0" mb="0" cb="0"/><line nr="160" mi="4" ci="0" mb="0" cb="0"/><line nr="161" mi="13" ci="0" mb="0" cb="0"/><line nr="166" mi="6" ci="0" mb="0" cb="0"/><line nr="167" mi="4" ci="0" mb="0" cb="0"/><line nr="168" mi="3" ci="0" mb="0" cb="0"/><line nr="169" mi="5" ci="0" mb="0" cb="0"/><line nr="170" mi="15" ci="0" mb="0" cb="0"/><line nr="172" mi="6" ci="0" mb="0" cb="0"/><line nr="173" mi="1" ci="0" mb="0" cb="0"/><line nr="174" mi="4" ci="0" mb="0" cb="0"/><line nr="175" mi="2" ci="0" mb="0" cb="0"/><line nr="176" mi="1" ci="0" mb="0" cb="0"/><line nr="177" mi="2" ci="0" mb="0" cb="0"/><line nr="182" mi="10" ci="0" mb="0" cb="0"/><line nr="183" mi="1" ci="0" mb="0" cb="0"/><line nr="187" mi="4" ci="0" mb="0" cb="0"/><line nr="188" mi="12" ci="0" mb="0" cb="0"/><line nr="189" mi="1" ci="0" mb="0" cb="0"/><line nr="193" mi="3" ci="0" mb="2" cb="0"/><line nr="194" mi="16" ci="0" mb="0" cb="0"/><line nr="195" mi="2" ci="0" mb="0" cb="0"/><line nr="196" mi="4" ci="0" mb="0" cb="0"/><line nr="197" mi="9" ci="0" mb="0" cb="0"/><line nr="198" mi="4" ci="0" mb="0" cb="0"/><line nr="200" mi="10" ci="0" mb="0" cb="0"/><line nr="201" mi="1" ci="0" mb="0" cb="0"/><line nr="203" mi="7" ci="0" mb="0" cb="0"/><line nr="204" mi="4" ci="0" mb="0" cb="0"/><line nr="205" mi="6" ci="0" mb="0" cb="0"/><line nr="207" mi="1" ci="0" mb="0" cb="0"/><line nr="211" mi="4" ci="0" mb="0" cb="0"/><line nr="212" mi="3" ci="0" mb="2" cb="0"/><line nr="213" mi="13" ci="0" mb="0" cb="0"/><line nr="214" mi="2" ci="0" mb="0" cb="0"/><line nr="215" mi="4" ci="0" mb="0" cb="0"/><line nr="216" mi="9" ci="0" mb="0" cb="0"/><line nr="217" mi="4" ci="0" mb="0" cb="0"/><line nr="219" mi="7" ci="0" mb="0" cb="0"/><line nr="220" mi="1" ci="0" mb="0" cb="0"/><line nr="222" mi="5" ci="0" mb="0" cb="0"/><line nr="223" mi="4" ci="0" mb="0" cb="0"/><line nr="224" mi="6" ci="0" mb="0" cb="0"/><line nr="226" mi="1" ci="0" mb="0" cb="0"/><line nr="229" mi="5" ci="0" mb="0" cb="0"/><line nr="230" mi="6" ci="0" mb="0" cb="0"/><line nr="231" mi="17" ci="0" mb="4" cb="0"/><line nr="232" mi="12" ci="0" mb="4" cb="0"/><line nr="233" mi="10" ci="0" mb="0" cb="0"/><line nr="234" mi="1" ci="0" mb="0" cb="0"/><line nr="235" mi="15" ci="0" mb="0" cb="0"/><line nr="236" mi="15" ci="0" mb="0" cb="0"/><line nr="237" mi="1" ci="0" mb="0" cb="0"/><line nr="238" mi="4" ci="0" mb="0" cb="0"/><line nr="240" mi="3" ci="0" mb="0" cb="0"/><line nr="242" mi="6" ci="0" mb="0" cb="0"/><line nr="243" mi="1" ci="0" mb="0" cb="0"/><line nr="244" mi="4" ci="0" mb="0" cb="0"/><line nr="245" mi="2" ci="0" mb="0" cb="0"/><line nr="246" mi="1" ci="0" mb="0" cb="0"/><line nr="247" mi="1" ci="0" mb="0" cb="0"/><line nr="250" mi="5" ci="0" mb="0" cb="0"/><line nr="252" mi="4" ci="0" mb="0" cb="0"/><line nr="253" mi="5" ci="0" mb="0" cb="0"/><line nr="254" mi="9" ci="0" mb="0" cb="0"/><line nr="255" mi="5" ci="0" mb="0" cb="0"/><line nr="256" mi="6" ci="0" mb="0" cb="0"/><line nr="257" mi="8" ci="0" mb="0" cb="0"/><line nr="258" mi="9" ci="0" mb="0" cb="0"/><line nr="261" mi="4" ci="0" mb="2" cb="0"/><line nr="262" mi="4" ci="0" mb="0" cb="0"/><line nr="264" mi="6" ci="0" mb="0" cb="0"/><line nr="266" mi="1" ci="0" mb="0" cb="0"/><line nr="267" mi="2" ci="0" mb="0" cb="0"/><line nr="268" mi="17" ci="0" mb="0" cb="0"/><line nr="269" mi="4" ci="0" mb="0" cb="0"/><line nr="272" mi="2" ci="0" mb="0" cb="0"/><line nr="273" mi="2" ci="0" mb="0" cb="0"/><line nr="274" mi="2" ci="0" mb="0" cb="0"/><line nr="275" mi="3" ci="0" mb="0" cb="0"/><line nr="278" mi="3" ci="0" mb="2" cb="0"/><line nr="279" mi="7" ci="0" mb="0" cb="0"/><line nr="281" mi="4" ci="0" mb="2" cb="0"/><line nr="282" mi="6" ci="0" mb="0" cb="0"/><line nr="283" mi="1" ci="0" mb="0" cb="0"/><line nr="284" mi="2" ci="0" mb="0" cb="0"/><line nr="285" mi="2" ci="0" mb="0" cb="0"/><line nr="286" mi="3" ci="0" mb="0" cb="0"/><line nr="289" mi="4" ci="0" mb="2" cb="0"/><line nr="290" mi="6" ci="0" mb="0" cb="0"/><line nr="291" mi="3" ci="0" mb="0" cb="0"/><line nr="292" mi="21" ci="0" mb="0" cb="0"/><line nr="293" mi="3" ci="0" mb="0" cb="0"/><line nr="296" mi="3" ci="0" mb="2" cb="0"/><line nr="297" mi="7" ci="0" mb="0" cb="0"/><line nr="299" mi="3" ci="0" mb="2" cb="0"/><line nr="300" mi="7" ci="0" mb="0" cb="0"/><line nr="302" mi="4" ci="0" mb="0" cb="0"/><line nr="303" mi="4" ci="0" mb="0" cb="0"/><line nr="305" mi="3" ci="0" mb="2" cb="0"/><line nr="306" mi="5" ci="0" mb="0" cb="0"/><line nr="308" mi="7" ci="0" mb="0" cb="0"/><line nr="309" mi="1" ci="0" mb="0" cb="0"/><line nr="310" mi="2" ci="0" mb="0" cb="0"/><line nr="316" mi="7" ci="0" mb="0" cb="0"/><line nr="320" mi="7" ci="0" mb="0" cb="0"/><line nr="321" mi="3" ci="0" mb="0" cb="0"/><line nr="322" mi="3" ci="0" mb="0" cb="0"/><line nr="323" mi="4" ci="0" mb="0" cb="0"/><line nr="324" mi="5" ci="0" mb="0" cb="0"/><line nr="326" mi="5" ci="0" mb="0" cb="0"/><line nr="329" mi="2" ci="0" mb="0" cb="0"/><line nr="330" mi="1" ci="0" mb="0" cb="0"/><line nr="331" mi="3" ci="0" mb="0" cb="0"/><line nr="335" mi="3" ci="0" mb="2" cb="0"/><line nr="336" mi="3" ci="0" mb="0" cb="0"/><line nr="337" mi="3" ci="0" mb="0" cb="0"/><line nr="338" mi="2" ci="0" mb="2" cb="0"/><line nr="339" mi="2" ci="0" mb="0" cb="0"/><line nr="340" mi="4" ci="0" mb="2" cb="0"/><line nr="341" mi="5" ci="0" mb="0" cb="0"/><line nr="342" mi="4" ci="0" mb="2" cb="0"/><line nr="343" mi="5" ci="0" mb="0" cb="0"/><line nr="345" mi="2" ci="0" mb="0" cb="0"/><line nr="346" mi="3" ci="0" mb="2" cb="0"/><line nr="347" mi="3" ci="0" mb="0" cb="0"/><line nr="348" mi="4" ci="0" mb="0" cb="0"/><line nr="349" mi="3" ci="0" mb="2" cb="0"/><line nr="350" mi="6" ci="0" mb="0" cb="0"/><line nr="352" mi="2" ci="0" mb="0" cb="0"/><line nr="357" mi="14" ci="0" mb="0" cb="0"/><line nr="358" mi="18" ci="0" mb="4" cb="0"/><line nr="359" mi="7" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="1065" covered="0"/><counter type="BRANCH" missed="50" covered="0"/><counter type="LINE" missed="199" covered="0"/><counter type="COMPLEXITY" missed="68" covered="0"/><counter type="METHOD" missed="43" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="JdbcDao.java"/><counter type="INSTRUCTION" missed="1065" covered="0"/><counter type="BRANCH" missed="50" covered="0"/><counter type="LINE" missed="199" covered="0"/><counter type="COMPLEXITY" missed="68" covered="0"/><counter type="METHOD" missed="43" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/poc/hss/fasttrack/model"><class name="com/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder" sourcefilename="DbColumn.java"><method name="&lt;init&gt;" desc="()V" line="17"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="name" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="dataType" desc="(Lcom/poc/hss/fasttrack/enums/PGDataType;)Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="isIndexed" desc="(Ljava/lang/Boolean;)Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="isMultiple" desc="(Ljava/lang/Boolean;)Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="contentSchema" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="defaultValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/model/DbColumn;" line="17"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="17"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="31" covered="34"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="4" covered="5"/><counter type="METHOD" missed="4" covered="5"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/model/DbColumn" sourcefilename="DbColumn.java"><method name="getReservedColumns" desc="()Ljava/util/List;" line="44"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getReservedColumnNames" desc="()Ljava/util/List;" line="48"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/model/DbColumn$DbColumnBuilder;" line="17"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getName" desc="()Ljava/lang/String;" line="22"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getDataType" desc="()Lcom/poc/hss/fasttrack/enums/PGDataType;" line="23"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getIsIndexed" desc="()Ljava/lang/Boolean;" line="24"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getIsMultiple" desc="()Ljava/lang/Boolean;" line="25"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getContentSchema" desc="()Ljava/util/List;" line="26"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getDefaultValue" desc="()Ljava/lang/String;" line="27"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setName" desc="(Ljava/lang/String;)V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDataType" desc="(Lcom/poc/hss/fasttrack/enums/PGDataType;)V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setIsIndexed" desc="(Ljava/lang/Boolean;)V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setIsMultiple" desc="(Ljava/lang/Boolean;)V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setContentSchema" desc="(Ljava/util/List;)V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDefaultValue" desc="(Ljava/lang/String;)V" line="16"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="16"><counter type="INSTRUCTION" missed="123" covered="0"/><counter type="BRANCH" missed="42" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="22" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="16"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="16"><counter type="INSTRUCTION" missed="90" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="16"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="18"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/enums/PGDataType;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/util/List;Ljava/lang/String;)V" line="19"><counter type="INSTRUCTION" missed="0" covered="35"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="29"><counter type="INSTRUCTION" missed="0" covered="113"/><counter type="LINE" missed="0" covered="11"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="267" covered="186"/><counter type="BRANCH" missed="54" covered="0"/><counter type="LINE" missed="5" covered="20"/><counter type="COMPLEXITY" missed="39" covered="10"/><counter type="METHOD" missed="12" covered="10"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="DbColumn.java"><line nr="16" mi="240" ci="16" mb="54" cb="0"/><line nr="17" mi="31" ci="38" mb="0" cb="0"/><line nr="18" mi="2" ci="0" mb="0" cb="0"/><line nr="19" mi="0" ci="21" mb="0" cb="0"/><line nr="22" mi="0" ci="3" mb="0" cb="0"/><line nr="23" mi="3" ci="6" mb="0" cb="0"/><line nr="24" mi="4" ci="7" mb="0" cb="0"/><line nr="25" mi="4" ci="7" mb="0" cb="0"/><line nr="26" mi="4" ci="6" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="0"/><line nr="29" mi="0" ci="4" mb="0" cb="0"/><line nr="32" mi="0" ci="12" mb="0" cb="0"/><line nr="33" mi="0" ci="12" mb="0" cb="0"/><line nr="34" mi="0" ci="12" mb="0" cb="0"/><line nr="35" mi="0" ci="12" mb="0" cb="0"/><line nr="36" mi="0" ci="12" mb="0" cb="0"/><line nr="37" mi="0" ci="12" mb="0" cb="0"/><line nr="38" mi="0" ci="12" mb="0" cb="0"/><line nr="39" mi="0" ci="12" mb="0" cb="0"/><line nr="40" mi="0" ci="12" mb="0" cb="0"/><line nr="41" mi="0" ci="1" mb="0" cb="0"/><line nr="44" mi="2" ci="0" mb="0" cb="0"/><line nr="48" mi="4" ci="0" mb="0" cb="0"/><line nr="49" mi="1" ci="0" mb="0" cb="0"/><line nr="50" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="298" covered="220"/><counter type="BRANCH" missed="54" covered="0"/><counter type="LINE" missed="5" covered="20"/><counter type="COMPLEXITY" missed="43" covered="15"/><counter type="METHOD" missed="16" covered="15"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="298" covered="220"/><counter type="BRANCH" missed="54" covered="0"/><counter type="LINE" missed="5" covered="20"/><counter type="COMPLEXITY" missed="43" covered="15"/><counter type="METHOD" missed="16" covered="15"/><counter type="CLASS" missed="0" covered="2"/></package><package name="com/poc/hss/fasttrack/jdbc/sqlbuilder"><class name="com/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilderFactory" sourcefilename="JdbcSQLBuilderFactory.java"><method name="&lt;init&gt;" desc="()V" line="5"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getInstance" desc="(Lcom/poc/hss/fasttrack/enums/CustomApiMode;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="7"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/jdbc/sqlbuilder/OracleJdbcSQLBuilder" sourcefilename="OracleJdbcSQLBuilder.java"><method name="&lt;init&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="limit" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="5"><counter type="INSTRUCTION" missed="32" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="offset" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="19"><counter type="INSTRUCTION" missed="32" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="33"><counter type="INSTRUCTION" missed="29" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="96" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="29" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/jdbc/sqlbuilder/PostgresJdbcSQLBuilder" sourcefilename="PostgresJdbcSQLBuilder.java"><method name="&lt;init&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder" sourcefilename="JdbcSQLBuilder.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="43" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="select" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="21"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="from" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="30"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="join" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="39"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="where" desc="(Ljava/lang/Boolean;Ljava/util/List;Ljava/lang/String;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="47"><counter type="INSTRUCTION" missed="27" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="where" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="58"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="orderBy" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="67"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="groupBy" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="76"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="limit" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="85"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="offset" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/jdbc/sqlbuilder/JdbcSQLBuilder;" line="96"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="108"><counter type="INSTRUCTION" missed="29" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getWhereClause" desc="()Ljava/lang/String;" line="121"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="266" covered="0"/><counter type="BRANCH" missed="26" covered="0"/><counter type="LINE" missed="69" covered="0"/><counter type="COMPLEXITY" missed="25" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="JdbcSQLBuilder.java"><line nr="9" mi="2" ci="0" mb="0" cb="0"/><line nr="11" mi="5" ci="0" mb="0" cb="0"/><line nr="12" mi="5" ci="0" mb="0" cb="0"/><line nr="13" mi="5" ci="0" mb="0" cb="0"/><line nr="14" mi="5" ci="0" mb="0" cb="0"/><line nr="15" mi="5" ci="0" mb="0" cb="0"/><line nr="16" mi="5" ci="0" mb="0" cb="0"/><line nr="17" mi="5" ci="0" mb="0" cb="0"/><line nr="18" mi="6" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="2" cb="0"/><line nr="22" mi="5" ci="0" mb="0" cb="0"/><line nr="24" mi="5" ci="0" mb="0" cb="0"/><line nr="25" mi="7" ci="0" mb="0" cb="0"/><line nr="26" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="3" ci="0" mb="2" cb="0"/><line nr="31" mi="5" ci="0" mb="0" cb="0"/><line nr="33" mi="5" ci="0" mb="0" cb="0"/><line nr="34" mi="7" ci="0" mb="0" cb="0"/><line nr="35" mi="2" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="2" cb="0"/><line nr="40" mi="2" ci="0" mb="0" cb="0"/><line nr="42" mi="7" ci="0" mb="0" cb="0"/><line nr="43" mi="2" ci="0" mb="0" cb="0"/><line nr="47" mi="6" ci="0" mb="4" cb="0"/><line nr="48" mi="2" ci="0" mb="0" cb="0"/><line nr="50" mi="5" ci="0" mb="0" cb="0"/><line nr="51" mi="7" ci="0" mb="2" cb="0"/><line nr="52" mi="5" ci="0" mb="0" cb="0"/><line nr="53" mi="2" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="2" cb="0"/><line nr="59" mi="2" ci="0" mb="0" cb="0"/><line nr="61" mi="5" ci="0" mb="0" cb="0"/><line nr="62" mi="7" ci="0" mb="0" cb="0"/><line nr="63" mi="2" ci="0" mb="0" cb="0"/><line nr="67" mi="3" ci="0" mb="2" cb="0"/><line nr="68" mi="2" ci="0" mb="0" cb="0"/><line nr="70" mi="5" ci="0" mb="0" cb="0"/><line nr="71" mi="7" ci="0" mb="0" cb="0"/><line nr="72" mi="2" ci="0" mb="0" cb="0"/><line nr="76" mi="3" ci="0" mb="2" cb="0"/><line nr="77" mi="2" ci="0" mb="0" cb="0"/><line nr="79" mi="5" ci="0" mb="0" cb="0"/><line nr="80" mi="7" ci="0" mb="0" cb="0"/><line nr="81" mi="2" ci="0" mb="0" cb="0"/><line nr="85" mi="2" ci="0" mb="2" cb="0"/><line nr="86" mi="2" ci="0" mb="0" cb="0"/><line nr="87" mi="3" ci="0" mb="2" cb="0"/><line nr="88" mi="5" ci="0" mb="0" cb="0"/><line nr="90" mi="5" ci="0" mb="0" cb="0"/><line nr="91" mi="5" ci="0" mb="0" cb="0"/><line nr="92" mi="2" ci="0" mb="0" cb="0"/><line nr="96" mi="2" ci="0" mb="2" cb="0"/><line nr="97" mi="2" ci="0" mb="0" cb="0"/><line nr="98" mi="3" ci="0" mb="2" cb="0"/><line nr="99" mi="5" ci="0" mb="0" cb="0"/><line nr="101" mi="5" ci="0" mb="0" cb="0"/><line nr="102" mi="5" ci="0" mb="0" cb="0"/><line nr="103" mi="2" ci="0" mb="0" cb="0"/><line nr="108" mi="6" ci="0" mb="0" cb="0"/><line nr="109" mi="3" ci="0" mb="0" cb="0"/><line nr="110" mi="3" ci="0" mb="0" cb="0"/><line nr="111" mi="3" ci="0" mb="0" cb="0"/><line nr="112" mi="3" ci="0" mb="0" cb="0"/><line nr="113" mi="3" ci="0" mb="0" cb="0"/><line nr="114" mi="3" ci="0" mb="0" cb="0"/><line nr="115" mi="3" ci="0" mb="0" cb="0"/><line nr="116" mi="1" ci="0" mb="0" cb="0"/><line nr="117" mi="1" ci="0" mb="0" cb="0"/><line nr="121" mi="4" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="266" covered="0"/><counter type="BRANCH" missed="26" covered="0"/><counter type="LINE" missed="69" covered="0"/><counter type="COMPLEXITY" missed="25" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="OracleJdbcSQLBuilder.java"><line nr="3" mi="3" ci="0" mb="0" cb="0"/><line nr="5" mi="2" ci="0" mb="2" cb="0"/><line nr="6" mi="2" ci="0" mb="0" cb="0"/><line nr="7" mi="3" ci="0" mb="2" cb="0"/><line nr="8" mi="5" ci="0" mb="0" cb="0"/><line nr="10" mi="3" ci="0" mb="2" cb="0"/><line nr="11" mi="5" ci="0" mb="0" cb="0"/><line nr="12" mi="5" ci="0" mb="0" cb="0"/><line nr="13" mi="5" ci="0" mb="0" cb="0"/><line nr="15" mi="2" ci="0" mb="0" cb="0"/><line nr="19" mi="2" ci="0" mb="2" cb="0"/><line nr="20" mi="2" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="2" cb="0"/><line nr="22" mi="5" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="2" cb="0"/><line nr="25" mi="5" ci="0" mb="0" cb="0"/><line nr="26" mi="5" ci="0" mb="0" cb="0"/><line nr="27" mi="5" ci="0" mb="0" cb="0"/><line nr="29" mi="2" ci="0" mb="0" cb="0"/><line nr="33" mi="6" ci="0" mb="0" cb="0"/><line nr="34" mi="3" ci="0" mb="0" cb="0"/><line nr="35" mi="3" ci="0" mb="0" cb="0"/><line nr="36" mi="3" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="1" ci="0" mb="0" cb="0"/><line nr="42" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="96" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="29" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="JdbcSQLBuilderFactory.java"><line nr="5" mi="3" ci="0" mb="0" cb="0"/><line nr="7" mi="5" ci="0" mb="2" cb="0"/><line nr="9" mi="4" ci="0" mb="0" cb="0"/><line nr="12" mi="4" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="PostgresJdbcSQLBuilder.java"><line nr="3" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="381" covered="0"/><counter type="BRANCH" missed="40" covered="0"/><counter type="LINE" missed="103" covered="0"/><counter type="COMPLEXITY" missed="39" covered="0"/><counter type="METHOD" missed="19" covered="0"/><counter type="CLASS" missed="4" covered="0"/></package><package name="com/poc/hss/fasttrack/jdbc/util"><class name="com/poc/hss/fasttrack/jdbc/util/GenericJdbcUtils" sourcefilename="GenericJdbcUtils.java"><method name="&lt;init&gt;" desc="()V" line="23"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDoubleQuotedSchemaName" desc="(Ljava/lang/String;)Ljava/lang/String;" line="28"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSchemaName" desc="(Ljava/lang/String;)Ljava/lang/String;" line="32"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="transformTableName" desc="(Ljava/lang/String;)Ljava/lang/String;" line="36"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toDoubleQuoteStr" desc="(Ljava/lang/String;)Ljava/lang/String;" line="40"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="removeDoubleQuoteStr" desc="(Ljava/lang/String;)Ljava/lang/String;" line="44"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toSingleQuoteStr" desc="(Ljava/lang/String;)Ljava/lang/String;" line="48"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toParenthesesStr" desc="(Ljava/lang/String;)Ljava/lang/String;" line="52"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="transformIdList" desc="(Ljava/util/List;)Ljava/lang/String;" line="56"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="transformColumns" desc="(Ljava/util/List;)Ljava/lang/String;" line="62"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="genUpsertStatement" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)Ljava/lang/String;" line="69"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="genUpsertStatement" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;)Ljava/lang/String;" line="73"><counter type="INSTRUCTION" missed="0" covered="7"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="genUpsertStatement" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)Ljava/lang/String;" line="77"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="genUpsertStatement" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/Map;)Ljava/lang/String;" line="81"><counter type="INSTRUCTION" missed="5" covered="152"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="1" covered="22"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="genInsertStatement" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/util/List;)Ljava/lang/String;" line="137"><counter type="INSTRUCTION" missed="78" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="genBatchSoftDeleteStatement" desc="(Ljava/lang/String;Ljava/lang/String;I)Ljava/lang/String;" line="156"><counter type="INSTRUCTION" missed="33" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="generateInParameterValueMap" desc="(Ljava/util/List;)Ljava/util/Map;" line="167"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="genInQueryParameters" desc="(I)Ljava/lang/String;" line="175"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="secureSQLStatement" desc="(Ljava/lang/String;)Ljava/lang/String;" line="181"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$genInQueryParameters$7" desc="(I)Ljava/lang/String;" line="176"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$genInsertStatement$6" desc="(Ljava/lang/String;)Ljava/lang/String;" line="151"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$genInsertStatement$5" desc="(Ljava/lang/String;)Z" line="139"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$genUpsertStatement$4" desc="(Ljava/util/Map;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;" line="112"><counter type="INSTRUCTION" missed="0" covered="81"/><counter type="BRANCH" missed="2" covered="6"/><counter type="LINE" missed="0" covered="13"/><counter type="COMPLEXITY" missed="2" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$genUpsertStatement$3" desc="(Ljava/lang/String;)Z" line="110"><counter type="INSTRUCTION" missed="1" covered="7"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$genUpsertStatement$2" desc="(Ljava/lang/String;)Ljava/lang/String;" line="101"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$genUpsertStatement$1" desc="(Ljava/lang/String;)Z" line="86"><counter type="INSTRUCTION" missed="1" covered="27"/><counter type="BRANCH" missed="6" covered="6"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="6" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$transformColumns$0" desc="(Lcom/poc/hss/fasttrack/model/DbColumn;)Ljava/lang/String;" line="64"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="25"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="276" covered="301"/><counter type="BRANCH" missed="28" covered="14"/><counter type="LINE" missed="38" covered="43"/><counter type="COMPLEXITY" missed="37" covered="12"/><counter type="METHOD" missed="18" covered="10"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/jdbc/util/PGUtils" sourcefilename="PGUtils.java"><method name="createPGObject" desc="(Ljava/lang/String;)Lorg/postgresql/util/PGobject;" line="18"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="escapeNullCharacter" desc="(Ljava/lang/String;)Ljava/lang/String;" line="28"><counter type="INSTRUCTION" missed="21" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="serializePGObject" desc="(Lorg/postgresql/util/PGobject;)Ljava/lang/Object;" line="39"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="57" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="PGUtils.java"><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="4" ci="0" mb="0" cb="0"/><line nr="21" mi="4" ci="0" mb="0" cb="0"/><line nr="22" mi="4" ci="0" mb="0" cb="0"/><line nr="23" mi="2" ci="0" mb="0" cb="0"/><line nr="28" mi="4" ci="0" mb="2" cb="0"/><line nr="29" mi="5" ci="0" mb="0" cb="0"/><line nr="32" mi="4" ci="0" mb="2" cb="0"/><line nr="33" mi="6" ci="0" mb="0" cb="0"/><line nr="35" mi="2" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="2" cb="0"/><line nr="42" mi="5" ci="0" mb="0" cb="0"/><line nr="44" mi="5" ci="0" mb="0" cb="0"/><line nr="45" mi="1" ci="0" mb="0" cb="0"/><line nr="46" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="57" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="GenericJdbcUtils.java"><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="25" mi="0" ci="5" mb="0" cb="0"/><line nr="28" mi="8" ci="0" mb="2" cb="0"/><line nr="32" mi="9" ci="0" mb="2" cb="0"/><line nr="36" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="0" ci="9" mb="0" cb="0"/><line nr="44" mi="5" ci="0" mb="0" cb="0"/><line nr="48" mi="9" ci="0" mb="0" cb="0"/><line nr="52" mi="9" ci="0" mb="0" cb="0"/><line nr="56" mi="4" ci="0" mb="0" cb="0"/><line nr="57" mi="2" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="0" cb="0"/><line nr="62" mi="6" ci="0" mb="2" cb="0"/><line nr="63" mi="2" ci="0" mb="0" cb="0"/><line nr="64" mi="26" ci="0" mb="2" cb="0"/><line nr="65" mi="4" ci="0" mb="0" cb="0"/><line nr="69" mi="0" ci="7" mb="0" cb="0"/><line nr="73" mi="0" ci="7" mb="0" cb="0"/><line nr="77" mi="7" ci="0" mb="0" cb="0"/><line nr="81" mi="0" ci="25" mb="0" cb="0"/><line nr="82" mi="0" ci="4" mb="1" cb="1"/><line nr="83" mi="5" ci="0" mb="0" cb="0"/><line nr="85" mi="0" ci="6" mb="0" cb="0"/><line nr="86" mi="0" ci="11" mb="2" cb="2"/><line nr="87" mi="0" ci="8" mb="2" cb="2"/><line nr="88" mi="1" ci="8" mb="2" cb="2"/><line nr="89" mi="0" ci="4" mb="0" cb="0"/><line nr="91" mi="0" ci="16" mb="0" cb="0"/><line nr="94" mi="0" ci="5" mb="0" cb="0"/><line nr="95" mi="0" ci="5" mb="0" cb="0"/><line nr="96" mi="0" ci="5" mb="0" cb="0"/><line nr="97" mi="0" ci="5" mb="0" cb="0"/><line nr="98" mi="0" ci="18" mb="0" cb="0"/><line nr="101" mi="0" ci="17" mb="0" cb="0"/><line nr="103" mi="0" ci="9" mb="0" cb="0"/><line nr="105" mi="0" ci="5" mb="0" cb="0"/><line nr="106" mi="0" ci="5" mb="0" cb="0"/><line nr="107" mi="0" ci="5" mb="0" cb="0"/><line nr="109" mi="0" ci="2" mb="0" cb="0"/><line nr="110" mi="1" ci="11" mb="1" cb="1"/><line nr="111" mi="0" ci="2" mb="0" cb="0"/><line nr="112" mi="0" ci="4" mb="0" cb="2"/><line nr="113" mi="0" ci="5" mb="0" cb="0"/><line nr="114" mi="0" ci="2" mb="0" cb="0"/><line nr="115" mi="0" ci="2" mb="0" cb="0"/><line nr="116" mi="0" ci="3" mb="0" cb="2"/><line nr="117" mi="0" ci="4" mb="1" cb="1"/><line nr="118" mi="0" ci="6" mb="0" cb="0"/><line nr="119" mi="0" ci="5" mb="1" cb="1"/><line nr="120" mi="0" ci="6" mb="0" cb="0"/><line nr="123" mi="0" ci="8" mb="0" cb="0"/><line nr="124" mi="0" ci="15" mb="0" cb="0"/><line nr="125" mi="0" ci="7" mb="0" cb="0"/><line nr="127" mi="0" ci="14" mb="0" cb="0"/><line nr="130" mi="0" ci="3" mb="0" cb="0"/><line nr="131" mi="0" ci="3" mb="0" cb="0"/><line nr="132" mi="0" ci="5" mb="0" cb="0"/><line nr="133" mi="0" ci="2" mb="0" cb="0"/><line nr="137" mi="4" ci="0" mb="0" cb="0"/><line nr="138" mi="6" ci="0" mb="0" cb="0"/><line nr="139" mi="20" ci="0" mb="8" cb="0"/><line nr="140" mi="4" ci="0" mb="0" cb="0"/><line nr="141" mi="16" ci="0" mb="0" cb="0"/><line nr="144" mi="5" ci="0" mb="0" cb="0"/><line nr="145" mi="5" ci="0" mb="0" cb="0"/><line nr="146" mi="5" ci="0" mb="0" cb="0"/><line nr="147" mi="5" ci="0" mb="0" cb="0"/><line nr="148" mi="18" ci="0" mb="0" cb="0"/><line nr="151" mi="10" ci="0" mb="0" cb="0"/><line nr="152" mi="3" ci="0" mb="0" cb="0"/><line nr="156" mi="28" ci="0" mb="0" cb="0"/><line nr="162" mi="2" ci="0" mb="0" cb="0"/><line nr="163" mi="3" ci="0" mb="0" cb="0"/><line nr="167" mi="4" ci="0" mb="0" cb="0"/><line nr="168" mi="8" ci="0" mb="2" cb="0"/><line nr="169" mi="8" ci="0" mb="0" cb="0"/><line nr="171" mi="2" ci="0" mb="0" cb="0"/><line nr="175" mi="5" ci="0" mb="0" cb="0"/><line nr="176" mi="5" ci="0" mb="0" cb="0"/><line nr="177" mi="3" ci="0" mb="0" cb="0"/><line nr="181" mi="0" ci="3" mb="0" cb="0"/><counter type="INSTRUCTION" missed="276" covered="301"/><counter type="BRANCH" missed="28" covered="14"/><counter type="LINE" missed="38" covered="43"/><counter type="COMPLEXITY" missed="37" covered="12"/><counter type="METHOD" missed="18" covered="10"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="333" covered="301"/><counter type="BRANCH" missed="34" covered="14"/><counter type="LINE" missed="54" covered="43"/><counter type="COMPLEXITY" missed="43" covered="12"/><counter type="METHOD" missed="21" covered="10"/><counter type="CLASS" missed="1" covered="1"/></package><package name="com/poc/hss/fasttrack/enums"><class name="com/poc/hss/fasttrack/enums/DataSourceType" sourcefilename="DataSourceType.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="13"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="20"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/DataSourceType;" line="25"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="24"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="62"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/enums/SqlOperator" sourcefilename="SqlOperator.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="21"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/SqlOperator;" line="26"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getValue" desc="()Ljava/lang/String;" line="35"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="3"><counter type="INSTRUCTION" missed="87" covered="0"/><counter type="LINE" missed="13" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="124" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="21" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/enums/DatabaseType" sourcefilename="DatabaseType.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="19"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/DatabaseType;" line="24"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="17"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="55"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="11"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/enums/PGDataType" sourcefilename="PGDataType.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="21"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;Ljava/lang/String;)V" line="25"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getValue" desc="()Ljava/lang/String;" line="31"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDisplayValue" desc="()Ljava/lang/String;" line="35"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="41"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/enums/PGDataType;" line="46"><counter type="INSTRUCTION" missed="30" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="0" covered="51"/><counter type="LINE" missed="0" covered="7"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="44" covered="66"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="9" covered="12"/><counter type="COMPLEXITY" missed="7" covered="3"/><counter type="METHOD" missed="4" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="SqlOperator.java"><line nr="3" mi="3" ci="0" mb="0" cb="0"/><line nr="5" mi="7" ci="0" mb="0" cb="0"/><line nr="6" mi="7" ci="0" mb="0" cb="0"/><line nr="7" mi="7" ci="0" mb="0" cb="0"/><line nr="8" mi="7" ci="0" mb="0" cb="0"/><line nr="9" mi="7" ci="0" mb="0" cb="0"/><line nr="10" mi="7" ci="0" mb="0" cb="0"/><line nr="11" mi="7" ci="0" mb="0" cb="0"/><line nr="12" mi="7" ci="0" mb="0" cb="0"/><line nr="13" mi="7" ci="0" mb="0" cb="0"/><line nr="14" mi="7" ci="0" mb="0" cb="0"/><line nr="15" mi="7" ci="0" mb="0" cb="0"/><line nr="16" mi="7" ci="0" mb="0" cb="0"/><line nr="21" mi="4" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="1" ci="0" mb="0" cb="0"/><line nr="26" mi="16" ci="0" mb="2" cb="0"/><line nr="27" mi="6" ci="0" mb="2" cb="0"/><line nr="28" mi="2" ci="0" mb="0" cb="0"/><line nr="31" mi="2" ci="0" mb="0" cb="0"/><line nr="35" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="124" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="21" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="PGDataType.java"><line nr="8" mi="0" ci="3" mb="0" cb="0"/><line nr="10" mi="0" ci="8" mb="0" cb="0"/><line nr="11" mi="0" ci="8" mb="0" cb="0"/><line nr="12" mi="0" ci="8" mb="0" cb="0"/><line nr="13" mi="0" ci="8" mb="0" cb="0"/><line nr="14" mi="0" ci="8" mb="0" cb="0"/><line nr="15" mi="0" ci="8" mb="0" cb="0"/><line nr="21" mi="4" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="1" ci="0" mb="0" cb="0"/><line nr="25" mi="0" ci="4" mb="0" cb="0"/><line nr="26" mi="0" ci="3" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="0"/><line nr="28" mi="0" ci="1" mb="0" cb="0"/><line nr="31" mi="3" ci="0" mb="0" cb="0"/><line nr="35" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="0" ci="4" mb="0" cb="0"/><line nr="46" mi="16" ci="0" mb="2" cb="0"/><line nr="47" mi="10" ci="0" mb="4" cb="0"/><line nr="48" mi="2" ci="0" mb="0" cb="0"/><line nr="51" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="44" covered="66"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="9" covered="12"/><counter type="COMPLEXITY" missed="7" covered="3"/><counter type="METHOD" missed="4" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="DataSourceType.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="9" mi="0" ci="7" mb="0" cb="0"/><line nr="13" mi="0" ci="4" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="1" mb="0" cb="0"/><line nr="20" mi="0" ci="4" mb="0" cb="0"/><line nr="25" mi="0" ci="16" mb="0" cb="2"/><line nr="26" mi="0" ci="6" mb="0" cb="2"/><line nr="27" mi="0" ci="2" mb="0" cb="0"/><line nr="30" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="62"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="DatabaseType.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="12" mi="0" ci="4" mb="0" cb="0"/><line nr="13" mi="0" ci="3" mb="0" cb="0"/><line nr="14" mi="0" ci="1" mb="0" cb="0"/><line nr="19" mi="0" ci="4" mb="0" cb="0"/><line nr="24" mi="0" ci="16" mb="0" cb="2"/><line nr="25" mi="0" ci="6" mb="0" cb="2"/><line nr="26" mi="0" ci="2" mb="0" cb="0"/><line nr="29" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="55"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="11"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="168" covered="183"/><counter type="BRANCH" missed="10" covered="8"/><counter type="LINE" missed="30" covered="35"/><counter type="COMPLEXITY" missed="13" covered="15"/><counter type="METHOD" missed="8" covered="11"/><counter type="CLASS" missed="1" covered="3"/></package><package name="com/poc/hss/fasttrack/dto"><class name="com/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder" sourcefilename="JdbcCriteriaDTO.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fields" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="where" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="offset" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="limit" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="sorts" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fieldAggregations" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="distinct" desc="(Ljava/lang/Boolean;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="groups" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO;" line="9"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="85" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="11" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort$SortBuilder" sourcefilename="JdbcCriteriaDTO.java"><method name="&lt;init&gt;" desc="()V" line="21"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="field" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort$SortBuilder;" line="21"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="direction" desc="(Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Direction;)Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort$SortBuilder;" line="21"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort;" line="21"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="21"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="5" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort" sourcefilename="JdbcCriteriaDTO.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Direction;)V" line="21"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Sort$SortBuilder;" line="21"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getField" desc="()Ljava/lang/String;" line="23"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDirection" desc="()Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Direction;" line="24"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setField" desc="(Ljava/lang/String;)V" line="20"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDirection" desc="(Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Direction;)V" line="20"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="20"><counter type="INSTRUCTION" missed="55" covered="0"/><counter type="BRANCH" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="20"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="20"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="20"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="126" covered="0"/><counter type="BRANCH" missed="22" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="21" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/JdbcCriteriaDTO" sourcefilename="JdbcCriteriaDTO.java"><method name="&lt;init&gt;" desc="(Ljava/util/List;Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/util/List;Ljava/util/List;Ljava/lang/Boolean;Ljava/util/List;)V" line="9"><counter type="INSTRUCTION" missed="27" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/JdbcCriteriaDTO$JdbcCriteriaDTOBuilder;" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getFields" desc="()Ljava/util/List;" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getWhere" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getOffset" desc="()Ljava/lang/Integer;" line="13"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getLimit" desc="()Ljava/lang/Integer;" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSorts" desc="()Ljava/util/List;" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getFieldAggregations" desc="()Ljava/util/List;" line="16"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDistinct" desc="()Ljava/lang/Boolean;" line="17"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getGroups" desc="()Ljava/util/List;" line="18"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setFields" desc="(Ljava/util/List;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setWhere" desc="(Ljava/lang/String;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setOffset" desc="(Ljava/lang/Integer;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setLimit" desc="(Ljava/lang/Integer;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setSorts" desc="(Ljava/util/List;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setFieldAggregations" desc="(Ljava/util/List;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDistinct" desc="(Ljava/lang/Boolean;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setGroups" desc="(Ljava/util/List;)V" line="8"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="157" covered="0"/><counter type="BRANCH" missed="54" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="28" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="8"><counter type="INSTRUCTION" missed="118" covered="0"/><counter type="BRANCH" missed="16" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="9" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="8"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="387" covered="0"/><counter type="BRANCH" missed="70" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="57" covered="0"/><counter type="METHOD" missed="22" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/JdbcCriteriaDTO$Direction" sourcefilename="JdbcCriteriaDTO.java"><method name="&lt;clinit&gt;" desc="()V" line="27"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/FieldAggregationDTO$FieldAggregationDTOBuilder" sourcefilename="FieldAggregationDTO.java"><method name="&lt;init&gt;" desc="()V" line="7"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="function" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/FieldAggregationDTO$FieldAggregationDTOBuilder;" line="7"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="field" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/FieldAggregationDTO$FieldAggregationDTOBuilder;" line="7"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="rename" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/dto/FieldAggregationDTO$FieldAggregationDTOBuilder;" line="7"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/dto/FieldAggregationDTO;" line="7"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="7"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="36" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/dto/FieldAggregationDTO" sourcefilename="FieldAggregationDTO.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V" line="7"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/dto/FieldAggregationDTO$FieldAggregationDTOBuilder;" line="7"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getFunction" desc="()Ljava/lang/String;" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getField" desc="()Ljava/lang/String;" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getRename" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setFunction" desc="(Ljava/lang/String;)V" line="6"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setField" desc="(Ljava/lang/String;)V" line="6"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setRename" desc="(Ljava/lang/String;)V" line="6"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="6"><counter type="INSTRUCTION" missed="72" covered="0"/><counter type="BRANCH" missed="24" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="13" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="6"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="6"><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="6"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="168" covered="0"/><counter type="BRANCH" missed="30" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="27" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="JdbcCriteriaDTO.java"><line nr="8" mi="332" ci="0" mb="70" cb="0"/><line nr="9" mi="116" ci="0" mb="0" cb="0"/><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="16" mi="3" ci="0" mb="0" cb="0"/><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="107" ci="0" mb="22" cb="0"/><line nr="21" mi="41" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="28" mi="6" ci="0" mb="0" cb="0"/><line nr="29" mi="6" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="641" covered="0"/><counter type="BRANCH" missed="92" covered="0"/><counter type="LINE" missed="17" covered="0"/><counter type="COMPLEXITY" missed="95" covered="0"/><counter type="METHOD" missed="49" covered="0"/><counter type="CLASS" missed="5" covered="0"/></sourcefile><sourcefile name="FieldAggregationDTO.java"><line nr="6" mi="143" ci="0" mb="30" cb="0"/><line nr="7" mi="52" ci="0" mb="0" cb="0"/><line nr="9" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="11" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="204" covered="0"/><counter type="BRANCH" missed="30" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="33" covered="0"/><counter type="METHOD" missed="18" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="845" covered="0"/><counter type="BRANCH" missed="122" covered="0"/><counter type="LINE" missed="22" covered="0"/><counter type="COMPLEXITY" missed="128" covered="0"/><counter type="METHOD" missed="67" covered="0"/><counter type="CLASS" missed="7" covered="0"/></package><package name="com/poc/hss/fasttrack/jdbc/converter"><class name="com/poc/hss/fasttrack/jdbc/converter/JdbcLocalDateConverter" sourcefilename="JdbcLocalDateConverter.java"><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convert" desc="(Ljava/lang/String;)Ljava/time/LocalDate;" line="14"><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/jdbc/converter/PGToSqlTypeConverter" sourcefilename="PGToSqlTypeConverter.java"><method name="&lt;init&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="convert" desc="(Lcom/poc/hss/fasttrack/enums/PGDataType;)Ljava/lang/Integer;" line="14"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="BRANCH" missed="7" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="29" covered="0"/><counter type="BRANCH" missed="7" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="PGToSqlTypeConverter.java"><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="5" ci="0" mb="7" cb="0"/><line nr="16" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="3" ci="0" mb="0" cb="0"/><line nr="28" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="29" covered="0"/><counter type="BRANCH" missed="7" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="JdbcLocalDateConverter.java"><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="9" ci="0" mb="4" cb="0"/><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="16" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="47" covered="0"/><counter type="BRANCH" missed="11" covered="0"/><counter type="LINE" missed="13" covered="0"/><counter type="COMPLEXITY" missed="12" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="2" covered="0"/></package><counter type="INSTRUCTION" missed="3137" covered="704"/><counter type="BRANCH" missed="321" covered="22"/><counter type="LINE" missed="426" covered="98"/><counter type="COMPLEXITY" missed="346" covered="42"/><counter type="METHOD" missed="178" covered="36"/><counter type="CLASS" missed="16" covered="6"/></report>