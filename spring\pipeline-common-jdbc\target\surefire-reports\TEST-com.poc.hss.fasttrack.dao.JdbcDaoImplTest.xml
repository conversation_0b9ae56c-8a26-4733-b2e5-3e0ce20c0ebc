<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.02" tests="27" errors="0" skipped="27" failures="0">
  <properties/>
  <testcase name="testBatchInsert_OverwriteMode" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testFindJsonObjectByCriteria_WithNullCriteria" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testExecute" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testFindJsonObjectByCriteria_WithParameters" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testIsTableExist_TableExists" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testBatchUpsert_WithCustomPrimaryKey" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testCreateTableIfNotExist" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testDropIndexIfExist" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testFindJsonObjectByCriteria_WithLimit" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testFindJsonObjectByCriteria_WithoutParameters" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testBatchSoftDelete" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testBatchInsert_PatchMode" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testCreateIndexIfNotExist" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testFindJsonObjectByCriteria_WithOrderBy" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testAddColumn" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testDropDefaultValue" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testAddColumn_EmptyList" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testUpdateDefaultValue" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testGetColumns" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testDelete" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testTruncateTable" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testAlterDataType_EmptyList" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testAlterDataType" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testCreateSchemaIfNotExist" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testBatchUpsert_WithDefaultParameters" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testFindJsonObjectByCriteria_IncorrectResultSizeException" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
  <testcase name="testIsTableExist_TableDoesNotExist" classname="com.poc.hss.fasttrack.dao.JdbcDaoImplTest" time="0.0">
    <skipped message="class com.poc.hss.fasttrack.dao.JdbcDaoImplTest is @Disabled"/>
  </testcase>
</testsuite>