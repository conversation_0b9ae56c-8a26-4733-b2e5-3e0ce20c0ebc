C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\model\JpaTypeDefEntity.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\function\hibernate\RegexpLike.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\model\AuditInfoEntity.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\model\BaseEntity.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\function\hibernate\JsonbPathExists.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\repository\FieldAwareJpaSpecificationExecutor.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\criteria\SortCriteria.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\util\SortUtils.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\objectmapper\JsonObjectMapperSupplier.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\dto\PageDTO.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\function\hibernate\QueryFieldByText.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\dialect\UnityPostgreSQLDialect.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-jpa\src\main\java\com\poc\hss\fasttrack\jpa\repository\FieldAwareJpaRepository.java
