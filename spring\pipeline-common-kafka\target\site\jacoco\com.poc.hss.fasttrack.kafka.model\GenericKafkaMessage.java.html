<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GenericKafkaMessage.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.model</a> &gt; <span class="el_source">GenericKafkaMessage.java</span></div><h1>GenericKafkaMessage.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.poc.hss.fasttrack.constant.Constants;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;

<span class="nc bnc" id="L12" title="All 30 branches missed.">@Data</span>
<span class="nc" id="L13">@AllArgsConstructor</span>
<span class="nc" id="L14">@NoArgsConstructor</span>
<span class="nc" id="L15">@Builder</span>
public class GenericKafkaMessage&lt;T&gt; {
    @JsonProperty(Constants.ID)
<span class="nc" id="L18">    private String id;</span>
    @JsonProperty(Constants.DATA)
<span class="nc" id="L20">    private T data;</span>
    @JsonIgnore
<span class="nc" id="L22">    private ConsumerRecord consumerRecord;</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>