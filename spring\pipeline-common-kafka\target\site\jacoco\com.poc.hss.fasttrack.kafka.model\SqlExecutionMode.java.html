<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SqlExecutionMode.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.model</a> &gt; <span class="el_source">SqlExecutionMode.java</span></div><h1>SqlExecutionMode.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

<span class="nc" id="L6">public enum SqlExecutionMode {</span>
<span class="nc" id="L7">    SYNCHRONOUS(&quot;SYNCHRONOUS&quot;),</span>
<span class="nc" id="L8">    ASYNCHRONOUS(&quot;ASYNCHRONOUS&quot;);</span>

    private final String value;

<span class="nc" id="L12">    SqlExecutionMode(String value) {</span>
<span class="nc" id="L13">        this.value = value;</span>
<span class="nc" id="L14">    }</span>

    @Override
    @JsonValue
    public String toString() {
<span class="nc" id="L19">        return String.valueOf(value);</span>
    }

    @JsonCreator
    public static SqlExecutionMode fromValue(String text) {
<span class="nc bnc" id="L24" title="All 2 branches missed.">        for (SqlExecutionMode b : SqlExecutionMode.values()) {</span>
<span class="nc bnc" id="L25" title="All 2 branches missed.">            if (String.valueOf(b.value).equals(text)) {</span>
<span class="nc" id="L26">                return b;</span>
            }
        }
<span class="nc" id="L29">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>