<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaMessage</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.kafka.model</a> &gt; <span class="el_class">Unity2KafkaMessage</span></div><h1>Unity2KafkaMessage</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">911 of 1,098</td><td class="ctr2">17%</td><td class="bar">142 of 142</td><td class="ctr2">0%</td><td class="ctr1">113</td><td class="ctr2">119</td><td class="ctr1">70</td><td class="ctr2">94</td><td class="ctr1">42</td><td class="ctr2">48</td></tr></tfoot><tbody><tr><td id="a2"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="310" alt="310"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="108" alt="108"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">55</td><td class="ctr2" id="g0">55</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a25"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="94" height="10" title="244" alt="244"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="34" alt="34"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">18</td><td class="ctr2" id="g1">18</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="Unity2KafkaMessage.java.html#L112" class="el_method">from(JsonObject)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="50" height="10" title="131" alt="131"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h0">34</td><td class="ctr2" id="i0">34</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="Unity2KafkaMessage.java.html#L92" class="el_method">from(Unity2KafkaMessage)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="56" alt="56"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h1">17</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a46"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">toString()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="42" alt="42"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="Unity2KafkaMessage.java.html#L83" class="el_method">flatten()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a35"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setId(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a34"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setDeleted(Boolean)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a45"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setTraceId(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a40"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setSource(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a42"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setSourceGroup(String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a41"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setSourceComponent(Unity2Component)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a32"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setData(JsonObject)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a43"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setSql(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a44"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setSqlExecutionMode(SqlExecutionMode)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a30"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setBatchId(String)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a31"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setBatchSize(Long)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a37"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setMessageType(MessageType)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a39"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setRawPayload(String)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a36"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setKafkaPartitionKey(String)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a33"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setDataPersistMode(DataPersistMode)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a38"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setOperationMap(JsonObject)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a29"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">setB3TraceId(String)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a14"><a href="Unity2KafkaMessage.java.html#L21" class="el_method">getId()</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a13"><a href="Unity2KafkaMessage.java.html#L23" class="el_method">getDeleted()</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a24"><a href="Unity2KafkaMessage.java.html#L25" class="el_method">getTraceId()</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a19"><a href="Unity2KafkaMessage.java.html#L27" class="el_method">getSource()</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a21"><a href="Unity2KafkaMessage.java.html#L29" class="el_method">getSourceGroup()</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a20"><a href="Unity2KafkaMessage.java.html#L31" class="el_method">getSourceComponent()</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a11"><a href="Unity2KafkaMessage.java.html#L33" class="el_method">getData()</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a22"><a href="Unity2KafkaMessage.java.html#L35" class="el_method">getSql()</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a23"><a href="Unity2KafkaMessage.java.html#L37" class="el_method">getSqlExecutionMode()</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a9"><a href="Unity2KafkaMessage.java.html#L39" class="el_method">getBatchId()</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a10"><a href="Unity2KafkaMessage.java.html#L41" class="el_method">getBatchSize()</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a16"><a href="Unity2KafkaMessage.java.html#L43" class="el_method">getMessageType()</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a18"><a href="Unity2KafkaMessage.java.html#L45" class="el_method">getRawPayload()</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a15"><a href="Unity2KafkaMessage.java.html#L47" class="el_method">getKafkaPartitionKey()</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a12"><a href="Unity2KafkaMessage.java.html#L49" class="el_method">getDataPersistMode()</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a17"><a href="Unity2KafkaMessage.java.html#L51" class="el_method">getOperationMap()</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a8"><a href="Unity2KafkaMessage.java.html#L53" class="el_method">getB3TraceId()</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a1"><a href="Unity2KafkaMessage.java.html#L17" class="el_method">canEqual(Object)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a28"><a href="Unity2KafkaMessage.java.html#L83" class="el_method">lambda$flatten$2(String)</a></td><td class="bar" id="b41"/><td class="ctr2" id="c47">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a4"><a href="Unity2KafkaMessage.java.html#L56" class="el_method">flatten(Predicate)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/greenbar.gif" width="39" height="10" title="103" alt="103"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">0</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h42">0</td><td class="ctr2" id="i1">21</td><td class="ctr1" id="j42">0</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a47"><a href="Unity2KafkaMessage.java.html#L18" class="el_method">Unity2KafkaMessage(String, Boolean, String, String, String, Unity2Component, JsonObject, String, SqlExecutionMode, String, Long, MessageType, String, String, DataPersistMode, JsonObject, String)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="54" alt="54"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">0</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">0</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j43">0</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a5"><a href="Unity2KafkaMessage.java.html#L87" class="el_method">flatten(String[])</a></td><td class="bar" id="b44"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="11" alt="11"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">0</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h44">0</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j44">0</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a27"><a href="Unity2KafkaMessage.java.html#L77" class="el_method">lambda$flatten$1(JsonObject, Map.Entry)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="9" alt="9"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">0</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h45">0</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">0</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a26"><a href="Unity2KafkaMessage.java.html#L76" class="el_method">lambda$flatten$0(Predicate, Map.Entry)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">0</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">0</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">0</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a0"><a href="Unity2KafkaMessage.java.html#L18" class="el_method">builder()</a></td><td class="bar" id="b47"><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">0</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">0</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">0</td><td class="ctr2" id="k47">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>