<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaMessage.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.model</a> &gt; <span class="el_source">Unity2KafkaMessage.java</span></div><h1>Unity2KafkaMessage.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.poc.hss.fasttrack.constant.Constants;
import com.poc.hss.fasttrack.enums.DataPersistMode;
import com.poc.hss.fasttrack.model.Unity2Component;
import io.vertx.core.json.JsonObject;
import lombok.Builder;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.function.Predicate;

<span class="nc bnc" id="L17" title="All 142 branches missed.">@Data</span>
<span class="pc" id="L18">@Builder</span>
public class Unity2KafkaMessage {
    @JsonProperty(Constants.ID)
<span class="nc" id="L21">    private String id;</span>
    @JsonProperty(Constants.DELETED)
<span class="nc" id="L23">    private Boolean deleted;</span>
    @JsonProperty(Constants.TRACE_ID)
<span class="nc" id="L25">    private String traceId;</span>
    @JsonProperty(Constants.SOURCE)
<span class="nc" id="L27">    private String source;</span>
    @JsonProperty(Constants.SOURCE_GROUP)
<span class="nc" id="L29">    private String sourceGroup;</span>
    @JsonProperty(Constants.SOURCE_COMPONENT)
<span class="nc" id="L31">    private Unity2Component sourceComponent;</span>
    @JsonProperty(Constants.DATA)
<span class="nc" id="L33">    private JsonObject data;</span>
    @JsonProperty(Constants.SQL)
<span class="nc" id="L35">    private String sql;</span>
    @JsonProperty(Constants.SQL_EXECUTION_MODE)
<span class="nc" id="L37">    private SqlExecutionMode sqlExecutionMode;</span>
    @JsonProperty(Constants.BATCH_ID)
<span class="nc" id="L39">    private String batchId;</span>
    @JsonProperty(Constants.BATCH_SIZE)
<span class="nc" id="L41">    private Long batchSize;</span>
    @JsonProperty(Constants.MESSAGE_TYPE)
<span class="nc" id="L43">    private MessageType messageType;</span>
    @JsonProperty(Constants.RAW_PAYLOAD)
<span class="nc" id="L45">    private String rawPayload;</span>
    @JsonProperty(Constants.KAFKA_PARTITION_KEY)
<span class="nc" id="L47">    private String kafkaPartitionKey;</span>
    @JsonProperty(Constants.DATA_PERSIST_MODE)
<span class="nc" id="L49">    private DataPersistMode dataPersistMode;</span>
    @JsonProperty(Constants.OPERATION_MAP)
<span class="nc" id="L51">    private JsonObject operationMap;</span>
    @JsonIgnore
<span class="nc" id="L53">    private String b3TraceId;</span>

    private JsonObject flatten(Predicate&lt;String&gt; predicate) {
<span class="fc" id="L56">        JsonObject result = Optional.ofNullable(data).map(JsonObject::copy).orElseGet(JsonObject::new);</span>

<span class="fc" id="L58">        JsonObject metadata = new JsonObject()</span>
<span class="fc" id="L59">                .put(Constants.ID, id)</span>
<span class="fc" id="L60">                .put(Constants.DELETED, deleted)</span>
<span class="fc" id="L61">                .put(Constants.TRACE_ID, traceId)</span>
<span class="fc" id="L62">                .put(Constants.B3_TRACE_ID, b3TraceId)</span>
<span class="fc" id="L63">                .put(Constants.SOURCE, source)</span>
<span class="fc" id="L64">                .put(Constants.SOURCE_GROUP, sourceGroup)</span>
<span class="fc" id="L65">                .put(Constants.SOURCE_COMPONENT, Optional.ofNullable(sourceComponent).map(Unity2Component::toString).orElse(null))</span>
<span class="fc" id="L66">                .put(Constants.SQL, sql)</span>
<span class="fc" id="L67">                .put(Constants.SQL_EXECUTION_MODE, Optional.ofNullable(sqlExecutionMode).map(SqlExecutionMode::toString).orElse(null))</span>
<span class="fc" id="L68">                .put(Constants.BATCH_ID, batchId)</span>
<span class="fc" id="L69">                .put(Constants.BATCH_SIZE, batchSize)</span>
<span class="fc" id="L70">                .put(Constants.MESSAGE_TYPE, Optional.ofNullable(messageType).map(MessageType::toString).orElse(null))</span>
<span class="fc" id="L71">                .put(Constants.RAW_PAYLOAD, rawPayload)</span>
<span class="fc" id="L72">                .put(Constants.KAFKA_PARTITION_KEY, kafkaPartitionKey)</span>
<span class="fc" id="L73">                .put(Constants.OPERATION_MAP, Optional.ofNullable(operationMap).map(JsonObject::getMap).orElse(null));</span>

<span class="fc" id="L75">        metadata.stream()</span>
<span class="fc" id="L76">                .filter(entry -&gt; predicate.test(entry.getKey()))</span>
<span class="fc" id="L77">                .forEach(entry -&gt; result.put(entry.getKey(), entry.getValue()));</span>

<span class="fc" id="L79">        return result;</span>
    }

    public JsonObject flatten() {
<span class="nc" id="L83">        return flatten(x -&gt; true);</span>
    }

    public JsonObject flatten(String... metadataNames) {
<span class="fc" id="L87">        List&lt;String&gt; includes = Arrays.asList(metadataNames);</span>
<span class="fc" id="L88">        return flatten(includes::contains);</span>
    }

    public static Unity2KafkaMessageBuilder from(Unity2KafkaMessage message) {
<span class="nc" id="L92">        return builder()</span>
<span class="nc" id="L93">                .id(message.id)</span>
<span class="nc" id="L94">                .deleted(message.deleted)</span>
<span class="nc" id="L95">                .traceId(message.traceId)</span>
<span class="nc" id="L96">                .b3TraceId(message.b3TraceId)</span>
<span class="nc" id="L97">                .source(message.source)</span>
<span class="nc" id="L98">                .sourceGroup(message.sourceGroup)</span>
<span class="nc" id="L99">                .sourceComponent(message.sourceComponent)</span>
<span class="nc" id="L100">                .data(Optional.ofNullable(message.data).map(JsonObject::copy).orElse(null))</span>
<span class="nc" id="L101">                .sql(message.sql)</span>
<span class="nc" id="L102">                .sqlExecutionMode(message.sqlExecutionMode)</span>
<span class="nc" id="L103">                .batchId(message.batchId)</span>
<span class="nc" id="L104">                .batchSize(message.batchSize)</span>
<span class="nc" id="L105">                .messageType(message.messageType)</span>
<span class="nc" id="L106">                .rawPayload(message.rawPayload)</span>
<span class="nc" id="L107">                .kafkaPartitionKey(message.kafkaPartitionKey)</span>
<span class="nc" id="L108">                .operationMap(message.operationMap);</span>
    }

    public static Unity2KafkaMessage from(JsonObject jsonObject) {
<span class="nc" id="L112">        JsonObject data = jsonObject.copy();</span>
<span class="nc" id="L113">        data.remove(Constants.ID);</span>
<span class="nc" id="L114">        data.remove(Constants.DELETED);</span>
<span class="nc" id="L115">        data.remove(Constants.TRACE_ID);</span>
<span class="nc" id="L116">        data.remove(Constants.B3_TRACE_ID);</span>
<span class="nc" id="L117">        data.remove(Constants.SOURCE);</span>
<span class="nc" id="L118">        data.remove(Constants.SOURCE_GROUP);</span>
<span class="nc" id="L119">        data.remove(Constants.SOURCE_COMPONENT);</span>
<span class="nc" id="L120">        data.remove(Constants.SQL);</span>
<span class="nc" id="L121">        data.remove(Constants.SQL_EXECUTION_MODE);</span>
<span class="nc" id="L122">        data.remove(Constants.BATCH_ID);</span>
<span class="nc" id="L123">        data.remove(Constants.BATCH_SIZE);</span>
<span class="nc" id="L124">        data.remove(Constants.MESSAGE_TYPE);</span>
<span class="nc" id="L125">        data.remove(Constants.RAW_PAYLOAD);</span>
<span class="nc" id="L126">        data.remove(Constants.KAFKA_PARTITION_KEY);</span>
<span class="nc" id="L127">        data.remove(Constants.OPERATION_MAP);</span>

<span class="nc" id="L129">        return Unity2KafkaMessage.builder()</span>
<span class="nc" id="L130">                .id(jsonObject.getString(Constants.ID))</span>
<span class="nc" id="L131">                .deleted(jsonObject.getBoolean(Constants.DELETED))</span>
<span class="nc" id="L132">                .traceId(jsonObject.getString(Constants.TRACE_ID))</span>
<span class="nc" id="L133">                .b3TraceId(jsonObject.getString(Constants.B3_TRACE_ID))</span>
<span class="nc" id="L134">                .source(jsonObject.getString(Constants.SOURCE))</span>
<span class="nc" id="L135">                .sourceGroup(jsonObject.getString(Constants.SOURCE_GROUP))</span>
<span class="nc" id="L136">                .sourceComponent(Unity2Component.fromValue(jsonObject.getString(Constants.SOURCE_COMPONENT)))</span>
<span class="nc" id="L137">                .data(data)</span>
<span class="nc" id="L138">                .sql(jsonObject.getString(Constants.SQL))</span>
<span class="nc" id="L139">                .sqlExecutionMode(SqlExecutionMode.fromValue(jsonObject.getString(Constants.SQL_EXECUTION_MODE)))</span>
<span class="nc" id="L140">                .batchId(jsonObject.getString(Constants.BATCH_ID))</span>
<span class="nc" id="L141">                .batchSize(jsonObject.getLong(Constants.BATCH_SIZE))</span>
<span class="nc" id="L142">                .messageType(MessageType.fromValue(jsonObject.getString(Constants.MESSAGE_TYPE)))</span>
<span class="nc" id="L143">                .rawPayload(jsonObject.getString(Constants.RAW_PAYLOAD))</span>
<span class="nc" id="L144">                .kafkaPartitionKey(jsonObject.getString(Constants.KAFKA_PARTITION_KEY))</span>
<span class="nc" id="L145">                .operationMap(jsonObject.getJsonObject(Constants.OPERATION_MAP))</span>
<span class="nc" id="L146">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>