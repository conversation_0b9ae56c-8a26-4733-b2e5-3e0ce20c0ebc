<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.kafka.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <span class="el_package">com.poc.hss.fasttrack.kafka.model</span></div><h1>com.poc.hss.fasttrack.kafka.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,284 of 1,594</td><td class="ctr2">19%</td><td class="bar">176 of 180</td><td class="ctr2">2%</td><td class="ctr1">167</td><td class="ctr2">185</td><td class="ctr1">88</td><td class="ctr2">124</td><td class="ctr1">79</td><td class="ctr2">95</td><td class="ctr1">3</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a3"><a href="Unity2KafkaMessage.java.html" class="el_source">Unity2KafkaMessage.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="96" height="10" title="1,018" alt="1,018"/><img src="../jacoco-resources/greenbar.gif" width="23" height="10" title="248" alt="248"/></td><td class="ctr2" id="c1">19%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="142" alt="142"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">127</td><td class="ctr2" id="g0">139</td><td class="ctr1" id="h0">70</td><td class="ctr2" id="i0">94</td><td class="ctr1" id="j0">56</td><td class="ctr2" id="k0">68</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a0"><a href="GenericKafkaMessage.java.html" class="el_source">GenericKafkaMessage.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="211" alt="211"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="30" alt="30"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">34</td><td class="ctr2" id="g1">34</td><td class="ctr1" id="h2">7</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j1">19</td><td class="ctr2" id="k1">19</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a2"><a href="SqlExecutionMode.java.html" class="el_source">SqlExecutionMode.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="55" alt="55"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h1">11</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j2">4</td><td class="ctr2" id="k2">4</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="MessageType.java.html" class="el_source">MessageType.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="62" alt="62"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i1">12</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">4</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>