<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaReader.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.reader</a> &gt; <span class="el_source">KafkaReader.java</span></div><h1>KafkaReader.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.reader;

import com.poc.hss.fasttrack.constant.ErrorCodeConstant;
import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaException;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.errors.InterruptException;
import org.apache.kafka.common.errors.WakeupException;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

<span class="nc" id="L23">@Slf4j</span>
public abstract class KafkaReader&lt;R, T&gt; {
<span class="nc" id="L25">    @Getter</span>
    private final Consumer&lt;String, R&gt; consumer;
<span class="nc" id="L27">    @Getter</span>
    private final List&lt;String&gt; topics;
<span class="nc" id="L29">    @Getter</span>
    private final Properties properties;
<span class="nc" id="L31">    private final AtomicBoolean active = new AtomicBoolean(true);</span>

<span class="nc" id="L33">    public KafkaReader(final Properties properties, final List&lt;String&gt; topics) {</span>
<span class="nc" id="L34">        this.topics = Collections.unmodifiableList(topics);</span>

<span class="nc" id="L36">        Properties baseProperties = new Properties();</span>
<span class="nc" id="L37">        propertiesCustomizer(baseProperties);</span>
<span class="nc" id="L38">        baseProperties.putAll(properties);</span>
<span class="nc" id="L39">        disableAutoCommit(baseProperties);</span>
<span class="nc" id="L40">        this.properties = baseProperties;</span>

<span class="nc" id="L42">        this.consumer = new KafkaConsumer&lt;&gt;(this.properties);</span>
<span class="nc" id="L43">        this.consumer.subscribe(topics);</span>
<span class="nc" id="L44">    }</span>

    public boolean isActive() {
<span class="nc" id="L47">        return active.get();</span>
    }

    public void poll(final long timeout, java.util.function.Consumer&lt;Stream&lt;T&gt;&gt; messageConsumer) {
        try {
<span class="nc bnc" id="L52" title="All 4 branches missed.">            while (!Thread.currentThread().isInterrupted() &amp;&amp; active.get()) {</span>
<span class="nc" id="L53">                Map&lt;TopicPartition, Long&gt; offsets = consumer.assignment()</span>
<span class="nc" id="L54">                        .stream()</span>
<span class="nc" id="L55">                        .collect(Collectors.toMap(</span>
<span class="nc" id="L56">                                Function.identity(),</span>
<span class="nc" id="L57">                                consumer::position</span>
                        ));

<span class="nc" id="L60">                Stream&lt;T&gt; messages = poll(timeout);</span>
<span class="nc" id="L61">                Iterator&lt;T&gt; iterator = messages.iterator();</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">                if (iterator.hasNext()) {</span>
                    try {
<span class="nc" id="L64">                        messageConsumer.accept(StreamSupport.stream(Spliterators.spliteratorUnknownSize(iterator, 0), false));</span>
<span class="nc" id="L65">                        consumer.commitAsync();</span>
<span class="nc" id="L66">                    } catch (Unity2KafkaException e) {</span>
<span class="nc" id="L67">                        log.error(&quot;Not committing previous messages due to&quot;, e);</span>
<span class="nc" id="L68">                        offsets.forEach(consumer::seek);</span>
<span class="nc" id="L69">                    }</span>
                }
<span class="nc" id="L71">            }</span>
<span class="nc" id="L72">            log.warn(&quot;Kafka Reader is inactive.&quot;);</span>
<span class="nc" id="L73">        } catch (WakeupException e) {</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">            if (active.get()) throw e;</span>
<span class="nc" id="L75">        } catch (Exception e) {</span>
<span class="nc" id="L76">            log.error(String.format(&quot;%s - Unexpected Exception Encountered&quot;, ErrorCodeConstant.PIPELINE_FATAL_ERROR), e);</span>
<span class="nc" id="L77">            throw e;</span>
        } finally {
<span class="nc" id="L79">            log.info(&quot;Closing kafka reader&quot;);</span>
<span class="nc" id="L80">            this.consumer.close();</span>
        }
<span class="nc" id="L82">    }</span>

    public Stream&lt;T&gt; poll(final long timeout) {
        try {
<span class="nc" id="L86">            final ConsumerRecords&lt;String, R&gt; records = consumer.poll(Duration.ofMillis(Long.max(0, timeout)));</span>
<span class="nc" id="L87">            return StreamSupport</span>
<span class="nc" id="L88">                    .stream(records.spliterator(), false)</span>
<span class="nc" id="L89">                    .peek(rec -&gt; log.debug(&quot;Partition: {}, Offset: {}&quot;, rec.partition(), rec.offset()))</span>
<span class="nc" id="L90">                    .map(converter());</span>
<span class="nc" id="L91">        } catch (InterruptException e) {</span>
<span class="nc" id="L92">            log.error(&quot;Kafka Consumer Thread - interrupt&quot;);</span>
<span class="nc" id="L93">            return Stream.empty();</span>
        }
    }

    public void commitAsync(){
<span class="nc" id="L98">        this.consumer.commitAsync();</span>
<span class="nc" id="L99">    }</span>

    private void disableAutoCommit(Properties properties) {
<span class="nc" id="L102">        properties.put(&quot;enable.auto.commit&quot;, false);</span>
<span class="nc" id="L103">    }</span>

    abstract protected Function&lt;ConsumerRecord&lt;String, R&gt;, T&gt; converter();

    abstract protected void propertiesCustomizer(Properties properties);

    public void close() {
<span class="nc" id="L110">        log.info(&quot;Initiate kafka reader close&quot;);</span>
<span class="nc" id="L111">        this.active.set(false);</span>
<span class="nc" id="L112">        this.consumer.wakeup();</span>
<span class="nc" id="L113">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>