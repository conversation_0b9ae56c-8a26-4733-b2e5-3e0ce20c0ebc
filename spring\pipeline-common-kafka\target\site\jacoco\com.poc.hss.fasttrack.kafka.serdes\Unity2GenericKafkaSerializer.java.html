<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2GenericKafkaSerializer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.serdes</a> &gt; <span class="el_source">Unity2GenericKafkaSerializer.java</span></div><h1>Unity2GenericKafkaSerializer.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.serdes;

import com.poc.hss.fasttrack.util.JsonUtils;
import org.apache.kafka.common.serialization.Serializer;

import java.nio.charset.StandardCharsets;

<span class="nc" id="L8">public class Unity2GenericKafkaSerializer implements Serializer&lt;Object&gt; {</span>
    @Override
    public byte[] serialize(String s, Object object) {
<span class="nc bnc" id="L11" title="All 2 branches missed.">        if (object instanceof String)</span>
<span class="nc" id="L12">            return ((String) object).getBytes(StandardCharsets.UTF_8);</span>

<span class="nc" id="L14">        return JsonUtils.serialize(object).getBytes(StandardCharsets.UTF_8);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>