<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaMessageDeserializer.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.serdes</a> &gt; <span class="el_source">Unity2KafkaMessageDeserializer.java</span></div><h1>Unity2KafkaMessageDeserializer.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.serdes;

import com.poc.hss.fasttrack.kafka.model.Unity2KafkaMessage;
import com.poc.hss.fasttrack.util.JsonUtils;
import org.apache.kafka.common.serialization.Deserializer;

import java.nio.charset.StandardCharsets;

<span class="nc" id="L9">public class Unity2KafkaMessageDeserializer implements Deserializer&lt;Unity2KafkaMessage&gt; {</span>

    @Override
    public Unity2KafkaMessage deserialize(String topic, byte[] content) {
<span class="nc" id="L13">        String utf8 = new String(content, StandardCharsets.UTF_8);</span>
<span class="nc" id="L14">        return JsonUtils.deserialize(utf8, Unity2KafkaMessage.class);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>