<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaPropertiesUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.util</a> &gt; <span class="el_source">KafkaPropertiesUtil.java</span></div><h1>KafkaPropertiesUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.util;

import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Properties;

<span class="nc" id="L11">public class KafkaPropertiesUtil {</span>
    public static boolean sameBroker(Properties sourceKafkaProperties, Properties targetKafkaProperties) {
<span class="nc" id="L13">        return new HashSet&lt;&gt;(Arrays.asList(sourceKafkaProperties.getProperty(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG).split(&quot;,&quot;)))</span>
<span class="nc" id="L14">                .equals(new HashSet&lt;&gt;(Arrays.asList(targetKafkaProperties.getProperty(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG).split(&quot;,&quot;))));</span>
    }

    public static boolean sameBroker(Map&lt;String, ?&gt; sourceKafkaProperties, Map&lt;String, ?&gt; targetKafkaProperties) {
<span class="nc" id="L18">        return new HashSet&lt;&gt;(Arrays.asList(sourceKafkaProperties.get(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG).toString().split(&quot;,&quot;)))</span>
<span class="nc" id="L19">                .equals(new HashSet&lt;&gt;(Arrays.asList(targetKafkaProperties.get(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG).toString().split(&quot;,&quot;))));</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>