<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.util</a> &gt; <span class="el_source">KafkaUtil.java</span></div><h1>KafkaUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.util;

import lombok.Builder;
import lombok.Data;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.PartitionInfo;
import org.apache.kafka.common.TopicPartition;

import java.util.*;
import java.util.stream.Collectors;

public class KafkaUtil {

    private KafkaUtil() {
    }

    public static final String KEY_PARTITION = &quot;partition&quot;;
    public static final String KEY_BEGINNING_OFFSET = &quot;beginning-offset&quot;;
    public static final String KEY_COMMITTED_OFFSET = &quot;committed-offset&quot;;
    public static final String KEY_END_OFFSET = &quot;end-offset&quot;;
    public static final String KEY_LAG = &quot;lag&quot;;

    public static List&lt;Metric&gt; getMetrics(Consumer&lt;?, ?&gt; consumer, String topic) {
<span class="nc" id="L25">        List&lt;PartitionInfo&gt; partitionsList = consumer.partitionsFor(topic);</span>
<span class="nc" id="L26">        List&lt;Metric&gt; metricsList = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L27">        final Set&lt;TopicPartition&gt; topicPartitionSet = partitionsList.stream()</span>
<span class="nc" id="L28">                .map(pi -&gt; new TopicPartition(topic, pi.partition()))</span>
<span class="nc" id="L29">                .collect(Collectors.toSet());</span>

<span class="nc" id="L31">        final Map&lt;TopicPartition, Long&gt; beginningOffsets = consumer.beginningOffsets(topicPartitionSet);</span>
<span class="nc" id="L32">        final Map&lt;TopicPartition, OffsetAndMetadata&gt; committedOffsets = consumer.committed(topicPartitionSet);</span>
<span class="nc" id="L33">        final Map&lt;TopicPartition, Long&gt; endOffsets = consumer.endOffsets(topicPartitionSet);</span>

<span class="nc" id="L35">        topicPartitionSet.forEach(tp -&gt; {</span>
<span class="nc bnc" id="L36" title="All 2 branches missed.">            long beginningOffset = beginningOffsets.get(tp) == null ? 0 : beginningOffsets.get(tp);</span>
<span class="nc bnc" id="L37" title="All 2 branches missed.">            Long committedOffset = committedOffsets.get(tp) == null ? null : committedOffsets.get(tp).offset();</span>
<span class="nc bnc" id="L38" title="All 2 branches missed.">            long endOffset = endOffsets.get(tp) == null ? 0 : endOffsets.get(tp);</span>
<span class="nc" id="L39">            metricsList.add(Metric.builder()</span>
<span class="nc" id="L40">                    .partition(tp.partition())</span>
<span class="nc" id="L41">                    .beginningOffset(beginningOffset)</span>
<span class="nc" id="L42">                    .committedOffset(committedOffset)</span>
<span class="nc" id="L43">                    .endOffset(endOffset)</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">                    .lag(committedOffset == null ? endOffset - beginningOffset : endOffset - committedOffset)</span>
<span class="nc" id="L45">                    .build());</span>
<span class="nc" id="L46">        });</span>
<span class="nc" id="L47">        metricsList.sort(Comparator.comparingInt(Metric::getPartition));</span>
<span class="nc" id="L48">        return metricsList;</span>
    }

<span class="nc bnc" id="L51" title="All 46 branches missed.">    @Data</span>
<span class="nc" id="L52">    @Builder</span>
    public static class Metric {
<span class="nc" id="L54">        private Integer partition;</span>
<span class="nc" id="L55">        private Long beginningOffset;</span>
<span class="nc" id="L56">        private Long committedOffset;</span>
<span class="nc" id="L57">        private Long endOffset;</span>
<span class="nc" id="L58">        private Long lag;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>