<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GenericKafkaWriter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.writer</a> &gt; <span class="el_source">GenericKafkaWriter.java</span></div><h1>GenericKafkaWriter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.writer;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.*;
import org.apache.kafka.common.serialization.Serializer;

import java.util.Properties;
import java.util.concurrent.Future;

<span class="nc" id="L11">@Slf4j</span>
public class GenericKafkaWriter&lt;K, V&gt; {
    protected final KafkaProducer&lt;K, V&gt; producer;

    public GenericKafkaWriter(
            final Properties properties,
            Class&lt;? extends Serializer&lt;? extends K&gt;&gt; keySerializer,
            Class&lt;? extends Serializer&lt;? extends V&gt;&gt; valueSerializer
<span class="nc" id="L19">    ) {</span>
<span class="nc" id="L20">        Properties props = new Properties();</span>
<span class="nc" id="L21">        props.putAll(properties);</span>
<span class="nc" id="L22">        props.putIfAbsent(</span>
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG,
<span class="nc" id="L24">                keySerializer.getName()</span>
        );
<span class="nc" id="L26">        props.putIfAbsent(</span>
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG,
<span class="nc" id="L28">                valueSerializer.getName()</span>
        );
<span class="nc" id="L30">        this.producer = new KafkaProducer&lt;&gt;(props);</span>
<span class="nc" id="L31">    }</span>

<span class="nc" id="L33">    @SneakyThrows</span>
    public void publish(ProducerRecord&lt;K, V&gt; record) {
<span class="nc" id="L35">        producer.send(record).get();</span>
<span class="nc" id="L36">    }</span>

    public Future&lt;RecordMetadata&gt; publishAsync(ProducerRecord&lt;K, V&gt; record) {
<span class="nc" id="L39">        return producer.send(record);</span>
    }

    public Future&lt;RecordMetadata&gt; publishAsync(ProducerRecord&lt;K, V&gt; record, Callback callback) {
<span class="nc" id="L43">        return producer.send(record, callback);</span>
    }

    public void close() {
<span class="nc" id="L47">        log.info(&quot;Closing kafka avro writer&quot;);</span>
<span class="nc" id="L48">        producer.close();</span>
<span class="nc" id="L49">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>