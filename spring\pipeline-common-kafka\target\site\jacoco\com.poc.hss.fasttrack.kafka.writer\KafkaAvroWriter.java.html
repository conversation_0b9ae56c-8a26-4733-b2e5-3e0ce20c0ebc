<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaAvroWriter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.writer</a> &gt; <span class="el_source">KafkaAvroWriter.java</span></div><h1>KafkaAvroWriter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.writer;

import io.confluent.kafka.serializers.KafkaAvroSerializer;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.avro.Schema;
import org.apache.avro.generic.GenericData;
import org.apache.avro.generic.GenericRecord;
import org.apache.kafka.clients.producer.ProducerRecord;

import java.net.URL;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.stream.Collectors;

<span class="nc" id="L18">@Slf4j</span>
public class KafkaAvroWriter extends KeyValueKafkaWriter&lt;Map&lt;String, Object&gt;, Map&lt;String, Object&gt;, Object, Object&gt; {

<span class="nc" id="L21">    @Getter</span>
    private final Schema keySchema;
<span class="nc" id="L23">    @Getter</span>
    private final Schema valueSchema;
    private final Map&lt;String, Schema.Type&gt; keyTypeMap;
    private final Map&lt;String, Schema.Type&gt; valueTypeMap;

<span class="nc" id="L28">    @SneakyThrows</span>
    public KafkaAvroWriter(String topic, Properties properties, String schemaRegistryUrl) {
<span class="nc" id="L30">        super(topic, properties, KafkaAvroSerializer.class, KafkaAvroSerializer.class);</span>
<span class="nc" id="L31">        this.keySchema = new Schema.Parser().parse(new URL(String.format(&quot;%ssubjects/%s-key/versions/latest/schema&quot;, schemaRegistryUrl, topic)).openStream());</span>
<span class="nc" id="L32">        this.valueSchema = new Schema.Parser().parse(new URL(String.format(&quot;%ssubjects/%s-value/versions/latest/schema&quot;, schemaRegistryUrl, topic)).openStream());</span>
<span class="nc" id="L33">        this.keyTypeMap = keySchema.getFields().stream().collect(Collectors.toMap(Schema.Field::name, field -&gt; field.schema().getType()));</span>
<span class="nc" id="L34">        this.valueTypeMap = valueSchema.getFields().stream().collect(Collectors.toMap(Schema.Field::name, field -&gt; field.schema().getType()));</span>
<span class="nc" id="L35">    }</span>

    @Override
    protected ProducerRecord&lt;Object, Object&gt; createProducerRecord(Map&lt;String, Object&gt; key, Map&lt;String, Object&gt; value) {
<span class="nc" id="L39">        GenericRecord keyRecord = new GenericData.Record(this.keySchema);</span>
<span class="nc" id="L40">        GenericRecord valueRecord = new GenericData.Record(this.valueSchema);</span>
<span class="nc" id="L41">        this.keyTypeMap.forEach((fieldName, type) -&gt;</span>
<span class="nc" id="L42">                keyRecord.put(fieldName, getTypeSensitiveValue(Optional.ofNullable(key).orElse(value).get(fieldName), type))</span>
        );
<span class="nc" id="L44">        this.valueTypeMap.forEach((fieldName, type) -&gt;</span>
<span class="nc" id="L45">                valueRecord.put(fieldName, getTypeSensitiveValue(value.get(fieldName), type))</span>
        );
<span class="nc" id="L47">        return new ProducerRecord&lt;&gt;(this.topic, keyRecord, valueRecord);</span>
    }

    private Object getTypeSensitiveValue(Object value, Schema.Type type) {
<span class="nc bnc" id="L51" title="All 2 branches missed.">        if (value == null) {</span>
<span class="nc" id="L52">            return null;</span>
        }

<span class="nc bnc" id="L55" title="All 6 branches missed.">        switch (type) {</span>
            case INT:
<span class="nc" id="L57">                return new Integer(value.toString());</span>
            case LONG:
<span class="nc" id="L59">                return new Long(value.toString());</span>
            case FLOAT:
<span class="nc" id="L61">                return new Float(value.toString());</span>
            case DOUBLE:
<span class="nc" id="L63">                return new Double(value.toString());</span>
            case BOOLEAN:
<span class="nc" id="L65">                return Boolean.valueOf(value.toString());</span>
            default:
<span class="nc" id="L67">                return value.toString();</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>