<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KeyValueKafkaWriter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.writer</a> &gt; <span class="el_source">KeyValueKafkaWriter.java</span></div><h1>KeyValueKafkaWriter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.writer;

import lombok.Getter;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.Serializer;

import java.util.Properties;
import java.util.concurrent.Future;

public abstract class KeyValueKafkaWriter&lt;K_IN, V_IN, K_OUT, V_OUT&gt; {
<span class="nc" id="L13">    @Getter</span>
    protected final String topic;
    protected final GenericKafkaWriter&lt;K_OUT, V_OUT&gt; delegate;

    public KeyValueKafkaWriter(
            String topic,
            Properties properties,
            Class&lt;? extends Serializer&lt;? extends K_OUT&gt;&gt; keySerializer,
            Class&lt;? extends Serializer&lt;? extends V_OUT&gt;&gt; valueSerializer
<span class="nc" id="L22">    ) {</span>
<span class="nc" id="L23">        this.topic = topic;</span>
<span class="nc" id="L24">        this.delegate = new GenericKafkaWriter&lt;&gt;(properties, keySerializer, valueSerializer);</span>
<span class="nc" id="L25">    }</span>

    public void publish(V_IN value) {
<span class="nc" id="L28">        publish(null, value);</span>
<span class="nc" id="L29">    }</span>

    public Future&lt;RecordMetadata&gt; publishAsync(V_IN value) {
<span class="nc" id="L32">        return publishAsync(null, value);</span>
    }

    public Future&lt;RecordMetadata&gt; publishAsync(V_IN value, Callback callback) {
<span class="nc" id="L36">        return publishAsync(null, value, callback);</span>
    }

    public void publish(K_IN key, V_IN value) {
<span class="nc" id="L40">        this.delegate.publish(createProducerRecord(key, value));</span>
<span class="nc" id="L41">    }</span>

    public Future&lt;RecordMetadata&gt; publishAsync(K_IN key, V_IN value) {
<span class="nc" id="L44">        return this.delegate.publishAsync(createProducerRecord(key, value));</span>
    }

    public Future&lt;RecordMetadata&gt; publishAsync(K_IN key, V_IN value, Callback callback) {
<span class="nc" id="L48">        return this.delegate.publishAsync(createProducerRecord(key, value), callback);</span>
    }

    protected abstract ProducerRecord&lt;K_OUT, V_OUT&gt; createProducerRecord(K_IN key, V_IN value);

    public void close() {
<span class="nc" id="L54">        this.delegate.close();</span>
<span class="nc" id="L55">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>