<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaMessageWriter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.writer</a> &gt; <span class="el_source">Unity2KafkaMessageWriter.java</span></div><h1>Unity2KafkaMessageWriter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.writer;

import com.poc.hss.fasttrack.kafka.serdes.Unity2KafkaMessageSerializer;
import com.poc.hss.fasttrack.kafka.model.Unity2KafkaMessage;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;

import java.util.Properties;
import java.util.concurrent.Future;

public class Unity2KafkaMessageWriter extends KeyValueKafkaWriter&lt;String, Unity2KafkaMessage, String, Unity2KafkaMessage&gt; {
    public Unity2KafkaMessageWriter(String topic, Properties properties) {
<span class="nc" id="L15">        super(topic, properties, StringSerializer.class, Unity2KafkaMessageSerializer.class);</span>
<span class="nc" id="L16">    }</span>

    public void publish(Unity2KafkaMessage value) {
<span class="nc" id="L19">        publish(null, value);</span>
<span class="nc" id="L20">    }</span>

    public Future&lt;RecordMetadata&gt; publishAsync(Unity2KafkaMessage value, Callback callback) {
<span class="nc" id="L23">        return publishAsync(null, value, callback);</span>
    }

    public void publish(String key, Unity2KafkaMessage value) {
<span class="nc" id="L27">        this.delegate.publish(createProducerRecord(key, value));</span>
<span class="nc" id="L28">    }</span>

    public Future&lt;RecordMetadata&gt; publishAsync(String key, Unity2KafkaMessage value, Callback callback) {
<span class="nc" id="L31">        return this.delegate.publishAsync(createProducerRecord(key, value), callback);</span>
    }

    protected ProducerRecord&lt;String, Unity2KafkaMessage&gt; createProducerRecord(String key, Unity2KafkaMessage value) {
<span class="nc" id="L35">        return new ProducerRecord&lt;&gt;(this.topic, key, value);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>