<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.kafka.writer</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-kafka</a> &gt; <span class="el_package">com.poc.hss.fasttrack.kafka.writer</span></div><h1>com.poc.hss.fasttrack.kafka.writer</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">371 of 371</td><td class="ctr2">0%</td><td class="bar">8 of 8</td><td class="ctr2">0%</td><td class="ctr1">39</td><td class="ctr2">39</td><td class="ctr1">72</td><td class="ctr2">72</td><td class="ctr1">33</td><td class="ctr2">33</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a1"><a href="KafkaAvroWriter.html" class="el_class">KafkaAvroWriter</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="194" alt="194"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">16</td><td class="ctr2" id="g0">16</td><td class="ctr1" id="h0">26</td><td class="ctr2" id="i0">26</td><td class="ctr1" id="j0">10</td><td class="ctr2" id="k0">10</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a3"><a href="KeyValueKafkaWriter.html" class="el_class">KeyValueKafkaWriter</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="62" alt="62"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h2">15</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j1">9</td><td class="ctr2" id="k1">9</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="GenericKafkaWriter.html" class="el_class">GenericKafkaWriter</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="61" alt="61"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h1">18</td><td class="ctr2" id="i1">18</td><td class="ctr1" id="j2">6</td><td class="ctr2" id="k2">6</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="Unity2KafkaMessageWriter.html" class="el_class">Unity2KafkaMessageWriter</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="43" alt="43"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j3">6</td><td class="ctr2" id="k3">6</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a2"><a href="KafkaMessageWriter.html" class="el_class">KafkaMessageWriter</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="11" alt="11"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">4</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j4">2</td><td class="ctr2" id="k4">2</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>