<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="jacoco-resources/report.gif" type="image/gif"/><title>pipeline-common-kafka</title><script type="text/javascript" src="jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="jacoco-sessions.html" class="el_session">Sessions</a></span><span class="el_report">pipeline-common-kafka</span></div><h1>pipeline-common-kafka</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,489 of 2,799</td><td class="ctr2">11%</td><td class="bar">248 of 252</td><td class="ctr2">1%</td><td class="ctr1">306</td><td class="ctr2">324</td><td class="ctr1">292</td><td class="ctr2">328</td><td class="ctr1">180</td><td class="ctr2">196</td><td class="ctr1">23</td><td class="ctr2">26</td></tr></tfoot><tbody><tr><td id="a1"><a href="com.poc.hss.fasttrack.kafka.model/index.html" class="el_package">com.poc.hss.fasttrack.kafka.model</a></td><td class="bar" id="b0"><img src="jacoco-resources/redbar.gif" width="96" height="10" title="1,284" alt="1,284"/><img src="jacoco-resources/greenbar.gif" width="23" height="10" title="310" alt="310"/></td><td class="ctr2" id="c0">19%</td><td class="bar" id="d0"><img src="jacoco-resources/redbar.gif" width="117" height="10" title="176" alt="176"/><img src="jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">2%</td><td class="ctr1" id="f0">167</td><td class="ctr2" id="g0">185</td><td class="ctr1" id="h0">88</td><td class="ctr2" id="i0">124</td><td class="ctr1" id="j0">79</td><td class="ctr2" id="k0">95</td><td class="ctr1" id="l4">3</td><td class="ctr2" id="m0">6</td></tr><tr><td id="a4"><a href="com.poc.hss.fasttrack.kafka.util/index.html" class="el_package">com.poc.hss.fasttrack.kafka.util</a></td><td class="bar" id="b1"><img src="jacoco-resources/redbar.gif" width="35" height="10" title="469" alt="469"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="jacoco-resources/redbar.gif" width="36" height="10" title="54" alt="54"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">57</td><td class="ctr2" id="g1">57</td><td class="ctr1" id="h3">34</td><td class="ctr2" id="i3">34</td><td class="ctr1" id="j2">30</td><td class="ctr2" id="k2">30</td><td class="ctr1" id="l2">4</td><td class="ctr2" id="m3">4</td></tr><tr><td id="a5"><a href="com.poc.hss.fasttrack.kafka.writer/index.html" class="el_package">com.poc.hss.fasttrack.kafka.writer</a></td><td class="bar" id="b2"><img src="jacoco-resources/redbar.gif" width="27" height="10" title="371" alt="371"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">39</td><td class="ctr2" id="g2">39</td><td class="ctr1" id="h2">72</td><td class="ctr2" id="i2">72</td><td class="ctr1" id="j1">33</td><td class="ctr2" id="k1">33</td><td class="ctr1" id="l1">5</td><td class="ctr2" id="m2">5</td></tr><tr><td id="a2"><a href="com.poc.hss.fasttrack.kafka.reader/index.html" class="el_package">com.poc.hss.fasttrack.kafka.reader</a></td><td class="bar" id="b3"><img src="jacoco-resources/redbar.gif" width="21" height="10" title="280" alt="280"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="jacoco-resources/redbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">27</td><td class="ctr2" id="g3">27</td><td class="ctr1" id="h1">75</td><td class="ctr2" id="i1">75</td><td class="ctr1" id="j3">23</td><td class="ctr2" id="k3">23</td><td class="ctr1" id="l3">4</td><td class="ctr2" id="m4">4</td></tr><tr><td id="a3"><a href="com.poc.hss.fasttrack.kafka.serdes/index.html" class="el_package">com.poc.hss.fasttrack.kafka.serdes</a></td><td class="bar" id="b4"><img src="jacoco-resources/redbar.gif" width="5" height="10" title="78" alt="78"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">14</td><td class="ctr2" id="g4">14</td><td class="ctr1" id="h4">19</td><td class="ctr2" id="i4">19</td><td class="ctr1" id="j4">13</td><td class="ctr2" id="k4">13</td><td class="ctr1" id="l0">6</td><td class="ctr2" id="m1">6</td></tr><tr><td id="a0"><a href="com.poc.hss.fasttrack.kafka.exception/index.html" class="el_package">com.poc.hss.fasttrack.kafka.exception</a></td><td class="bar" id="b5"/><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j5">2</td><td class="ctr2" id="k5">2</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>