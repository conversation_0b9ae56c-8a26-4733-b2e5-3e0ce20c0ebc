<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="pipeline-common-kafka"><sessioninfo id="H344L0L63UFKL6Z-56baca64" start="1752782179673" dump="1752782180669"/><package name="com/poc/hss/fasttrack/kafka/exception"><class name="com/poc/hss/fasttrack/kafka/exception/Unity2KafkaException" sourcefilename="Unity2KafkaException.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;)V" line="10"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="Unity2KafkaException.java"><line nr="6" mi="2" ci="0" mb="0" cb="0"/><line nr="7" mi="1" ci="0" mb="0" cb="0"/><line nr="10" mi="3" ci="0" mb="0" cb="0"/><line nr="11" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/util"><class name="com/poc/hss/fasttrack/kafka/util/KafkaUtil" sourcefilename="KafkaUtil.java"><method name="getMetrics" desc="(Lorg/apache/kafka/clients/consumer/Consumer;Ljava/lang/String;)Ljava/util/List;" line="25"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$getMetrics$1" desc="(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/List;Lorg/apache/kafka/common/TopicPartition;)V" line="36"><counter type="INSTRUCTION" missed="67" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$getMetrics$0" desc="(Ljava/lang/String;Lorg/apache/kafka/common/PartitionInfo;)Lorg/apache/kafka/common/TopicPartition;" line="28"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="116" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="22" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder" sourcefilename="KafkaUtil.java"><method name="&lt;init&gt;" desc="()V" line="52"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="partition" desc="(Ljava/lang/Integer;)Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder;" line="52"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="beginningOffset" desc="(Ljava/lang/Long;)Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder;" line="52"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="committedOffset" desc="(Ljava/lang/Long;)Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder;" line="52"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="endOffset" desc="(Ljava/lang/Long;)Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder;" line="52"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lag" desc="(Ljava/lang/Long;)Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder;" line="52"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric;" line="52"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="52"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="54" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="8" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric" sourcefilename="KafkaUtil.java"><method name="&lt;init&gt;" desc="(Ljava/lang/Integer;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;Ljava/lang/Long;)V" line="52"><counter type="INSTRUCTION" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/kafka/util/KafkaUtil$Metric$MetricBuilder;" line="52"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getPartition" desc="()Ljava/lang/Integer;" line="54"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getBeginningOffset" desc="()Ljava/lang/Long;" line="55"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getCommittedOffset" desc="()Ljava/lang/Long;" line="56"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getEndOffset" desc="()Ljava/lang/Long;" line="57"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getLag" desc="()Ljava/lang/Long;" line="58"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setPartition" desc="(Ljava/lang/Integer;)V" line="51"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setBeginningOffset" desc="(Ljava/lang/Long;)V" line="51"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setCommittedOffset" desc="(Ljava/lang/Long;)V" line="51"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setEndOffset" desc="(Ljava/lang/Long;)V" line="51"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setLag" desc="(Ljava/lang/Long;)V" line="51"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="51"><counter type="INSTRUCTION" missed="106" covered="0"/><counter type="BRANCH" missed="36" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="19" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="51"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="51"><counter type="INSTRUCTION" missed="76" covered="0"/><counter type="BRANCH" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="51"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="254" covered="0"/><counter type="BRANCH" missed="46" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="39" covered="0"/><counter type="METHOD" missed="16" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/util/KafkaPropertiesUtil" sourcefilename="KafkaPropertiesUtil.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="sameBroker" desc="(Ljava/util/Properties;Ljava/util/Properties;)Z" line="13"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="sameBroker" desc="(Ljava/util/Map;Ljava/util/Map;)Z" line="18"><counter type="INSTRUCTION" missed="22" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="45" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="KafkaUtil.java"><line nr="25" mi="4" ci="0" mb="0" cb="0"/><line nr="26" mi="4" ci="0" mb="0" cb="0"/><line nr="27" mi="4" ci="0" mb="0" cb="0"/><line nr="28" mi="8" ci="0" mb="0" cb="0"/><line nr="29" mi="4" ci="0" mb="0" cb="0"/><line nr="31" mi="4" ci="0" mb="0" cb="0"/><line nr="32" mi="4" ci="0" mb="0" cb="0"/><line nr="33" mi="4" ci="0" mb="0" cb="0"/><line nr="35" mi="7" ci="0" mb="0" cb="0"/><line nr="36" mi="12" ci="0" mb="2" cb="0"/><line nr="37" mi="13" ci="0" mb="2" cb="0"/><line nr="38" mi="12" ci="0" mb="2" cb="0"/><line nr="39" mi="5" ci="0" mb="0" cb="0"/><line nr="40" mi="4" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="0" cb="0"/><line nr="42" mi="2" ci="0" mb="0" cb="0"/><line nr="43" mi="2" ci="0" mb="0" cb="0"/><line nr="44" mi="12" ci="0" mb="2" cb="0"/><line nr="45" mi="1" ci="0" mb="0" cb="0"/><line nr="46" mi="1" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="0" cb="0"/><line nr="48" mi="2" ci="0" mb="0" cb="0"/><line nr="51" mi="217" ci="0" mb="46" cb="0"/><line nr="52" mi="76" ci="0" mb="0" cb="0"/><line nr="54" mi="3" ci="0" mb="0" cb="0"/><line nr="55" mi="3" ci="0" mb="0" cb="0"/><line nr="56" mi="3" ci="0" mb="0" cb="0"/><line nr="57" mi="3" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="424" covered="0"/><counter type="BRANCH" missed="54" covered="0"/><counter type="LINE" missed="29" covered="0"/><counter type="COMPLEXITY" missed="54" covered="0"/><counter type="METHOD" missed="27" covered="0"/><counter type="CLASS" missed="3" covered="0"/></sourcefile><sourcefile name="KafkaPropertiesUtil.java"><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="14" ci="0" mb="0" cb="0"/><line nr="14" mi="6" ci="0" mb="0" cb="0"/><line nr="18" mi="15" ci="0" mb="0" cb="0"/><line nr="19" mi="7" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="45" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="469" covered="0"/><counter type="BRANCH" missed="54" covered="0"/><counter type="LINE" missed="34" covered="0"/><counter type="COMPLEXITY" missed="57" covered="0"/><counter type="METHOD" missed="30" covered="0"/><counter type="CLASS" missed="4" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/model"><class name="com/poc/hss/fasttrack/kafka/model/MessageType" sourcefilename="MessageType.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="13"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="20"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/MessageType;" line="25"><counter type="INSTRUCTION" missed="0" covered="26"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="3"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="0" covered="24"/><counter type="LINE" missed="0" covered="4"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="62"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/model/GenericKafkaMessage$GenericKafkaMessageBuilder" sourcefilename="GenericKafkaMessage.java"><method name="&lt;init&gt;" desc="()V" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="id" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/GenericKafkaMessage$GenericKafkaMessageBuilder;" line="15"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="data" desc="(Ljava/lang/Object;)Lcom/poc/hss/fasttrack/kafka/model/GenericKafkaMessage$GenericKafkaMessageBuilder;" line="15"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="consumerRecord" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Lcom/poc/hss/fasttrack/kafka/model/GenericKafkaMessage$GenericKafkaMessageBuilder;" line="15"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/kafka/model/GenericKafkaMessage;" line="15"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="15"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="38" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/model/GenericKafkaMessage" sourcefilename="GenericKafkaMessage.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/kafka/model/GenericKafkaMessage$GenericKafkaMessageBuilder;" line="15"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getId" desc="()Ljava/lang/String;" line="18"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getData" desc="()Ljava/lang/Object;" line="20"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getConsumerRecord" desc="()Lorg/apache/kafka/clients/consumer/ConsumerRecord;" line="22"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setId" desc="(Ljava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setData" desc="(Ljava/lang/Object;)V" line="12"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setConsumerRecord" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="12"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="12"><counter type="INSTRUCTION" missed="72" covered="0"/><counter type="BRANCH" missed="24" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="13" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="12"><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/Object;Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="13"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="()V" line="14"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="173" covered="0"/><counter type="BRANCH" missed="30" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="28" covered="0"/><counter type="METHOD" missed="13" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder" sourcefilename="Unity2KafkaMessage.java"><method name="&lt;init&gt;" desc="()V" line="18"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="id" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="deleted" desc="(Ljava/lang/Boolean;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="traceId" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="source" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="sourceGroup" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="sourceComponent" desc="(Lcom/poc/hss/fasttrack/model/Unity2Component;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="data" desc="(Lio/vertx/core/json/JsonObject;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="sql" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="sqlExecutionMode" desc="(Lcom/poc/hss/fasttrack/kafka/model/SqlExecutionMode;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="batchId" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="batchSize" desc="(Ljava/lang/Long;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="messageType" desc="(Lcom/poc/hss/fasttrack/kafka/model/MessageType;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="rawPayload" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kafkaPartitionKey" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="dataPersistMode" desc="(Lcom/poc/hss/fasttrack/enums/DataPersistMode;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="operationMap" desc="(Lio/vertx/core/json/JsonObject;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="b3TraceId" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;" line="18"><counter type="INSTRUCTION" missed="0" covered="38"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="18"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="107" covered="61"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="14" covered="6"/><counter type="METHOD" missed="14" covered="6"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/model/SqlExecutionMode" sourcefilename="SqlExecutionMode.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;ILjava/lang/String;)V" line="12"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="19"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="fromValue" desc="(Ljava/lang/String;)Lcom/poc/hss/fasttrack/kafka/model/SqlExecutionMode;" line="24"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="55" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage" sourcefilename="Unity2KafkaMessage.java"><method name="flatten" desc="(Ljava/util/function/Predicate;)Lio/vertx/core/json/JsonObject;" line="56"><counter type="INSTRUCTION" missed="0" covered="103"/><counter type="LINE" missed="0" covered="21"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="flatten" desc="()Lio/vertx/core/json/JsonObject;" line="83"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="flatten" desc="([Ljava/lang/String;)Lio/vertx/core/json/JsonObject;" line="87"><counter type="INSTRUCTION" missed="0" covered="11"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="from" desc="(Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="92"><counter type="INSTRUCTION" missed="56" covered="0"/><counter type="LINE" missed="17" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="from" desc="(Lio/vertx/core/json/JsonObject;)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;" line="112"><counter type="INSTRUCTION" missed="131" covered="0"/><counter type="LINE" missed="34" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/poc/hss/fasttrack/model/Unity2Component;Lio/vertx/core/json/JsonObject;Ljava/lang/String;Lcom/poc/hss/fasttrack/kafka/model/SqlExecutionMode;Ljava/lang/String;Ljava/lang/Long;Lcom/poc/hss/fasttrack/kafka/model/MessageType;Ljava/lang/String;Ljava/lang/String;Lcom/poc/hss/fasttrack/enums/DataPersistMode;Lio/vertx/core/json/JsonObject;Ljava/lang/String;)V" line="18"><counter type="INSTRUCTION" missed="0" covered="54"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage$Unity2KafkaMessageBuilder;" line="18"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getId" desc="()Ljava/lang/String;" line="21"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDeleted" desc="()Ljava/lang/Boolean;" line="23"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTraceId" desc="()Ljava/lang/String;" line="25"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSource" desc="()Ljava/lang/String;" line="27"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSourceGroup" desc="()Ljava/lang/String;" line="29"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSourceComponent" desc="()Lcom/poc/hss/fasttrack/model/Unity2Component;" line="31"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getData" desc="()Lio/vertx/core/json/JsonObject;" line="33"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSql" desc="()Ljava/lang/String;" line="35"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getSqlExecutionMode" desc="()Lcom/poc/hss/fasttrack/kafka/model/SqlExecutionMode;" line="37"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getBatchId" desc="()Ljava/lang/String;" line="39"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getBatchSize" desc="()Ljava/lang/Long;" line="41"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getMessageType" desc="()Lcom/poc/hss/fasttrack/kafka/model/MessageType;" line="43"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getRawPayload" desc="()Ljava/lang/String;" line="45"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getKafkaPartitionKey" desc="()Ljava/lang/String;" line="47"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getDataPersistMode" desc="()Lcom/poc/hss/fasttrack/enums/DataPersistMode;" line="49"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getOperationMap" desc="()Lio/vertx/core/json/JsonObject;" line="51"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getB3TraceId" desc="()Ljava/lang/String;" line="53"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setId" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDeleted" desc="(Ljava/lang/Boolean;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setTraceId" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setSource" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setSourceGroup" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setSourceComponent" desc="(Lcom/poc/hss/fasttrack/model/Unity2Component;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setData" desc="(Lio/vertx/core/json/JsonObject;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setSql" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setSqlExecutionMode" desc="(Lcom/poc/hss/fasttrack/kafka/model/SqlExecutionMode;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setBatchId" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setBatchSize" desc="(Ljava/lang/Long;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setMessageType" desc="(Lcom/poc/hss/fasttrack/kafka/model/MessageType;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setRawPayload" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setKafkaPartitionKey" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setDataPersistMode" desc="(Lcom/poc/hss/fasttrack/enums/DataPersistMode;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setOperationMap" desc="(Lio/vertx/core/json/JsonObject;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setB3TraceId" desc="(Ljava/lang/String;)V" line="17"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="17"><counter type="INSTRUCTION" missed="310" covered="0"/><counter type="BRANCH" missed="108" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="55" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="17"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="17"><counter type="INSTRUCTION" missed="244" covered="0"/><counter type="BRANCH" missed="34" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="18" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="17"><counter type="INSTRUCTION" missed="42" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$flatten$2" desc="(Ljava/lang/String;)Z" line="83"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$flatten$1" desc="(Lio/vertx/core/json/JsonObject;Ljava/util/Map$Entry;)V" line="77"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$flatten$0" desc="(Ljava/util/function/Predicate;Ljava/util/Map$Entry;)Z" line="76"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="911" covered="187"/><counter type="BRANCH" missed="142" covered="0"/><counter type="LINE" missed="70" covered="24"/><counter type="COMPLEXITY" missed="113" covered="6"/><counter type="METHOD" missed="42" covered="6"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="MessageType.java"><line nr="6" mi="0" ci="3" mb="0" cb="0"/><line nr="7" mi="0" ci="7" mb="0" cb="0"/><line nr="8" mi="0" ci="7" mb="0" cb="0"/><line nr="9" mi="0" ci="7" mb="0" cb="0"/><line nr="13" mi="0" ci="4" mb="0" cb="0"/><line nr="14" mi="0" ci="3" mb="0" cb="0"/><line nr="15" mi="0" ci="1" mb="0" cb="0"/><line nr="20" mi="0" ci="4" mb="0" cb="0"/><line nr="25" mi="0" ci="16" mb="0" cb="2"/><line nr="26" mi="0" ci="6" mb="0" cb="2"/><line nr="27" mi="0" ci="2" mb="0" cb="0"/><line nr="30" mi="0" ci="2" mb="0" cb="0"/><counter type="INSTRUCTION" missed="0" covered="62"/><counter type="BRANCH" missed="0" covered="4"/><counter type="LINE" missed="0" covered="12"/><counter type="COMPLEXITY" missed="0" covered="6"/><counter type="METHOD" missed="0" covered="4"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="Unity2KafkaMessage.java"><line nr="17" mi="667" ci="0" mb="142" cb="0"/><line nr="18" mi="107" ci="119" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="29" mi="3" ci="0" mb="0" cb="0"/><line nr="31" mi="3" ci="0" mb="0" cb="0"/><line nr="33" mi="3" ci="0" mb="0" cb="0"/><line nr="35" mi="3" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="0" cb="0"/><line nr="43" mi="3" ci="0" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="47" mi="3" ci="0" mb="0" cb="0"/><line nr="49" mi="3" ci="0" mb="0" cb="0"/><line nr="51" mi="3" ci="0" mb="0" cb="0"/><line nr="53" mi="3" ci="0" mb="0" cb="0"/><line nr="56" mi="0" ci="9" mb="0" cb="0"/><line nr="58" mi="0" ci="6" mb="0" cb="0"/><line nr="59" mi="0" ci="4" mb="0" cb="0"/><line nr="60" mi="0" ci="4" mb="0" cb="0"/><line nr="61" mi="0" ci="4" mb="0" cb="0"/><line nr="62" mi="0" ci="4" mb="0" cb="0"/><line nr="63" mi="0" ci="4" mb="0" cb="0"/><line nr="64" mi="0" ci="4" mb="0" cb="0"/><line nr="65" mi="0" ci="9" mb="0" cb="0"/><line nr="66" mi="0" ci="4" mb="0" cb="0"/><line nr="67" mi="0" ci="9" mb="0" cb="0"/><line nr="68" mi="0" ci="4" mb="0" cb="0"/><line nr="69" mi="0" ci="4" mb="0" cb="0"/><line nr="70" mi="0" ci="9" mb="0" cb="0"/><line nr="71" mi="0" ci="4" mb="0" cb="0"/><line nr="72" mi="0" ci="4" mb="0" cb="0"/><line nr="73" mi="0" ci="7" mb="0" cb="0"/><line nr="75" mi="0" ci="4" mb="0" cb="0"/><line nr="76" mi="0" ci="9" mb="0" cb="0"/><line nr="77" mi="0" ci="10" mb="0" cb="0"/><line nr="79" mi="0" ci="2" mb="0" cb="0"/><line nr="83" mi="6" ci="0" mb="0" cb="0"/><line nr="87" mi="0" ci="3" mb="0" cb="0"/><line nr="88" mi="0" ci="8" mb="0" cb="0"/><line nr="92" mi="4" ci="0" mb="0" cb="0"/><line nr="93" mi="3" ci="0" mb="0" cb="0"/><line nr="94" mi="3" ci="0" mb="0" cb="0"/><line nr="95" mi="3" ci="0" mb="0" cb="0"/><line nr="96" mi="3" ci="0" mb="0" cb="0"/><line nr="97" mi="3" ci="0" mb="0" cb="0"/><line nr="98" mi="3" ci="0" mb="0" cb="0"/><line nr="99" mi="3" ci="0" mb="0" cb="0"/><line nr="100" mi="9" ci="0" mb="0" cb="0"/><line nr="101" mi="3" ci="0" mb="0" cb="0"/><line nr="102" mi="3" ci="0" mb="0" cb="0"/><line nr="103" mi="3" ci="0" mb="0" cb="0"/><line nr="104" mi="3" ci="0" mb="0" cb="0"/><line nr="105" mi="3" ci="0" mb="0" cb="0"/><line nr="106" mi="3" ci="0" mb="0" cb="0"/><line nr="107" mi="3" ci="0" mb="0" cb="0"/><line nr="108" mi="1" ci="0" mb="0" cb="0"/><line nr="112" mi="3" ci="0" mb="0" cb="0"/><line nr="113" mi="4" ci="0" mb="0" cb="0"/><line nr="114" mi="4" ci="0" mb="0" cb="0"/><line nr="115" mi="4" ci="0" mb="0" cb="0"/><line nr="116" mi="4" ci="0" mb="0" cb="0"/><line nr="117" mi="4" ci="0" mb="0" cb="0"/><line nr="118" mi="4" ci="0" mb="0" cb="0"/><line nr="119" mi="4" ci="0" mb="0" cb="0"/><line nr="120" mi="4" ci="0" mb="0" cb="0"/><line nr="121" mi="4" ci="0" mb="0" cb="0"/><line nr="122" mi="4" ci="0" mb="0" cb="0"/><line nr="123" mi="4" ci="0" mb="0" cb="0"/><line nr="124" mi="4" ci="0" mb="0" cb="0"/><line nr="125" mi="4" ci="0" mb="0" cb="0"/><line nr="126" mi="4" ci="0" mb="0" cb="0"/><line nr="127" mi="4" ci="0" mb="0" cb="0"/><line nr="129" mi="4" ci="0" mb="0" cb="0"/><line nr="130" mi="4" ci="0" mb="0" cb="0"/><line nr="131" mi="4" ci="0" mb="0" cb="0"/><line nr="132" mi="4" ci="0" mb="0" cb="0"/><line nr="133" mi="4" ci="0" mb="0" cb="0"/><line nr="134" mi="4" ci="0" mb="0" cb="0"/><line nr="135" mi="4" ci="0" mb="0" cb="0"/><line nr="136" mi="4" ci="0" mb="0" cb="0"/><line nr="137" mi="3" ci="0" mb="0" cb="0"/><line nr="138" mi="4" ci="0" mb="0" cb="0"/><line nr="139" mi="5" ci="0" mb="0" cb="0"/><line nr="140" mi="4" ci="0" mb="0" cb="0"/><line nr="141" mi="4" ci="0" mb="0" cb="0"/><line nr="142" mi="5" ci="0" mb="0" cb="0"/><line nr="143" mi="4" ci="0" mb="0" cb="0"/><line nr="144" mi="4" ci="0" mb="0" cb="0"/><line nr="145" mi="2" ci="0" mb="0" cb="0"/><line nr="146" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="1018" covered="248"/><counter type="BRANCH" missed="142" covered="0"/><counter type="LINE" missed="70" covered="24"/><counter type="COMPLEXITY" missed="127" covered="12"/><counter type="METHOD" missed="56" covered="12"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><sourcefile name="SqlExecutionMode.java"><line nr="6" mi="3" ci="0" mb="0" cb="0"/><line nr="7" mi="7" ci="0" mb="0" cb="0"/><line nr="8" mi="7" ci="0" mb="0" cb="0"/><line nr="12" mi="4" ci="0" mb="0" cb="0"/><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="14" mi="1" ci="0" mb="0" cb="0"/><line nr="19" mi="4" ci="0" mb="0" cb="0"/><line nr="24" mi="16" ci="0" mb="2" cb="0"/><line nr="25" mi="6" ci="0" mb="2" cb="0"/><line nr="26" mi="2" ci="0" mb="0" cb="0"/><line nr="29" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="55" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="GenericKafkaMessage.java"><line nr="12" mi="145" ci="0" mb="30" cb="0"/><line nr="13" mi="12" ci="0" mb="0" cb="0"/><line nr="14" mi="3" ci="0" mb="0" cb="0"/><line nr="15" mi="42" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="211" covered="0"/><counter type="BRANCH" missed="30" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="34" covered="0"/><counter type="METHOD" missed="19" covered="0"/><counter type="CLASS" missed="2" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="1284" covered="310"/><counter type="BRANCH" missed="176" covered="4"/><counter type="LINE" missed="88" covered="36"/><counter type="COMPLEXITY" missed="167" covered="18"/><counter type="METHOD" missed="79" covered="16"/><counter type="CLASS" missed="3" covered="3"/></package><package name="com/poc/hss/fasttrack/kafka/writer"><class name="com/poc/hss/fasttrack/kafka/writer/KafkaMessageWriter" sourcefilename="KafkaMessageWriter.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/lang/Class;)V" line="11"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/util/Properties;)V" line="15"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/writer/KeyValueKafkaWriter" sourcefilename="KeyValueKafkaWriter.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/util/Properties;Ljava/lang/Class;Ljava/lang/Class;)V" line="22"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publish" desc="(Ljava/lang/Object;)V" line="28"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Ljava/lang/Object;)Ljava/util/concurrent/Future;" line="32"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Ljava/lang/Object;Lorg/apache/kafka/clients/producer/Callback;)Ljava/util/concurrent/Future;" line="36"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publish" desc="(Ljava/lang/Object;Ljava/lang/Object;)V" line="40"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/concurrent/Future;" line="44"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Ljava/lang/Object;Ljava/lang/Object;Lorg/apache/kafka/clients/producer/Callback;)Ljava/util/concurrent/Future;" line="48"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="close" desc="()V" line="54"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTopic" desc="()Ljava/lang/String;" line="13"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="62" covered="0"/><counter type="LINE" missed="15" covered="0"/><counter type="COMPLEXITY" missed="9" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/writer/Unity2KafkaMessageWriter" sourcefilename="Unity2KafkaMessageWriter.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/util/Properties;)V" line="15"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publish" desc="(Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;)V" line="19"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;Lorg/apache/kafka/clients/producer/Callback;)Ljava/util/concurrent/Future;" line="23"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publish" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;)V" line="27"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;Lorg/apache/kafka/clients/producer/Callback;)Ljava/util/concurrent/Future;" line="31"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createProducerRecord" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;)Lorg/apache/kafka/clients/producer/ProducerRecord;" line="35"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="43" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/writer/GenericKafkaWriter" sourcefilename="GenericKafkaWriter.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/lang/Class;Ljava/lang/Class;)V" line="19"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publish" desc="(Lorg/apache/kafka/clients/producer/ProducerRecord;)V" line="33"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Lorg/apache/kafka/clients/producer/ProducerRecord;)Ljava/util/concurrent/Future;" line="39"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="publishAsync" desc="(Lorg/apache/kafka/clients/producer/ProducerRecord;Lorg/apache/kafka/clients/producer/Callback;)Ljava/util/concurrent/Future;" line="43"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="close" desc="()V" line="47"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="61" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/writer/KafkaAvroWriter" sourcefilename="KafkaAvroWriter.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/util/Properties;Ljava/lang/String;)V" line="28"><counter type="INSTRUCTION" missed="77" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createProducerRecord" desc="(Ljava/util/Map;Ljava/util/Map;)Lorg/apache/kafka/clients/producer/ProducerRecord;" line="39"><counter type="INSTRUCTION" missed="35" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTypeSensitiveValue" desc="(Ljava/lang/Object;Lorg/apache/avro/Schema$Type;)Ljava/lang/Object;" line="51"><counter type="INSTRUCTION" missed="40" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getKeySchema" desc="()Lorg/apache/avro/Schema;" line="21"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getValueSchema" desc="()Lorg/apache/avro/Schema;" line="23"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$createProducerRecord$3" desc="(Lorg/apache/avro/generic/GenericRecord;Ljava/util/Map;Ljava/lang/String;Lorg/apache/avro/Schema$Type;)V" line="45"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$createProducerRecord$2" desc="(Lorg/apache/avro/generic/GenericRecord;Ljava/util/Map;Ljava/util/Map;Ljava/lang/String;Lorg/apache/avro/Schema$Type;)V" line="42"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$new$1" desc="(Lorg/apache/avro/Schema$Field;)Lorg/apache/avro/Schema$Type;" line="34"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$new$0" desc="(Lorg/apache/avro/Schema$Field;)Lorg/apache/avro/Schema$Type;" line="33"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="194" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="26" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="KeyValueKafkaWriter.java"><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="2" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="8" ci="0" mb="0" cb="0"/><line nr="25" mi="1" ci="0" mb="0" cb="0"/><line nr="28" mi="4" ci="0" mb="0" cb="0"/><line nr="29" mi="1" ci="0" mb="0" cb="0"/><line nr="32" mi="5" ci="0" mb="0" cb="0"/><line nr="36" mi="6" ci="0" mb="0" cb="0"/><line nr="40" mi="7" ci="0" mb="0" cb="0"/><line nr="41" mi="1" ci="0" mb="0" cb="0"/><line nr="44" mi="8" ci="0" mb="0" cb="0"/><line nr="48" mi="9" ci="0" mb="0" cb="0"/><line nr="54" mi="3" ci="0" mb="0" cb="0"/><line nr="55" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="62" covered="0"/><counter type="LINE" missed="15" covered="0"/><counter type="COMPLEXITY" missed="9" covered="0"/><counter type="METHOD" missed="9" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaMessageWriter.java"><line nr="11" mi="5" ci="0" mb="0" cb="0"/><line nr="12" mi="1" ci="0" mb="0" cb="0"/><line nr="15" mi="4" ci="0" mb="0" cb="0"/><line nr="16" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaAvroWriter.java"><line nr="18" mi="4" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="28" mi="4" ci="0" mb="0" cb="0"/><line nr="30" mi="6" ci="0" mb="0" cb="0"/><line nr="31" mi="22" ci="0" mb="0" cb="0"/><line nr="32" mi="22" ci="0" mb="0" cb="0"/><line nr="33" mi="15" ci="0" mb="0" cb="0"/><line nr="34" mi="15" ci="0" mb="0" cb="0"/><line nr="35" mi="1" ci="0" mb="0" cb="0"/><line nr="39" mi="6" ci="0" mb="0" cb="0"/><line nr="40" mi="6" ci="0" mb="0" cb="0"/><line nr="41" mi="8" ci="0" mb="0" cb="0"/><line nr="42" mi="14" ci="0" mb="0" cb="0"/><line nr="44" mi="7" ci="0" mb="0" cb="0"/><line nr="45" mi="10" ci="0" mb="0" cb="0"/><line nr="47" mi="8" ci="0" mb="0" cb="0"/><line nr="51" mi="2" ci="0" mb="2" cb="0"/><line nr="52" mi="2" ci="0" mb="0" cb="0"/><line nr="55" mi="5" ci="0" mb="6" cb="0"/><line nr="57" mi="6" ci="0" mb="0" cb="0"/><line nr="59" mi="6" ci="0" mb="0" cb="0"/><line nr="61" mi="6" ci="0" mb="0" cb="0"/><line nr="63" mi="6" ci="0" mb="0" cb="0"/><line nr="65" mi="4" ci="0" mb="0" cb="0"/><line nr="67" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="194" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="26" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaMessageWriter.java"><line nr="15" mi="6" ci="0" mb="0" cb="0"/><line nr="16" mi="1" ci="0" mb="0" cb="0"/><line nr="19" mi="4" ci="0" mb="0" cb="0"/><line nr="20" mi="1" ci="0" mb="0" cb="0"/><line nr="23" mi="6" ci="0" mb="0" cb="0"/><line nr="27" mi="7" ci="0" mb="0" cb="0"/><line nr="28" mi="1" ci="0" mb="0" cb="0"/><line nr="31" mi="9" ci="0" mb="0" cb="0"/><line nr="35" mi="8" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="43" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="GenericKafkaWriter.java"><line nr="11" mi="4" ci="0" mb="0" cb="0"/><line nr="19" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="4" ci="0" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="22" mi="5" ci="0" mb="0" cb="0"/><line nr="24" mi="1" ci="0" mb="0" cb="0"/><line nr="26" mi="5" ci="0" mb="0" cb="0"/><line nr="28" mi="1" ci="0" mb="0" cb="0"/><line nr="30" mi="6" ci="0" mb="0" cb="0"/><line nr="31" mi="1" ci="0" mb="0" cb="0"/><line nr="33" mi="4" ci="0" mb="0" cb="0"/><line nr="35" mi="6" ci="0" mb="0" cb="0"/><line nr="36" mi="1" ci="0" mb="0" cb="0"/><line nr="39" mi="5" ci="0" mb="0" cb="0"/><line nr="43" mi="6" ci="0" mb="0" cb="0"/><line nr="47" mi="3" ci="0" mb="0" cb="0"/><line nr="48" mi="3" ci="0" mb="0" cb="0"/><line nr="49" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="61" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="6" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="371" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="72" covered="0"/><counter type="COMPLEXITY" missed="39" covered="0"/><counter type="METHOD" missed="33" covered="0"/><counter type="CLASS" missed="5" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/serdes"><class name="com/poc/hss/fasttrack/kafka/serdes/JsonObjectDeserializer" sourcefilename="JsonObjectDeserializer.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="deserialize" desc="(Ljava/lang/String;[B)Lio/vertx/core/json/JsonObject;" line="12"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/serdes/Unity2GenericKafkaSerializer" sourcefilename="Unity2GenericKafkaSerializer.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="serialize" desc="(Ljava/lang/String;Ljava/lang/Object;)[B" line="11"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/serdes/Unity2KafkaMessageDeserializer" sourcefilename="Unity2KafkaMessageDeserializer.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="deserialize" desc="(Ljava/lang/String;[B)Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;" line="13"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/serdes/Unity2KafkaMessageSerializer" sourcefilename="Unity2KafkaMessageSerializer.java"><method name="&lt;init&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="serialize" desc="(Ljava/lang/String;Lcom/poc/hss/fasttrack/kafka/model/Unity2KafkaMessage;)[B" line="13"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/serdes/JsonObjectSerdes" sourcefilename="JsonObjectSerdes.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="serializer" desc="()Lorg/apache/kafka/common/serialization/Serializer;" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="deserializer" desc="()Lorg/apache/kafka/common/serialization/Deserializer;" line="20"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/serdes/JsonObjectSerializer" sourcefilename="JsonObjectSerializer.java"><method name="&lt;init&gt;" desc="()V" line="6"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="serialize" desc="(Ljava/lang/String;Lio/vertx/core/json/JsonObject;)[B" line="10"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="Unity2KafkaMessageDeserializer.java"><line nr="9" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="6" ci="0" mb="0" cb="0"/><line nr="14" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="JsonObjectSerializer.java"><line nr="6" mi="3" ci="0" mb="0" cb="0"/><line nr="10" mi="4" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="JsonObjectDeserializer.java"><line nr="8" mi="3" ci="0" mb="0" cb="0"/><line nr="12" mi="6" ci="0" mb="0" cb="0"/><line nr="13" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaMessageSerializer.java"><line nr="9" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2GenericKafkaSerializer.java"><line nr="8" mi="3" ci="0" mb="0" cb="0"/><line nr="11" mi="3" ci="0" mb="2" cb="0"/><line nr="12" mi="5" ci="0" mb="0" cb="0"/><line nr="14" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="JsonObjectSerdes.java"><line nr="8" mi="2" ci="0" mb="0" cb="0"/><line nr="10" mi="5" ci="0" mb="0" cb="0"/><line nr="11" mi="6" ci="0" mb="0" cb="0"/><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="78" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="19" covered="0"/><counter type="COMPLEXITY" missed="14" covered="0"/><counter type="METHOD" missed="13" covered="0"/><counter type="CLASS" missed="6" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/reader"><class name="com/poc/hss/fasttrack/kafka/reader/Unity2KafkaMessageReader" sourcefilename="Unity2KafkaMessageReader.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/util/List;)V" line="16"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="converter" desc="()Ljava/util/function/Function;" line="21"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="propertiesCustomizer" desc="(Ljava/util/Properties;)V" line="26"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/reader/KafkaReader" sourcefilename="KafkaReader.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/util/List;)V" line="31"><counter type="INSTRUCTION" missed="40" covered="0"/><counter type="LINE" missed="11" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="isActive" desc="()Z" line="47"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="poll" desc="(JLjava/util/function/Consumer;)V" line="52"><counter type="INSTRUCTION" missed="88" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="25" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="poll" desc="(J)Ljava/util/stream/Stream;" line="86"><counter type="INSTRUCTION" missed="24" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="commitAsync" desc="()V" line="98"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="disableAutoCommit" desc="(Ljava/util/Properties;)V" line="102"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="close" desc="()V" line="110"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getConsumer" desc="()Lorg/apache/kafka/clients/consumer/Consumer;" line="25"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTopics" desc="()Ljava/util/List;" line="27"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getProperties" desc="()Ljava/util/Properties;" line="29"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$poll$0" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="89"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="23"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="201" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="57" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/reader/KafkaMessageReader" sourcefilename="KafkaMessageReader.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/util/List;)V" line="15"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="converter" desc="()Ljava/util/function/Function;" line="20"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="propertiesCustomizer" desc="(Ljava/util/Properties;)V" line="25"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$converter$0" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Lcom/poc/hss/fasttrack/kafka/model/GenericKafkaMessage;" line="20"><counter type="INSTRUCTION" missed="11" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/reader/KafkaAvroReader" sourcefilename="KafkaAvroReader.java"><method name="&lt;init&gt;" desc="(Ljava/util/Properties;Ljava/util/List;)V" line="17"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="converter" desc="()Ljava/util/function/Function;" line="22"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="propertiesCustomizer" desc="(Ljava/util/Properties;)V" line="27"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$converter$0" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Lio/vertx/core/json/JsonObject;" line="22"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="KafkaAvroReader.java"><line nr="17" mi="4" ci="0" mb="0" cb="0"/><line nr="18" mi="1" ci="0" mb="0" cb="0"/><line nr="22" mi="10" ci="0" mb="0" cb="0"/><line nr="27" mi="6" ci="0" mb="0" cb="0"/><line nr="28" mi="6" ci="0" mb="0" cb="0"/><line nr="30" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaMessageReader.java"><line nr="15" mi="4" ci="0" mb="0" cb="0"/><line nr="16" mi="1" ci="0" mb="0" cb="0"/><line nr="20" mi="13" ci="0" mb="0" cb="0"/><line nr="25" mi="6" ci="0" mb="0" cb="0"/><line nr="26" mi="6" ci="0" mb="0" cb="0"/><line nr="27" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaMessageReader.java"><line nr="16" mi="4" ci="0" mb="0" cb="0"/><line nr="17" mi="1" ci="0" mb="0" cb="0"/><line nr="21" mi="2" ci="0" mb="0" cb="0"/><line nr="26" mi="6" ci="0" mb="0" cb="0"/><line nr="27" mi="6" ci="0" mb="0" cb="0"/><line nr="28" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaReader.java"><line nr="23" mi="4" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="29" mi="3" ci="0" mb="0" cb="0"/><line nr="31" mi="6" ci="0" mb="0" cb="0"/><line nr="33" mi="2" ci="0" mb="0" cb="0"/><line nr="34" mi="4" ci="0" mb="0" cb="0"/><line nr="36" mi="4" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="3" ci="0" mb="0" cb="0"/><line nr="42" mi="7" ci="0" mb="0" cb="0"/><line nr="43" mi="4" ci="0" mb="0" cb="0"/><line nr="44" mi="1" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="0" cb="0"/><line nr="52" mi="7" ci="0" mb="4" cb="0"/><line nr="53" mi="3" ci="0" mb="0" cb="0"/><line nr="54" mi="1" ci="0" mb="0" cb="0"/><line nr="55" mi="4" ci="0" mb="0" cb="0"/><line nr="56" mi="4" ci="0" mb="0" cb="0"/><line nr="57" mi="3" ci="0" mb="0" cb="0"/><line nr="60" mi="4" ci="0" mb="0" cb="0"/><line nr="61" mi="3" ci="0" mb="0" cb="0"/><line nr="62" mi="3" ci="0" mb="2" cb="0"/><line nr="64" mi="7" ci="0" mb="0" cb="0"/><line nr="65" mi="3" ci="0" mb="0" cb="0"/><line nr="66" mi="1" ci="0" mb="0" cb="0"/><line nr="67" mi="4" ci="0" mb="0" cb="0"/><line nr="68" mi="8" ci="0" mb="0" cb="0"/><line nr="69" mi="1" ci="0" mb="0" cb="0"/><line nr="71" mi="1" ci="0" mb="0" cb="0"/><line nr="72" mi="3" ci="0" mb="0" cb="0"/><line nr="73" mi="1" ci="0" mb="0" cb="0"/><line nr="74" mi="6" ci="0" mb="2" cb="0"/><line nr="75" mi="1" ci="0" mb="0" cb="0"/><line nr="76" mi="11" ci="0" mb="0" cb="0"/><line nr="77" mi="2" ci="0" mb="0" cb="0"/><line nr="79" mi="3" ci="0" mb="0" cb="0"/><line nr="80" mi="3" ci="0" mb="0" cb="0"/><line nr="82" mi="1" ci="0" mb="0" cb="0"/><line nr="86" mi="8" ci="0" mb="0" cb="0"/><line nr="87" mi="2" ci="0" mb="0" cb="0"/><line nr="88" mi="4" ci="0" mb="0" cb="0"/><line nr="89" mi="12" ci="0" mb="0" cb="0"/><line nr="90" mi="2" ci="0" mb="0" cb="0"/><line nr="91" mi="1" ci="0" mb="0" cb="0"/><line nr="92" mi="3" ci="0" mb="0" cb="0"/><line nr="93" mi="2" ci="0" mb="0" cb="0"/><line nr="98" mi="3" ci="0" mb="0" cb="0"/><line nr="99" mi="1" ci="0" mb="0" cb="0"/><line nr="102" mi="6" ci="0" mb="0" cb="0"/><line nr="103" mi="1" ci="0" mb="0" cb="0"/><line nr="110" mi="3" ci="0" mb="0" cb="0"/><line nr="111" mi="4" ci="0" mb="0" cb="0"/><line nr="112" mi="3" ci="0" mb="0" cb="0"/><line nr="113" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="201" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="57" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="280" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="75" covered="0"/><counter type="COMPLEXITY" missed="27" covered="0"/><counter type="METHOD" missed="23" covered="0"/><counter type="CLASS" missed="4" covered="0"/></package><counter type="INSTRUCTION" missed="2489" covered="310"/><counter type="BRANCH" missed="248" covered="4"/><counter type="LINE" missed="292" covered="36"/><counter type="COMPLEXITY" missed="306" covered="18"/><counter type="METHOD" missed="180" covered="16"/><counter type="CLASS" missed="23" covered="3"/></report>