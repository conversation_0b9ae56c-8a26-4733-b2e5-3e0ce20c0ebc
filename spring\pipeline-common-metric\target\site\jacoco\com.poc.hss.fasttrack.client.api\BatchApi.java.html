<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.api</a> &gt; <span class="el_source">BatchApi.java</span></div><h1>BatchApi.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.client.api;

import com.poc.hss.fasttrack.client.ApiClient;

import com.poc.hss.fasttrack.client.model.BatchPageResponse;
import com.poc.hss.fasttrack.client.model.BatchStatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

@Component(&quot;com.poc.hss.fasttrack.client.api.BatchApi&quot;)
public class BatchApi {
    private ApiClient apiClient;

    public BatchApi() {
<span class="nc" id="L32">        this(new ApiClient());</span>
<span class="nc" id="L33">    }</span>

    @Autowired
<span class="nc" id="L36">    public BatchApi(ApiClient apiClient) {</span>
<span class="nc" id="L37">        this.apiClient = apiClient;</span>
<span class="nc" id="L38">    }</span>

    public ApiClient getApiClient() {
<span class="nc" id="L41">        return apiClient;</span>
    }

    public void setApiClient(ApiClient apiClient) {
<span class="nc" id="L45">        this.apiClient = apiClient;</span>
<span class="nc" id="L46">    }</span>

    /**
     * Search batch
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param group The group parameter
     * @param component The component parameter
     * @param batch The batch parameter
     * @param status The status parameter
     * @param offset The offset parameter
     * @param limit The limit parameter
     * @param sort format is {name}:[asc|desc]
     * @return BatchPageResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public BatchPageResponse searchBatch(String group, String component, String batch, BatchStatus status, Integer offset, Integer limit, String sort) throws RestClientException {
<span class="nc" id="L63">        Object postBody = null;</span>
<span class="nc" id="L64">        String path = UriComponentsBuilder.fromPath(&quot;/batch&quot;).build().toUriString();</span>
        
<span class="nc" id="L66">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L67">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L68">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>
<span class="nc" id="L69">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;group&quot;, group));</span>
<span class="nc" id="L70">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;component&quot;, component));</span>
<span class="nc" id="L71">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;batch&quot;, batch));</span>
<span class="nc" id="L72">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;status&quot;, status));</span>
<span class="nc" id="L73">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;offset&quot;, offset));</span>
<span class="nc" id="L74">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;limit&quot;, limit));</span>
<span class="nc" id="L75">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;sort&quot;, sort));</span>

<span class="nc" id="L77">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L80">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L81">        final String[] contentTypes = {  };</span>
<span class="nc" id="L82">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L84">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L86">        ParameterizedTypeReference&lt;BatchPageResponse&gt; returnType = new ParameterizedTypeReference&lt;BatchPageResponse&gt;() {};</span>
<span class="nc" id="L87">        return apiClient.invokeAPI(path, HttpMethod.GET, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>