<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheV3Api</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.client.api</a> &gt; <span class="el_class">CacheV3Api</span></div><h1>CacheV3Api</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">638 of 638</td><td class="ctr2">0%</td><td class="bar">22 of 22</td><td class="ctr2">0%</td><td class="ctr1">21</td><td class="ctr2">21</td><td class="ctr1">130</td><td class="ctr2">130</td><td class="ctr1">10</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a7"><a href="CacheV3Api.java.html#L227" class="el_method">searchCache(String, String, String, String, BatchStatus, Boolean, Integer, Integer, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="134" alt="134"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">21</td><td class="ctr2" id="i3">21</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="CacheV3Api.java.html#L146" class="el_method">getCache(String, String, String, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="102" height="10" title="114" alt="114"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h1">23</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="CacheV3Api.java.html#L99" class="el_method">evictCache(String, String, String, String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="98" height="10" title="110" alt="110"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h0">24</td><td class="ctr2" id="i0">24</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="CacheV3Api.java.html#L267" class="el_method">updateCache(String, String, String, CacheRequestV3)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="98" height="10" title="110" alt="110"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h2">22</td><td class="ctr2" id="i2">22</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="CacheV3Api.java.html#L59" class="el_method">clearCache(String, String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="79" height="10" title="89" alt="89"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h4">20</td><td class="ctr2" id="i4">20</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="CacheV3Api.java.html#L191" class="el_method">getCacheNames()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="62" alt="62"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">12</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="CacheV3Api.java.html#L34" class="el_method">CacheV3Api()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a1"><a href="CacheV3Api.java.html#L38" class="el_method">CacheV3Api(ApiClient)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="CacheV3Api.java.html#L47" class="el_method">setApiClient(ApiClient)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="CacheV3Api.java.html#L43" class="el_method">getApiClient()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>