<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheV3Api.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.api</a> &gt; <span class="el_source">CacheV3Api.java</span></div><h1>CacheV3Api.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.client.api;

import com.poc.hss.fasttrack.client.ApiClient;

import com.poc.hss.fasttrack.client.model.BatchStatus;
import com.poc.hss.fasttrack.client.model.CachePageResponseV3;
import com.poc.hss.fasttrack.client.model.CacheRequestV3;
import com.poc.hss.fasttrack.client.model.CacheResponseV3;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

@Component(&quot;com.poc.hss.fasttrack.client.api.CacheV3Api&quot;)
public class CacheV3Api {
    private ApiClient apiClient;

    public CacheV3Api() {
<span class="nc" id="L34">        this(new ApiClient());</span>
<span class="nc" id="L35">    }</span>

    @Autowired
<span class="nc" id="L38">    public CacheV3Api(ApiClient apiClient) {</span>
<span class="nc" id="L39">        this.apiClient = apiClient;</span>
<span class="nc" id="L40">    }</span>

    public ApiClient getApiClient() {
<span class="nc" id="L43">        return apiClient;</span>
    }

    public void setApiClient(ApiClient apiClient) {
<span class="nc" id="L47">        this.apiClient = apiClient;</span>
<span class="nc" id="L48">    }</span>

    /**
     * Clear cache
     * 
     * &lt;p&gt;&lt;b&gt;204&lt;/b&gt; - OK
     * @param group The group parameter
     * @param component The component parameter
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void clearCache(String group, String component) throws RestClientException {
<span class="nc" id="L59">        Object postBody = null;</span>
        // verify the required parameter 'group' is set
<span class="nc bnc" id="L61" title="All 2 branches missed.">        if (group == null) {</span>
<span class="nc" id="L62">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'group' when calling clearCache&quot;);</span>
        }
        // verify the required parameter 'component' is set
<span class="nc bnc" id="L65" title="All 2 branches missed.">        if (component == null) {</span>
<span class="nc" id="L66">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'component' when calling clearCache&quot;);</span>
        }
        // create path and map variables
<span class="nc" id="L69">        final Map&lt;String, Object&gt; uriVariables = new HashMap&lt;String, Object&gt;();</span>
<span class="nc" id="L70">        uriVariables.put(&quot;group&quot;, group);</span>
<span class="nc" id="L71">        uriVariables.put(&quot;component&quot;, component);</span>
<span class="nc" id="L72">        String path = UriComponentsBuilder.fromPath(&quot;/cache/{group}/{component}&quot;).buildAndExpand(uriVariables).toUriString();</span>
        
<span class="nc" id="L74">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L75">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L76">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="nc" id="L78">        final String[] accepts = {  };</span>
<span class="nc" id="L79">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L80">        final String[] contentTypes = {  };</span>
<span class="nc" id="L81">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L83">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L85">        ParameterizedTypeReference&lt;Void&gt; returnType = new ParameterizedTypeReference&lt;Void&gt;() {};</span>
<span class="nc" id="L86">        apiClient.invokeAPI(path, HttpMethod.DELETE, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
<span class="nc" id="L87">    }</span>
    /**
     * Evict cache
     * 
     * &lt;p&gt;&lt;b&gt;204&lt;/b&gt; - OK
     * @param group The group parameter
     * @param component The component parameter
     * @param key The key parameter
     * @param batch The batch parameter
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void evictCache(String group, String component, String key, String batch) throws RestClientException {
<span class="nc" id="L99">        Object postBody = null;</span>
        // verify the required parameter 'group' is set
<span class="nc bnc" id="L101" title="All 2 branches missed.">        if (group == null) {</span>
<span class="nc" id="L102">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'group' when calling evictCache&quot;);</span>
        }
        // verify the required parameter 'component' is set
<span class="nc bnc" id="L105" title="All 2 branches missed.">        if (component == null) {</span>
<span class="nc" id="L106">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'component' when calling evictCache&quot;);</span>
        }
        // verify the required parameter 'key' is set
<span class="nc bnc" id="L109" title="All 2 branches missed.">        if (key == null) {</span>
<span class="nc" id="L110">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'key' when calling evictCache&quot;);</span>
        }
        // create path and map variables
<span class="nc" id="L113">        final Map&lt;String, Object&gt; uriVariables = new HashMap&lt;String, Object&gt;();</span>
<span class="nc" id="L114">        uriVariables.put(&quot;group&quot;, group);</span>
<span class="nc" id="L115">        uriVariables.put(&quot;component&quot;, component);</span>
<span class="nc" id="L116">        uriVariables.put(&quot;key&quot;, key);</span>
<span class="nc" id="L117">        String path = UriComponentsBuilder.fromPath(&quot;/cache/{group}/{component}/{key}&quot;).buildAndExpand(uriVariables).toUriString();</span>
        
<span class="nc" id="L119">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L120">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L121">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>
<span class="nc" id="L122">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;batch&quot;, batch));</span>

<span class="nc" id="L124">        final String[] accepts = {  };</span>
<span class="nc" id="L125">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L126">        final String[] contentTypes = {  };</span>
<span class="nc" id="L127">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L129">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L131">        ParameterizedTypeReference&lt;Void&gt; returnType = new ParameterizedTypeReference&lt;Void&gt;() {};</span>
<span class="nc" id="L132">        apiClient.invokeAPI(path, HttpMethod.DELETE, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
<span class="nc" id="L133">    }</span>
    /**
     * Get cache
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param group The group parameter
     * @param component The component parameter
     * @param key The key parameter
     * @param batch The batch parameter
     * @return CacheResponseV3
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CacheResponseV3 getCache(String group, String component, String key, String batch) throws RestClientException {
<span class="nc" id="L146">        Object postBody = null;</span>
        // verify the required parameter 'group' is set
<span class="nc bnc" id="L148" title="All 2 branches missed.">        if (group == null) {</span>
<span class="nc" id="L149">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'group' when calling getCache&quot;);</span>
        }
        // verify the required parameter 'component' is set
<span class="nc bnc" id="L152" title="All 2 branches missed.">        if (component == null) {</span>
<span class="nc" id="L153">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'component' when calling getCache&quot;);</span>
        }
        // verify the required parameter 'key' is set
<span class="nc bnc" id="L156" title="All 2 branches missed.">        if (key == null) {</span>
<span class="nc" id="L157">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'key' when calling getCache&quot;);</span>
        }
        // create path and map variables
<span class="nc" id="L160">        final Map&lt;String, Object&gt; uriVariables = new HashMap&lt;String, Object&gt;();</span>
<span class="nc" id="L161">        uriVariables.put(&quot;group&quot;, group);</span>
<span class="nc" id="L162">        uriVariables.put(&quot;component&quot;, component);</span>
<span class="nc" id="L163">        uriVariables.put(&quot;key&quot;, key);</span>
<span class="nc" id="L164">        String path = UriComponentsBuilder.fromPath(&quot;/cache/{group}/{component}/{key}&quot;).buildAndExpand(uriVariables).toUriString();</span>
        
<span class="nc" id="L166">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L167">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L168">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>
<span class="nc" id="L169">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;batch&quot;, batch));</span>

<span class="nc" id="L171">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L174">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L175">        final String[] contentTypes = {  };</span>
<span class="nc" id="L176">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L178">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L180">        ParameterizedTypeReference&lt;CacheResponseV3&gt; returnType = new ParameterizedTypeReference&lt;CacheResponseV3&gt;() {};</span>
<span class="nc" id="L181">        return apiClient.invokeAPI(path, HttpMethod.GET, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
    /**
     * Get cache names
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @return List&amp;lt;String&amp;gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List&lt;String&gt; getCacheNames() throws RestClientException {
<span class="nc" id="L191">        Object postBody = null;</span>
<span class="nc" id="L192">        String path = UriComponentsBuilder.fromPath(&quot;/cache-names&quot;).build().toUriString();</span>
        
<span class="nc" id="L194">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L195">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L196">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="nc" id="L198">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L201">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L202">        final String[] contentTypes = {  };</span>
<span class="nc" id="L203">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L205">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L207">        ParameterizedTypeReference&lt;List&lt;String&gt;&gt; returnType = new ParameterizedTypeReference&lt;List&lt;String&gt;&gt;() {};</span>
<span class="nc" id="L208">        return apiClient.invokeAPI(path, HttpMethod.GET, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
    /**
     * Search cache
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param group The group parameter
     * @param component The component parameter
     * @param key The key parameter
     * @param batch The batch parameter
     * @param status The status parameter
     * @param isBatch The isBatch parameter
     * @param offset The offset parameter
     * @param limit The limit parameter
     * @param sort format is {name}:[asc|desc]
     * @return CachePageResponseV3
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CachePageResponseV3 searchCache(String group, String component, String key, String batch, BatchStatus status, Boolean isBatch, Integer offset, Integer limit, String sort) throws RestClientException {
<span class="nc" id="L227">        Object postBody = null;</span>
<span class="nc" id="L228">        String path = UriComponentsBuilder.fromPath(&quot;/cache&quot;).build().toUriString();</span>
        
<span class="nc" id="L230">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L231">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L232">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>
<span class="nc" id="L233">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;group&quot;, group));</span>
<span class="nc" id="L234">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;component&quot;, component));</span>
<span class="nc" id="L235">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;key&quot;, key));</span>
<span class="nc" id="L236">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;batch&quot;, batch));</span>
<span class="nc" id="L237">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;status&quot;, status));</span>
<span class="nc" id="L238">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;isBatch&quot;, isBatch));</span>
<span class="nc" id="L239">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;offset&quot;, offset));</span>
<span class="nc" id="L240">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;limit&quot;, limit));</span>
<span class="nc" id="L241">        queryParams.putAll(apiClient.parameterToMultiValueMap(null, &quot;sort&quot;, sort));</span>

<span class="nc" id="L243">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L246">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L247">        final String[] contentTypes = {  };</span>
<span class="nc" id="L248">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L250">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L252">        ParameterizedTypeReference&lt;CachePageResponseV3&gt; returnType = new ParameterizedTypeReference&lt;CachePageResponseV3&gt;() {};</span>
<span class="nc" id="L253">        return apiClient.invokeAPI(path, HttpMethod.GET, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
    /**
     * Update cache
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param group The group parameter
     * @param component The component parameter
     * @param key The key parameter
     * @param body The body parameter
     * @return CacheResponseV3
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CacheResponseV3 updateCache(String group, String component, String key, CacheRequestV3 body) throws RestClientException {
<span class="nc" id="L267">        Object postBody = body;</span>
        // verify the required parameter 'group' is set
<span class="nc bnc" id="L269" title="All 2 branches missed.">        if (group == null) {</span>
<span class="nc" id="L270">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'group' when calling updateCache&quot;);</span>
        }
        // verify the required parameter 'component' is set
<span class="nc bnc" id="L273" title="All 2 branches missed.">        if (component == null) {</span>
<span class="nc" id="L274">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'component' when calling updateCache&quot;);</span>
        }
        // verify the required parameter 'key' is set
<span class="nc bnc" id="L277" title="All 2 branches missed.">        if (key == null) {</span>
<span class="nc" id="L278">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'key' when calling updateCache&quot;);</span>
        }
        // create path and map variables
<span class="nc" id="L281">        final Map&lt;String, Object&gt; uriVariables = new HashMap&lt;String, Object&gt;();</span>
<span class="nc" id="L282">        uriVariables.put(&quot;group&quot;, group);</span>
<span class="nc" id="L283">        uriVariables.put(&quot;component&quot;, component);</span>
<span class="nc" id="L284">        uriVariables.put(&quot;key&quot;, key);</span>
<span class="nc" id="L285">        String path = UriComponentsBuilder.fromPath(&quot;/cache/{group}/{component}/{key}&quot;).buildAndExpand(uriVariables).toUriString();</span>
        
<span class="nc" id="L287">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L288">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L289">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="nc" id="L291">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L294">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L295">        final String[] contentTypes = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L298">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L300">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L302">        ParameterizedTypeReference&lt;CacheResponseV3&gt; returnType = new ParameterizedTypeReference&lt;CacheResponseV3&gt;() {};</span>
<span class="nc" id="L303">        return apiClient.invokeAPI(path, HttpMethod.PUT, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>