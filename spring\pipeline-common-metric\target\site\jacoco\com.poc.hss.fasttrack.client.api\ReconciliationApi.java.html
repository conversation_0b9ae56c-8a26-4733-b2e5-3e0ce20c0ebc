<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.api</a> &gt; <span class="el_source">ReconciliationApi.java</span></div><h1>ReconciliationApi.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.client.api;

import com.poc.hss.fasttrack.client.ApiClient;

import com.poc.hss.fasttrack.client.model.ReconciliationRequest;
import com.poc.hss.fasttrack.client.model.ReconciliationResponse;
import com.poc.hss.fasttrack.client.model.ResetRequest;
import com.poc.hss.fasttrack.client.model.ResetResponse;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.util.UriComponentsBuilder;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;

@Component(&quot;com.poc.hss.fasttrack.client.api.ReconciliationApi&quot;)
public class ReconciliationApi {
    private ApiClient apiClient;

    public ReconciliationApi() {
<span class="nc" id="L34">        this(new ApiClient());</span>
<span class="nc" id="L35">    }</span>

    @Autowired
<span class="nc" id="L38">    public ReconciliationApi(ApiClient apiClient) {</span>
<span class="nc" id="L39">        this.apiClient = apiClient;</span>
<span class="nc" id="L40">    }</span>

    public ApiClient getApiClient() {
<span class="nc" id="L43">        return apiClient;</span>
    }

    public void setApiClient(ApiClient apiClient) {
<span class="nc" id="L47">        this.apiClient = apiClient;</span>
<span class="nc" id="L48">    }</span>

    /**
     * Reconcile the metric
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param body The body parameter
     * @return ReconciliationResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ReconciliationResponse reconcile(ReconciliationRequest body) throws RestClientException {
<span class="nc" id="L59">        Object postBody = body;</span>
<span class="nc" id="L60">        String path = UriComponentsBuilder.fromPath(&quot;/reconcile&quot;).build().toUriString();</span>
        
<span class="nc" id="L62">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L63">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L64">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="nc" id="L66">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L69">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L70">        final String[] contentTypes = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L73">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L75">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L77">        ParameterizedTypeReference&lt;ReconciliationResponse&gt; returnType = new ParameterizedTypeReference&lt;ReconciliationResponse&gt;() {};</span>
<span class="nc" id="L78">        return apiClient.invokeAPI(path, HttpMethod.POST, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
    /**
     * Reset the batch
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param body The body parameter
     * @return ResetResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResetResponse reset(ResetRequest body) throws RestClientException {
<span class="nc" id="L89">        Object postBody = body;</span>
<span class="nc" id="L90">        String path = UriComponentsBuilder.fromPath(&quot;/reset&quot;).build().toUriString();</span>
        
<span class="nc" id="L92">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="nc" id="L93">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="nc" id="L94">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="nc" id="L96">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L99">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="nc" id="L100">        final String[] contentTypes = { </span>
            &quot;application/json&quot;
         };
<span class="nc" id="L103">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="nc" id="L105">        String[] authNames = new String[] {  };</span>

<span class="nc" id="L107">        ParameterizedTypeReference&lt;ResetResponse&gt; returnType = new ParameterizedTypeReference&lt;ResetResponse&gt;() {};</span>
<span class="nc" id="L108">        return apiClient.invokeAPI(path, HttpMethod.POST, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>