<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.client.api</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <span class="el_package">com.poc.hss.fasttrack.client.api</span></div><h1>com.poc.hss.fasttrack.client.api</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">953 of 953</td><td class="ctr2">0%</td><td class="bar">22 of 22</td><td class="ctr2">0%</td><td class="ctr1">41</td><td class="ctr2">41</td><td class="ctr1">189</td><td class="ctr2">189</td><td class="ctr1">30</td><td class="ctr2">30</td><td class="ctr1">12</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a2"><a href="CacheV3Api.html" class="el_class">CacheV3Api</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="638" alt="638"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">21</td><td class="ctr2" id="g0">21</td><td class="ctr1" id="h0">130</td><td class="ctr2" id="i0">130</td><td class="ctr1" id="j0">10</td><td class="ctr2" id="k0">10</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a9"><a href="ReconciliationApi.html" class="el_class">ReconciliationApi</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="151" alt="151"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">32</td><td class="ctr2" id="i1">32</td><td class="ctr1" id="j1">6</td><td class="ctr2" id="k1">6</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="BatchApi.html" class="el_class">BatchApi</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="137" alt="137"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">27</td><td class="ctr2" id="i2">27</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k2">5</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="BatchApi$1.html" class="el_class">BatchApi.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b3"/><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a3"><a href="CacheV3Api$1.html" class="el_class">CacheV3Api.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b4"/><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a4"><a href="CacheV3Api$2.html" class="el_class">CacheV3Api.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b5"/><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a5"><a href="CacheV3Api$3.html" class="el_class">CacheV3Api.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b6"/><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a6"><a href="CacheV3Api$4.html" class="el_class">CacheV3Api.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b7"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a7"><a href="CacheV3Api$5.html" class="el_class">CacheV3Api.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b8"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a8"><a href="CacheV3Api$6.html" class="el_class">CacheV3Api.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b9"/><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td><td class="ctr1" id="l9">1</td><td class="ctr2" id="m9">1</td></tr><tr><td id="a10"><a href="ReconciliationApi$1.html" class="el_class">ReconciliationApi.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b10"/><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td><td class="ctr1" id="l10">1</td><td class="ctr2" id="m10">1</td></tr><tr><td id="a11"><a href="ReconciliationApi$2.html" class="el_class">ReconciliationApi.new ParameterizedTypeReference() {...}</a></td><td class="bar" id="b11"/><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td><td class="ctr1" id="l11">1</td><td class="ctr2" id="m11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>