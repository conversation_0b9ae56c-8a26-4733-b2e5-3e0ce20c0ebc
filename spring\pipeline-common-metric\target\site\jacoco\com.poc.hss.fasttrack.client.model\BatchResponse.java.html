<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">BatchResponse.java</span></div><h1>BatchResponse.java</h1><pre class="source lang-java linenums">/*
 * Cache service
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 3.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.poc.hss.fasttrack.client.model.BatchStatus;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * BatchResponse
 */


<span class="nc" id="L27">public class BatchResponse {</span>
<span class="nc" id="L28">  @JsonProperty(&quot;id&quot;)</span>
  private String id = null;

<span class="nc" id="L31">  @JsonProperty(&quot;group&quot;)</span>
  private String group = null;

<span class="nc" id="L34">  @JsonProperty(&quot;component&quot;)</span>
  private String component = null;

<span class="nc" id="L37">  @JsonProperty(&quot;batch&quot;)</span>
  private String batch = null;

<span class="nc" id="L40">  @JsonProperty(&quot;deployment&quot;)</span>
  private String deployment = null;

<span class="nc" id="L43">  @JsonProperty(&quot;sourceMetricOut&quot;)</span>
  private Long sourceMetricOut = null;

<span class="nc" id="L46">  @JsonProperty(&quot;metricIn&quot;)</span>
  private Long metricIn = null;

<span class="nc" id="L49">  @JsonProperty(&quot;topic&quot;)</span>
  private String topic = null;

<span class="nc" id="L52">  @JsonProperty(&quot;consumerGroup&quot;)</span>
  private String consumerGroup = null;

<span class="nc" id="L55">  @JsonProperty(&quot;completed&quot;)</span>
  private Boolean completed = null;

<span class="nc" id="L58">  @JsonProperty(&quot;status&quot;)</span>
  private BatchStatus status = null;

  public BatchResponse id(String id) {
<span class="nc" id="L62">    this.id = id;</span>
<span class="nc" id="L63">    return this;</span>
  }

   /**
   * Get id
   * @return id
  **/
  @Schema(description = &quot;&quot;)
  public String getId() {
<span class="nc" id="L72">    return id;</span>
  }

  public void setId(String id) {
<span class="nc" id="L76">    this.id = id;</span>
<span class="nc" id="L77">  }</span>

  public BatchResponse group(String group) {
<span class="nc" id="L80">    this.group = group;</span>
<span class="nc" id="L81">    return this;</span>
  }

   /**
   * Get group
   * @return group
  **/
  @Schema(description = &quot;&quot;)
  public String getGroup() {
<span class="nc" id="L90">    return group;</span>
  }

  public void setGroup(String group) {
<span class="nc" id="L94">    this.group = group;</span>
<span class="nc" id="L95">  }</span>

  public BatchResponse component(String component) {
<span class="nc" id="L98">    this.component = component;</span>
<span class="nc" id="L99">    return this;</span>
  }

   /**
   * Get component
   * @return component
  **/
  @Schema(description = &quot;&quot;)
  public String getComponent() {
<span class="nc" id="L108">    return component;</span>
  }

  public void setComponent(String component) {
<span class="nc" id="L112">    this.component = component;</span>
<span class="nc" id="L113">  }</span>

  public BatchResponse batch(String batch) {
<span class="nc" id="L116">    this.batch = batch;</span>
<span class="nc" id="L117">    return this;</span>
  }

   /**
   * Get batch
   * @return batch
  **/
  @Schema(description = &quot;&quot;)
  public String getBatch() {
<span class="nc" id="L126">    return batch;</span>
  }

  public void setBatch(String batch) {
<span class="nc" id="L130">    this.batch = batch;</span>
<span class="nc" id="L131">  }</span>

  public BatchResponse deployment(String deployment) {
<span class="nc" id="L134">    this.deployment = deployment;</span>
<span class="nc" id="L135">    return this;</span>
  }

   /**
   * Get deployment
   * @return deployment
  **/
  @Schema(description = &quot;&quot;)
  public String getDeployment() {
<span class="nc" id="L144">    return deployment;</span>
  }

  public void setDeployment(String deployment) {
<span class="nc" id="L148">    this.deployment = deployment;</span>
<span class="nc" id="L149">  }</span>

  public BatchResponse sourceMetricOut(Long sourceMetricOut) {
<span class="nc" id="L152">    this.sourceMetricOut = sourceMetricOut;</span>
<span class="nc" id="L153">    return this;</span>
  }

   /**
   * Get sourceMetricOut
   * @return sourceMetricOut
  **/
  @Schema(description = &quot;&quot;)
  public Long getSourceMetricOut() {
<span class="nc" id="L162">    return sourceMetricOut;</span>
  }

  public void setSourceMetricOut(Long sourceMetricOut) {
<span class="nc" id="L166">    this.sourceMetricOut = sourceMetricOut;</span>
<span class="nc" id="L167">  }</span>

  public BatchResponse metricIn(Long metricIn) {
<span class="nc" id="L170">    this.metricIn = metricIn;</span>
<span class="nc" id="L171">    return this;</span>
  }

   /**
   * Get metricIn
   * @return metricIn
  **/
  @Schema(description = &quot;&quot;)
  public Long getMetricIn() {
<span class="nc" id="L180">    return metricIn;</span>
  }

  public void setMetricIn(Long metricIn) {
<span class="nc" id="L184">    this.metricIn = metricIn;</span>
<span class="nc" id="L185">  }</span>

  public BatchResponse topic(String topic) {
<span class="nc" id="L188">    this.topic = topic;</span>
<span class="nc" id="L189">    return this;</span>
  }

   /**
   * Get topic
   * @return topic
  **/
  @Schema(description = &quot;&quot;)
  public String getTopic() {
<span class="nc" id="L198">    return topic;</span>
  }

  public void setTopic(String topic) {
<span class="nc" id="L202">    this.topic = topic;</span>
<span class="nc" id="L203">  }</span>

  public BatchResponse consumerGroup(String consumerGroup) {
<span class="nc" id="L206">    this.consumerGroup = consumerGroup;</span>
<span class="nc" id="L207">    return this;</span>
  }

   /**
   * Get consumerGroup
   * @return consumerGroup
  **/
  @Schema(description = &quot;&quot;)
  public String getConsumerGroup() {
<span class="nc" id="L216">    return consumerGroup;</span>
  }

  public void setConsumerGroup(String consumerGroup) {
<span class="nc" id="L220">    this.consumerGroup = consumerGroup;</span>
<span class="nc" id="L221">  }</span>

  public BatchResponse completed(Boolean completed) {
<span class="nc" id="L224">    this.completed = completed;</span>
<span class="nc" id="L225">    return this;</span>
  }

   /**
   * Get completed
   * @return completed
  **/
  @Schema(description = &quot;&quot;)
  public Boolean isCompleted() {
<span class="nc" id="L234">    return completed;</span>
  }

  public void setCompleted(Boolean completed) {
<span class="nc" id="L238">    this.completed = completed;</span>
<span class="nc" id="L239">  }</span>

  public BatchResponse status(BatchStatus status) {
<span class="nc" id="L242">    this.status = status;</span>
<span class="nc" id="L243">    return this;</span>
  }

   /**
   * Get status
   * @return status
  **/
  @Schema(description = &quot;&quot;)
  public BatchStatus getStatus() {
<span class="nc" id="L252">    return status;</span>
  }

  public void setStatus(BatchStatus status) {
<span class="nc" id="L256">    this.status = status;</span>
<span class="nc" id="L257">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L262" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L263">      return true;</span>
    }
<span class="nc bnc" id="L265" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L266">      return false;</span>
    }
<span class="nc" id="L268">    BatchResponse batchResponse = (BatchResponse) o;</span>
<span class="nc bnc" id="L269" title="All 2 branches missed.">    return Objects.equals(this.id, batchResponse.id) &amp;&amp;</span>
<span class="nc bnc" id="L270" title="All 2 branches missed.">        Objects.equals(this.group, batchResponse.group) &amp;&amp;</span>
<span class="nc bnc" id="L271" title="All 2 branches missed.">        Objects.equals(this.component, batchResponse.component) &amp;&amp;</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">        Objects.equals(this.batch, batchResponse.batch) &amp;&amp;</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">        Objects.equals(this.deployment, batchResponse.deployment) &amp;&amp;</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">        Objects.equals(this.sourceMetricOut, batchResponse.sourceMetricOut) &amp;&amp;</span>
<span class="nc bnc" id="L275" title="All 2 branches missed.">        Objects.equals(this.metricIn, batchResponse.metricIn) &amp;&amp;</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">        Objects.equals(this.topic, batchResponse.topic) &amp;&amp;</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">        Objects.equals(this.consumerGroup, batchResponse.consumerGroup) &amp;&amp;</span>
<span class="nc bnc" id="L278" title="All 2 branches missed.">        Objects.equals(this.completed, batchResponse.completed) &amp;&amp;</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">        Objects.equals(this.status, batchResponse.status);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L284">    return Objects.hash(id, group, component, batch, deployment, sourceMetricOut, metricIn, topic, consumerGroup, completed, status);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L290">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L291">    sb.append(&quot;class BatchResponse {\n&quot;);</span>
    
<span class="nc" id="L293">    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</span>
<span class="nc" id="L294">    sb.append(&quot;    group: &quot;).append(toIndentedString(group)).append(&quot;\n&quot;);</span>
<span class="nc" id="L295">    sb.append(&quot;    component: &quot;).append(toIndentedString(component)).append(&quot;\n&quot;);</span>
<span class="nc" id="L296">    sb.append(&quot;    batch: &quot;).append(toIndentedString(batch)).append(&quot;\n&quot;);</span>
<span class="nc" id="L297">    sb.append(&quot;    deployment: &quot;).append(toIndentedString(deployment)).append(&quot;\n&quot;);</span>
<span class="nc" id="L298">    sb.append(&quot;    sourceMetricOut: &quot;).append(toIndentedString(sourceMetricOut)).append(&quot;\n&quot;);</span>
<span class="nc" id="L299">    sb.append(&quot;    metricIn: &quot;).append(toIndentedString(metricIn)).append(&quot;\n&quot;);</span>
<span class="nc" id="L300">    sb.append(&quot;    topic: &quot;).append(toIndentedString(topic)).append(&quot;\n&quot;);</span>
<span class="nc" id="L301">    sb.append(&quot;    consumerGroup: &quot;).append(toIndentedString(consumerGroup)).append(&quot;\n&quot;);</span>
<span class="nc" id="L302">    sb.append(&quot;    completed: &quot;).append(toIndentedString(completed)).append(&quot;\n&quot;);</span>
<span class="nc" id="L303">    sb.append(&quot;    status: &quot;).append(toIndentedString(status)).append(&quot;\n&quot;);</span>
<span class="nc" id="L304">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L305">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L313" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L314">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L316">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>