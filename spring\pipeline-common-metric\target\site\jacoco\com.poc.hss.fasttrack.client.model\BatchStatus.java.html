<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchStatus.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">BatchStatus.java</span></div><h1>BatchStatus.java</h1><pre class="source lang-java linenums">/*
 * Cache service
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 3.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * Gets or Sets BatchStatus
 */
<span class="fc" id="L23">public enum BatchStatus {</span>
<span class="fc" id="L24">  COMPLETED(&quot;COMPLETED&quot;),</span>
<span class="fc" id="L25">  IN_PROGRESS(&quot;IN_PROGRESS&quot;),</span>
<span class="fc" id="L26">  COUNT_MISMATCH(&quot;COUNT_MISMATCH&quot;);</span>

  private String value;

<span class="fc" id="L30">  BatchStatus(String value) {</span>
<span class="fc" id="L31">    this.value = value;</span>
<span class="fc" id="L32">  }</span>

  @JsonValue
  public String getValue() {
<span class="fc" id="L36">    return value;</span>
  }

  @Override
  public String toString() {
<span class="fc" id="L41">    return String.valueOf(value);</span>
  }

  @JsonCreator
  public static BatchStatus fromValue(String text) {
<span class="fc bfc" id="L46" title="All 2 branches covered.">    for (BatchStatus b : BatchStatus.values()) {</span>
<span class="fc bfc" id="L47" title="All 2 branches covered.">      if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L48">        return b;</span>
      }
    }
<span class="fc" id="L51">    return null;</span>
  }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>