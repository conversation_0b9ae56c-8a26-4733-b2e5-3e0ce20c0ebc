<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CachePageResponseV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">CachePageResponseV3.java</span></div><h1>CachePageResponseV3.java</h1><pre class="source lang-java linenums">/*
 * Cache service
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 3.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.poc.hss.fasttrack.client.model.CacheResponseV3;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
/**
 * CachePageResponseV3
 */


<span class="nc" id="L29">public class CachePageResponseV3 {</span>
<span class="nc" id="L30">  @JsonProperty(&quot;data&quot;)</span>
  private List&lt;CacheResponseV3&gt; data = null;

<span class="nc" id="L33">  @JsonProperty(&quot;total&quot;)</span>
  private Long total = null;

  public CachePageResponseV3 data(List&lt;CacheResponseV3&gt; data) {
<span class="nc" id="L37">    this.data = data;</span>
<span class="nc" id="L38">    return this;</span>
  }

  public CachePageResponseV3 addDataItem(CacheResponseV3 dataItem) {
<span class="nc bnc" id="L42" title="All 2 branches missed.">    if (this.data == null) {</span>
<span class="nc" id="L43">      this.data = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L45">    this.data.add(dataItem);</span>
<span class="nc" id="L46">    return this;</span>
  }

   /**
   * Get data
   * @return data
  **/
  @Schema(description = &quot;&quot;)
  public List&lt;CacheResponseV3&gt; getData() {
<span class="nc" id="L55">    return data;</span>
  }

  public void setData(List&lt;CacheResponseV3&gt; data) {
<span class="nc" id="L59">    this.data = data;</span>
<span class="nc" id="L60">  }</span>

  public CachePageResponseV3 total(Long total) {
<span class="nc" id="L63">    this.total = total;</span>
<span class="nc" id="L64">    return this;</span>
  }

   /**
   * Get total
   * @return total
  **/
  @Schema(description = &quot;&quot;)
  public Long getTotal() {
<span class="nc" id="L73">    return total;</span>
  }

  public void setTotal(Long total) {
<span class="nc" id="L77">    this.total = total;</span>
<span class="nc" id="L78">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L83" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L84">      return true;</span>
    }
<span class="nc bnc" id="L86" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L87">      return false;</span>
    }
<span class="nc" id="L89">    CachePageResponseV3 cachePageResponseV3 = (CachePageResponseV3) o;</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">    return Objects.equals(this.data, cachePageResponseV3.data) &amp;&amp;</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">        Objects.equals(this.total, cachePageResponseV3.total);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L96">    return Objects.hash(data, total);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L102">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L103">    sb.append(&quot;class CachePageResponseV3 {\n&quot;);</span>
    
<span class="nc" id="L105">    sb.append(&quot;    data: &quot;).append(toIndentedString(data)).append(&quot;\n&quot;);</span>
<span class="nc" id="L106">    sb.append(&quot;    total: &quot;).append(toIndentedString(total)).append(&quot;\n&quot;);</span>
<span class="nc" id="L107">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L108">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L116" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L117">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L119">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>