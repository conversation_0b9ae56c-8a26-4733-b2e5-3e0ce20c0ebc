<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheRequestV3</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_class">CacheRequestV3</span></div><h1>CacheRequestV3</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">6 of 201</td><td class="ctr2">97%</td><td class="bar">7 of 16</td><td class="ctr2">56%</td><td class="ctr1">7</td><td class="ctr2">25</td><td class="ctr1">3</td><td class="ctr2">46</td><td class="ctr1">0</td><td class="ctr2">17</td></tr></tfoot><tbody><tr><td id="a2"><a href="CacheRequestV3.java.html#L116" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="83" height="10" title="41" alt="41"/></td><td class="ctr2" id="c15">91%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="51" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="68" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">57%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h0">2</td><td class="ctr2" id="i0">9</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a13"><a href="CacheRequestV3.java.html#L153" class="el_method">toIndentedString(Object)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="8" alt="8"/></td><td class="ctr2" id="c16">80%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="1" alt="1"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i3">3</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a14"><a href="CacheRequestV3.java.html#L137" class="el_method">toString()</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="59" alt="59"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i1">8</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="CacheRequestV3.java.html#L131" class="el_method">hashCode()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="48" height="10" title="24" alt="24"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="CacheRequestV3.java.html#L28" class="el_method">CacheRequestV3()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="15" alt="15"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i2">5</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="CacheRequestV3.java.html#L42" class="el_method">batch(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a16"><a href="CacheRequestV3.java.html#L60" class="el_method">value(Object)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">2</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a15"><a href="CacheRequestV3.java.html#L78" class="el_method">type(CacheTypeV3)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="CacheRequestV3.java.html#L96" class="el_method">operation(CacheOperationV3)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="CacheRequestV3.java.html#L56" class="el_method">setBatch(String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a12"><a href="CacheRequestV3.java.html#L74" class="el_method">setValue(Object)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a11"><a href="CacheRequestV3.java.html#L92" class="el_method">setType(CacheTypeV3)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a10"><a href="CacheRequestV3.java.html#L110" class="el_method">setOperation(CacheOperationV3)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a3"><a href="CacheRequestV3.java.html#L52" class="el_method">getBatch()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a6"><a href="CacheRequestV3.java.html#L70" class="el_method">getValue()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a5"><a href="CacheRequestV3.java.html#L88" class="el_method">getType()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a4"><a href="CacheRequestV3.java.html#L106" class="el_method">getOperation()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>