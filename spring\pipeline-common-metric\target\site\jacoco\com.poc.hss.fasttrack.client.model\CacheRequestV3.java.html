<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheRequestV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">CacheRequestV3.java</span></div><h1>CacheRequestV3.java</h1><pre class="source lang-java linenums">/*
 * Cache service
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 3.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.poc.hss.fasttrack.client.model.CacheOperationV3;
import com.poc.hss.fasttrack.client.model.CacheTypeV3;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * CacheRequestV3
 */


<span class="fc" id="L28">public class CacheRequestV3 {</span>
<span class="fc" id="L29">  @JsonProperty(&quot;batch&quot;)</span>
  private String batch = null;

<span class="fc" id="L32">  @JsonProperty(&quot;value&quot;)</span>
  private Object value = null;

<span class="fc" id="L35">  @JsonProperty(&quot;type&quot;)</span>
  private CacheTypeV3 type = null;

<span class="fc" id="L38">  @JsonProperty(&quot;operation&quot;)</span>
  private CacheOperationV3 operation = null;

  public CacheRequestV3 batch(String batch) {
<span class="fc" id="L42">    this.batch = batch;</span>
<span class="fc" id="L43">    return this;</span>
  }

   /**
   * Get batch
   * @return batch
  **/
  @Schema(description = &quot;&quot;)
  public String getBatch() {
<span class="fc" id="L52">    return batch;</span>
  }

  public void setBatch(String batch) {
<span class="fc" id="L56">    this.batch = batch;</span>
<span class="fc" id="L57">  }</span>

  public CacheRequestV3 value(Object value) {
<span class="fc" id="L60">    this.value = value;</span>
<span class="fc" id="L61">    return this;</span>
  }

   /**
   * Get value
   * @return value
  **/
  @Schema(description = &quot;&quot;)
  public Object getValue() {
<span class="fc" id="L70">    return value;</span>
  }

  public void setValue(Object value) {
<span class="fc" id="L74">    this.value = value;</span>
<span class="fc" id="L75">  }</span>

  public CacheRequestV3 type(CacheTypeV3 type) {
<span class="fc" id="L78">    this.type = type;</span>
<span class="fc" id="L79">    return this;</span>
  }

   /**
   * Get type
   * @return type
  **/
  @Schema(description = &quot;&quot;)
  public CacheTypeV3 getType() {
<span class="fc" id="L88">    return type;</span>
  }

  public void setType(CacheTypeV3 type) {
<span class="fc" id="L92">    this.type = type;</span>
<span class="fc" id="L93">  }</span>

  public CacheRequestV3 operation(CacheOperationV3 operation) {
<span class="fc" id="L96">    this.operation = operation;</span>
<span class="fc" id="L97">    return this;</span>
  }

   /**
   * Get operation
   * @return operation
  **/
  @Schema(description = &quot;&quot;)
  public CacheOperationV3 getOperation() {
<span class="fc" id="L106">    return operation;</span>
  }

  public void setOperation(CacheOperationV3 operation) {
<span class="fc" id="L110">    this.operation = operation;</span>
<span class="fc" id="L111">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="pc bpc" id="L116" title="1 of 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L117">      return true;</span>
    }
<span class="pc bpc" id="L119" title="2 of 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L120">      return false;</span>
    }
<span class="fc" id="L122">    CacheRequestV3 cacheRequestV3 = (CacheRequestV3) o;</span>
<span class="fc bfc" id="L123" title="All 2 branches covered.">    return Objects.equals(this.batch, cacheRequestV3.batch) &amp;&amp;</span>
<span class="pc bpc" id="L124" title="1 of 2 branches missed.">        Objects.equals(this.value, cacheRequestV3.value) &amp;&amp;</span>
<span class="pc bpc" id="L125" title="1 of 2 branches missed.">        Objects.equals(this.type, cacheRequestV3.type) &amp;&amp;</span>
<span class="pc bpc" id="L126" title="1 of 2 branches missed.">        Objects.equals(this.operation, cacheRequestV3.operation);</span>
  }

  @Override
  public int hashCode() {
<span class="fc" id="L131">    return Objects.hash(batch, value, type, operation);</span>
  }


  @Override
  public String toString() {
<span class="fc" id="L137">    StringBuilder sb = new StringBuilder();</span>
<span class="fc" id="L138">    sb.append(&quot;class CacheRequestV3 {\n&quot;);</span>
    
<span class="fc" id="L140">    sb.append(&quot;    batch: &quot;).append(toIndentedString(batch)).append(&quot;\n&quot;);</span>
<span class="fc" id="L141">    sb.append(&quot;    value: &quot;).append(toIndentedString(value)).append(&quot;\n&quot;);</span>
<span class="fc" id="L142">    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</span>
<span class="fc" id="L143">    sb.append(&quot;    operation: &quot;).append(toIndentedString(operation)).append(&quot;\n&quot;);</span>
<span class="fc" id="L144">    sb.append(&quot;}&quot;);</span>
<span class="fc" id="L145">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="pc bpc" id="L153" title="1 of 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L154">      return &quot;null&quot;;</span>
    }
<span class="fc" id="L156">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>