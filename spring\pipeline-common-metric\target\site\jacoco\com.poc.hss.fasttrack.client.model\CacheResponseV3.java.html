<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheResponseV3.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">CacheResponseV3.java</span></div><h1>CacheResponseV3.java</h1><pre class="source lang-java linenums">/*
 * Cache service
 * No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)
 *
 * OpenAPI spec version: 3.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.poc.hss.fasttrack.client.model.BatchStatus;
import com.poc.hss.fasttrack.client.model.CacheTypeV3;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
/**
 * CacheResponseV3
 */


<span class="nc" id="L29">public class CacheResponseV3 {</span>
<span class="nc" id="L30">  @JsonProperty(&quot;id&quot;)</span>
  private String id = null;

<span class="nc" id="L33">  @JsonProperty(&quot;group&quot;)</span>
  private String group = null;

<span class="nc" id="L36">  @JsonProperty(&quot;component&quot;)</span>
  private String component = null;

<span class="nc" id="L39">  @JsonProperty(&quot;batch&quot;)</span>
  private String batch = null;

<span class="nc" id="L42">  @JsonProperty(&quot;key&quot;)</span>
  private String key = null;

<span class="nc" id="L45">  @JsonProperty(&quot;value&quot;)</span>
  private Object value = null;

<span class="nc" id="L48">  @JsonProperty(&quot;type&quot;)</span>
  private CacheTypeV3 type = null;

<span class="nc" id="L51">  @JsonProperty(&quot;status&quot;)</span>
  private BatchStatus status = null;

<span class="nc" id="L54">  @JsonProperty(&quot;createdTime&quot;)</span>
  private LocalDateTime createdTime = null;

<span class="nc" id="L57">  @JsonProperty(&quot;updatedTime&quot;)</span>
  private LocalDateTime updatedTime = null;

  public CacheResponseV3 id(String id) {
<span class="nc" id="L61">    this.id = id;</span>
<span class="nc" id="L62">    return this;</span>
  }

   /**
   * Get id
   * @return id
  **/
  @Schema(description = &quot;&quot;)
  public String getId() {
<span class="nc" id="L71">    return id;</span>
  }

  public void setId(String id) {
<span class="nc" id="L75">    this.id = id;</span>
<span class="nc" id="L76">  }</span>

  public CacheResponseV3 group(String group) {
<span class="nc" id="L79">    this.group = group;</span>
<span class="nc" id="L80">    return this;</span>
  }

   /**
   * Get group
   * @return group
  **/
  @Schema(description = &quot;&quot;)
  public String getGroup() {
<span class="nc" id="L89">    return group;</span>
  }

  public void setGroup(String group) {
<span class="nc" id="L93">    this.group = group;</span>
<span class="nc" id="L94">  }</span>

  public CacheResponseV3 component(String component) {
<span class="nc" id="L97">    this.component = component;</span>
<span class="nc" id="L98">    return this;</span>
  }

   /**
   * Get component
   * @return component
  **/
  @Schema(description = &quot;&quot;)
  public String getComponent() {
<span class="nc" id="L107">    return component;</span>
  }

  public void setComponent(String component) {
<span class="nc" id="L111">    this.component = component;</span>
<span class="nc" id="L112">  }</span>

  public CacheResponseV3 batch(String batch) {
<span class="nc" id="L115">    this.batch = batch;</span>
<span class="nc" id="L116">    return this;</span>
  }

   /**
   * Get batch
   * @return batch
  **/
  @Schema(description = &quot;&quot;)
  public String getBatch() {
<span class="nc" id="L125">    return batch;</span>
  }

  public void setBatch(String batch) {
<span class="nc" id="L129">    this.batch = batch;</span>
<span class="nc" id="L130">  }</span>

  public CacheResponseV3 key(String key) {
<span class="nc" id="L133">    this.key = key;</span>
<span class="nc" id="L134">    return this;</span>
  }

   /**
   * Get key
   * @return key
  **/
  @Schema(description = &quot;&quot;)
  public String getKey() {
<span class="nc" id="L143">    return key;</span>
  }

  public void setKey(String key) {
<span class="nc" id="L147">    this.key = key;</span>
<span class="nc" id="L148">  }</span>

  public CacheResponseV3 value(Object value) {
<span class="nc" id="L151">    this.value = value;</span>
<span class="nc" id="L152">    return this;</span>
  }

   /**
   * Get value
   * @return value
  **/
  @Schema(description = &quot;&quot;)
  public Object getValue() {
<span class="nc" id="L161">    return value;</span>
  }

  public void setValue(Object value) {
<span class="nc" id="L165">    this.value = value;</span>
<span class="nc" id="L166">  }</span>

  public CacheResponseV3 type(CacheTypeV3 type) {
<span class="nc" id="L169">    this.type = type;</span>
<span class="nc" id="L170">    return this;</span>
  }

   /**
   * Get type
   * @return type
  **/
  @Schema(description = &quot;&quot;)
  public CacheTypeV3 getType() {
<span class="nc" id="L179">    return type;</span>
  }

  public void setType(CacheTypeV3 type) {
<span class="nc" id="L183">    this.type = type;</span>
<span class="nc" id="L184">  }</span>

  public CacheResponseV3 status(BatchStatus status) {
<span class="nc" id="L187">    this.status = status;</span>
<span class="nc" id="L188">    return this;</span>
  }

   /**
   * Get status
   * @return status
  **/
  @Schema(description = &quot;&quot;)
  public BatchStatus getStatus() {
<span class="nc" id="L197">    return status;</span>
  }

  public void setStatus(BatchStatus status) {
<span class="nc" id="L201">    this.status = status;</span>
<span class="nc" id="L202">  }</span>

  public CacheResponseV3 createdTime(LocalDateTime createdTime) {
<span class="nc" id="L205">    this.createdTime = createdTime;</span>
<span class="nc" id="L206">    return this;</span>
  }

   /**
   * Get createdTime
   * @return createdTime
  **/
  @Schema(description = &quot;&quot;)
  public LocalDateTime getCreatedTime() {
<span class="nc" id="L215">    return createdTime;</span>
  }

  public void setCreatedTime(LocalDateTime createdTime) {
<span class="nc" id="L219">    this.createdTime = createdTime;</span>
<span class="nc" id="L220">  }</span>

  public CacheResponseV3 updatedTime(LocalDateTime updatedTime) {
<span class="nc" id="L223">    this.updatedTime = updatedTime;</span>
<span class="nc" id="L224">    return this;</span>
  }

   /**
   * Get updatedTime
   * @return updatedTime
  **/
  @Schema(description = &quot;&quot;)
  public LocalDateTime getUpdatedTime() {
<span class="nc" id="L233">    return updatedTime;</span>
  }

  public void setUpdatedTime(LocalDateTime updatedTime) {
<span class="nc" id="L237">    this.updatedTime = updatedTime;</span>
<span class="nc" id="L238">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L243" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L244">      return true;</span>
    }
<span class="nc bnc" id="L246" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L247">      return false;</span>
    }
<span class="nc" id="L249">    CacheResponseV3 cacheResponseV3 = (CacheResponseV3) o;</span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">    return Objects.equals(this.id, cacheResponseV3.id) &amp;&amp;</span>
<span class="nc bnc" id="L251" title="All 2 branches missed.">        Objects.equals(this.group, cacheResponseV3.group) &amp;&amp;</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">        Objects.equals(this.component, cacheResponseV3.component) &amp;&amp;</span>
<span class="nc bnc" id="L253" title="All 2 branches missed.">        Objects.equals(this.batch, cacheResponseV3.batch) &amp;&amp;</span>
<span class="nc bnc" id="L254" title="All 2 branches missed.">        Objects.equals(this.key, cacheResponseV3.key) &amp;&amp;</span>
<span class="nc bnc" id="L255" title="All 2 branches missed.">        Objects.equals(this.value, cacheResponseV3.value) &amp;&amp;</span>
<span class="nc bnc" id="L256" title="All 2 branches missed.">        Objects.equals(this.type, cacheResponseV3.type) &amp;&amp;</span>
<span class="nc bnc" id="L257" title="All 2 branches missed.">        Objects.equals(this.status, cacheResponseV3.status) &amp;&amp;</span>
<span class="nc bnc" id="L258" title="All 2 branches missed.">        Objects.equals(this.createdTime, cacheResponseV3.createdTime) &amp;&amp;</span>
<span class="nc bnc" id="L259" title="All 2 branches missed.">        Objects.equals(this.updatedTime, cacheResponseV3.updatedTime);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L264">    return Objects.hash(id, group, component, batch, key, value, type, status, createdTime, updatedTime);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L270">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L271">    sb.append(&quot;class CacheResponseV3 {\n&quot;);</span>
    
<span class="nc" id="L273">    sb.append(&quot;    id: &quot;).append(toIndentedString(id)).append(&quot;\n&quot;);</span>
<span class="nc" id="L274">    sb.append(&quot;    group: &quot;).append(toIndentedString(group)).append(&quot;\n&quot;);</span>
<span class="nc" id="L275">    sb.append(&quot;    component: &quot;).append(toIndentedString(component)).append(&quot;\n&quot;);</span>
<span class="nc" id="L276">    sb.append(&quot;    batch: &quot;).append(toIndentedString(batch)).append(&quot;\n&quot;);</span>
<span class="nc" id="L277">    sb.append(&quot;    key: &quot;).append(toIndentedString(key)).append(&quot;\n&quot;);</span>
<span class="nc" id="L278">    sb.append(&quot;    value: &quot;).append(toIndentedString(value)).append(&quot;\n&quot;);</span>
<span class="nc" id="L279">    sb.append(&quot;    type: &quot;).append(toIndentedString(type)).append(&quot;\n&quot;);</span>
<span class="nc" id="L280">    sb.append(&quot;    status: &quot;).append(toIndentedString(status)).append(&quot;\n&quot;);</span>
<span class="nc" id="L281">    sb.append(&quot;    createdTime: &quot;).append(toIndentedString(createdTime)).append(&quot;\n&quot;);</span>
<span class="nc" id="L282">    sb.append(&quot;    updatedTime: &quot;).append(toIndentedString(updatedTime)).append(&quot;\n&quot;);</span>
<span class="nc" id="L283">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L284">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L292" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L293">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L295">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>