<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheRequestDTO</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_class">CacheRequestDTO</span></div><h1>CacheRequestDTO</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">55 of 343</td><td class="ctr2">83%</td><td class="bar">38 of 62</td><td class="ctr2">38%</td><td class="ctr1">31</td><td class="ctr2">51</td><td class="ctr1">0</td><td class="ctr2">9</td><td class="ctr1">0</td><td class="ctr2">20</td></tr></tfoot><tbody><tr><td id="a3"><a href="CacheRequestDTO.java.html#L8" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="41" alt="41"/><img src="../jacoco-resources/greenbar.gif" width="84" height="10" title="99" alt="99"/></td><td class="ctr2" id="c19">70%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="31" alt="31"/><img src="../jacoco-resources/greenbar.gif" width="42" height="10" title="17" alt="17"/></td><td class="ctr2" id="e1">35%</td><td class="ctr1" id="f0">24</td><td class="ctr2" id="g0">25</td><td class="ctr1" id="h0">0</td><td class="ctr2" id="i0">1</td><td class="ctr1" id="j0">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a11"><a href="CacheRequestDTO.java.html#L8" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="77" height="10" title="90" alt="90"/></td><td class="ctr2" id="c18">86%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="17" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j1">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="CacheRequestDTO.java.html#L9" class="el_method">CacheRequestDTO(String, String, String, String, Object, CacheTypeV3, CacheOperationV3)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="24" alt="24"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a19"><a href="CacheRequestDTO.java.html#L8" class="el_method">toString()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="19" alt="19"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="CacheRequestDTO.java.html#L9" class="el_method">builder()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a14"><a href="CacheRequestDTO.java.html#L8" class="el_method">setGroup(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a13"><a href="CacheRequestDTO.java.html#L8" class="el_method">setComponent(String)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a12"><a href="CacheRequestDTO.java.html#L8" class="el_method">setBatch(String)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a15"><a href="CacheRequestDTO.java.html#L8" class="el_method">setKey(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a18"><a href="CacheRequestDTO.java.html#L8" class="el_method">setValue(Object)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a17"><a href="CacheRequestDTO.java.html#L8" class="el_method">setType(CacheTypeV3)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a16"><a href="CacheRequestDTO.java.html#L8" class="el_method">setOperation(CacheOperationV3)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a6"><a href="CacheRequestDTO.java.html#L11" class="el_method">getGroup()</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a5"><a href="CacheRequestDTO.java.html#L12" class="el_method">getComponent()</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a4"><a href="CacheRequestDTO.java.html#L13" class="el_method">getBatch()</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a7"><a href="CacheRequestDTO.java.html#L14" class="el_method">getKey()</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c13">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a10"><a href="CacheRequestDTO.java.html#L15" class="el_method">getValue()</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c14">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a9"><a href="CacheRequestDTO.java.html#L16" class="el_method">getType()</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a8"><a href="CacheRequestDTO.java.html#L17" class="el_method">getOperation()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a2"><a href="CacheRequestDTO.java.html#L8" class="el_method">canEqual(Object)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>