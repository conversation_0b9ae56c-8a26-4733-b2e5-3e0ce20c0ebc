<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AbstractCachingMetricFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.factory</a> &gt; <span class="el_source">AbstractCachingMetricFactory.java</span></div><h1>AbstractCachingMetricFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.factory;

import com.poc.hss.fasttrack.metric.holder.counter.MetricCounter;
import com.poc.hss.fasttrack.metric.holder.value.MetricValue;
import com.poc.hss.fasttrack.metric.specification.MetricKey;
import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import com.poc.hss.fasttrack.model.Unity2Component;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;

public abstract class AbstractCachingMetricFactory implements MetricFactory {

    private final String defaultCacheGroup;
    private final Unity2Component defaultCacheComponent;
<span class="nc" id="L19">    private final Map&lt;MetricSpecification&lt;?&gt;, MetricValue&lt;?&gt;&gt; metricValueCache = new HashMap&lt;&gt;();</span>
<span class="nc" id="L20">    private final Map&lt;MetricSpecification&lt;?&gt;, MetricCounter&gt; metricCounterCache = new HashMap&lt;&gt;();</span>

<span class="nc" id="L22">    public AbstractCachingMetricFactory(String defaultCacheGroup, Unity2Component defaultCacheComponent) {</span>
<span class="nc" id="L23">        this.defaultCacheGroup = defaultCacheGroup;</span>
<span class="nc" id="L24">        this.defaultCacheComponent = defaultCacheComponent;</span>
<span class="nc" id="L25">    }</span>

    private &lt;T&gt; MetricSpecification&lt;T&gt; getSpecification(String group, Unity2Component component, MetricKey&lt;T&gt; key, String batch) {
<span class="nc" id="L28">        MetricSpecification&lt;T&gt; specification = MetricSpecification.&lt;T&gt;builder()</span>
<span class="nc" id="L29">                .group(StringUtils.defaultIfBlank(group, defaultCacheGroup))</span>
<span class="nc" id="L30">                .component(ObjectUtils.defaultIfNull(component, defaultCacheComponent))</span>
<span class="nc" id="L31">                .batch(batch)</span>
<span class="nc" id="L32">                .key(key)</span>
<span class="nc" id="L33">                .build();</span>

<span class="nc" id="L35">        Assert.hasText(specification.getGroup(), &quot;Group must be provided&quot;);</span>
<span class="nc" id="L36">        Assert.notNull(specification.getComponent(), &quot;Component must be provided&quot;);</span>
<span class="nc" id="L37">        return specification;</span>
    }


    @Override
    public final &lt;T&gt; MetricValue&lt;T&gt; getMetricValue(String group, Unity2Component component, MetricKey&lt;T&gt; key, String batch) {
<span class="nc" id="L43">        MetricSpecification&lt;T&gt; specification = getSpecification(group, component, key, batch);</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">        if (metricValueCache.containsKey(specification)) {</span>
<span class="nc" id="L45">            return (MetricValue&lt;T&gt;) metricValueCache.get(specification);</span>
        } else {
<span class="nc" id="L47">            MetricValue&lt;T&gt; metricValue = doGetMetricValue(specification);</span>
<span class="nc" id="L48">            metricValueCache.put(specification, metricValue);</span>
<span class="nc" id="L49">            return metricValue;</span>
        }
    }

    @Override
    public &lt;T&gt; MetricValue&lt;T&gt; getMetricValue(Unity2Component component, MetricKey&lt;T&gt; key, String batch) {
<span class="nc" id="L55">        return getMetricValue(null, component, key, batch);</span>
    }


    @Override
    public final &lt;T&gt; MetricValue&lt;T&gt; getMetricValue(MetricKey&lt;T&gt; key, String batch) {
<span class="nc" id="L61">        return getMetricValue(null, null, key, batch);</span>
    }

    @Override
    public &lt;T&gt; MetricValue&lt;T&gt; getMetricValue(MetricKey&lt;T&gt; key) {
<span class="nc" id="L66">        return getMetricValue(null, null, key, null);</span>
    }

    @Override
    public final MetricCounter getMetricCounter(String group, Unity2Component component, MetricKey&lt;Long&gt; key, String batch) {
<span class="nc" id="L71">        MetricSpecification&lt;Long&gt; specification = getSpecification(group, component, key, batch);</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">        if (metricCounterCache.containsKey(specification)) {</span>
<span class="nc" id="L73">            return metricCounterCache.get(specification);</span>
        } else {
<span class="nc" id="L75">            MetricCounter metricCounter = doGetMetricCounter(specification);</span>
<span class="nc" id="L76">            metricCounterCache.put(specification, metricCounter);</span>
<span class="nc" id="L77">            return metricCounter;</span>
        }
    }

    @Override
    public final MetricCounter getMetricCounter(Unity2Component component, MetricKey&lt;Long&gt; key, String batch) {
<span class="nc" id="L83">        return getMetricCounter(null, component, key, batch);</span>
    }

    @Override
    public final MetricCounter getMetricCounter(MetricKey&lt;Long&gt; key, String batch) {
<span class="nc" id="L88">        return getMetricCounter(null, null, key, batch);</span>
    }

    @Override
    public MetricCounter getMetricCounter(MetricKey&lt;Long&gt; key) {
<span class="nc" id="L93">        return getMetricCounter(null, null, key, null);</span>
    }

    protected abstract &lt;T&gt; MetricValue&lt;T&gt; doGetMetricValue(MetricSpecification&lt;T&gt; specification);

    protected abstract MetricCounter doGetMetricCounter(MetricSpecification&lt;Long&gt; specification);

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>