<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InMemoryMetricFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.factory</a> &gt; <span class="el_source">InMemoryMetricFactory.java</span></div><h1>InMemoryMetricFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.factory;

import com.poc.hss.fasttrack.metric.holder.counter.InMemoryMetricCounter;
import com.poc.hss.fasttrack.metric.holder.counter.MetricCounter;
import com.poc.hss.fasttrack.metric.holder.value.InMemoryMetricValue;
import com.poc.hss.fasttrack.metric.holder.value.MetricValue;
import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import com.poc.hss.fasttrack.model.Unity2Component;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

<span class="nc" id="L14">@Slf4j</span>
@Component
@ConditionalOnProperty(value = &quot;cache.provider&quot;, havingValue = &quot;memory&quot;, matchIfMissing = true)
public class InMemoryMetricFactory extends AbstractCachingMetricFactory {

    public InMemoryMetricFactory(
            @Value(&quot;${cache.group:}&quot;) String defaultCacheGroup,
            @Value(&quot;${cache.component:}&quot;) Unity2Component defaultCacheComponent
    ) {
<span class="nc" id="L23">        super(defaultCacheGroup, defaultCacheComponent);</span>
<span class="nc" id="L24">    }</span>

    @Override
    protected &lt;T&gt; MetricValue&lt;T&gt; doGetMetricValue(MetricSpecification&lt;T&gt; specification) {
<span class="nc" id="L28">        return new InMemoryMetricValue&lt;&gt;(specification);</span>
    }

    @Override
    protected MetricCounter doGetMetricCounter(MetricSpecification&lt;Long&gt; specification) {
<span class="nc" id="L33">        return new InMemoryMetricCounter(specification);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>