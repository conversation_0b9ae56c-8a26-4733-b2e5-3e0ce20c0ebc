<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheGateway.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.gateway</a> &gt; <span class="el_source">CacheGateway.java</span></div><h1>CacheGateway.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.gateway;

import com.poc.hss.fasttrack.backoff.BackoffException;
import com.poc.hss.fasttrack.client.api.CacheV3Api;
import com.poc.hss.fasttrack.client.model.CacheOperationV3;
import com.poc.hss.fasttrack.client.model.CacheRequestV3;
import com.poc.hss.fasttrack.client.model.CacheResponseV3;
import com.poc.hss.fasttrack.client.model.CacheTypeV3;
import com.poc.hss.fasttrack.retry.RetryUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Optional;
import java.util.function.Function;

<span class="nc" id="L18">@Slf4j</span>
@Component
public class CacheGateway {

    private final CacheV3Api cacheV3Api;

    public CacheGateway(
            CacheV3Api cacheV3Api,
            @Value(&quot;${cache.address:http://cache}/api/v3&quot;) String cacheAddress
<span class="nc" id="L27">    ) {</span>
<span class="nc" id="L28">        this.cacheV3Api = cacheV3Api;</span>
<span class="nc" id="L29">        this.cacheV3Api.getApiClient().setBasePath(cacheAddress);</span>
<span class="nc" id="L30">    }</span>

    public &lt;T&gt; T getValue(String group, String component, String key, String batch, Function&lt;Object, T&gt; parser, T defaultValue) {
        try {
<span class="nc" id="L34">            return RetryUtils.run(</span>
                    () -&gt; {
<span class="nc" id="L36">                        CacheResponseV3 cache = this.cacheV3Api.getCache(</span>
                                group,
                                component,
                                key,
                                batch
                        );

<span class="nc" id="L43">                        return Optional.ofNullable(cache.getValue()).map(parser).orElse(defaultValue);</span>
                    },
<span class="nc" id="L45">                    Duration.ofMinutes(5)</span>
            );
<span class="nc" id="L47">        } catch (BackoffException e) {</span>
<span class="nc" id="L48">            log.error(String.format(&quot;Fail to get cache [group=%s, component=%s, key=%s, batch=%s]&quot;, group, component, key, batch), e);</span>
<span class="nc" id="L49">            throw new RuntimeException(e);</span>
        }
    }

    public &lt;T&gt; void setValue(String group, String component, String key, String batch, CacheTypeV3 cacheType, T value) {
        try {
<span class="nc" id="L55">            RetryUtils.run(</span>
<span class="nc" id="L56">                    () -&gt; this.cacheV3Api.updateCache(</span>
                            group,
                            component,
                            key,
                            new CacheRequestV3()
<span class="nc" id="L61">                                    .batch(batch)</span>
<span class="nc" id="L62">                                    .value(value)</span>
<span class="nc" id="L63">                                    .operation(CacheOperationV3.SET)</span>
<span class="nc" id="L64">                                    .type(cacheType)</span>
                    ),
<span class="nc" id="L66">                    Duration.ofMinutes(5)</span>
            );
<span class="nc" id="L68">        } catch (BackoffException e) {</span>
<span class="nc" id="L69">            log.error(String.format(&quot;Fail to set cache [group=%s, component=%s, key=%s, batch=%s] value to %s&quot;, group, component, key, batch, value), e);</span>
<span class="nc" id="L70">            throw new RuntimeException(e);</span>
<span class="nc" id="L71">        }</span>
<span class="nc" id="L72">    }</span>

    public &lt;T&gt; void increment(String group, String component, String key, String batch, T value) {
        try {
<span class="nc" id="L76">            RetryUtils.run(</span>
<span class="nc" id="L77">                    () -&gt; this.cacheV3Api.updateCache(</span>
                            group,
                            component,
                            key,
                            new CacheRequestV3()
<span class="nc" id="L82">                                    .batch(batch)</span>
<span class="nc" id="L83">                                    .value(value)</span>
<span class="nc" id="L84">                                    .operation(CacheOperationV3.INCREMENT)</span>
<span class="nc" id="L85">                                    .type(CacheTypeV3.COUNTER)</span>
                    ),
<span class="nc" id="L87">                    Duration.ofMinutes(5)</span>
            );
<span class="nc" id="L89">        } catch (BackoffException e) {</span>
<span class="nc" id="L90">            log.error(String.format(&quot;Fail to increment cache [group=%s, component=%s, key=%s, batch=%s] by %s&quot;, group, component, key, batch, value), e);</span>
<span class="nc" id="L91">            throw new RuntimeException(e);</span>
<span class="nc" id="L92">        }</span>
<span class="nc" id="L93">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>