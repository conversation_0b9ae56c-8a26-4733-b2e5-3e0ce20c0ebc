<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AbstractMetricCounter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.counter</a> &gt; <span class="el_source">AbstractMetricCounter.java</span></div><h1>AbstractMetricCounter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.counter;

import com.poc.hss.fasttrack.metric.holder.value.AbstractMetricValue;
import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import org.slf4j.Logger;

public abstract class AbstractMetricCounter extends AbstractMetricValue&lt;Long&gt; implements MetricCounter {
    public AbstractMetricCounter(MetricSpecification&lt;Long&gt; specification, Logger logger) {
<span class="nc" id="L9">        super(specification, logger);</span>
<span class="nc" id="L10">    }</span>

    public final void increment(Long value) {
<span class="nc bnc" id="L13" title="All 2 branches missed.">        if (this.logger.isDebugEnabled())</span>
<span class="nc" id="L14">            this.logger.debug(&quot;Metric [{}] increment value: {}&quot;, this.specification, value);</span>
<span class="nc" id="L15">        doIncrement(value);</span>
<span class="nc" id="L16">    }</span>

    public final void incrementSync(Long value) {
<span class="nc bnc" id="L19" title="All 2 branches missed.">        if (this.logger.isDebugEnabled())</span>
<span class="nc" id="L20">            this.logger.debug(&quot;Metric [{}] increment value sync: {}&quot;, this.specification, value);</span>
<span class="nc" id="L21">        doIncrementValueSync(value);</span>
<span class="nc" id="L22">    }</span>

    protected abstract void doIncrement(Long value);

    protected abstract void doIncrementValueSync(Long value);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>