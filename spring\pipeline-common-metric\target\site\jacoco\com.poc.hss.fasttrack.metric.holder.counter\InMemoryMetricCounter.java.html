<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InMemoryMetricCounter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.counter</a> &gt; <span class="el_source">InMemoryMetricCounter.java</span></div><h1>InMemoryMetricCounter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.counter;

import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import lombok.extern.slf4j.Slf4j;

<span class="nc" id="L6">@Slf4j</span>
public class InMemoryMetricCounter extends AbstractMetricCounter {

    private Long value;

    public InMemoryMetricCounter(MetricSpecification&lt;Long&gt; specification) {
<span class="nc" id="L12">        super(specification, log);</span>
<span class="nc" id="L13">        this.value = specification.getKey().getDefaultValue();</span>
<span class="nc" id="L14">    }</span>

    @Override
    protected void doIncrement(Long value) {
<span class="nc" id="L18">        doSetValue(doGetValue() + value);</span>
<span class="nc" id="L19">    }</span>

    @Override
    protected void doIncrementValueSync(Long value) {
<span class="nc" id="L23">        doIncrement(value);</span>
<span class="nc" id="L24">    }</span>

    @Override
    protected Long doGetValue() {
<span class="nc" id="L28">        return value;</span>
    }

    @Override
    protected void doSetValue(Long value) {
<span class="nc" id="L33">        this.value = value;</span>
<span class="nc" id="L34">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>