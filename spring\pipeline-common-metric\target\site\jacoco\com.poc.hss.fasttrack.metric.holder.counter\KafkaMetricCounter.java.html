<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaMetricCounter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.counter</a> &gt; <span class="el_source">KafkaMetricCounter.java</span></div><h1>KafkaMetricCounter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.counter;

import com.poc.hss.fasttrack.client.model.CacheOperationV3;
import com.poc.hss.fasttrack.client.model.CacheTypeV3;
import com.poc.hss.fasttrack.dto.CacheRequestDTO;
import com.poc.hss.fasttrack.metric.gateway.CacheGateway;
import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import com.poc.hss.fasttrack.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.Assert;

<span class="nc" id="L13">@Slf4j</span>
public class KafkaMetricCounter extends RestMetricCounter {

    private final String group;
    private final String component;
    private final String batch;
    private final String key;
    private final KafkaTemplate&lt;String, String&gt; kafkaTemplate;
    private final String kafkaPartitionKey;

    public KafkaMetricCounter(MetricSpecification&lt;Long&gt; specification, CacheGateway cacheGateway, KafkaTemplate&lt;String, String&gt; kafkaTemplate) {
<span class="nc" id="L24">        super(specification, cacheGateway);</span>
<span class="nc" id="L25">        Assert.hasText(kafkaTemplate.getDefaultTopic(), &quot;Kafka template must have a default topic&quot;);</span>
<span class="nc" id="L26">        this.group = specification.getGroup();</span>
<span class="nc" id="L27">        this.component = specification.getComponent().toString();</span>
<span class="nc" id="L28">        this.batch = specification.getBatch();</span>
<span class="nc" id="L29">        this.key = specification.getKey().getKey();</span>
<span class="nc" id="L30">        this.kafkaTemplate = kafkaTemplate;</span>
<span class="nc" id="L31">        this.kafkaPartitionKey = String.format(&quot;%s&quot;, this.batch);</span>
<span class="nc" id="L32">    }</span>

    @Override
    protected void doIncrement(Long value) {
<span class="nc" id="L36">        this.kafkaTemplate.sendDefault(</span>
                this.kafkaPartitionKey,
<span class="nc" id="L38">                JsonUtils.serialize(</span>
                        CacheRequestDTO
<span class="nc" id="L40">                                .builder()</span>
<span class="nc" id="L41">                                .group(this.group)</span>
<span class="nc" id="L42">                                .component(this.component)</span>
<span class="nc" id="L43">                                .batch(this.batch)</span>
<span class="nc" id="L44">                                .key(this.key)</span>
<span class="nc" id="L45">                                .value(value)</span>
<span class="nc" id="L46">                                .operation(CacheOperationV3.INCREMENT)</span>
<span class="nc" id="L47">                                .type(CacheTypeV3.COUNTER)</span>
<span class="nc" id="L48">                                .build()</span>
                )
        );
<span class="nc" id="L51">    }</span>

    @Override
    protected void doIncrementValueSync(Long value) {
<span class="nc" id="L55">        super.doIncrement(value);</span>
<span class="nc" id="L56">    }</span>

    @Override
    protected void doSetValue(Long value) {
<span class="nc" id="L60">        this.kafkaTemplate.sendDefault(</span>
                this.kafkaPartitionKey,
<span class="nc" id="L62">                JsonUtils.serialize(</span>
                        CacheRequestDTO
<span class="nc" id="L64">                                .builder()</span>
<span class="nc" id="L65">                                .group(this.group)</span>
<span class="nc" id="L66">                                .component(this.component)</span>
<span class="nc" id="L67">                                .batch(this.batch)</span>
<span class="nc" id="L68">                                .key(this.key)</span>
<span class="nc" id="L69">                                .value(value)</span>
<span class="nc" id="L70">                                .operation(CacheOperationV3.SET)</span>
<span class="nc" id="L71">                                .type(CacheTypeV3.COUNTER)</span>
<span class="nc" id="L72">                                .build()</span>
                )
        );
<span class="nc" id="L75">    }</span>

    @Override
    protected void doSetValueSync(Long value) {
<span class="nc" id="L79">        super.doSetValue(value);</span>
<span class="nc" id="L80">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>