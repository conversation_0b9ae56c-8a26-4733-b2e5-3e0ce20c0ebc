<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RestMetricCounter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.counter</a> &gt; <span class="el_source">RestMetricCounter.java</span></div><h1>RestMetricCounter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.counter;

import com.poc.hss.fasttrack.client.model.CacheTypeV3;
import com.poc.hss.fasttrack.metric.gateway.CacheGateway;
import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

<span class="nc" id="L10">@Slf4j</span>
public class RestMetricCounter extends AbstractMetricCounter {

    private final String group;
    private final String component;
    private final String key;
    private final String batch;
    private final Function&lt;Object, Long&gt; parser;
    private final Long defaultValue;
    private final CacheGateway cacheGateway;

    public RestMetricCounter(MetricSpecification&lt;Long&gt; specification, CacheGateway cacheGateway) {
<span class="nc" id="L22">        super(specification, log);</span>
<span class="nc" id="L23">        this.group = specification.getGroup();</span>
<span class="nc" id="L24">        this.component = specification.getComponent().toString();</span>
<span class="nc" id="L25">        this.key = specification.getKey().getKey();</span>
<span class="nc" id="L26">        this.batch = specification.getBatch();</span>
<span class="nc" id="L27">        this.parser = specification.getKey().getParser();</span>
<span class="nc" id="L28">        this.defaultValue = specification.getKey().getDefaultValue();</span>
<span class="nc" id="L29">        this.cacheGateway = cacheGateway;</span>
<span class="nc" id="L30">    }</span>

    @Override
    protected void doIncrement(Long value) {
<span class="nc" id="L34">        this.cacheGateway.increment(</span>
                this.group,
                this.component,
                this.key,
                this.batch,
                value
        );
<span class="nc" id="L41">    }</span>

    @Override
    protected void doIncrementValueSync(Long value) {
<span class="nc" id="L45">        doIncrement(value);</span>
<span class="nc" id="L46">    }</span>

    @Override
    protected Long doGetValue() {
<span class="nc" id="L50">        return this.cacheGateway.getValue(</span>
                this.group,
                this.component,
                this.key,
                this.batch,
                this.parser,
                this.defaultValue
        );
    }

    @Override
    protected void doSetValue(Long value) {
<span class="nc" id="L62">        this.cacheGateway.setValue(</span>
                this.group,
                this.component,
                this.key,
                this.batch,
                CacheTypeV3.COUNTER,
                value
        );
<span class="nc" id="L70">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>