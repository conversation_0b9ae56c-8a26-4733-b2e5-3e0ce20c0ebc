<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>AbstractMetricValue.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.value</a> &gt; <span class="el_source">AbstractMetricValue.java</span></div><h1>AbstractMetricValue.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.value;

import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import org.slf4j.Logger;

public abstract class AbstractMetricValue&lt;T&gt; implements MetricValue&lt;T&gt; {

    protected final MetricSpecification&lt;T&gt; specification;
    protected final Logger logger;

<span class="nc" id="L11">    public AbstractMetricValue(MetricSpecification&lt;T&gt; specification, Logger logger) {</span>
<span class="nc" id="L12">        this.specification = specification;</span>
<span class="nc" id="L13">        this.logger = logger;</span>
<span class="nc" id="L14">    }</span>

    @Override
    public final MetricSpecification&lt;T&gt; getSpecification() {
<span class="nc" id="L18">        return this.specification;</span>
    }

    @Override
    public final T getValue() {
<span class="nc" id="L23">        T value = doGetValue();</span>
<span class="nc bnc" id="L24" title="All 2 branches missed.">        if (this.logger.isDebugEnabled())</span>
<span class="nc" id="L25">            this.logger.debug(&quot;Metric [{}] get value: {}&quot;, this.specification, value);</span>
<span class="nc" id="L26">        return value;</span>
    }

    @Override
    public final void setValue(T value) {
<span class="nc bnc" id="L31" title="All 2 branches missed.">        if (this.logger.isDebugEnabled())</span>
<span class="nc" id="L32">            this.logger.debug(&quot;Metric [{}] set value: {}&quot;, this.specification, value);</span>
<span class="nc" id="L33">        doSetValue(value);</span>
<span class="nc" id="L34">    }</span>

    @Override
    public void setValueSync(T value) {
<span class="nc bnc" id="L38" title="All 2 branches missed.">        if (this.logger.isDebugEnabled())</span>
<span class="nc" id="L39">            this.logger.debug(&quot;Metric [{}] set value sync: {}&quot;, this.specification, value);</span>
<span class="nc" id="L40">        doSetValueSync(value);</span>
<span class="nc" id="L41">    }</span>

    protected abstract T doGetValue();

    protected abstract void doSetValue(T value);

    protected void doSetValueSync(T value) {
<span class="nc" id="L48">        doSetValue(value);</span>
<span class="nc" id="L49">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>