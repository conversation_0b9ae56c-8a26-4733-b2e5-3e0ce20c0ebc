<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InMemoryMetricValue.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.value</a> &gt; <span class="el_source">InMemoryMetricValue.java</span></div><h1>InMemoryMetricValue.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.value;

import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import lombok.extern.slf4j.Slf4j;

<span class="nc" id="L6">@Slf4j</span>
public class InMemoryMetricValue&lt;T&gt; extends AbstractMetricValue&lt;T&gt; {
    private T value;

    public InMemoryMetricValue(MetricSpecification&lt;T&gt; specification) {
<span class="nc" id="L11">        super(specification, log);</span>
<span class="nc" id="L12">        this.value = specification.getKey().getDefaultValue();</span>
<span class="nc" id="L13">    }</span>

    @Override
    protected T doGetValue() {
<span class="nc" id="L17">        return value;</span>
    }

    @Override
    protected void doSetValue(T value) {
<span class="nc" id="L22">        this.value = value;</span>
<span class="nc" id="L23">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>