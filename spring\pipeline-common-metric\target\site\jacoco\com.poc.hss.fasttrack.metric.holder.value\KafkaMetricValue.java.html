<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaMetricValue.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.holder.value</a> &gt; <span class="el_source">KafkaMetricValue.java</span></div><h1>KafkaMetricValue.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.holder.value;

import com.poc.hss.fasttrack.client.model.CacheOperationV3;
import com.poc.hss.fasttrack.client.model.CacheTypeV3;
import com.poc.hss.fasttrack.dto.CacheRequestDTO;
import com.poc.hss.fasttrack.metric.gateway.CacheGateway;
import com.poc.hss.fasttrack.metric.specification.MetricSpecification;
import com.poc.hss.fasttrack.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.util.Assert;

import java.util.Objects;

<span class="nc" id="L15">@Slf4j</span>
public class KafkaMetricValue&lt;T&gt; extends RestMetricValue&lt;T&gt; {

    private final String group;
    private final String component;
    private final String batch;
    private final String key;
    private final KafkaTemplate&lt;String, String&gt; kafkaTemplate;
    private final String kafkaPartitionKey;

    public KafkaMetricValue(MetricSpecification&lt;T&gt; specification, CacheGateway cacheGateway, KafkaTemplate&lt;String, String&gt; kafkaTemplate) {
<span class="nc" id="L26">        super(specification, cacheGateway);</span>
<span class="nc" id="L27">        Assert.hasText(kafkaTemplate.getDefaultTopic(), &quot;Kafka template must have a default topic&quot;);</span>
<span class="nc" id="L28">        this.group = specification.getGroup();</span>
<span class="nc" id="L29">        this.component = specification.getComponent().toString();</span>
<span class="nc" id="L30">        this.batch = specification.getBatch();</span>
<span class="nc" id="L31">        this.key = specification.getKey().getKey();</span>
<span class="nc" id="L32">        this.kafkaTemplate = kafkaTemplate;</span>
<span class="nc" id="L33">        this.kafkaPartitionKey = String.format(&quot;%s&quot;, this.batch);</span>
<span class="nc" id="L34">    }</span>

    @Override
    protected void doSetValue(T value) {
<span class="nc bnc" id="L38" title="All 2 branches missed.">        if (this.logger.isDebugEnabled())</span>
<span class="nc" id="L39">            this.logger.debug(&quot;KafkaMetricValue [{}] doSetValue: {}&quot;, this.specification, value);</span>
<span class="nc" id="L40">        this.kafkaTemplate.sendDefault(</span>
                this.kafkaPartitionKey,
<span class="nc" id="L42">                JsonUtils.serialize(</span>
                        CacheRequestDTO
<span class="nc" id="L44">                                .builder()</span>
<span class="nc" id="L45">                                .group(this.group)</span>
<span class="nc" id="L46">                                .component(this.component)</span>
<span class="nc" id="L47">                                .batch(this.batch)</span>
<span class="nc" id="L48">                                .key(this.key)</span>
<span class="nc" id="L49">                                .value(value)</span>
<span class="nc" id="L50">                                .operation(CacheOperationV3.SET)</span>
<span class="nc" id="L51">                                .type(CacheTypeV3.NORMAL)</span>
<span class="nc" id="L52">                                .build()</span>
                )
        );
<span class="nc" id="L55">    }</span>

    @Override
    protected void doSetValueSync(T value) {
<span class="nc" id="L59">        super.doSetValue(value);</span>
<span class="nc" id="L60">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>