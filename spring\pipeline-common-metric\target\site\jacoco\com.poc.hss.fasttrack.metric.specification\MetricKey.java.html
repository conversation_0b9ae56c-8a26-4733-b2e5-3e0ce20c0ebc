<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MetricKey.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.metric.specification</a> &gt; <span class="el_source">MetricKey.java</span></div><h1>MetricKey.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.metric.specification;

import com.poc.hss.fasttrack.constant.Constants;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.function.Function;

@Getter
<span class="nc bnc" id="L10" title="All 12 branches missed.">@EqualsAndHashCode(onlyExplicitlyIncluded = true)</span>
public final class MetricKey&lt;T&gt; {

<span class="nc" id="L13">    public static final MetricKey&lt;Long&gt; MESSAGE_IN = new MetricKey&lt;&gt;(</span>
            Constants.MESSAGE_IN,
<span class="nc" id="L15">            obj -&gt; Long.parseLong(obj.toString()),</span>
<span class="nc" id="L16">            0L</span>
    );
<span class="nc" id="L18">    public static final MetricKey&lt;Long&gt; MESSAGE_OUT = new MetricKey&lt;&gt;(</span>
            Constants.MESSAGE_OUT,
<span class="nc" id="L20">            obj -&gt; Long.parseLong(obj.toString()),</span>
<span class="nc" id="L21">            0L</span>
    );
<span class="nc" id="L23">    public static final MetricKey&lt;Boolean&gt; BATCH_COMPLETION_FLAG = new MetricKey&lt;&gt;(</span>
            Constants.BATCH_COMPLETION,
<span class="nc" id="L25">            obj -&gt; Boolean.parseBoolean(obj.toString()),</span>
<span class="nc" id="L26">            false</span>
    );

    @EqualsAndHashCode.Include
<span class="nc" id="L30">    private final String key;</span>
<span class="nc" id="L31">    private final T defaultValue;</span>
<span class="nc" id="L32">    private final Function&lt;Object, T&gt; parser;</span>

<span class="nc" id="L34">    public MetricKey(String key, Function&lt;Object, T&gt; parser, T defaultValue) {</span>
<span class="nc" id="L35">        this.key = key;</span>
<span class="nc" id="L36">        this.parser = parser;</span>
<span class="nc" id="L37">        this.defaultValue = defaultValue;</span>
<span class="nc" id="L38">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>