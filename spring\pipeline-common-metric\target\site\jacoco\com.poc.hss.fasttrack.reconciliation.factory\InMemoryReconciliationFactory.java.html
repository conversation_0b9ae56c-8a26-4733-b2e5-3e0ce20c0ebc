<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InMemoryReconciliationFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.reconciliation.factory</a> &gt; <span class="el_source">InMemoryReconciliationFactory.java</span></div><h1>InMemoryReconciliationFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.reconciliation.factory;

import com.poc.hss.fasttrack.model.Unity2Component;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

<span class="nc" id="L9">@Slf4j</span>
@Component
@ConditionalOnProperty(value = &quot;cache.provider&quot;, havingValue = &quot;memory&quot;, matchIfMissing = true)
public class InMemoryReconciliationFactory extends AbstractReconciliationFactory {

    public InMemoryReconciliationFactory(
            @Value(&quot;${cache.group:}&quot;) String defaultCacheGroup,
            @Value(&quot;${cache.component:}&quot;) Unity2Component defaultCacheComponent
    ) {
<span class="nc" id="L18">        super(defaultCacheGroup, defaultCacheComponent);</span>
<span class="nc" id="L19">    }</span>

    @Override
    public void reconcile(String group, Unity2Component component, String batch) {
<span class="nc" id="L23">        log.warn(&quot;[reconciliation] reconciliation is not supported when cache.provider=memory&quot;);</span>
<span class="nc" id="L24">    }</span>

    @Override
    public void reset(String batch) {
<span class="nc" id="L28">        log.warn(&quot;[reconciliation] reconciliation reset is not supported when cache.provider=memory&quot;);</span>
<span class="nc" id="L29">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>