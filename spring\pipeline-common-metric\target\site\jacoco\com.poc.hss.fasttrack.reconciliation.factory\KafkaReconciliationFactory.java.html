<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaReconciliationFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.reconciliation.factory</a> &gt; <span class="el_source">KafkaReconciliationFactory.java</span></div><h1>KafkaReconciliationFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.reconciliation.factory;

import com.poc.hss.fasttrack.client.model.CacheOperationV3;
import com.poc.hss.fasttrack.dto.ReconciliationRequestDTO;
import com.poc.hss.fasttrack.kafka.factory.KafkaTemplateFactory;
import com.poc.hss.fasttrack.model.Unity2Component;
import com.poc.hss.fasttrack.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

<span class="nc" id="L14">@Slf4j</span>
@Component
@ConditionalOnProperty(value = &quot;cache.provider&quot;, havingValue = &quot;kafka&quot;)
public class KafkaReconciliationFactory extends AbstractReconciliationFactory {
    private final KafkaTemplate&lt;String, String&gt; kafkaTemplate;

    public KafkaReconciliationFactory(
            @Value(&quot;${cache.group:}&quot;) String defaultCacheGroup,
            @Value(&quot;${cache.component:}&quot;) Unity2Component defaultCacheComponent,
            KafkaTemplateFactory&lt;String, String&gt; kafkaTemplateFactory,
            @Value(&quot;${cache.topic}&quot;) String cacheTopic
    ) {
<span class="nc" id="L26">        super(defaultCacheGroup, defaultCacheComponent);</span>
<span class="nc" id="L27">        this.kafkaTemplate = kafkaTemplateFactory.createKafkaTemplate(cacheTopic, false);</span>
<span class="nc" id="L28">    }</span>

    @Override
    public void reconcile(String group, Unity2Component component, String batch) {
<span class="nc" id="L32">        String kafkaPartitionKey = String.format(&quot;%s&quot;, batch);</span>
<span class="nc" id="L33">        this.kafkaTemplate.sendDefault(</span>
                kafkaPartitionKey,
<span class="nc" id="L35">                JsonUtils.serialize(</span>
<span class="nc" id="L36">                        ReconciliationRequestDTO.builder()</span>
<span class="nc" id="L37">                                .group(group)</span>
<span class="nc" id="L38">                                .component(component.toString())</span>
<span class="nc" id="L39">                                .batch(batch)</span>
<span class="nc" id="L40">                                .operation(CacheOperationV3.RECONCILE)</span>
<span class="nc" id="L41">                                .build()</span>
                )
        );
<span class="nc" id="L44">    }</span>

    @Override
    public void reset(String batch) {
<span class="nc" id="L48">        String kafkaPartitionKey = String.format(&quot;%s&quot;, batch);</span>
<span class="nc" id="L49">        this.kafkaTemplate.sendDefault(</span>
                kafkaPartitionKey,
<span class="nc" id="L51">                JsonUtils.serialize(</span>
<span class="nc" id="L52">                        ReconciliationRequestDTO.builder()</span>
<span class="nc" id="L53">                                .batch(batch)</span>
<span class="nc" id="L54">                                .operation(CacheOperationV3.RESET_BATCH)</span>
<span class="nc" id="L55">                                .build()</span>
                )
        );
<span class="nc" id="L58">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>