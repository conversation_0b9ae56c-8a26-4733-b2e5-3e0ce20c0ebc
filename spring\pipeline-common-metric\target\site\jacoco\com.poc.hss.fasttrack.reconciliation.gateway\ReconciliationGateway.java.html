<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationGateway.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.reconciliation.gateway</a> &gt; <span class="el_source">ReconciliationGateway.java</span></div><h1>ReconciliationGateway.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.reconciliation.gateway;

import com.poc.hss.fasttrack.backoff.BackoffException;
import com.poc.hss.fasttrack.client.api.ReconciliationApi;
import com.poc.hss.fasttrack.client.model.ReconciliationRequest;
import com.poc.hss.fasttrack.client.model.ReconciliationResponse;
import com.poc.hss.fasttrack.client.model.ResetRequest;
import com.poc.hss.fasttrack.client.model.ResetResponse;
import com.poc.hss.fasttrack.model.Unity2Component;
import com.poc.hss.fasttrack.retry.RetryUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;

<span class="nc" id="L17">@Slf4j</span>
@Component
public class ReconciliationGateway {
    private final ReconciliationApi reconciliationApi;

    public ReconciliationGateway(
            ReconciliationApi reconciliationApi,
            @Value(&quot;${cache.address:http://cache}/api/reconciliation&quot;) String cacheAddress
<span class="nc" id="L25">    ) {</span>
<span class="nc" id="L26">        this.reconciliationApi = reconciliationApi;</span>
<span class="nc" id="L27">        this.reconciliationApi.getApiClient().setBasePath(cacheAddress);</span>
<span class="nc" id="L28">    }</span>

    public void reconcile(String group, Unity2Component component, String batch) {
        try {
<span class="nc" id="L32">            final ReconciliationRequest request = new ReconciliationRequest();</span>
<span class="nc" id="L33">            request.setGroup(group);</span>
<span class="nc" id="L34">            request.setComponent(component.toString());</span>
<span class="nc" id="L35">            request.setBatch(batch);</span>
<span class="nc" id="L36">            ReconciliationResponse response = RetryUtils.run(</span>
<span class="nc" id="L37">                    () -&gt; this.reconciliationApi.reconcile(request),</span>
<span class="nc" id="L38">                    Duration.ofMinutes(5)</span>
            );
<span class="nc" id="L40">            log.info(&quot;Reconciliation Result: {}&quot;, response);</span>
<span class="nc" id="L41">        } catch (BackoffException e) {</span>
<span class="nc" id="L42">            log.error(String.format(&quot;Fail to reconcile [group=%s, component=%s, batch=%s]&quot;, group, component, batch), e);</span>
<span class="nc" id="L43">            throw new RuntimeException(e);</span>
<span class="nc" id="L44">        }</span>
<span class="nc" id="L45">    }</span>

    public void reset(String batch) {
        try {
<span class="nc" id="L49">            final ResetRequest request = new ResetRequest();;</span>
<span class="nc" id="L50">            request.setBatch(batch);</span>
<span class="nc" id="L51">            ResetResponse response = RetryUtils.run(</span>
<span class="nc" id="L52">                    () -&gt; this.reconciliationApi.reset(request),</span>
<span class="nc" id="L53">                    Duration.ofMinutes(5)</span>
            );
<span class="nc" id="L55">            log.info(&quot;Reset Result: {}&quot;, response);</span>
<span class="nc" id="L56">        } catch (BackoffException e) {</span>
<span class="nc" id="L57">            log.error(String.format(&quot;Fail to reset [batch=%s]&quot;, batch), e);</span>
<span class="nc" id="L58">            throw new RuntimeException(e);</span>
<span class="nc" id="L59">        }</span>
<span class="nc" id="L60">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>