<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MetricUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.utils</a> &gt; <span class="el_source">MetricUtils.java</span></div><h1>MetricUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.utils;

import com.poc.hss.fasttrack.metric.factory.MetricFactory;
import com.poc.hss.fasttrack.metric.holder.counter.MetricCounter;
import com.poc.hss.fasttrack.metric.holder.value.MetricValue;
import com.poc.hss.fasttrack.metric.specification.MetricKey;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

<span class="nc" id="L16">@Slf4j</span>
@Component
public class MetricUtils {

    private final MetricFactory metricFactory;

<span class="nc" id="L22">    public MetricUtils(MetricFactory metricFactory) {</span>
<span class="nc" id="L23">        this.metricFactory = metricFactory;</span>
<span class="nc" id="L24">    }</span>

    public &lt;T&gt; void updateBatchCountForList(List&lt;T&gt; records, MetricKey&lt;Long&gt; key, Function&lt;T, String&gt; batchIdMapper) {
<span class="nc" id="L27">        doUpdateBatchCountForList(records, key, batchIdMapper, false);</span>
<span class="nc" id="L28">    }</span>

    public &lt;T&gt; void updateBatchCountForListSync(List&lt;T&gt; records, MetricKey&lt;Long&gt; key, Function&lt;T, String&gt; batchIdMapper) {
<span class="nc" id="L31">        doUpdateBatchCountForList(records, key, batchIdMapper, true);</span>
<span class="nc" id="L32">    }</span>

    private &lt;T&gt; void doUpdateBatchCountForList(List&lt;T&gt; records, MetricKey&lt;Long&gt; key, Function&lt;T, String&gt; batchIdMapper, boolean sync) {
<span class="nc" id="L35">        records.stream()</span>
<span class="nc" id="L36">                .collect(Collectors.groupingBy(</span>
<span class="nc" id="L37">                        rec -&gt; Optional.ofNullable(batchIdMapper.apply(rec)),</span>
<span class="nc" id="L38">                        Collectors.counting()</span>
                ))
<span class="nc" id="L40">                .forEach((batchId, count) -&gt; {</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">                            if (log.isDebugEnabled())</span>
<span class="nc" id="L42">                                log.debug(&quot;key: {}, batchId: {}, count: {}&quot;, key, batchId, count);</span>
<span class="nc" id="L43">                            MetricCounter metricCounter = metricFactory.getMetricCounter(key, batchId.orElse(null));</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">                            if (sync)</span>
<span class="nc" id="L45">                                metricCounter.incrementSync(count);</span>
                            else
<span class="nc" id="L47">                                metricCounter.increment(count);</span>
<span class="nc" id="L48">                        }</span>
                );
<span class="nc" id="L50">    }</span>

    public &lt;T&gt; void updateBatchCompletionForList(List&lt;T&gt; records, Function&lt;T, String&gt; batchIdMapper) {
<span class="nc" id="L53">        records.stream()</span>
<span class="nc" id="L54">                .map(rec -&gt; Optional.ofNullable(batchIdMapper.apply(rec)).orElse(null))</span>
<span class="nc" id="L55">                .filter(Objects::nonNull)</span>
<span class="nc" id="L56">                .distinct()</span>
<span class="nc" id="L57">                .forEach(batchId-&gt; {</span>
<span class="nc" id="L58">                            metricFactory</span>
<span class="nc" id="L59">                                    .getMetricValue(MetricKey.BATCH_COMPLETION_FLAG, batchId)</span>
<span class="nc" id="L60">                                    .setValue(true);</span>
<span class="nc" id="L61">                        }</span>
                );
<span class="nc" id="L63">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>