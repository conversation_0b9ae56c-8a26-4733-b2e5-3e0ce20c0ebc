<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-metric</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.utils</a> &gt; <span class="el_source">ReconciliationUtils.java</span></div><h1>ReconciliationUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.utils;

import com.poc.hss.fasttrack.reconciliation.factory.ReconciliationFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;

<span class="nc" id="L12">@Slf4j</span>
@Component
public class ReconciliationUtils {
    private final ReconciliationFactory reconciliationFactory;

<span class="nc" id="L17">    public ReconciliationUtils(ReconciliationFactory reconciliationFactory) {</span>
<span class="nc" id="L18">        this.reconciliationFactory = reconciliationFactory;</span>
<span class="nc" id="L19">    }</span>

    public &lt;T&gt; void reconcileForList(List&lt;T&gt; records, Function&lt;T, String&gt; batchIdMapper) {
<span class="nc" id="L22">        records.stream()</span>
<span class="nc" id="L23">                .map(rec -&gt; Optional.ofNullable(batchIdMapper.apply(rec)).orElse(null))</span>
<span class="nc" id="L24">                .filter(Objects::nonNull)</span>
<span class="nc" id="L25">                .distinct()</span>
<span class="nc" id="L26">                .forEach(reconciliationFactory::reconcile);</span>
<span class="nc" id="L27">    }</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>