package com.poc.hss.fasttrack.client.auth;

import com.poc.hss.fasttrack.client.auth.model.RestOauth2Config;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.security.oauth2.client.*;
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

public class OAuth2ClientConfigUtilsTest {
    @Test
    public void testCreateAuthorizedClientRepository() {
        ReactiveOAuth2AuthorizedClientService service = Mockito.mock(ReactiveOAuth2AuthorizedClientService.class);
        ServerOAuth2AuthorizedClientRepository repo = OAuth2ClientConfigUtils.createAuthorizedClientRepository(service);
        assertNotNull(repo);
    }

    @Test
    public void testCreateOAuth2AuthorizedClientManager() {
        ReactiveClientRegistrationRepository repo = Mockito.mock(ReactiveClientRegistrationRepository.class);
        ReactiveOAuth2AuthorizedClientService service = Mockito.mock(ReactiveOAuth2AuthorizedClientService.class);
        ReactiveOAuth2AuthorizedClientManager manager = OAuth2ClientConfigUtils.createOAuth2AuthorizedClientManager(repo, service);
        assertNotNull(manager);
    }

    @Test
    public void testCreateClientRegistrationRepository_valid() {
        RestOauth2Config config = RestOauth2Config.builder()
            .url("http://token")
            .clientId("cid")
            .clientSecret("secret")
            .scopes("scope1,scope2")
            .grantType("client_credentials")
            .authenticationMethod("client_secret_basic")
            .build();
        ReactiveClientRegistrationRepository repo = OAuth2ClientConfigUtils.createClientRegistrationRepository(List.of(config));
        assertNotNull(repo);
    }

    @Test
    public void testCreateClientRegistrationRepository_invalidGrantType() {
        RestOauth2Config config = RestOauth2Config.builder()
            .url("http://token")
            .clientId("cid")
            .clientSecret("secret")
            .scopes("scope1,scope2")
            .grantType("invalid")
            .authenticationMethod("client_secret_basic")
            .build();
        assertThrows(IllegalArgumentException.class, () ->
            OAuth2ClientConfigUtils.createClientRegistrationRepository(List.of(config)));
    }

    @Test
    public void testGetAuthenticationMethod() throws Exception {
        // Use reflection to access private method
        var method = OAuth2ClientConfigUtils.class.getDeclaredMethod("getAuthenticationMethod", String.class);
        method.setAccessible(true);
        assertEquals(ClientAuthenticationMethod.CLIENT_SECRET_BASIC, method.invoke(null, "client_secret_basic"));
        assertEquals(ClientAuthenticationMethod.CLIENT_SECRET_POST, method.invoke(null, "client_secret_post"));
        assertEquals(ClientAuthenticationMethod.CLIENT_SECRET_JWT, method.invoke(null, "client_secret_jwt"));
        assertEquals(ClientAuthenticationMethod.PRIVATE_KEY_JWT, method.invoke(null, "private_key_jwt"));
        assertEquals(ClientAuthenticationMethod.NONE, method.invoke(null, "none"));
        assertNull(method.invoke(null, "unknown"));
        assertNull(method.invoke(null, (Object) null));
    }

    @Test
    public void testGetAuthorizationGrantType() throws Exception {
        var method = OAuth2ClientConfigUtils.class.getDeclaredMethod("getAuthorizationGrantType", String.class);
        method.setAccessible(true);
        assertEquals(AuthorizationGrantType.AUTHORIZATION_CODE, method.invoke(null, "authorization_code"));
        assertEquals(AuthorizationGrantType.CLIENT_CREDENTIALS, method.invoke(null, "client_credentials"));
        assertEquals(AuthorizationGrantType.PASSWORD, method.invoke(null, "password"));

        // When using reflection, exceptions are wrapped in InvocationTargetException
        InvocationTargetException exception1 = assertThrows(InvocationTargetException.class, () -> method.invoke(null, "invalid"));
        assertTrue(exception1.getCause() instanceof IllegalArgumentException);

        InvocationTargetException exception2 = assertThrows(InvocationTargetException.class, () -> method.invoke(null, (Object) null));
        assertTrue(exception2.getCause() instanceof IllegalArgumentException);
    }

    @Test
    public void testCreateAuthorizedClientService() {
        ReactiveClientRegistrationRepository repo = Mockito.mock(ReactiveClientRegistrationRepository.class);
        ReactiveOAuth2AuthorizedClientService service = OAuth2ClientConfigUtils.createAuthorizedClientService(repo);
        assertNotNull(service);
    }
}
