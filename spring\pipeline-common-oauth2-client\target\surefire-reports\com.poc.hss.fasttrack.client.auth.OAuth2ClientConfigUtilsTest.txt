-------------------------------------------------------------------------------
Test set: com.poc.hss.fasttrack.client.auth.OAuth2ClientConfigUtilsTest
-------------------------------------------------------------------------------
Tests run: 7, Failures: 1, Errors: 0, Skipped: 0, Time elapsed: 0.048 s <<< FAILURE! -- in com.poc.hss.fasttrack.client.auth.OAuth2ClientConfigUtilsTest
com.poc.hss.fasttrack.client.auth.OAuth2ClientConfigUtilsTest.testGetAuthorizationGrantType -- Time elapsed: 0.006 s <<< FAILURE!
org.opentest4j.AssertionFailedError: Unexpected exception type thrown, expected: <java.lang.IllegalArgumentException> but was: <java.lang.reflect.InvocationTargetException>
	at org.junit.jupiter.api.AssertionFailureBuilder.build(AssertionFailureBuilder.java:151)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:67)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:35)
	at org.junit.jupiter.api.Assertions.assertThrows(Assertions.java:3115)
	at com.poc.hss.fasttrack.client.auth.OAuth2ClientConfigUtilsTest.testGetAuthorizationGrantType(OAuth2ClientConfigUtilsTest.java:80)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
Caused by: java.lang.reflect.InvocationTargetException
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:118)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at com.poc.hss.fasttrack.client.auth.OAuth2ClientConfigUtilsTest.lambda$testGetAuthorizationGrantType$1(OAuth2ClientConfigUtilsTest.java:80)
	at org.junit.jupiter.api.AssertThrows.assertThrows(AssertThrows.java:53)
	... 6 more
Caused by: java.lang.IllegalArgumentException: invalid grant type - invalid
	at com.poc.hss.fasttrack.client.auth.OAuth2ClientConfigUtils.getAuthorizationGrantType(OAuth2ClientConfigUtils.java:114)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 9 more

