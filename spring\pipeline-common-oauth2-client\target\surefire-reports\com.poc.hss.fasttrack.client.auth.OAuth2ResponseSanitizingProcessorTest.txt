-------------------------------------------------------------------------------
Test set: com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest
-------------------------------------------------------------------------------
Tests run: 3, Failures: 0, Errors: 2, Skipped: 0, Time elapsed: 0.077 s <<< FAILURE! -- in com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest
com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest.testGetResponseWithoutInvalidFields_removesBlankRefreshToken -- Time elapsed: 0.070 s <<< ERROR!
org.springframework.web.reactive.function.UnsupportedMediaTypeException: Content type 'application/octet-stream' not supported for bodyType=net.minidev.json.JSONObject
	at org.springframework.web.reactive.function.BodyExtractors.readWithMessageReaders(BodyExtractors.java:205)
	Suppressed: The stacktrace has been enhanced by Reactor, refer to additional information below: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from UNKNOWN  [DefaultClientResponse]
Original Stack Trace:
		at org.springframework.web.reactive.function.BodyExtractors.readWithMessageReaders(BodyExtractors.java:205)
		at org.springframework.web.reactive.function.BodyExtractors.lambda$toMono$2(BodyExtractors.java:84)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.body(DefaultClientResponse.java:135)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.bodyToMono(DefaultClientResponse.java:150)
		at com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessor.getResponseWithoutInvalidFields(OAuth2ResponseSanitizingProcessor.java:13)
		at com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest.testGetResponseWithoutInvalidFields_removesBlankRefreshToken(OAuth2ResponseSanitizingProcessorTest.java:19)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104)
		at reactor.core.publisher.Mono.block(Mono.java:1779)
		at com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest.testGetResponseWithoutInvalidFields_removesBlankRefreshToken(OAuth2ResponseSanitizingProcessorTest.java:20)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest.testGetResponseWithoutInvalidFields_keepsNonBlankRefreshToken -- Time elapsed: 0.002 s <<< ERROR!
org.springframework.web.reactive.function.UnsupportedMediaTypeException: Content type 'application/octet-stream' not supported for bodyType=net.minidev.json.JSONObject
	at org.springframework.web.reactive.function.BodyExtractors.readWithMessageReaders(BodyExtractors.java:205)
	Suppressed: The stacktrace has been enhanced by Reactor, refer to additional information below: 
Error has been observed at the following site(s):
	*__checkpoint ⇢ Body from UNKNOWN  [DefaultClientResponse]
Original Stack Trace:
		at org.springframework.web.reactive.function.BodyExtractors.readWithMessageReaders(BodyExtractors.java:205)
		at org.springframework.web.reactive.function.BodyExtractors.lambda$toMono$2(BodyExtractors.java:84)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.body(DefaultClientResponse.java:135)
		at org.springframework.web.reactive.function.client.DefaultClientResponse.bodyToMono(DefaultClientResponse.java:150)
		at com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessor.getResponseWithoutInvalidFields(OAuth2ResponseSanitizingProcessor.java:13)
		at com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest.testGetResponseWithoutInvalidFields_keepsNonBlankRefreshToken(OAuth2ResponseSanitizingProcessorTest.java:34)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	Suppressed: java.lang.Exception: #block terminated with an error
		at reactor.core.publisher.BlockingSingleSubscriber.blockingGet(BlockingSingleSubscriber.java:104)
		at reactor.core.publisher.Mono.block(Mono.java:1779)
		at com.poc.hss.fasttrack.client.auth.OAuth2ResponseSanitizingProcessorTest.testGetResponseWithoutInvalidFields_keepsNonBlankRefreshToken(OAuth2ResponseSanitizingProcessorTest.java:35)
		at java.base/java.lang.reflect.Method.invoke(Method.java:580)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
		at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)

