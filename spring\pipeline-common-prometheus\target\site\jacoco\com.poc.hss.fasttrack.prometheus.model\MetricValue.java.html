<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>MetricValue.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-prometheus</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.prometheus.model</a> &gt; <span class="el_source">MetricValue.java</span></div><h1>MetricValue.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.prometheus.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

<span class="nc" id="L6">@Builder</span>
<span class="pc bnc" id="L7" title="All 16 branches missed.">@Data</span>
<span class="nc" id="L8">@AllArgsConstructor</span>
<span class="fc" id="L9">@NoArgsConstructor</span>
<span class="nc" id="L10">@ToString</span>
public class MetricValue {
<span class="fc" id="L12">    private Metric metric;</span>
<span class="fc" id="L13">    private Object[] value;</span>

<span class="nc" id="L15">    @Builder</span>
<span class="pc bnc" id="L16" title="All 70 branches missed.">    @Data</span>
<span class="nc" id="L17">    @AllArgsConstructor</span>
<span class="fc" id="L18">    @NoArgsConstructor</span>
<span class="nc" id="L19">    @ToString</span>
    public static class Metric {
        @JsonProperty(&quot;__name__&quot;)
<span class="fc" id="L22">        private String name;</span>
        @JsonProperty(&quot;app_name&quot;)
<span class="nc" id="L24">        private String appName;</span>
<span class="nc" id="L25">        private String application;</span>
<span class="nc" id="L26">        private String namespace;</span>
        @JsonProperty(&quot;pod_name&quot;)
<span class="nc" id="L28">        private String podName;</span>
        @JsonProperty(&quot;service_type&quot;)
<span class="nc" id="L30">        private String serviceType;</span>
<span class="nc" id="L31">        private String pipeline;</span>
<span class="nc" id="L32">        private String topic;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>