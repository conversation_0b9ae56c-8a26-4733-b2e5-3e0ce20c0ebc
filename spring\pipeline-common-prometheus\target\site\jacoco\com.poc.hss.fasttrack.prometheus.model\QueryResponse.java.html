<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>QueryResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-prometheus</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.prometheus.model</a> &gt; <span class="el_source">QueryResponse.java</span></div><h1>QueryResponse.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.prometheus.model;

import lombok.*;

import java.util.List;

<span class="nc" id="L7">@Builder</span>
<span class="pc bnc" id="L8" title="All 22 branches missed.">@Data</span>
<span class="nc" id="L9">@AllArgsConstructor</span>
<span class="fc" id="L10">@NoArgsConstructor</span>
<span class="nc" id="L11">@ToString</span>
public class QueryResponse {
<span class="nc" id="L13">    private String status;</span>
<span class="fc" id="L14">    private QueryResponseData data;</span>

<span class="nc" id="L16">    @Builder</span>
<span class="pc bpc" id="L17" title="21 of 22 branches missed.">    @Data</span>
<span class="nc" id="L18">    @AllArgsConstructor</span>
<span class="fc" id="L19">    @NoArgsConstructor</span>
<span class="nc" id="L20">    @ToString</span>
    public static class QueryResponseData {
<span class="nc" id="L22">        private String resultType;</span>
<span class="fc" id="L23">        private List&lt;MetricValue&gt; result;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>