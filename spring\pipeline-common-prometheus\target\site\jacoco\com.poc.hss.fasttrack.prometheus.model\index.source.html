<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.prometheus.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-prometheus</a> &gt; <span class="el_package">com.poc.hss.fasttrack.prometheus.model</span></div><h1>com.poc.hss.fasttrack.prometheus.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">865 of 925</td><td class="ctr2">6%</td><td class="bar">129 of 130</td><td class="ctr2">0%</td><td class="ctr1">130</td><td class="ctr2">147</td><td class="ctr1">21</td><td class="ctr2">34</td><td class="ctr1">65</td><td class="ctr2">82</td><td class="ctr1">4</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a0"><a href="MetricValue.java.html" class="el_source">MetricValue.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="114" height="10" title="584" alt="584"/><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="27" alt="27"/></td><td class="ctr2" id="c1">4%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="86" alt="86"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">85</td><td class="ctr2" id="g0">93</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">20</td><td class="ctr1" id="j0">42</td><td class="ctr2" id="k0">50</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">4</td></tr><tr><td id="a1"><a href="QueryResponse.java.html" class="el_source">QueryResponse.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="281" alt="281"/><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="33" alt="33"/></td><td class="ctr2" id="c0">10%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="43" alt="43"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e0">2%</td><td class="ctr1" id="f1">45</td><td class="ctr2" id="g1">54</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i1">14</td><td class="ctr1" id="j1">23</td><td class="ctr2" id="k1">32</td><td class="ctr1" id="l1">2</td><td class="ctr2" id="m1">4</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>