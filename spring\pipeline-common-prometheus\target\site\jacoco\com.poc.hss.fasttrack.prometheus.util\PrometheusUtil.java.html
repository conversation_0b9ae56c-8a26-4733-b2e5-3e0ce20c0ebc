<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>PrometheusUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-prometheus</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.prometheus.util</a> &gt; <span class="el_source">PrometheusUtil.java</span></div><h1>PrometheusUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.prometheus.util;

import com.poc.hss.fasttrack.prometheus.model.MetricValue;
import com.poc.hss.fasttrack.prometheus.model.QueryResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

<span class="fc" id="L14">@Slf4j</span>
<span class="nc" id="L15">public class PrometheusUtil {</span>
    public static List&lt;MetricValue&gt; query(String host, String query) {
<span class="fc" id="L17">        log.info(&quot;host: {}, query: {}&quot;, host, query);</span>
<span class="fc" id="L18">        final String queryUri = UriComponentsBuilder.fromHttpUrl(String.format(&quot;%s/api/v1/query&quot;, host))</span>
<span class="fc" id="L19">                .queryParam(&quot;query&quot;, &quot;{query}&quot;)</span>
<span class="fc" id="L20">                .encode().toUriString();</span>
<span class="fc" id="L21">        HttpHeaders headers = new HttpHeaders();</span>
<span class="fc" id="L22">        headers.set(HttpHeaders.ACCEPT, MediaType.APPLICATION_JSON_VALUE);</span>
<span class="fc" id="L23">        HttpEntity&lt;?&gt; entity = new HttpEntity&lt;&gt;(headers);</span>

<span class="fc" id="L25">        final ResponseEntity&lt;QueryResponse&gt; responseEntity = new RestTemplate().exchange(queryUri, HttpMethod.GET, entity, QueryResponse.class, Collections.singletonMap(&quot;query&quot;, query));</span>
<span class="fc" id="L26">        return Optional.ofNullable(responseEntity.getBody())</span>
<span class="fc" id="L27">                .map(QueryResponse::getData)</span>
<span class="fc" id="L28">                .map(QueryResponse.QueryResponseData::getResult).orElse(null);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>