C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\config\Unity2MethodSecurityConfig.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\config\Unity2SecurityConfigurer.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\advice\AuditAdvice.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\config\Unity2MethodSecurityExpressionHandler.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\auth\SimpleScope.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\config\Unity2MethodSecurityExpressionRoot.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\auth\ResourceIdScope.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\CachingOpaqueTokenIntrospector.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\bean\DefaultScopeResourceIdResolver.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\bean\ScopeResourceIdResolver.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\auth\Scope.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\bean\AuthenticationResolver.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-security\src\main\java\com\poc\hss\fasttrack\model\ResourceOperation.java
