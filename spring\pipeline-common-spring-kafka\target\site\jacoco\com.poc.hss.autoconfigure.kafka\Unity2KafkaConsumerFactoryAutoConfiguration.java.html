<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaConsumerFactoryAutoConfiguration.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.autoconfigure.kafka</a> &gt; <span class="el_source">Unity2KafkaConsumerFactoryAutoConfiguration.java</span></div><h1>Unity2KafkaConsumerFactoryAutoConfiguration.java</h1><pre class="source lang-java linenums">package com.poc.hss.autoconfigure.kafka;

import com.poc.hss.fasttrack.kafka.config.KafkaConsumerPropertiesProvider;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.DefaultKafkaConsumerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
<span class="nc" id="L17">public class Unity2KafkaConsumerFactoryAutoConfiguration {</span>

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(KafkaConsumerPropertiesProvider.class)
    public &lt;K, V&gt; ConsumerFactory&lt;K, V&gt; kafkaConsumerFactory(KafkaConsumerPropertiesProvider&lt;K, V&gt; kafkaConsumerPropertiesProvider) {
<span class="nc" id="L23">        Map&lt;String, Object&gt; kafkaProperties = new HashMap&lt;&gt;(kafkaConsumerPropertiesProvider.kafkaProperties());</span>
<span class="nc" id="L24">        kafkaProperties.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);</span>
<span class="nc bnc" id="L25" title="All 2 branches missed.">        if (kafkaConsumerPropertiesProvider.isTransactional())</span>
<span class="nc" id="L26">            kafkaProperties.put(ConsumerConfig.ISOLATION_LEVEL_CONFIG, &quot;read_committed&quot;);</span>
<span class="nc" id="L27">        kafkaProperties.putIfAbsent(ConsumerConfig.MAX_POLL_RECORDS_CONFIG, 10000);</span>
<span class="nc" id="L28">        DefaultKafkaConsumerFactory&lt;K, V&gt; kafkaConsumerFactory = new DefaultKafkaConsumerFactory&lt;&gt;(kafkaProperties);</span>

<span class="nc" id="L30">        kafkaConsumerFactory.setKeyDeserializer(kafkaConsumerPropertiesProvider.keyDeserializer());</span>
<span class="nc" id="L31">        kafkaConsumerFactory.setValueDeserializer(kafkaConsumerPropertiesProvider.valueDeserializer());</span>

<span class="nc" id="L33">        return kafkaConsumerFactory;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>