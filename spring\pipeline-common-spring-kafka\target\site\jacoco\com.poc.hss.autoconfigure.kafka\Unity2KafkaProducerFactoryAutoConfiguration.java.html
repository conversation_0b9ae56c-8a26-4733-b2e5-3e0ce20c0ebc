<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaProducerFactoryAutoConfiguration.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.autoconfigure.kafka</a> &gt; <span class="el_source">Unity2KafkaProducerFactoryAutoConfiguration.java</span></div><h1>Unity2KafkaProducerFactoryAutoConfiguration.java</h1><pre class="source lang-java linenums">package com.poc.hss.autoconfigure.kafka;

import com.poc.hss.fasttrack.kafka.factory.KafkaProducerFactoryFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;

import java.time.Duration;

@Configuration
<span class="nc" id="L15">public class Unity2KafkaProducerFactoryAutoConfiguration&lt;K, V&gt; {</span>

    @Value(&quot;#{T(org.springframework.boot.convert.DurationStyle).detectAndParse('${unity2.kafka.producer.maxAge:1d}')}&quot;)
    private Duration maxAge;
    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnBean(KafkaProducerFactoryFactory.class)
    public ProducerFactory&lt;K, V&gt; kafkaProducerFactory(KafkaProducerFactoryFactory&lt;K, V&gt; factory) {
<span class="nc" id="L23">        ProducerFactory&lt;K, V&gt; kafkaProducerFactory = factory.createKafkaProducerFactory();</span>
<span class="nc" id="L24">        ((DefaultKafkaProducerFactory) kafkaProducerFactory).setMaxAge(maxAge);</span>
<span class="nc" id="L25">        return kafkaProducerFactory;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>