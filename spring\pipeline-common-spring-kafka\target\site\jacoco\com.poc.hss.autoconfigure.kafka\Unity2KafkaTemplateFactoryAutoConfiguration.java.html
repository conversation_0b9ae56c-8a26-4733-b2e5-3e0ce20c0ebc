<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaTemplateFactoryAutoConfiguration.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.autoconfigure.kafka</a> &gt; <span class="el_source">Unity2KafkaTemplateFactoryAutoConfiguration.java</span></div><h1>Unity2KafkaTemplateFactoryAutoConfiguration.java</h1><pre class="source lang-java linenums">package com.poc.hss.autoconfigure.kafka;

import com.poc.hss.fasttrack.kafka.factory.KafkaTemplateFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.ProducerFactory;

@Configuration
<span class="nc" id="L13">public class Unity2KafkaTemplateFactoryAutoConfiguration&lt;K, V&gt; {</span>

    @Bean
    @ConditionalOnBean(ProducerFactory.class)
    @ConditionalOnMissingBean
    public KafkaTemplateFactory&lt;K, V&gt; kafkaTemplateFactory(ApplicationContext context,
                                                           ProducerFactory&lt;K, V&gt; factory,
                                                           @Value(&quot;${management.tracing.enabled:false}&quot;) boolean observationEnabled) {
<span class="nc" id="L21">        return new KafkaTemplateFactory&lt;&gt;(context, factory, observationEnabled);</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>