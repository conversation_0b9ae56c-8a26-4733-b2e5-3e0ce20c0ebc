<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.autoconfigure.kafka</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <span class="el_package">com.poc.hss.autoconfigure.kafka</span></div><h1>com.poc.hss.autoconfigure.kafka</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">90 of 90</td><td class="ctr2">0%</td><td class="bar">2 of 2</td><td class="ctr2">0%</td><td class="ctr1">11</td><td class="ctr2">11</td><td class="ctr1">20</td><td class="ctr2">20</td><td class="ctr1">10</td><td class="ctr2">10</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a0"><a href="Unity2KafkaConsumerFactoryAutoConfiguration.html" class="el_class">Unity2KafkaConsumerFactoryAutoConfiguration</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="44" alt="44"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h0">10</td><td class="ctr2" id="i0">10</td><td class="ctr1" id="j0">2</td><td class="ctr2" id="k0">2</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="Unity2KafkaListenerContainerFactoryAutoConfiguration.html" class="el_class">Unity2KafkaListenerContainerFactoryAutoConfiguration</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h2">2</td><td class="ctr2" id="i2">2</td><td class="ctr1" id="j1">2</td><td class="ctr2" id="k1">2</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="Unity2KafkaProducerFactoryAutoConfiguration.html" class="el_class">Unity2KafkaProducerFactoryAutoConfiguration</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="13" alt="13"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h1">4</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k2">2</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a4"><a href="Unity2KafkaTemplateFactoryAutoConfiguration.html" class="el_class">Unity2KafkaTemplateFactoryAutoConfiguration</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="27" height="10" title="10" alt="10"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">2</td><td class="ctr2" id="k3">2</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a3"><a href="Unity2KafkaProducerFactoryFactoryAutoConfiguration.html" class="el_class">Unity2KafkaProducerFactoryFactoryAutoConfiguration</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="21" height="10" title="8" alt="8"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">2</td><td class="ctr2" id="i4">2</td><td class="ctr1" id="j4">2</td><td class="ctr2" id="k4">2</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>