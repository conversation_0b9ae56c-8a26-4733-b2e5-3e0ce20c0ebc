<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2KafkaDeadLetterException.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.exception</a> &gt; <span class="el_source">Unity2KafkaDeadLetterException.java</span></div><h1>Unity2KafkaDeadLetterException.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.exception;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.listener.BatchListenerFailedException;

public class Unity2KafkaDeadLetterException extends BatchListenerFailedException {

    public Unity2KafkaDeadLetterException(String message, ConsumerRecord&lt;?, ?&gt; record) {
<span class="nc" id="L9">        super(message + &quot;, Record: &quot;, record);</span>
<span class="nc" id="L10">    }</span>

    public Unity2KafkaDeadLetterException(String message, Throwable cause, ConsumerRecord&lt;?, ?&gt; record) {
<span class="nc" id="L13">        super(message, cause, record);</span>
<span class="nc" id="L14">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>