<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>UnityDefaultErrorHandler.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.exception</a> &gt; <span class="el_source">UnityDefaultErrorHandler.java</span></div><h1>UnityDefaultErrorHandler.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.exception;

import com.poc.hss.fasttrack.config.ShutdownManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.common.KafkaException;
import org.apache.kafka.common.config.ConfigException;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.kafka.listener.MessageListenerContainer;
import org.springframework.stereotype.Component;
import org.springframework.util.backoff.ExponentialBackOff;

import java.net.UnknownHostException;

@Component
<span class="nc" id="L18">@Slf4j</span>
public class UnityDefaultErrorHandler extends DefaultErrorHandler {

    private ShutdownManager shutdownManager;

    public UnityDefaultErrorHandler(ShutdownManager shutdownManager) {
<span class="nc" id="L24">        super(new ExponentialBackOff());</span>
<span class="nc" id="L25">        this.shutdownManager = shutdownManager;</span>
<span class="nc" id="L26">    }</span>
    /*
        Handles exception related to message processing.
        Triggered when an exception occurs during processing of batch of messages in kafka listener.
     */

    @Override
    public void handleBatch(Exception thrownException, ConsumerRecords&lt;?, ?&gt; data, Consumer&lt;?, ?&gt; consumer,
                            MessageListenerContainer container, Runnable invokeListener) {
<span class="nc" id="L35">        log.error(&quot;[UnityDefaultErrorHandler - handleBatch] Handling error message receive:{} container:{} consumer:{} invokeListener:{}&quot;, thrownException, container, consumer, invokeListener);</span>
<span class="nc bnc" id="L36" title="All 4 branches missed.">        if (thrownException instanceof KafkaException || thrownException instanceof UnknownHostException) {</span>
<span class="nc" id="L37">            log.error(&quot;[UnityDefaultErrorHandler - handleBatch] Non-retryable exception encountered :{}&quot;, ExceptionUtils.getStackTrace(thrownException));</span>
<span class="nc" id="L38">            log.error(&quot;[UnityDefaultErrorHandler - handleBatch] Initiating service shutdown as non-retryable exception encountered&quot;);</span>
<span class="nc" id="L39">            shutdownManager.initiateShutdown(1);</span>
        } else {
<span class="nc" id="L41">            super.handleBatch(thrownException, data, consumer, container, invokeListener);</span>
        }
<span class="nc" id="L43">    }</span>

     /*
        Handles exception unrelated to processing specific messages or batches.
        Triggered when an exception occurs outside the scope of batch or record handling, such as in kafka consumer poll loop
        e.g. network issue, broker unavailability or runtime failures likes ConfigException

     */

    @Override
    public void handleOtherException(Exception thrownException, Consumer&lt;?, ?&gt; consumer,
                                     MessageListenerContainer container, boolean batchListener){
<span class="nc" id="L55">        log.error(&quot;[UnityDefaultErrorHandler - handleOtherException] Handling other error message receive:{} container:{} consumer:{} batchListener:{}&quot;, thrownException, container, consumer, batchListener);</span>
<span class="nc bnc" id="L56" title="All 4 branches missed.">        if (thrownException instanceof ConfigException || thrownException instanceof UnknownHostException) {</span>
<span class="nc" id="L57">            log.error(&quot;[UnityDefaultErrorHandler - handleOtherException] Non-retryable exception encountered :{}&quot;, ExceptionUtils.getStackTrace(thrownException));</span>
<span class="nc" id="L58">            log.error(&quot;[UnityDefaultErrorHandler - handleOtherException] Initiating service shutdown as non-retryable exception encountered&quot;);</span>
<span class="nc" id="L59">            shutdownManager.initiateShutdown(1);</span>
        }else{
<span class="nc" id="L61">            super.handleOtherException(thrownException, consumer, container, batchListener);</span>
        }
<span class="nc" id="L63">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>