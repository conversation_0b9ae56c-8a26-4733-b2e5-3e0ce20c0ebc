<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaListenerContainerErrorHandlerFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.factory</a> &gt; <span class="el_source">KafkaListenerContainerErrorHandlerFactory.java</span></div><h1>KafkaListenerContainerErrorHandlerFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.factory;

import com.poc.hss.fasttrack.config.ShutdownManager;
import com.poc.hss.fasttrack.kafka.exception.UnityDefaultErrorHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.listener.DefaultErrorHandler;
import org.springframework.stereotype.Component;

@Component
<span class="nc" id="L11">@Slf4j</span>
<span class="nc" id="L12">public class KafkaListenerContainerErrorHandlerFactory {</span>

    @Autowired
    private ShutdownManager shutdownManager;

    public DefaultErrorHandler createRecordErrorHandler() {
<span class="nc" id="L18">        DefaultErrorHandler seekToCurrentErrorHandler = new UnityDefaultErrorHandler(shutdownManager);</span>
<span class="nc" id="L19">        seekToCurrentErrorHandler.setAckAfterHandle(false);</span>
<span class="nc" id="L20">        seekToCurrentErrorHandler.setCommitRecovered(true);</span>
<span class="nc" id="L21">        return seekToCurrentErrorHandler;</span>
    }

    public DefaultErrorHandler createBatchErrorHandler() {
<span class="nc" id="L25">        DefaultErrorHandler recoveringBatchErrorHandler = new UnityDefaultErrorHandler(shutdownManager);</span>
<span class="nc" id="L26">        recoveringBatchErrorHandler.setAckAfterHandle(false);</span>
<span class="nc" id="L27">        recoveringBatchErrorHandler.setCommitRecovered(true);</span>
<span class="nc" id="L28">        return recoveringBatchErrorHandler;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>