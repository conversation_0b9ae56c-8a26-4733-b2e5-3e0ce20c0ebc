<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaListenerContainerFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.factory</a> &gt; <span class="el_source">KafkaListenerContainerFactory.java</span></div><h1>KafkaListenerContainerFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.factory;

import com.poc.hss.fasttrack.config.ShutdownManager;
import com.poc.hss.fasttrack.kafka.interceptor.Unity2CommonKafkaBatchInterceptor;
import com.poc.hss.fasttrack.kafka.interceptor.Unity2CommonKafkaRecordInterceptor;
import com.poc.hss.fasttrack.kafka.listener.ErrorHandlingBatchMessageListener;
import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptorFactory;
import com.poc.hss.fasttrack.kafka.util.KafkaPropertiesUtil;
import io.micrometer.observation.ObservationRegistry;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory;
import org.springframework.kafka.core.ConsumerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.listener.BatchMessageListener;
import org.springframework.kafka.listener.ConcurrentMessageListenerContainer;
import org.springframework.kafka.listener.ContainerProperties;
import org.springframework.kafka.listener.MessageListener;
import org.springframework.kafka.transaction.KafkaTransactionManager;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.List;

public class KafkaListenerContainerFactory&lt;K, V&gt; {
    private final ApplicationContext context;
    private final ConcurrentKafkaListenerContainerFactory&lt;K, V&gt; concurrentKafkaListenerContainerFactory;
    private final ProducerFactory&lt;K, V&gt; producerFactory;
    private final KafkaListenerContainerErrorHandlerFactory kafkaListenerContainerErrorHandlerFactory;
    private final KafkaLogAdaptorFactory kafkaLogAdaptorFactory;
    private final boolean observationEnabled;
    private final ObservationRegistry observationRegistry;
    private final boolean supportsDeadLetter;
    private final ShutdownManager shutdownManager;

    public KafkaListenerContainerFactory(
            ApplicationContext context,
            ProducerFactory&lt;K, V&gt; producerFactory,
            ConsumerFactory&lt;K, V&gt; consumerFactory,
            KafkaListenerContainerErrorHandlerFactory kafkaListenerContainerErrorHandlerFactory,
            KafkaLogAdaptorFactory kafkaLogAdaptorFactory,
            boolean observationEnabled,
            ObservationRegistry observationRegistry,
            ShutdownManager shutdownManager
<span class="nc" id="L45">    ) {</span>
<span class="nc" id="L46">        this.context = context;</span>
<span class="nc" id="L47">        this.producerFactory = producerFactory;</span>
<span class="nc" id="L48">        this.kafkaListenerContainerErrorHandlerFactory = kafkaListenerContainerErrorHandlerFactory;</span>
<span class="nc" id="L49">        this.kafkaLogAdaptorFactory = kafkaLogAdaptorFactory;</span>
<span class="nc" id="L50">        this.observationEnabled = observationEnabled;</span>
<span class="nc" id="L51">        this.observationRegistry = observationRegistry;</span>
<span class="nc" id="L52">        this.shutdownManager = shutdownManager;</span>

<span class="nc" id="L54">        this.concurrentKafkaListenerContainerFactory = new ConcurrentKafkaListenerContainerFactory&lt;&gt;();</span>
<span class="nc" id="L55">        this.concurrentKafkaListenerContainerFactory.setConsumerFactory(consumerFactory);</span>

<span class="nc bnc" id="L57" title="All 2 branches missed.">        this.supportsDeadLetter = producerFactory != null;</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">        boolean isTransactional = producerFactory != null</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">                &amp;&amp; StringUtils.hasText(producerFactory.getTransactionIdPrefix())</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">                &amp;&amp; KafkaPropertiesUtil.sameBroker(consumerFactory.getConfigurationProperties(), producerFactory.getConfigurationProperties());</span>

<span class="nc bnc" id="L62" title="All 2 branches missed.">        if (isTransactional) {</span>
<span class="nc" id="L63">            this.concurrentKafkaListenerContainerFactory.getContainerProperties().setTransactionManager(new KafkaTransactionManager&lt;&gt;(producerFactory));</span>
        }
<span class="nc" id="L65">        this.concurrentKafkaListenerContainerFactory.getContainerProperties().setFixTxOffsets(true);</span>

<span class="nc bnc" id="L67" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L68">            this.concurrentKafkaListenerContainerFactory.getContainerProperties().setObservationEnabled(true);</span>
<span class="nc" id="L69">            this.concurrentKafkaListenerContainerFactory.setApplicationContext(context);</span>
        }
<span class="nc" id="L71">    }</span>

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createRecordContainer(String sourceTopic) {
<span class="nc" id="L74">        return createRecordContainer(Collections.singletonList(sourceTopic), null);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createRecordContainer(List&lt;String&gt; sourceTopics, MessageListener&lt;K, V&gt; messageListener) {
<span class="nc" id="L78">        ConcurrentMessageListenerContainer&lt;K, V&gt; container = this.concurrentKafkaListenerContainerFactory.createContainer(sourceTopics.toArray(new String[0]));</span>

<span class="nc" id="L80">        container.setCommonErrorHandler(kafkaListenerContainerErrorHandlerFactory.createRecordErrorHandler());</span>
<span class="nc" id="L81">        container.setRecordInterceptor(new Unity2CommonKafkaRecordInterceptor&lt;&gt;(kafkaLogAdaptorFactory));</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">        if (messageListener != null)</span>
<span class="nc" id="L83">            container.setupMessageListener(messageListener);</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L85">            container.getContainerProperties().setObservationEnabled(true);</span>
<span class="nc" id="L86">            container.setApplicationContext(context);</span>
        }
<span class="nc" id="L88">        return container;</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(String sourceTopic) {
<span class="nc" id="L92">        return createBatchContainer(Collections.singletonList(sourceTopic), null, null);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(List&lt;String&gt; sourceTopics) {
<span class="nc" id="L96">        return createBatchContainer(sourceTopics, null, null);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(String sourceTopic, Boolean observationEnabled) {
<span class="nc" id="L100">        return createBatchContainer(Collections.singletonList(sourceTopic), null, null, observationEnabled);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(String sourceTopic, BatchMessageListener&lt;K, V&gt; batchMessageListener) {
<span class="nc" id="L104">        return createBatchContainer(Collections.singletonList(sourceTopic), null, batchMessageListener);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(List&lt;String&gt; sourceTopics, BatchMessageListener&lt;K, V&gt; batchMessageListener) {
<span class="nc" id="L108">        return createBatchContainer(sourceTopics, null, batchMessageListener);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(String sourceTopic, String deadLetterTopic, BatchMessageListener&lt;K, V&gt; batchMessageListener) {
<span class="nc" id="L112">        return createBatchContainer(Collections.singletonList(sourceTopic), deadLetterTopic, batchMessageListener);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(List&lt;String&gt; sourceTopics, String deadLetterTopic, BatchMessageListener&lt;K, V&gt; batchMessageListener) {
<span class="nc" id="L116">        return createBatchContainer(sourceTopics, deadLetterTopic, batchMessageListener, observationEnabled);</span>
    }

    public ConcurrentMessageListenerContainer&lt;K, V&gt; createBatchContainer(List&lt;String&gt; sourceTopics, String deadLetterTopic, BatchMessageListener&lt;K, V&gt; batchMessageListener, Boolean observationEnabled) {
<span class="nc" id="L120">        ConcurrentMessageListenerContainer&lt;K, V&gt; container = this.concurrentKafkaListenerContainerFactory.createContainer(sourceTopics.toArray(new String[0]));</span>

<span class="nc" id="L122">        KafkaTemplate&lt;K, V&gt; kafkaTemplate = null;</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">        if (StringUtils.hasText(deadLetterTopic)) {</span>
<span class="nc" id="L124">            assertDeadLetterSupport();</span>
<span class="nc" id="L125">            kafkaTemplate = new KafkaTemplate&lt;&gt;(this.producerFactory);</span>
<span class="nc" id="L126">            kafkaTemplate.setDefaultTopic(deadLetterTopic);</span>
        }
<span class="nc" id="L128">        container.setCommonErrorHandler(kafkaListenerContainerErrorHandlerFactory.createBatchErrorHandler());</span>
<span class="nc" id="L129">        container.setBatchInterceptor(new Unity2CommonKafkaBatchInterceptor&lt;&gt;(kafkaLogAdaptorFactory));</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">        if (batchMessageListener != null) {</span>
<span class="nc" id="L131">            container.setupMessageListener(new ErrorHandlingBatchMessageListener&lt;&gt;(batchMessageListener, kafkaTemplate, kafkaLogAdaptorFactory));</span>
<span class="nc" id="L132">            container.getContainerProperties().setAckMode(ContainerProperties.AckMode.MANUAL);</span>
        }

<span class="nc bnc" id="L135" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L136">            container.setBatchInterceptor(new Unity2ObservedKafkaBatchInterceptor&lt;&gt;(kafkaLogAdaptorFactory, observationRegistry));</span>
<span class="nc" id="L137">            container.getContainerProperties().setObservationEnabled(true);</span>
<span class="nc" id="L138">            container.setApplicationContext(context);</span>
        }
<span class="nc" id="L140">        return container;</span>
    }

    private void assertDeadLetterSupport() {
<span class="nc bnc" id="L144" title="All 2 branches missed.">        if (!this.supportsDeadLetter)</span>
<span class="nc" id="L145">            throw new UnsupportedOperationException(&quot;No producer factory found, dead letter is not supported.&quot;);</span>
<span class="nc" id="L146">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>