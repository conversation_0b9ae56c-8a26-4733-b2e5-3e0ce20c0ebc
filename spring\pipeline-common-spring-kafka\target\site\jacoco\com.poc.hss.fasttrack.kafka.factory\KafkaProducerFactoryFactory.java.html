<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaProducerFactoryFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.factory</a> &gt; <span class="el_source">KafkaProducerFactoryFactory.java</span></div><h1>KafkaProducerFactoryFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.factory;

import com.poc.hss.fasttrack.kafka.config.KafkaProducerPropertiesProvider;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.Serializer;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class KafkaProducerFactoryFactory&lt;K, V&gt; {
    private final KafkaProducerPropertiesProvider&lt;K, V&gt; defaultKafkaProducerPropertiesProvider;

<span class="nc" id="L16">    public KafkaProducerFactoryFactory(KafkaProducerPropertiesProvider&lt;K, V&gt; defaultKafkaProducerPropertiesProvider) {</span>
<span class="nc" id="L17">        this.defaultKafkaProducerPropertiesProvider = defaultKafkaProducerPropertiesProvider;</span>
<span class="nc" id="L18">    }</span>

    public ProducerFactory&lt;K, V&gt; createKafkaProducerFactory() {
<span class="nc" id="L21">        return this.createKafkaProducerFactory(this.defaultKafkaProducerPropertiesProvider);</span>
    }

    public ProducerFactory&lt;K, V&gt; createKafkaProducerFactory(KafkaProducerPropertiesProvider&lt;K, V&gt; kafkaProducerPropertiesProvider) {
<span class="nc" id="L25">        Map&lt;String, Object&gt; kafkaProperties = new HashMap&lt;&gt;(kafkaProducerPropertiesProvider.kafkaProperties());</span>
<span class="nc" id="L26">        kafkaProperties.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);</span>
<span class="nc" id="L27">        kafkaProperties.put(ProducerConfig.ACKS_CONFIG, &quot;all&quot;);</span>
<span class="nc" id="L28">        kafkaProperties.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 1);</span>

<span class="nc" id="L30">        kafkaProperties.putIfAbsent(ProducerConfig.LINGER_MS_CONFIG, 1000);</span>
<span class="nc" id="L31">        kafkaProperties.putIfAbsent(ProducerConfig.BATCH_SIZE_CONFIG, 131072);</span>
<span class="nc" id="L32">        kafkaProperties.putIfAbsent(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 600000);</span>
<span class="nc" id="L33">        kafkaProperties.putIfAbsent(ProducerConfig.TRANSACTION_TIMEOUT_CONFIG, 600000);</span>
<span class="nc" id="L34">        kafkaProperties.putIfAbsent(ProducerConfig.MAX_BLOCK_MS_CONFIG, 600000);</span>

<span class="nc" id="L36">        Serializer&lt;K&gt; keySerializer = kafkaProducerPropertiesProvider.keySerializer();</span>
<span class="nc" id="L37">        Serializer&lt;V&gt; valueSerializer = kafkaProducerPropertiesProvider.valueSerializer();</span>
<span class="nc" id="L38">        boolean transactional = kafkaProducerPropertiesProvider.isTransactional();</span>

<span class="nc" id="L40">        DefaultKafkaProducerFactory&lt;K, V&gt; kafkaProducerFactory = new DefaultKafkaProducerFactory&lt;&gt;(kafkaProperties);</span>
<span class="nc" id="L41">        kafkaProducerFactory.setKeySerializer(keySerializer);</span>
<span class="nc" id="L42">        kafkaProducerFactory.setValueSerializer(valueSerializer);</span>
<span class="nc bnc" id="L43" title="All 2 branches missed.">        if (transactional)</span>
<span class="nc" id="L44">            kafkaProducerFactory.setTransactionIdPrefix(UUID.randomUUID().toString());</span>
<span class="nc" id="L45">        return kafkaProducerFactory;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.************</span></div></body></html>