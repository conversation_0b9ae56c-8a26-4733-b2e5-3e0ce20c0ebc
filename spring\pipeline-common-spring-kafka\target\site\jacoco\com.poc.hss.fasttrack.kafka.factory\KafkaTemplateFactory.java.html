<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaTemplateFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.factory</a> &gt; <span class="el_source">KafkaTemplateFactory.java</span></div><h1>KafkaTemplateFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.factory;

import com.poc.hss.fasttrack.kafka.observation.sender.Unity2KafkaTemplateObservationConvention;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;
import org.springframework.kafka.support.LoggingProducerListener;

<span class="nc" id="L10">@Slf4j</span>
public class KafkaTemplateFactory&lt;K, V&gt; {
    private final ApplicationContext context;
    private final ProducerFactory&lt;K, V&gt; factory;
    private final boolean observationEnabled;

<span class="nc" id="L16">    public KafkaTemplateFactory(ApplicationContext context, ProducerFactory&lt;K, V&gt; factory, boolean observationEnabled) {</span>
<span class="nc" id="L17">        this.context = context;</span>
<span class="nc" id="L18">        this.factory = factory;</span>
<span class="nc" id="L19">        this.observationEnabled = observationEnabled;</span>
<span class="nc" id="L20">    }</span>

    public KafkaTemplate&lt;K, V&gt; createKafkaTemplate() {
<span class="nc" id="L23">        final KafkaTemplate&lt;K, V&gt; kafkaTemplate = new KafkaTemplate&lt;&gt;(factory);</span>
<span class="nc bnc" id="L24" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L25">            kafkaTemplate.setObservationEnabled(true);</span>
<span class="nc" id="L26">            kafkaTemplate.setObservationConvention(new Unity2KafkaTemplateObservationConvention());</span>
<span class="nc" id="L27">            kafkaTemplate.setApplicationContext(context);</span>
<span class="nc" id="L28">            kafkaTemplate.afterSingletonsInstantiated();</span>
        }
<span class="nc" id="L30">        return kafkaTemplate;</span>
    }

    public KafkaTemplate&lt;K, V&gt; createKafkaTemplate(Boolean observationEnabled) {
<span class="nc" id="L34">        final KafkaTemplate&lt;K, V&gt; kafkaTemplate = new KafkaTemplate&lt;&gt;(factory);</span>
<span class="nc bnc" id="L35" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L36">            kafkaTemplate.setObservationEnabled(true);</span>
<span class="nc" id="L37">            kafkaTemplate.setObservationConvention(new Unity2KafkaTemplateObservationConvention());</span>
<span class="nc" id="L38">            kafkaTemplate.setApplicationContext(context);</span>
<span class="nc" id="L39">            kafkaTemplate.afterSingletonsInstantiated();</span>
        }
<span class="nc" id="L41">        return kafkaTemplate;</span>
    }

    public KafkaTemplate&lt;K, V&gt; createKafkaTemplate(String topic) {
<span class="nc" id="L45">        KafkaTemplate&lt;K, V&gt; kafkaTemplate = createKafkaTemplate();</span>
<span class="nc" id="L46">        kafkaTemplate.setDefaultTopic(topic);</span>
<span class="nc" id="L47">        kafkaTemplate.setProducerListener(new LoggingProducerListener&lt;&gt;());</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L49">            kafkaTemplate.setObservationEnabled(true);</span>
<span class="nc" id="L50">            kafkaTemplate.setApplicationContext(context);</span>
<span class="nc" id="L51">            kafkaTemplate.afterSingletonsInstantiated();</span>
        }
<span class="nc" id="L53">        return kafkaTemplate;</span>
    }

    public KafkaTemplate&lt;K, V&gt; createKafkaTemplate(String topic, Boolean observationEnabled) {
<span class="nc" id="L57">        KafkaTemplate&lt;K, V&gt; kafkaTemplate = createKafkaTemplate(observationEnabled);</span>
<span class="nc" id="L58">        kafkaTemplate.setDefaultTopic(topic);</span>
<span class="nc" id="L59">        kafkaTemplate.setProducerListener(new LoggingProducerListener&lt;&gt;());</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">        if (observationEnabled) {</span>
<span class="nc" id="L61">            kafkaTemplate.setObservationEnabled(true);</span>
<span class="nc" id="L62">            kafkaTemplate.setApplicationContext(context);</span>
<span class="nc" id="L63">            kafkaTemplate.afterSingletonsInstantiated();</span>
        }
<span class="nc" id="L65">        return kafkaTemplate;</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>