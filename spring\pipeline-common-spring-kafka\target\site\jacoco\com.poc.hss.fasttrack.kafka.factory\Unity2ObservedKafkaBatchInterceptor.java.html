<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2ObservedKafkaBatchInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.factory</a> &gt; <span class="el_source">Unity2ObservedKafkaBatchInterceptor.java</span></div><h1>Unity2ObservedKafkaBatchInterceptor.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.factory;

import com.poc.hss.fasttrack.kafka.interceptor.Unity2CommonKafkaBatchInterceptor;
import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptorFactory;
import com.poc.hss.fasttrack.kafka.observation.receiver.Unity2KafkaListenerObservationConvention;
import io.micrometer.observation.Observation;
import io.micrometer.observation.ObservationRegistry;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.kafka.support.micrometer.KafkaListenerObservation;
import org.springframework.kafka.support.micrometer.KafkaRecordReceiverContext;

public class Unity2ObservedKafkaBatchInterceptor&lt;K, V&gt; extends Unity2CommonKafkaBatchInterceptor&lt;K, V&gt; {
    private final ObservationRegistry observationRegistry;

    public Unity2ObservedKafkaBatchInterceptor(KafkaLogAdaptorFactory factory, ObservationRegistry observationRegistry) {
<span class="nc" id="L17">        super(factory);</span>
<span class="nc" id="L18">        this.observationRegistry = observationRegistry;</span>
<span class="nc" id="L19">    }</span>

    @Override
    public ConsumerRecords&lt;K, V&gt; intercept(ConsumerRecords&lt;K, V&gt; records, Consumer&lt;K, V&gt; consumer) {
<span class="nc" id="L23">        records.forEach(record -&gt; {</span>
<span class="nc" id="L24">            final Observation observation = KafkaListenerObservation.LISTENER_OBSERVATION.observation(</span>
                    new Unity2KafkaListenerObservationConvention(),
                    KafkaListenerObservation.DefaultKafkaListenerObservationConvention.INSTANCE,
<span class="nc" id="L27">                    () -&gt; new KafkaRecordReceiverContext(record, &quot;&quot;, () -&gt; &quot;&quot;),</span>
                    this.observationRegistry);
<span class="nc" id="L29">            observation.start();</span>
<span class="nc" id="L30">            observation.stop();</span>
<span class="nc" id="L31">        });</span>
<span class="nc" id="L32">        return records;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>