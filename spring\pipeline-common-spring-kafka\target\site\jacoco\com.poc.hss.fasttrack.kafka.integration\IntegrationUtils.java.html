<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>IntegrationUtils.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.integration</a> &gt; <span class="el_source">IntegrationUtils.java</span></div><h1>IntegrationUtils.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.integration;

import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.common.record.TimestampType;
import org.springframework.kafka.support.KafkaHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;

import java.util.Arrays;
import java.util.List;

<span class="nc" id="L12">public class IntegrationUtils {</span>
<span class="nc" id="L13">    public static List&lt;String&gt; internalHeaders = Arrays.asList(</span>
            KafkaHeaders.TIMESTAMP_TYPE,
            KafkaHeaders.RECEIVED_KEY,
            KafkaHeaders.BATCH_CONVERTED_HEADERS,
            KafkaHeaders.RECEIVED_TOPIC,
            KafkaHeaders.OFFSET,
            KafkaHeaders.CONSUMER,
            KafkaHeaders.RECEIVED_PARTITION,
            KafkaHeaders.RECEIVED_TIMESTAMP,
            KafkaHeaders.GROUP_ID
    );

    public static ConsumerRecord&lt;?, ?&gt; createConsumerRecord(Message&lt;?&gt; message, int index) {
<span class="nc" id="L26">        String topic = getHeader(message.getHeaders(), KafkaHeaders.RECEIVED_TOPIC, index);</span>
<span class="nc" id="L27">        Long offset = getHeader(message.getHeaders(), KafkaHeaders.OFFSET, index);</span>
<span class="nc" id="L28">        Integer partition = getHeader(message.getHeaders(), KafkaHeaders.RECEIVED_PARTITION, index);</span>
<span class="nc" id="L29">        String messageKey = getHeader(message.getHeaders(), KafkaHeaders.RECEIVED_KEY, index);</span>
<span class="nc" id="L30">        Long receivedTimestamp = getHeader(message.getHeaders(), KafkaHeaders.RECEIVED_TIMESTAMP, index);</span>

<span class="nc" id="L32">        return new ConsumerRecord&lt;&gt;(topic, partition, offset, receivedTimestamp, TimestampType.CREATE_TIME, -1, -1, -1, messageKey, null);</span>
    }

    private static &lt;T&gt; T getHeader(MessageHeaders headers, String header, int index) {
<span class="nc" id="L36">        final Object headerObj = headers.get(header);</span>
<span class="nc bnc" id="L37" title="All 2 branches missed.">        if (headerObj instanceof List) {</span>
<span class="nc" id="L38">            return ((List&lt;T&gt;) headerObj).get(index);</span>
        } else {
<span class="nc" id="L40">            return (T) headerObj;</span>
        }
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>