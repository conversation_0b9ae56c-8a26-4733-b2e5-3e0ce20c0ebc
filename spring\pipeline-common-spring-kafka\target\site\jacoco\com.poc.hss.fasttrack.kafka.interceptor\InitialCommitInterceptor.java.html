<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>InitialCommitInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.interceptor</a> &gt; <span class="el_source">InitialCommitInterceptor.java</span></div><h1>InitialCommitInterceptor.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.interceptor;

import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptor;
import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptorFactory;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;
import org.slf4j.Logger;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.Map;

public abstract class InitialCommitInterceptor {
    private final Logger logger;
    protected final KafkaLogAdaptor kafkaLogAdaptor;
<span class="nc" id="L17">    private boolean firstExecution = true;</span>

<span class="nc" id="L19">    public InitialCommitInterceptor(KafkaLogAdaptorFactory factory, Logger logger) {</span>
<span class="nc" id="L20">        this.logger = logger;</span>
<span class="nc" id="L21">        this.kafkaLogAdaptor = factory.createAdaptor(logger);</span>
<span class="nc" id="L22">    }</span>

    protected void commitInitialOffsetsIfNeeded(Consumer&lt;?, ?&gt; consumer) {
<span class="nc bnc" id="L25" title="All 2 branches missed.">        if (firstExecution) {</span>
<span class="nc" id="L26">            firstExecution = false;</span>
        } else {
<span class="nc" id="L28">            return;</span>
        }
<span class="nc" id="L30">        final Map&lt;TopicPartition, Long&gt; beginningOffsets = consumer.beginningOffsets(consumer.assignment());</span>
<span class="nc" id="L31">        final Map&lt;TopicPartition, OffsetAndMetadata&gt; committedOffsets = consumer.committed(consumer.assignment());</span>

<span class="nc" id="L33">        Map&lt;TopicPartition, OffsetAndMetadata&gt; offsets = new HashMap&lt;&gt;();</span>
<span class="nc" id="L34">        consumer.assignment().forEach(</span>
                tp -&gt; {
<span class="nc bnc" id="L36" title="All 2 branches missed.">                    if (committedOffsets.get(tp) == null) {</span>
<span class="nc" id="L37">                        offsets.put(tp, new OffsetAndMetadata(beginningOffsets.get(tp)));</span>
                    }
<span class="nc" id="L39">                }</span>
        );

<span class="nc" id="L42">        StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L43">        offsets.forEach((topicPartition, offsetAndMetadata) -&gt;</span>
<span class="nc" id="L44">                sb.append(kafkaLogAdaptor.formatRecordMetadata(</span>
<span class="nc" id="L45">                        topicPartition.topic(),</span>
<span class="nc" id="L46">                        topicPartition.partition(),</span>
<span class="nc" id="L47">                        offsetAndMetadata.offset()</span>
                ))
        );

<span class="nc bnc" id="L51" title="All 2 branches missed.">        if (!CollectionUtils.isEmpty(offsets)) {</span>
<span class="nc" id="L52">            logger.info(&quot;[Init] Commit offsets {}&quot;, sb);</span>
<span class="nc" id="L53">            consumer.commitSync(offsets);</span>
        }
<span class="nc" id="L55">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>