<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Unity2CommonKafkaBatchInterceptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.interceptor</a> &gt; <span class="el_source">Unity2CommonKafkaBatchInterceptor.java</span></div><h1>Unity2CommonKafkaBatchInterceptor.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.interceptor;

import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptor;
import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.springframework.kafka.listener.BatchInterceptor;

<span class="nc" id="L10">@Slf4j</span>
public class Unity2CommonKafkaBatchInterceptor&lt;K, V&gt; extends InitialCommitInterceptor implements BatchInterceptor&lt;K, V&gt; {

    private final KafkaLogAdaptor kafkaLogAdaptor;

    public Unity2CommonKafkaBatchInterceptor(KafkaLogAdaptorFactory factory) {
<span class="nc" id="L16">        super(factory, log);</span>
<span class="nc" id="L17">        this.kafkaLogAdaptor = factory.createAdaptor(log);</span>
<span class="nc" id="L18">    }</span>

    @Override
    public ConsumerRecords&lt;K, V&gt; intercept(ConsumerRecords&lt;K, V&gt; records, Consumer&lt;K, V&gt; consumer) {
<span class="nc" id="L22">        commitInitialOffsetsIfNeeded(consumer);</span>
<span class="nc" id="L23">        return records;</span>
    }

    @Override
    public void failure(ConsumerRecords&lt;K, V&gt; records, Exception exception, Consumer&lt;K, V&gt; consumer) {
<span class="nc" id="L28">        kafkaLogAdaptor.errorLogging(records, exception);</span>
<span class="nc" id="L29">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>