<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ErrorHandlingBatchMessageListener.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.listener</a> &gt; <span class="el_source">ErrorHandlingBatchMessageListener.java</span></div><h1>ErrorHandlingBatchMessageListener.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.listener;

import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaDeadLetterException;
import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaRetryException;
import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptor;
import com.poc.hss.fasttrack.kafka.log.KafkaLogAdaptorFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.KafkaException;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.listener.BatchAcknowledgingMessageListener;
import org.springframework.kafka.listener.BatchListenerFailedException;
import org.springframework.kafka.listener.BatchMessageListener;
import org.springframework.kafka.support.Acknowledgment;

import java.time.Duration;
import java.util.List;

<span class="nc" id="L20">@Slf4j</span>
public class ErrorHandlingBatchMessageListener&lt;K, V&gt; implements BatchAcknowledgingMessageListener&lt;K, V&gt; {
    private static final int SLEEP_INTERVAL_MS = 100;

    private final BatchMessageListener&lt;K, V&gt; delegate;
    private final KafkaTemplate&lt;K, V&gt; deadLetterKafkaTemplate;
    private final KafkaLogAdaptor kafkaLogAdaptor;


<span class="nc" id="L29">    public ErrorHandlingBatchMessageListener(BatchMessageListener&lt;K, V&gt; batchMessageListener, KafkaTemplate&lt;K, V&gt; deadLetterKafkaTemplate, KafkaLogAdaptorFactory kafkaLogAdaptorFactory) {</span>
<span class="nc" id="L30">        this.delegate = batchMessageListener;</span>
<span class="nc" id="L31">        this.deadLetterKafkaTemplate = deadLetterKafkaTemplate;</span>
<span class="nc" id="L32">        this.kafkaLogAdaptor = kafkaLogAdaptorFactory.createAdaptor(log);</span>
<span class="nc" id="L33">    }</span>

    @Override
    public void onMessage(List&lt;ConsumerRecord&lt;K, V&gt;&gt; consumerRecords, Acknowledgment acknowledgment) {
        try {
<span class="nc" id="L38">            this.delegate.onMessage(consumerRecords);</span>
<span class="nc" id="L39">            acknowledgment.acknowledge();</span>
<span class="nc" id="L40">        } catch (Unity2KafkaDeadLetterException e) {</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">            retryOrDeadLetter(consumerRecords, acknowledgment, e, this.deadLetterKafkaTemplate != null);</span>
<span class="nc" id="L42">        } catch (Unity2KafkaRetryException e) {</span>
<span class="nc" id="L43">            retryOrDeadLetter(consumerRecords, acknowledgment, e, false);</span>
<span class="nc" id="L44">        }</span>
<span class="nc" id="L45">    }</span>

    private void retryOrDeadLetter(List&lt;ConsumerRecord&lt;K, V&gt;&gt; consumerRecords, Acknowledgment acknowledgment, BatchListenerFailedException e, boolean isDeadLetter) {
<span class="nc" id="L48">        kafkaLogAdaptor.errorLogging(e);</span>
<span class="nc" id="L49">        ConsumerRecord&lt;K, V&gt; record = (ConsumerRecord&lt;K, V&gt;) e.getRecord();</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">        if (record == null)</span>
<span class="nc" id="L51">            throw new KafkaException(&quot;Cannot determine error position&quot;);</span>
<span class="nc bnc" id="L52" title="All 4 branches missed.">        if (isDeadLetter &amp;&amp; this.deadLetterKafkaTemplate != null) {</span>
<span class="nc" id="L53">            log.info(&quot;Sending record to dead letter topic: {}&quot;, kafkaLogAdaptor.formatRecordMetadata(record));</span>
<span class="nc" id="L54">            this.deadLetterKafkaTemplate.sendDefault(record.partition(), record.key(), record.value());</span>
        }
<span class="nc" id="L56">        acknowledgeNack(consumerRecords, record, acknowledgment, isDeadLetter);</span>
<span class="nc" id="L57">    }</span>

    private void acknowledgeNack(List&lt;ConsumerRecord&lt;K, V&gt;&gt; consumerRecords, ConsumerRecord&lt;?, ?&gt; target, Acknowledgment acknowledgment, boolean isDeadLetter) {
<span class="nc" id="L60">        int index = findIndex(consumerRecords, target);</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">        if (isDeadLetter) {</span>
<span class="nc" id="L62">            int deadLetterIndex = index + 1;</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">            if (deadLetterIndex == consumerRecords.size())</span>
<span class="nc" id="L64">                acknowledgment.acknowledge();</span>
            else
<span class="nc" id="L66">                acknowledgment.nack(deadLetterIndex, Duration.ofMillis(SLEEP_INTERVAL_MS));</span>
<span class="nc" id="L67">        } else {</span>
<span class="nc" id="L68">            acknowledgment.nack(index, Duration.ofMillis(SLEEP_INTERVAL_MS));</span>
        }
<span class="nc" id="L70">    }</span>

    private int findIndex(List&lt;ConsumerRecord&lt;K, V&gt;&gt; consumerRecords, ConsumerRecord&lt;?, ?&gt; target) {
<span class="nc bnc" id="L73" title="All 2 branches missed.">        for (int i = 0; i &lt; consumerRecords.size(); i++) {</span>
<span class="nc" id="L74">            ConsumerRecord&lt;K, V&gt; record = consumerRecords.get(i);</span>
<span class="nc" id="L75">            if (</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">                    record.offset() == target.offset()</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">                            &amp;&amp; record.partition() == target.partition()</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">                            &amp;&amp; StringUtils.equals(record.topic(), target.topic())</span>
            )
<span class="nc" id="L80">                return i;</span>
        }

<span class="nc" id="L83">        return -1;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>