<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaLogAdaptor</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.kafka.log</a> &gt; <span class="el_class">KafkaLogAdaptor</span></div><h1>KafkaLogAdaptor</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">255 of 303</td><td class="ctr2">15%</td><td class="bar">17 of 24</td><td class="ctr2">29%</td><td class="ctr1">17</td><td class="ctr2">23</td><td class="ctr1">39</td><td class="ctr2">51</td><td class="ctr1">8</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a1"><a href="KafkaLogAdaptor.java.html#L51" class="el_method">errorLogging(ConsumerRecord, Exception)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="126" alt="126"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h0">14</td><td class="ctr2" id="i0">14</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a2"><a href="KafkaLogAdaptor.java.html#L33" class="el_method">errorLogging(Iterable, Exception)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="51" alt="51"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i1">8</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="KafkaLogAdaptor.java.html#L113" class="el_method">isWithinRetryableDuration(ConsumerRecord)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="20" alt="20"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h2">5</td><td class="ctr2" id="i3">5</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="KafkaLogAdaptor.java.html#L109" class="el_method">formatRecordMetadata(String, int, long)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="18" height="10" title="19" alt="19"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="KafkaLogAdaptor.java.html#L121" class="el_method">findException(Exception, Class)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="16" alt="16"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h3">5</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="KafkaLogAdaptor.java.html#L103" class="el_method">formatRecordMetadata(ConsumerRecord)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="12" height="10" title="13" alt="13"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="KafkaLogAdaptor.java.html#L46" class="el_method">errorLogging(BatchListenerFailedException)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a10"><a href="KafkaLogAdaptor.java.html#L123" class="el_method">lambda$findException$0(Throwable)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="KafkaLogAdaptor.java.html#L94" class="el_method">exceptionInstanceOf(Throwable, Class, int)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="25" height="10" title="27" alt="27"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">87%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i5">5</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a9"><a href="KafkaLogAdaptor.java.html#L24" class="el_method">KafkaLogAdaptor(Logger, int, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="KafkaLogAdaptor.java.html#L90" class="el_method">exceptionInstanceOf(Throwable, Class)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="6" alt="6"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>