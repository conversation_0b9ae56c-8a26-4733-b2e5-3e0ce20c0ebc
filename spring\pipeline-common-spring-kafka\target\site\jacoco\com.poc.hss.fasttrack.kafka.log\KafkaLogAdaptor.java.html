<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaLogAdaptor.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.log</a> &gt; <span class="el_source">KafkaLogAdaptor.java</span></div><h1>KafkaLogAdaptor.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.log;

import com.poc.hss.fasttrack.constant.ErrorCodeConstant;
import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaDeadLetterException;
import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaRetryException;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.slf4j.Logger;
import org.springframework.kafka.listener.BatchListenerFailedException;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.TimeZone;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

public class KafkaLogAdaptor {

    private final Logger logger;
    private final int timeElapsedForRetryableErrorAlert;
    private final String consumerGroup;
<span class="fc" id="L24">    private final int MAX_DEPTH_EXCEPTION = 10;</span>

<span class="fc" id="L26">    public KafkaLogAdaptor(Logger logger, int timeElapsedForRetryableErrorAlert, String consumerGroup) {</span>
<span class="fc" id="L27">        this.logger = logger;</span>
<span class="fc" id="L28">        this.timeElapsedForRetryableErrorAlert = timeElapsedForRetryableErrorAlert;</span>
<span class="fc" id="L29">        this.consumerGroup = consumerGroup;</span>
<span class="fc" id="L30">    }</span>

    public void errorLogging(Iterable&lt;? extends ConsumerRecord&lt;?, ?&gt;&gt; records, Exception thrownException) {
<span class="nc" id="L33">        BatchListenerFailedException batchListenerFailedException = findException(thrownException, BatchListenerFailedException.class);</span>
<span class="nc bnc" id="L34" title="All 4 branches missed.">        if (batchListenerFailedException != null &amp;&amp; batchListenerFailedException.getRecord() != null) {</span>
<span class="nc" id="L35">            errorLogging(batchListenerFailedException);</span>
        } else {
<span class="nc" id="L37">            String metadata = StreamSupport.stream(records.spliterator(), false)</span>
<span class="nc" id="L38">                    .map(this::formatRecordMetadata)</span>
<span class="nc" id="L39">                    .collect(Collectors.joining(&quot;, &quot;));</span>

<span class="nc" id="L41">            logger.error(&quot;{} - Encounter unexpected exception, consumer group = {}, records metadata = {}&quot;, ErrorCodeConstant.PIPELINE_FATAL_ERROR, consumerGroup, metadata, thrownException);</span>
        }
<span class="nc" id="L43">    }</span>

    public void errorLogging(BatchListenerFailedException thrownException) {
<span class="nc" id="L46">        ConsumerRecord&lt;?, ?&gt; record = thrownException.getRecord();</span>
<span class="nc" id="L47">        errorLogging(record, thrownException);</span>
<span class="nc" id="L48">    }</span>

    public void errorLogging(ConsumerRecord&lt;?, ?&gt; record, Exception thrownException) {
<span class="nc bnc" id="L51" title="All 2 branches missed.">        if (exceptionInstanceOf(thrownException, Unity2KafkaRetryException.class)) {</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">            if (isWithinRetryableDuration(record)) {</span>
<span class="nc" id="L53">                logger.warn(</span>
                        &quot;Encounter retryable exception (retrying within {} minutes), consumer group = {}, record metadata = {}&quot;,
<span class="nc" id="L55">                        timeElapsedForRetryableErrorAlert,</span>
                        consumerGroup,
<span class="nc" id="L57">                        formatRecordMetadata(record),</span>
                        thrownException
                );
            } else {
<span class="nc" id="L61">                logger.error(</span>
                        &quot;{} - Encounter retryable exception (message keeps failing after {} minutes), consumer group = {}, record metadata = {}&quot;,
                        ErrorCodeConstant.PIPELINE_FATAL_ERROR,
<span class="nc" id="L64">                        timeElapsedForRetryableErrorAlert,</span>
                        consumerGroup,
<span class="nc" id="L66">                        formatRecordMetadata(record),</span>
                        thrownException
                );
            }
<span class="nc bnc" id="L70" title="All 2 branches missed.">        } else if (exceptionInstanceOf(thrownException, Unity2KafkaDeadLetterException.class)) {</span>
<span class="nc" id="L71">            logger.error(</span>
                    &quot;{} - Encounter dead letter exception, consumer group = {}, record metadata = {}&quot;,
                    ErrorCodeConstant.PIPELINE_FATAL_ERROR,
                    consumerGroup,
<span class="nc" id="L75">                    formatRecordMetadata(record),</span>
                    thrownException
            );
        } else {
<span class="nc" id="L79">            logger.error(</span>
                    &quot;{} - Encounter unexpected exception, consumer group = {}, record metadata = {}&quot;,
                    ErrorCodeConstant.PIPELINE_FATAL_ERROR,
                    consumerGroup,
<span class="nc" id="L83">                    formatRecordMetadata(record),</span>
                    thrownException
            );
        }
<span class="nc" id="L87">    }</span>

    public boolean exceptionInstanceOf(Throwable exceptionObj, Class&lt;? extends Exception&gt; exceptionClass) {
<span class="fc" id="L90">        return exceptionInstanceOf(exceptionObj, exceptionClass, 0);</span>
    }

    private boolean exceptionInstanceOf(Throwable exceptionObj, Class&lt;? extends Exception&gt; exceptionClass, int depth) {
<span class="fc bfc" id="L94" title="All 2 branches covered.">        if (exceptionClass.isInstance(exceptionObj))</span>
<span class="fc" id="L95">            return true;</span>
<span class="fc bfc" id="L96" title="All 2 branches covered.">        else if (exceptionObj.getCause() != null)</span>
<span class="pc bpc" id="L97" title="1 of 4 branches missed.">            return depth &lt;= MAX_DEPTH_EXCEPTION &amp;&amp; exceptionInstanceOf(exceptionObj.getCause(), exceptionClass, depth + 1);</span>
        else
<span class="fc" id="L99">            return false;</span>
    }

    public String formatRecordMetadata(ConsumerRecord&lt;?, ?&gt; record) {
<span class="nc bnc" id="L103" title="All 2 branches missed.">        if (record == null)</span>
<span class="nc" id="L104">            return &quot;[Topic: Unknown, Partition: Unknown, Offset: Unknown]&quot;;</span>
<span class="nc" id="L105">        return formatRecordMetadata(record.topic(), record.partition(), record.offset());</span>
    }

    public String formatRecordMetadata(String topic, int partition, long offset) {
<span class="nc" id="L109">        return String.format(&quot;[Topic: %s, Partition: %s, Offset: %s]&quot;, topic, partition, offset);</span>
    }

    private boolean isWithinRetryableDuration(ConsumerRecord&lt;?, ?&gt; record) {
<span class="nc bnc" id="L113" title="All 2 branches missed.">        return record != null &amp;&amp;</span>
<span class="nc" id="L114">                Duration.between(</span>
<span class="nc" id="L115">                        LocalDateTime.ofInstant(Instant.ofEpochMilli(record.timestamp()),</span>
<span class="nc" id="L116">                                TimeZone.getDefault().toZoneId()), LocalDateTime.now()</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">                ).toMinutes() &lt; timeElapsedForRetryableErrorAlert;</span>
    }

    private &lt;T extends Throwable&gt; T findException(Exception thrownException, Class&lt;T&gt; clazz) {
<span class="nc" id="L121">        return Arrays.stream(ExceptionUtils.getThrowables(thrownException))</span>
<span class="nc" id="L122">                .filter(clazz::isInstance)</span>
<span class="nc" id="L123">                .map(e -&gt; (T) e)</span>
<span class="nc" id="L124">                .findFirst()</span>
<span class="nc" id="L125">                .orElse(null);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>