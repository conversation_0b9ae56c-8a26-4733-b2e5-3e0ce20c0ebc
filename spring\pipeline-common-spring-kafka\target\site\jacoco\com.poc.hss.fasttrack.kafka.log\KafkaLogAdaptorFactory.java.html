<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaLogAdaptorFactory.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.kafka.log</a> &gt; <span class="el_source">KafkaLogAdaptorFactory.java</span></div><h1>KafkaLogAdaptorFactory.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.kafka.log;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
<span class="nc" id="L8">public class KafkaLogAdaptorFactory {</span>

    @Value(&quot;${errorLogging.timeElapsedForRetryableErrorAlert:5}&quot;)
    private int timeElapsedForRetryableErrorAlert;

    @Value(&quot;${KAFKA_CONSUMER_GROUP:unknown}&quot;)
    private String kafkaConsumerGroup;

    public KafkaLogAdaptor createAdaptor(Logger logger) {
<span class="nc" id="L17">        return new KafkaLogAdaptor(logger, timeElapsedForRetryableErrorAlert, kafkaConsumerGroup);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>