<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.kafka.log</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-spring-kafka</a> &gt; <span class="el_package">com.poc.hss.fasttrack.kafka.log</span></div><h1>com.poc.hss.fasttrack.kafka.log</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">267 of 315</td><td class="ctr2">15%</td><td class="bar">17 of 24</td><td class="ctr2">29%</td><td class="ctr1">19</td><td class="ctr2">25</td><td class="ctr1">41</td><td class="ctr2">53</td><td class="ctr1">10</td><td class="ctr2">13</td><td class="ctr1">1</td><td class="ctr2">2</td></tr></tfoot><tbody><tr><td id="a0"><a href="KafkaLogAdaptor.java.html" class="el_source">KafkaLogAdaptor.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="255" alt="255"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="48" alt="48"/></td><td class="ctr2" id="c0">15%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="85" height="10" title="17" alt="17"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">29%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">23</td><td class="ctr1" id="h0">39</td><td class="ctr2" id="i0">51</td><td class="ctr1" id="j0">8</td><td class="ctr2" id="k0">11</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="KafkaLogAdaptorFactory.java.html" class="el_source">KafkaLogAdaptorFactory.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="12" alt="12"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"/><td class="ctr2" id="e1">n/a</td><td class="ctr1" id="f1">2</td><td class="ctr2" id="g1">2</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i1">2</td><td class="ctr1" id="j1">2</td><td class="ctr2" id="k1">2</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>