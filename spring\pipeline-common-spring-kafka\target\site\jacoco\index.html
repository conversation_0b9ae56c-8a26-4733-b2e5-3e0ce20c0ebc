<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="jacoco-resources/report.gif" type="image/gif"/><title>pipeline-common-spring-kafka</title><script type="text/javascript" src="jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="jacoco-sessions.html" class="el_session">Sessions</a></span><span class="el_report">pipeline-common-spring-kafka</span></div><h1>pipeline-common-spring-kafka</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,432 of 1,486</td><td class="ctr2">3%</td><td class="bar">89 of 96</td><td class="ctr2">7%</td><td class="ctr1">124</td><td class="ctr2">131</td><td class="ctr1">312</td><td class="ctr2">326</td><td class="ctr1">79</td><td class="ctr2">83</td><td class="ctr1">18</td><td class="ctr2">20</td></tr></tfoot><tbody><tr><td id="a2"><a href="com.poc.hss.fasttrack.kafka.factory/index.html" class="el_package">com.poc.hss.fasttrack.kafka.factory</a></td><td class="bar" id="b0"><img src="jacoco-resources/redbar.gif" width="120" height="10" title="545" alt="545"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="jacoco-resources/redbar.gif" width="120" height="10" title="34" alt="34"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">47</td><td class="ctr2" id="g0">47</td><td class="ctr1" id="h0">135</td><td class="ctr2" id="i0">135</td><td class="ctr1" id="j0">30</td><td class="ctr2" id="k0">30</td><td class="ctr1" id="l0">5</td><td class="ctr2" id="m0">5</td></tr><tr><td id="a6"><a href="com.poc.hss.fasttrack.kafka.log/index.html" class="el_package">com.poc.hss.fasttrack.kafka.log</a></td><td class="bar" id="b1"><img src="jacoco-resources/redbar.gif" width="58" height="10" title="267" alt="267"/><img src="jacoco-resources/greenbar.gif" width="10" height="10" title="48" alt="48"/></td><td class="ctr2" id="c0">15%</td><td class="bar" id="d2"><img src="jacoco-resources/redbar.gif" width="60" height="10" title="17" alt="17"/><img src="jacoco-resources/greenbar.gif" width="24" height="10" title="7" alt="7"/></td><td class="ctr2" id="e0">29%</td><td class="ctr1" id="f1">19</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h1">41</td><td class="ctr2" id="i1">53</td><td class="ctr1" id="j2">10</td><td class="ctr2" id="k1">13</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">2</td></tr><tr><td id="a5"><a href="com.poc.hss.fasttrack.kafka.listener/index.html" class="el_package">com.poc.hss.fasttrack.kafka.listener</a></td><td class="bar" id="b2"><img src="jacoco-resources/redbar.gif" width="33" height="10" title="154" alt="154"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="jacoco-resources/redbar.gif" width="70" height="10" title="20" alt="20"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">16</td><td class="ctr2" id="g2">16</td><td class="ctr1" id="h2">40</td><td class="ctr2" id="i2">40</td><td class="ctr1" id="j5">6</td><td class="ctr2" id="k5">6</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a4"><a href="com.poc.hss.fasttrack.kafka.interceptor/index.html" class="el_package">com.poc.hss.fasttrack.kafka.interceptor</a></td><td class="bar" id="b3"><img src="jacoco-resources/redbar.gif" width="30" height="10" title="139" alt="139"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="jacoco-resources/redbar.gif" width="21" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">15</td><td class="ctr2" id="g3">15</td><td class="ctr1" id="h3">40</td><td class="ctr2" id="i3">40</td><td class="ctr1" id="j1">12</td><td class="ctr2" id="k2">12</td><td class="ctr1" id="l2">3</td><td class="ctr2" id="m2">3</td></tr><tr><td id="a1"><a href="com.poc.hss.fasttrack.kafka.exception/index.html" class="el_package">com.poc.hss.fasttrack.kafka.exception</a></td><td class="bar" id="b4"><img src="jacoco-resources/redbar.gif" width="27" height="10" title="127" alt="127"/><img src="jacoco-resources/greenbar.gif" width="1" height="10" title="6" alt="6"/></td><td class="ctr2" id="c1">4%</td><td class="bar" id="d3"><img src="jacoco-resources/redbar.gif" width="28" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">11</td><td class="ctr2" id="g4">12</td><td class="ctr1" id="h4">24</td><td class="ctr2" id="i4">26</td><td class="ctr1" id="j4">7</td><td class="ctr2" id="k4">8</td><td class="ctr1" id="l3">2</td><td class="ctr2" id="m3">3</td></tr><tr><td id="a3"><a href="com.poc.hss.fasttrack.kafka.integration/index.html" class="el_package">com.poc.hss.fasttrack.kafka.integration</a></td><td class="bar" id="b5"><img src="jacoco-resources/redbar.gif" width="24" height="10" title="110" alt="110"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j6">4</td><td class="ctr2" id="k6">4</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a0"><a href="com.poc.hss.autoconfigure.kafka/index.html" class="el_package">com.poc.hss.autoconfigure.kafka</a></td><td class="bar" id="b6"><img src="jacoco-resources/redbar.gif" width="19" height="10" title="90" alt="90"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="jacoco-resources/redbar.gif" width="7" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">11</td><td class="ctr2" id="g5">11</td><td class="ctr1" id="h5">20</td><td class="ctr2" id="i5">20</td><td class="ctr1" id="j3">10</td><td class="ctr2" id="k3">10</td><td class="ctr1" id="l1">5</td><td class="ctr2" id="m1">5</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>