GROUP,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON>CH_MISSED,<PERSON><PERSON>CH_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,METHOD_MISSED,METHOD_COVERED
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.factory,KafkaListenerContainerErrorHandlerFactory,35,0,0,0,10,0,4,0,4,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.factory,KafkaListenerContainerFactory,262,0,24,0,57,0,24,0,12,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.factory,Unity2ObservedKafkaBatchInterceptor,39,0,0,0,10,0,5,0,5,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.factory,KafkaProducerFactoryFactory,92,0,2,0,22,0,4,0,3,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.factory,KafkaTemplateFactory,117,0,8,0,36,0,10,0,6,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.interceptor,Unity2CommonKafkaBatchInterceptor,25,0,0,0,8,0,4,0,4,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.interceptor,InitialCommitInterceptor,94,0,6,0,25,0,7,0,4,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.interceptor,Unity2CommonKafkaRecordInterceptor,20,0,0,0,7,0,4,0,4,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.log,KafkaLogAdaptor,255,48,17,7,39,12,17,6,8,3
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.log,KafkaLogAdaptorFactory,12,0,0,0,2,0,2,0,2,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.exception,UnityDefaultErrorHandler,109,0,8,0,18,0,8,0,4,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.exception,Unity2KafkaRetryException,6,6,0,0,2,2,1,1,1,1
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.exception,Unity2KafkaDeadLetterException,12,0,0,0,4,0,2,0,2,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.integration,IntegrationUtils,110,0,2,0,12,0,5,0,4,0
pipeline-common-spring-kafka,com.poc.hss.autoconfigure.kafka,Unity2KafkaProducerFactoryAutoConfiguration,13,0,0,0,4,0,2,0,2,0
pipeline-common-spring-kafka,com.poc.hss.autoconfigure.kafka,Unity2KafkaProducerFactoryFactoryAutoConfiguration,8,0,0,0,2,0,2,0,2,0
pipeline-common-spring-kafka,com.poc.hss.autoconfigure.kafka,Unity2KafkaTemplateFactoryAutoConfiguration,10,0,0,0,2,0,2,0,2,0
pipeline-common-spring-kafka,com.poc.hss.autoconfigure.kafka,Unity2KafkaListenerContainerFactoryAutoConfiguration,15,0,0,0,2,0,2,0,2,0
pipeline-common-spring-kafka,com.poc.hss.autoconfigure.kafka,Unity2KafkaConsumerFactoryAutoConfiguration,44,0,2,0,10,0,3,0,2,0
pipeline-common-spring-kafka,com.poc.hss.fasttrack.kafka.listener,ErrorHandlingBatchMessageListener,154,0,20,0,40,0,16,0,6,0
