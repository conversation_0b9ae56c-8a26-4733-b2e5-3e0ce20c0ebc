<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="pipeline-common-spring-kafka"><sessioninfo id="H344L0L63UFKL6Z-67af411a" start="1752767423962" dump="1752767424893"/><package name="com/poc/hss/fasttrack/kafka/factory"><class name="com/poc/hss/fasttrack/kafka/factory/KafkaListenerContainerErrorHandlerFactory" sourcefilename="KafkaListenerContainerErrorHandlerFactory.java"><method name="&lt;init&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createRecordErrorHandler" desc="()Lorg/springframework/kafka/listener/DefaultErrorHandler;" line="18"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchErrorHandler" desc="()Lorg/springframework/kafka/listener/DefaultErrorHandler;" line="25"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="35" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/factory/KafkaListenerContainerFactory" sourcefilename="KafkaListenerContainerFactory.java"><method name="&lt;init&gt;" desc="(Lorg/springframework/context/ApplicationContext;Lorg/springframework/kafka/core/ProducerFactory;Lorg/springframework/kafka/core/ConsumerFactory;Lcom/poc/hss/fasttrack/kafka/factory/KafkaListenerContainerErrorHandlerFactory;Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;ZLio/micrometer/observation/ObservationRegistry;Lcom/poc/hss/fasttrack/config/ShutdownManager;)V" line="45"><counter type="INSTRUCTION" missed="82" covered="0"/><counter type="BRANCH" missed="12" covered="0"/><counter type="LINE" missed="21" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createRecordContainer" desc="(Ljava/lang/String;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="74"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createRecordContainer" desc="(Ljava/util/List;Lorg/springframework/kafka/listener/MessageListener;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="78"><counter type="INSTRUCTION" missed="40" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/lang/String;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="92"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/util/List;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="96"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/lang/String;Ljava/lang/Boolean;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="100"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/lang/String;Lorg/springframework/kafka/listener/BatchMessageListener;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="104"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/util/List;Lorg/springframework/kafka/listener/BatchMessageListener;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="108"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/lang/String;Ljava/lang/String;Lorg/springframework/kafka/listener/BatchMessageListener;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="112"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/util/List;Ljava/lang/String;Lorg/springframework/kafka/listener/BatchMessageListener;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="116"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createBatchContainer" desc="(Ljava/util/List;Ljava/lang/String;Lorg/springframework/kafka/listener/BatchMessageListener;Ljava/lang/Boolean;)Lorg/springframework/kafka/listener/ConcurrentMessageListenerContainer;" line="120"><counter type="INSTRUCTION" missed="75" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="16" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="assertDeadLetterSupport" desc="()V" line="144"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="262" covered="0"/><counter type="BRANCH" missed="24" covered="0"/><counter type="LINE" missed="57" covered="0"/><counter type="COMPLEXITY" missed="24" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/factory/Unity2ObservedKafkaBatchInterceptor" sourcefilename="Unity2ObservedKafkaBatchInterceptor.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;Lio/micrometer/observation/ObservationRegistry;)V" line="17"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="intercept" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecords;Lorg/apache/kafka/clients/consumer/Consumer;)Lorg/apache/kafka/clients/consumer/ConsumerRecords;" line="23"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$intercept$2" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="24"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$intercept$1" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Lorg/springframework/kafka/support/micrometer/KafkaRecordReceiverContext;" line="27"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$intercept$0" desc="()Ljava/lang/String;" line="27"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="39" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="5" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/factory/KafkaProducerFactoryFactory" sourcefilename="KafkaProducerFactoryFactory.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/config/KafkaProducerPropertiesProvider;)V" line="16"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createKafkaProducerFactory" desc="()Lorg/springframework/kafka/core/ProducerFactory;" line="21"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createKafkaProducerFactory" desc="(Lcom/poc/hss/fasttrack/kafka/config/KafkaProducerPropertiesProvider;)Lorg/springframework/kafka/core/ProducerFactory;" line="25"><counter type="INSTRUCTION" missed="81" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="92" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="22" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/factory/KafkaTemplateFactory" sourcefilename="KafkaTemplateFactory.java"><method name="&lt;init&gt;" desc="(Lorg/springframework/context/ApplicationContext;Lorg/springframework/kafka/core/ProducerFactory;Z)V" line="16"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createKafkaTemplate" desc="()Lorg/springframework/kafka/core/KafkaTemplate;" line="23"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createKafkaTemplate" desc="(Ljava/lang/Boolean;)Lorg/springframework/kafka/core/KafkaTemplate;" line="34"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createKafkaTemplate" desc="(Ljava/lang/String;)Lorg/springframework/kafka/core/KafkaTemplate;" line="45"><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createKafkaTemplate" desc="(Ljava/lang/String;Ljava/lang/Boolean;)Lorg/springframework/kafka/core/KafkaTemplate;" line="57"><counter type="INSTRUCTION" missed="26" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="117" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="36" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="KafkaListenerContainerErrorHandlerFactory.java"><line nr="11" mi="4" ci="0" mb="0" cb="0"/><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="6" ci="0" mb="0" cb="0"/><line nr="19" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="21" mi="2" ci="0" mb="0" cb="0"/><line nr="25" mi="6" ci="0" mb="0" cb="0"/><line nr="26" mi="3" ci="0" mb="0" cb="0"/><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="28" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="35" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaTemplateFactory.java"><line nr="10" mi="4" ci="0" mb="0" cb="0"/><line nr="16" mi="2" ci="0" mb="0" cb="0"/><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="19" mi="3" ci="0" mb="0" cb="0"/><line nr="20" mi="1" ci="0" mb="0" cb="0"/><line nr="23" mi="6" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="2" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="5" ci="0" mb="0" cb="0"/><line nr="27" mi="4" ci="0" mb="0" cb="0"/><line nr="28" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="2" ci="0" mb="0" cb="0"/><line nr="34" mi="6" ci="0" mb="0" cb="0"/><line nr="35" mi="3" ci="0" mb="2" cb="0"/><line nr="36" mi="3" ci="0" mb="0" cb="0"/><line nr="37" mi="5" ci="0" mb="0" cb="0"/><line nr="38" mi="4" ci="0" mb="0" cb="0"/><line nr="39" mi="2" ci="0" mb="0" cb="0"/><line nr="41" mi="2" ci="0" mb="0" cb="0"/><line nr="45" mi="3" ci="0" mb="0" cb="0"/><line nr="46" mi="3" ci="0" mb="0" cb="0"/><line nr="47" mi="5" ci="0" mb="0" cb="0"/><line nr="48" mi="3" ci="0" mb="2" cb="0"/><line nr="49" mi="3" ci="0" mb="0" cb="0"/><line nr="50" mi="4" ci="0" mb="0" cb="0"/><line nr="51" mi="2" ci="0" mb="0" cb="0"/><line nr="53" mi="2" ci="0" mb="0" cb="0"/><line nr="57" mi="4" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="0" cb="0"/><line nr="59" mi="5" ci="0" mb="0" cb="0"/><line nr="60" mi="3" ci="0" mb="2" cb="0"/><line nr="61" mi="3" ci="0" mb="0" cb="0"/><line nr="62" mi="4" ci="0" mb="0" cb="0"/><line nr="63" mi="2" ci="0" mb="0" cb="0"/><line nr="65" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="117" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="36" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2ObservedKafkaBatchInterceptor.java"><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="19" mi="1" ci="0" mb="0" cb="0"/><line nr="23" mi="4" ci="0" mb="0" cb="0"/><line nr="24" mi="11" ci="0" mb="0" cb="0"/><line nr="27" mi="9" ci="0" mb="0" cb="0"/><line nr="29" mi="3" ci="0" mb="0" cb="0"/><line nr="30" mi="2" ci="0" mb="0" cb="0"/><line nr="31" mi="1" ci="0" mb="0" cb="0"/><line nr="32" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="39" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="5" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaListenerContainerFactory.java"><line nr="45" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="3" ci="0" mb="0" cb="0"/><line nr="47" mi="3" ci="0" mb="0" cb="0"/><line nr="48" mi="3" ci="0" mb="0" cb="0"/><line nr="49" mi="3" ci="0" mb="0" cb="0"/><line nr="50" mi="3" ci="0" mb="0" cb="0"/><line nr="51" mi="3" ci="0" mb="0" cb="0"/><line nr="52" mi="3" ci="0" mb="0" cb="0"/><line nr="54" mi="5" ci="0" mb="0" cb="0"/><line nr="55" mi="4" ci="0" mb="0" cb="0"/><line nr="57" mi="7" ci="0" mb="2" cb="0"/><line nr="58" mi="3" ci="0" mb="2" cb="0"/><line nr="59" mi="4" ci="0" mb="2" cb="0"/><line nr="60" mi="9" ci="0" mb="2" cb="0"/><line nr="62" mi="2" ci="0" mb="2" cb="0"/><line nr="63" mi="8" ci="0" mb="0" cb="0"/><line nr="65" mi="5" ci="0" mb="0" cb="0"/><line nr="67" mi="2" ci="0" mb="2" cb="0"/><line nr="68" mi="5" ci="0" mb="0" cb="0"/><line nr="69" mi="4" ci="0" mb="0" cb="0"/><line nr="71" mi="1" ci="0" mb="0" cb="0"/><line nr="74" mi="6" ci="0" mb="0" cb="0"/><line nr="78" mi="10" ci="0" mb="0" cb="0"/><line nr="80" mi="5" ci="0" mb="0" cb="0"/><line nr="81" mi="7" ci="0" mb="0" cb="0"/><line nr="82" mi="2" ci="0" mb="2" cb="0"/><line nr="83" mi="3" ci="0" mb="0" cb="0"/><line nr="84" mi="3" ci="0" mb="2" cb="0"/><line nr="85" mi="4" ci="0" mb="0" cb="0"/><line nr="86" mi="4" ci="0" mb="0" cb="0"/><line nr="88" mi="2" ci="0" mb="0" cb="0"/><line nr="92" mi="7" ci="0" mb="0" cb="0"/><line nr="96" mi="6" ci="0" mb="0" cb="0"/><line nr="100" mi="8" ci="0" mb="0" cb="0"/><line nr="104" mi="7" ci="0" mb="0" cb="0"/><line nr="108" mi="6" ci="0" mb="0" cb="0"/><line nr="112" mi="7" ci="0" mb="0" cb="0"/><line nr="116" mi="9" ci="0" mb="0" cb="0"/><line nr="120" mi="10" ci="0" mb="0" cb="0"/><line nr="122" mi="2" ci="0" mb="0" cb="0"/><line nr="123" mi="3" ci="0" mb="2" cb="0"/><line nr="124" mi="2" ci="0" mb="0" cb="0"/><line nr="125" mi="6" ci="0" mb="0" cb="0"/><line nr="126" mi="3" ci="0" mb="0" cb="0"/><line nr="128" mi="5" ci="0" mb="0" cb="0"/><line nr="129" mi="7" ci="0" mb="0" cb="0"/><line nr="130" mi="2" ci="0" mb="2" cb="0"/><line nr="131" mi="9" ci="0" mb="0" cb="0"/><line nr="132" mi="4" ci="0" mb="0" cb="0"/><line nr="135" mi="3" ci="0" mb="2" cb="0"/><line nr="136" mi="9" ci="0" mb="0" cb="0"/><line nr="137" mi="4" ci="0" mb="0" cb="0"/><line nr="138" mi="4" ci="0" mb="0" cb="0"/><line nr="140" mi="2" ci="0" mb="0" cb="0"/><line nr="144" mi="3" ci="0" mb="2" cb="0"/><line nr="145" mi="5" ci="0" mb="0" cb="0"/><line nr="146" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="262" covered="0"/><counter type="BRANCH" missed="24" covered="0"/><counter type="LINE" missed="57" covered="0"/><counter type="COMPLEXITY" missed="24" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaProducerFactoryFactory.java"><line nr="16" mi="2" ci="0" mb="0" cb="0"/><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="1" ci="0" mb="0" cb="0"/><line nr="21" mi="5" ci="0" mb="0" cb="0"/><line nr="25" mi="6" ci="0" mb="0" cb="0"/><line nr="26" mi="6" ci="0" mb="0" cb="0"/><line nr="27" mi="5" ci="0" mb="0" cb="0"/><line nr="28" mi="6" ci="0" mb="0" cb="0"/><line nr="30" mi="6" ci="0" mb="0" cb="0"/><line nr="31" mi="6" ci="0" mb="0" cb="0"/><line nr="32" mi="6" ci="0" mb="0" cb="0"/><line nr="33" mi="6" ci="0" mb="0" cb="0"/><line nr="34" mi="6" ci="0" mb="0" cb="0"/><line nr="36" mi="3" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="5" ci="0" mb="0" cb="0"/><line nr="41" mi="3" ci="0" mb="0" cb="0"/><line nr="42" mi="3" ci="0" mb="0" cb="0"/><line nr="43" mi="2" ci="0" mb="2" cb="0"/><line nr="44" mi="4" ci="0" mb="0" cb="0"/><line nr="45" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="92" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="22" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="3" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="545" covered="0"/><counter type="BRANCH" missed="34" covered="0"/><counter type="LINE" missed="135" covered="0"/><counter type="COMPLEXITY" missed="47" covered="0"/><counter type="METHOD" missed="30" covered="0"/><counter type="CLASS" missed="5" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/interceptor"><class name="com/poc/hss/fasttrack/kafka/interceptor/Unity2CommonKafkaBatchInterceptor" sourcefilename="Unity2CommonKafkaBatchInterceptor.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;)V" line="16"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="intercept" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecords;Lorg/apache/kafka/clients/consumer/Consumer;)Lorg/apache/kafka/clients/consumer/ConsumerRecords;" line="22"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="failure" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecords;Ljava/lang/Exception;Lorg/apache/kafka/clients/consumer/Consumer;)V" line="28"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="10"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/interceptor/InitialCommitInterceptor" sourcefilename="InitialCommitInterceptor.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;Lorg/slf4j/Logger;)V" line="17"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="commitInitialOffsetsIfNeeded" desc="(Lorg/apache/kafka/clients/consumer/Consumer;)V" line="25"><counter type="INSTRUCTION" missed="50" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="13" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$commitInitialOffsetsIfNeeded$1" desc="(Ljava/lang/StringBuilder;Lorg/apache/kafka/common/TopicPartition;Lorg/apache/kafka/clients/consumer/OffsetAndMetadata;)V" line="44"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$commitInitialOffsetsIfNeeded$0" desc="(Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Lorg/apache/kafka/common/TopicPartition;)V" line="36"><counter type="INSTRUCTION" missed="17" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="94" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="25" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/interceptor/Unity2CommonKafkaRecordInterceptor" sourcefilename="Unity2CommonKafkaRecordInterceptor.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;)V" line="13"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="intercept" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;Lorg/apache/kafka/clients/consumer/Consumer;)Lorg/apache/kafka/clients/consumer/ConsumerRecord;" line="18"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="failure" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;Ljava/lang/Exception;Lorg/apache/kafka/clients/consumer/Consumer;)V" line="24"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="9"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="InitialCommitInterceptor.java"><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="19" mi="2" ci="0" mb="0" cb="0"/><line nr="20" mi="3" ci="0" mb="0" cb="0"/><line nr="21" mi="5" ci="0" mb="0" cb="0"/><line nr="22" mi="1" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="2" cb="0"/><line nr="26" mi="4" ci="0" mb="0" cb="0"/><line nr="28" mi="1" ci="0" mb="0" cb="0"/><line nr="30" mi="5" ci="0" mb="0" cb="0"/><line nr="31" mi="5" ci="0" mb="0" cb="0"/><line nr="33" mi="4" ci="0" mb="0" cb="0"/><line nr="34" mi="7" ci="0" mb="0" cb="0"/><line nr="36" mi="4" ci="0" mb="2" cb="0"/><line nr="37" mi="12" ci="0" mb="0" cb="0"/><line nr="39" mi="1" ci="0" mb="0" cb="0"/><line nr="42" mi="4" ci="0" mb="0" cb="0"/><line nr="43" mi="5" ci="0" mb="0" cb="0"/><line nr="44" mi="8" ci="0" mb="0" cb="0"/><line nr="45" mi="2" ci="0" mb="0" cb="0"/><line nr="46" mi="2" ci="0" mb="0" cb="0"/><line nr="47" mi="1" ci="0" mb="0" cb="0"/><line nr="51" mi="3" ci="0" mb="2" cb="0"/><line nr="52" mi="5" ci="0" mb="0" cb="0"/><line nr="53" mi="3" ci="0" mb="0" cb="0"/><line nr="55" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="94" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="25" covered="0"/><counter type="COMPLEXITY" missed="7" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2CommonKafkaBatchInterceptor.java"><line nr="10" mi="4" ci="0" mb="0" cb="0"/><line nr="16" mi="4" ci="0" mb="0" cb="0"/><line nr="17" mi="5" ci="0" mb="0" cb="0"/><line nr="18" mi="1" ci="0" mb="0" cb="0"/><line nr="22" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="2" ci="0" mb="0" cb="0"/><line nr="28" mi="5" ci="0" mb="0" cb="0"/><line nr="29" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="25" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2CommonKafkaRecordInterceptor.java"><line nr="9" mi="4" ci="0" mb="0" cb="0"/><line nr="13" mi="4" ci="0" mb="0" cb="0"/><line nr="14" mi="1" ci="0" mb="0" cb="0"/><line nr="18" mi="3" ci="0" mb="0" cb="0"/><line nr="19" mi="2" ci="0" mb="0" cb="0"/><line nr="24" mi="5" ci="0" mb="0" cb="0"/><line nr="25" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="139" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="40" covered="0"/><counter type="COMPLEXITY" missed="15" covered="0"/><counter type="METHOD" missed="12" covered="0"/><counter type="CLASS" missed="3" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/log"><class name="com/poc/hss/fasttrack/kafka/log/KafkaLogAdaptor" sourcefilename="KafkaLogAdaptor.java"><method name="&lt;init&gt;" desc="(Lorg/slf4j/Logger;ILjava/lang/String;)V" line="24"><counter type="INSTRUCTION" missed="0" covered="15"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="errorLogging" desc="(Ljava/lang/Iterable;Ljava/lang/Exception;)V" line="33"><counter type="INSTRUCTION" missed="51" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="errorLogging" desc="(Lorg/springframework/kafka/listener/BatchListenerFailedException;)V" line="46"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="errorLogging" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;Ljava/lang/Exception;)V" line="51"><counter type="INSTRUCTION" missed="126" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="14" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="exceptionInstanceOf" desc="(Ljava/lang/Throwable;Ljava/lang/Class;)Z" line="90"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="exceptionInstanceOf" desc="(Ljava/lang/Throwable;Ljava/lang/Class;I)Z" line="94"><counter type="INSTRUCTION" missed="0" covered="27"/><counter type="BRANCH" missed="1" covered="7"/><counter type="LINE" missed="0" covered="5"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="0" covered="1"/></method><method name="formatRecordMetadata" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Ljava/lang/String;" line="103"><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="formatRecordMetadata" desc="(Ljava/lang/String;IJ)Ljava/lang/String;" line="109"><counter type="INSTRUCTION" missed="19" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="isWithinRetryableDuration" desc="(Lorg/apache/kafka/clients/consumer/ConsumerRecord;)Z" line="113"><counter type="INSTRUCTION" missed="20" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="findException" desc="(Ljava/lang/Exception;Ljava/lang/Class;)Ljava/lang/Throwable;" line="121"><counter type="INSTRUCTION" missed="16" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$findException$0" desc="(Ljava/lang/Throwable;)Ljava/lang/Throwable;" line="123"><counter type="INSTRUCTION" missed="2" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="255" covered="48"/><counter type="BRANCH" missed="17" covered="7"/><counter type="LINE" missed="39" covered="12"/><counter type="COMPLEXITY" missed="17" covered="6"/><counter type="METHOD" missed="8" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory" sourcefilename="KafkaLogAdaptorFactory.java"><method name="&lt;init&gt;" desc="()V" line="8"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createAdaptor" desc="(Lorg/slf4j/Logger;)Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptor;" line="17"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="KafkaLogAdaptorFactory.java"><line nr="8" mi="3" ci="0" mb="0" cb="0"/><line nr="17" mi="9" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="KafkaLogAdaptor.java"><line nr="24" mi="0" ci="3" mb="0" cb="0"/><line nr="26" mi="0" ci="2" mb="0" cb="0"/><line nr="27" mi="0" ci="3" mb="0" cb="0"/><line nr="28" mi="0" ci="3" mb="0" cb="0"/><line nr="29" mi="0" ci="3" mb="0" cb="0"/><line nr="30" mi="0" ci="1" mb="0" cb="0"/><line nr="33" mi="6" ci="0" mb="0" cb="0"/><line nr="34" mi="5" ci="0" mb="4" cb="0"/><line nr="35" mi="4" ci="0" mb="0" cb="0"/><line nr="37" mi="6" ci="0" mb="0" cb="0"/><line nr="38" mi="2" ci="0" mb="0" cb="0"/><line nr="39" mi="4" ci="0" mb="0" cb="0"/><line nr="41" mi="23" ci="0" mb="0" cb="0"/><line nr="43" mi="1" ci="0" mb="0" cb="0"/><line nr="46" mi="3" ci="0" mb="0" cb="0"/><line nr="47" mi="4" ci="0" mb="0" cb="0"/><line nr="48" mi="1" ci="0" mb="0" cb="0"/><line nr="51" mi="5" ci="0" mb="2" cb="0"/><line nr="52" mi="4" ci="0" mb="2" cb="0"/><line nr="53" mi="11" ci="0" mb="0" cb="0"/><line nr="55" mi="11" ci="0" mb="0" cb="0"/><line nr="57" mi="6" ci="0" mb="0" cb="0"/><line nr="61" mi="15" ci="0" mb="0" cb="0"/><line nr="64" mi="11" ci="0" mb="0" cb="0"/><line nr="66" mi="6" ci="0" mb="0" cb="0"/><line nr="70" mi="5" ci="0" mb="2" cb="0"/><line nr="71" mi="20" ci="0" mb="0" cb="0"/><line nr="75" mi="6" ci="0" mb="0" cb="0"/><line nr="79" mi="19" ci="0" mb="0" cb="0"/><line nr="83" mi="6" ci="0" mb="0" cb="0"/><line nr="87" mi="1" ci="0" mb="0" cb="0"/><line nr="90" mi="0" ci="6" mb="0" cb="0"/><line nr="94" mi="0" ci="4" mb="0" cb="2"/><line nr="95" mi="0" ci="2" mb="0" cb="0"/><line nr="96" mi="0" ci="3" mb="0" cb="2"/><line nr="97" mi="0" ci="16" mb="1" cb="3"/><line nr="99" mi="0" ci="2" mb="0" cb="0"/><line nr="103" mi="2" ci="0" mb="2" cb="0"/><line nr="104" mi="2" ci="0" mb="0" cb="0"/><line nr="105" mi="9" ci="0" mb="0" cb="0"/><line nr="109" mi="19" ci="0" mb="0" cb="0"/><line nr="113" mi="4" ci="0" mb="2" cb="0"/><line nr="114" mi="1" ci="0" mb="0" cb="0"/><line nr="115" mi="3" ci="0" mb="0" cb="0"/><line nr="116" mi="3" ci="0" mb="0" cb="0"/><line nr="117" mi="9" ci="0" mb="2" cb="0"/><line nr="121" mi="6" ci="0" mb="0" cb="0"/><line nr="122" mi="5" ci="0" mb="0" cb="0"/><line nr="123" mi="3" ci="0" mb="0" cb="0"/><line nr="124" mi="2" ci="0" mb="0" cb="0"/><line nr="125" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="255" covered="48"/><counter type="BRANCH" missed="17" covered="7"/><counter type="LINE" missed="39" covered="12"/><counter type="COMPLEXITY" missed="17" covered="6"/><counter type="METHOD" missed="8" covered="3"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="267" covered="48"/><counter type="BRANCH" missed="17" covered="7"/><counter type="LINE" missed="41" covered="12"/><counter type="COMPLEXITY" missed="19" covered="6"/><counter type="METHOD" missed="10" covered="3"/><counter type="CLASS" missed="1" covered="1"/></package><package name="com/poc/hss/fasttrack/kafka/exception"><class name="com/poc/hss/fasttrack/kafka/exception/UnityDefaultErrorHandler" sourcefilename="UnityDefaultErrorHandler.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/config/ShutdownManager;)V" line="24"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="handleBatch" desc="(Ljava/lang/Exception;Lorg/apache/kafka/clients/consumer/ConsumerRecords;Lorg/apache/kafka/clients/consumer/Consumer;Lorg/springframework/kafka/listener/MessageListenerContainer;Ljava/lang/Runnable;)V" line="35"><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="handleOtherException" desc="(Ljava/lang/Exception;Lorg/apache/kafka/clients/consumer/Consumer;Lorg/springframework/kafka/listener/MessageListenerContainer;Z)V" line="55"><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="7" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="109" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/fasttrack/kafka/exception/Unity2KafkaRetryException" sourcefilename="Unity2KafkaRetryException.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="9"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/Throwable;Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="13"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="6" covered="6"/><counter type="LINE" missed="2" covered="2"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="1" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/kafka/exception/Unity2KafkaDeadLetterException" sourcefilename="Unity2KafkaDeadLetterException.java"><method name="&lt;init&gt;" desc="(Ljava/lang/String;Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="9"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Ljava/lang/String;Ljava/lang/Throwable;Lorg/apache/kafka/clients/consumer/ConsumerRecord;)V" line="13"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="Unity2KafkaRetryException.java"><line nr="9" mi="5" ci="0" mb="0" cb="0"/><line nr="10" mi="1" ci="0" mb="0" cb="0"/><line nr="13" mi="0" ci="5" mb="0" cb="0"/><line nr="14" mi="0" ci="1" mb="0" cb="0"/><counter type="INSTRUCTION" missed="6" covered="6"/><counter type="LINE" missed="2" covered="2"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="1" covered="1"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><sourcefile name="UnityDefaultErrorHandler.java"><line nr="18" mi="4" ci="0" mb="0" cb="0"/><line nr="24" mi="5" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="1" ci="0" mb="0" cb="0"/><line nr="35" mi="21" ci="0" mb="0" cb="0"/><line nr="36" mi="6" ci="0" mb="4" cb="0"/><line nr="37" mi="5" ci="0" mb="0" cb="0"/><line nr="38" mi="3" ci="0" mb="0" cb="0"/><line nr="39" mi="5" ci="0" mb="0" cb="0"/><line nr="41" mi="7" ci="0" mb="0" cb="0"/><line nr="43" mi="1" ci="0" mb="0" cb="0"/><line nr="55" mi="22" ci="0" mb="0" cb="0"/><line nr="56" mi="6" ci="0" mb="4" cb="0"/><line nr="57" mi="5" ci="0" mb="0" cb="0"/><line nr="58" mi="3" ci="0" mb="0" cb="0"/><line nr="59" mi="5" ci="0" mb="0" cb="0"/><line nr="61" mi="6" ci="0" mb="0" cb="0"/><line nr="63" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="109" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="18" covered="0"/><counter type="COMPLEXITY" missed="8" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaDeadLetterException.java"><line nr="9" mi="5" ci="0" mb="0" cb="0"/><line nr="10" mi="1" ci="0" mb="0" cb="0"/><line nr="13" mi="5" ci="0" mb="0" cb="0"/><line nr="14" mi="1" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="127" covered="6"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="24" covered="2"/><counter type="COMPLEXITY" missed="11" covered="1"/><counter type="METHOD" missed="7" covered="1"/><counter type="CLASS" missed="2" covered="1"/></package><package name="com/poc/hss/fasttrack/kafka/integration"><class name="com/poc/hss/fasttrack/kafka/integration/IntegrationUtils" sourcefilename="IntegrationUtils.java"><method name="&lt;init&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="createConsumerRecord" desc="(Lorg/springframework/messaging/Message;I)Lorg/apache/kafka/clients/consumer/ConsumerRecord;" line="26"><counter type="INSTRUCTION" missed="52" covered="0"/><counter type="LINE" missed="6" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getHeader" desc="(Lorg/springframework/messaging/MessageHeaders;Ljava/lang/String;I)Ljava/lang/Object;" line="36"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="13"><counter type="INSTRUCTION" missed="41" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="110" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="IntegrationUtils.java"><line nr="12" mi="3" ci="0" mb="0" cb="0"/><line nr="13" mi="41" ci="0" mb="0" cb="0"/><line nr="26" mi="7" ci="0" mb="0" cb="0"/><line nr="27" mi="7" ci="0" mb="0" cb="0"/><line nr="28" mi="7" ci="0" mb="0" cb="0"/><line nr="29" mi="7" ci="0" mb="0" cb="0"/><line nr="30" mi="7" ci="0" mb="0" cb="0"/><line nr="32" mi="17" ci="0" mb="0" cb="0"/><line nr="36" mi="4" ci="0" mb="0" cb="0"/><line nr="37" mi="3" ci="0" mb="2" cb="0"/><line nr="38" mi="5" ci="0" mb="0" cb="0"/><line nr="40" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="110" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="110" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="12" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="4" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><package name="com/poc/hss/autoconfigure/kafka"><class name="com/poc/hss/autoconfigure/kafka/Unity2KafkaProducerFactoryAutoConfiguration" sourcefilename="Unity2KafkaProducerFactoryAutoConfiguration.java"><method name="&lt;init&gt;" desc="()V" line="15"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kafkaProducerFactory" desc="(Lcom/poc/hss/fasttrack/kafka/factory/KafkaProducerFactoryFactory;)Lorg/springframework/kafka/core/ProducerFactory;" line="23"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/autoconfigure/kafka/Unity2KafkaProducerFactoryFactoryAutoConfiguration" sourcefilename="Unity2KafkaProducerFactoryFactoryAutoConfiguration.java"><method name="&lt;init&gt;" desc="()V" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="KafkaProducerFactoryFactory" desc="(Lcom/poc/hss/fasttrack/kafka/config/KafkaProducerPropertiesProvider;)Lcom/poc/hss/fasttrack/kafka/factory/KafkaProducerFactoryFactory;" line="17"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/autoconfigure/kafka/Unity2KafkaTemplateFactoryAutoConfiguration" sourcefilename="Unity2KafkaTemplateFactoryAutoConfiguration.java"><method name="&lt;init&gt;" desc="()V" line="13"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kafkaTemplateFactory" desc="(Lorg/springframework/context/ApplicationContext;Lorg/springframework/kafka/core/ProducerFactory;Z)Lcom/poc/hss/fasttrack/kafka/factory/KafkaTemplateFactory;" line="21"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/autoconfigure/kafka/Unity2KafkaListenerContainerFactoryAutoConfiguration" sourcefilename="Unity2KafkaListenerContainerFactoryAutoConfiguration.java"><method name="&lt;init&gt;" desc="()V" line="19"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kafkaListenerContainerFactory" desc="(Lorg/springframework/context/ApplicationContext;Lorg/springframework/kafka/core/ProducerFactory;Lorg/springframework/kafka/core/ConsumerFactory;Lcom/poc/hss/fasttrack/kafka/factory/KafkaListenerContainerErrorHandlerFactory;Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;ZLio/micrometer/observation/ObservationRegistry;Lcom/poc/hss/fasttrack/config/ShutdownManager;)Lcom/poc/hss/fasttrack/kafka/factory/KafkaListenerContainerFactory;" line="34"><counter type="INSTRUCTION" missed="12" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><class name="com/poc/hss/autoconfigure/kafka/Unity2KafkaConsumerFactoryAutoConfiguration" sourcefilename="Unity2KafkaConsumerFactoryAutoConfiguration.java"><method name="&lt;init&gt;" desc="()V" line="17"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="kafkaConsumerFactory" desc="(Lcom/poc/hss/fasttrack/kafka/config/KafkaConsumerPropertiesProvider;)Lorg/springframework/kafka/core/ConsumerFactory;" line="23"><counter type="INSTRUCTION" missed="41" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="44" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="Unity2KafkaTemplateFactoryAutoConfiguration.java"><line nr="13" mi="3" ci="0" mb="0" cb="0"/><line nr="21" mi="7" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaConsumerFactoryAutoConfiguration.java"><line nr="17" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="6" ci="0" mb="0" cb="0"/><line nr="24" mi="6" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="2" cb="0"/><line nr="26" mi="5" ci="0" mb="0" cb="0"/><line nr="27" mi="6" ci="0" mb="0" cb="0"/><line nr="28" mi="5" ci="0" mb="0" cb="0"/><line nr="30" mi="4" ci="0" mb="0" cb="0"/><line nr="31" mi="4" ci="0" mb="0" cb="0"/><line nr="33" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="44" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="10" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaListenerContainerFactoryAutoConfiguration.java"><line nr="19" mi="3" ci="0" mb="0" cb="0"/><line nr="34" mi="12" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="15" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaProducerFactoryAutoConfiguration.java"><line nr="15" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="5" ci="0" mb="0" cb="0"/><line nr="25" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="13" covered="0"/><counter type="LINE" missed="4" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><sourcefile name="Unity2KafkaProducerFactoryFactoryAutoConfiguration.java"><line nr="11" mi="3" ci="0" mb="0" cb="0"/><line nr="17" mi="5" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="2" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="90" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="20" covered="0"/><counter type="COMPLEXITY" missed="11" covered="0"/><counter type="METHOD" missed="10" covered="0"/><counter type="CLASS" missed="5" covered="0"/></package><package name="com/poc/hss/fasttrack/kafka/listener"><class name="com/poc/hss/fasttrack/kafka/listener/ErrorHandlingBatchMessageListener" sourcefilename="ErrorHandlingBatchMessageListener.java"><method name="&lt;init&gt;" desc="(Lorg/springframework/kafka/listener/BatchMessageListener;Lorg/springframework/kafka/core/KafkaTemplate;Lcom/poc/hss/fasttrack/kafka/log/KafkaLogAdaptorFactory;)V" line="29"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="onMessage" desc="(Ljava/util/List;Lorg/springframework/kafka/support/Acknowledgment;)V" line="38"><counter type="INSTRUCTION" missed="28" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="retryOrDeadLetter" desc="(Ljava/util/List;Lorg/springframework/kafka/support/Acknowledgment;Lorg/springframework/kafka/listener/BatchListenerFailedException;Z)V" line="48"><counter type="INSTRUCTION" missed="44" covered="0"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="4" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="acknowledgeNack" desc="(Ljava/util/List;Lorg/apache/kafka/clients/consumer/ConsumerRecord;Lorg/springframework/kafka/support/Acknowledgment;Z)V" line="60"><counter type="INSTRUCTION" missed="30" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="findIndex" desc="(Ljava/util/List;Lorg/apache/kafka/clients/consumer/ConsumerRecord;)I" line="73"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="BRANCH" missed="8" covered="0"/><counter type="LINE" missed="8" covered="0"/><counter type="COMPLEXITY" missed="5" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="20"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="154" covered="0"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="40" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></class><sourcefile name="ErrorHandlingBatchMessageListener.java"><line nr="20" mi="4" ci="0" mb="0" cb="0"/><line nr="29" mi="2" ci="0" mb="0" cb="0"/><line nr="30" mi="3" ci="0" mb="0" cb="0"/><line nr="31" mi="3" ci="0" mb="0" cb="0"/><line nr="32" mi="5" ci="0" mb="0" cb="0"/><line nr="33" mi="1" ci="0" mb="0" cb="0"/><line nr="38" mi="4" ci="0" mb="0" cb="0"/><line nr="39" mi="2" ci="0" mb="0" cb="0"/><line nr="40" mi="1" ci="0" mb="0" cb="0"/><line nr="41" mi="11" ci="0" mb="2" cb="0"/><line nr="42" mi="1" ci="0" mb="0" cb="0"/><line nr="43" mi="6" ci="0" mb="0" cb="0"/><line nr="44" mi="2" ci="0" mb="0" cb="0"/><line nr="45" mi="1" ci="0" mb="0" cb="0"/><line nr="48" mi="4" ci="0" mb="0" cb="0"/><line nr="49" mi="3" ci="0" mb="0" cb="0"/><line nr="50" mi="2" ci="0" mb="2" cb="0"/><line nr="51" mi="5" ci="0" mb="0" cb="0"/><line nr="52" mi="5" ci="0" mb="4" cb="0"/><line nr="53" mi="7" ci="0" mb="0" cb="0"/><line nr="54" mi="11" ci="0" mb="0" cb="0"/><line nr="56" mi="6" ci="0" mb="0" cb="0"/><line nr="57" mi="1" ci="0" mb="0" cb="0"/><line nr="60" mi="5" ci="0" mb="0" cb="0"/><line nr="61" mi="2" ci="0" mb="2" cb="0"/><line nr="62" mi="4" ci="0" mb="0" cb="0"/><line nr="63" mi="4" ci="0" mb="2" cb="0"/><line nr="64" mi="3" ci="0" mb="0" cb="0"/><line nr="66" mi="5" ci="0" mb="0" cb="0"/><line nr="67" mi="1" ci="0" mb="0" cb="0"/><line nr="68" mi="5" ci="0" mb="0" cb="0"/><line nr="70" mi="1" ci="0" mb="0" cb="0"/><line nr="73" mi="8" ci="0" mb="2" cb="0"/><line nr="74" mi="5" ci="0" mb="0" cb="0"/><line nr="75" mi="1" ci="0" mb="0" cb="0"/><line nr="76" mi="6" ci="0" mb="2" cb="0"/><line nr="77" mi="5" ci="0" mb="2" cb="0"/><line nr="78" mi="5" ci="0" mb="2" cb="0"/><line nr="80" mi="2" ci="0" mb="0" cb="0"/><line nr="83" mi="2" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="154" covered="0"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="40" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></sourcefile><counter type="INSTRUCTION" missed="154" covered="0"/><counter type="BRANCH" missed="20" covered="0"/><counter type="LINE" missed="40" covered="0"/><counter type="COMPLEXITY" missed="16" covered="0"/><counter type="METHOD" missed="6" covered="0"/><counter type="CLASS" missed="1" covered="0"/></package><counter type="INSTRUCTION" missed="1432" covered="54"/><counter type="BRANCH" missed="89" covered="7"/><counter type="LINE" missed="312" covered="14"/><counter type="COMPLEXITY" missed="124" covered="7"/><counter type="METHOD" missed="79" covered="4"/><counter type="CLASS" missed="18" covered="2"/></report>