<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaMessageDTOConsumerRecordConverter.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.converter</a> &gt; <span class="el_source">KafkaMessageDTOConsumerRecordConverter.java</span></div><h1>KafkaMessageDTOConsumerRecordConverter.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.converter;

import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.util.ConversionHelperService;
import io.vertx.core.json.JsonObject;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Optional;
import java.util.TimeZone;

@Component
<span class="nc" id="L17">public class KafkaMessageDTOConsumerRecordConverter implements Converter&lt;ConsumerRecord, KafkaMessageDTO&gt; {</span>

    @Autowired
    private ConversionHelperService conversionHelperService;

    @Override
    public KafkaMessageDTO convert(ConsumerRecord source) {
<span class="nc" id="L24">        return KafkaMessageDTO.builder()</span>
<span class="nc" id="L25">                .topicName(source.topic())</span>
<span class="nc" id="L26">                .key(Optional.ofNullable(source.key()).map(Object::toString).orElse(null))</span>
<span class="nc" id="L27">                .partition(source.partition())</span>
<span class="nc" id="L28">                .offset(source.offset())</span>
<span class="nc" id="L29">                .timestamp(LocalDateTime.ofInstant(Instant.ofEpochMilli(source.timestamp()), TimeZone.getDefault().toZoneId()))</span>
<span class="nc" id="L30">                .payload(conversionHelperService.convert(source.value(), JsonObject.class))</span>
<span class="nc" id="L31">                .consumerRecord(source)</span>
<span class="nc" id="L32">                .build();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>