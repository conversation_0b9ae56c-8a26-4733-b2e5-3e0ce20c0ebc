<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaMessageDTO.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_source">KafkaMessageDTO.java</span></div><h1>KafkaMessageDTO.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dto;

import io.vertx.core.json.JsonObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.TimeZone;

<span class="pc" id="L14">@Builder</span>
<span class="pc bpc" id="L15" title="37 of 62 branches missed.">@Data</span>
<span class="fc" id="L16">@AllArgsConstructor</span>
<span class="fc" id="L17">@NoArgsConstructor</span>
public class KafkaMessageDTO {
<span class="fc" id="L19">    private String topicName;</span>
<span class="fc" id="L20">    private Integer partition;</span>
<span class="fc" id="L21">    private Long offset;</span>
<span class="fc" id="L22">    private String key;</span>
<span class="fc" id="L23">    private JsonObject payload;</span>
<span class="fc" id="L24">    private LocalDateTime timestamp;</span>
<span class="fc" id="L25">    private ConsumerRecord&lt;?, ?&gt; consumerRecord;</span>

    public static String[] getCsvHeaders() {
<span class="fc" id="L28">        return new String[]{</span>
                &quot;topicName&quot;,
                &quot;partition&quot;,
                &quot;offset&quot;,
                &quot;key&quot;,
                &quot;payload&quot;,
                &quot;timestamp&quot;
        };
    }

    public String[] toStringArray() {
<span class="fc" id="L39">        return new String[]{</span>
                topicName,
<span class="fc" id="L41">                partition.toString(),</span>
<span class="fc" id="L42">                offset.toString(),</span>
                key,
<span class="fc" id="L44">                payload.toString(),</span>
<span class="fc" id="L45">                timestamp.toString()</span>
        };
    }

    @Override
    public String toString() {
<span class="fc" id="L51">        return String.format(&quot;Topic name: %s \n&quot;, this.consumerRecord.topic()) +</span>
<span class="fc" id="L52">                String.format(&quot;Partition: %s \n&quot;, this.consumerRecord.partition()) +</span>
<span class="fc" id="L53">                String.format(&quot;Offset: %s \n&quot;, this.consumerRecord.offset()) +</span>
<span class="fc" id="L54">                String.format(&quot;Kafka Ack time: %s \n&quot;, LocalDateTime.ofInstant(Instant.ofEpochMilli(this.consumerRecord.timestamp()), TimeZone.getDefault().toZoneId())) +</span>
<span class="fc" id="L55">                String.format(&quot;Kafka key: %s \n&quot;, this.consumerRecord.key()) +</span>
<span class="fc bfc" id="L56" title="All 2 branches covered.">                String.format(&quot;Kafka payload: %s \n&quot;, this.payload == null ? null : this.payload.toString());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>