<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>KafkaMessageFormat.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_source">KafkaMessageFormat.java</span></div><h1>KafkaMessageFormat.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dto;

import org.apache.commons.lang3.StringUtils;

<span class="fc" id="L5">public enum KafkaMessageFormat {</span>
<span class="fc" id="L6">    AVRO(&quot;AVRO&quot;),</span>
<span class="fc" id="L7">    JSON(&quot;JSON&quot;);</span>

<span class="fc" id="L9">    private KafkaMessageFormat(String value) {</span>
<span class="fc" id="L10">        this.value = value;</span>
<span class="fc" id="L11">    }</span>

    private String value;

    public static KafkaMessageFormat fromValue(String value) {
<span class="fc bfc" id="L16" title="All 2 branches covered.">        for (KafkaMessageFormat en : KafkaMessageFormat.values()) {</span>
<span class="fc bfc" id="L17" title="All 2 branches covered.">            if (StringUtils.equals(en.value, value)) {</span>
<span class="fc" id="L18">                return en;</span>
            }
        }
<span class="fc" id="L21">        return null;</span>
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>