<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RecalculatedBatchCacheDTO</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_class">RecalculatedBatchCacheDTO</span></div><h1>RecalculatedBatchCacheDTO</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">43 of 170</td><td class="ctr2">74%</td><td class="bar">18 of 30</td><td class="ctr2">40%</td><td class="ctr1">18</td><td class="ctr2">27</td><td class="ctr1">0</td><td class="ctr2">5</td><td class="ctr1">3</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a2"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="25" alt="25"/><img src="../jacoco-resources/greenbar.gif" width="78" height="10" title="47" alt="47"/></td><td class="ctr2" id="c8">65%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="15" alt="15"/><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">37%</td><td class="ctr1" id="f0">12</td><td class="ctr2" id="g0">13</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i0">1</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="6" alt="6"/><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="42" alt="42"/></td><td class="ctr2" id="c7">87%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="15" height="10" title="3" alt="3"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="3" alt="3"/></td><td class="ctr2" id="e0">50%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">setBatchId(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a9"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">setSourceStageRecalculationList(List)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a10"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">setTargetStageRecalculation(RecalculatedBatchCacheDTO.TargetStageRecalculation)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="RecalculatedBatchCacheDTO.java.html#L10" class="el_method">RecalculatedBatchCacheDTO(String, List, RecalculatedBatchCacheDTO.TargetStageRecalculation)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="20" height="10" title="12" alt="12"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a11"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">toString()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="10" alt="10"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="RecalculatedBatchCacheDTO.java.html#L10" class="el_method">builder()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a3"><a href="RecalculatedBatchCacheDTO.java.html#L12" class="el_method">getBatchId()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a4"><a href="RecalculatedBatchCacheDTO.java.html#L13" class="el_method">getSourceStageRecalculationList()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a5"><a href="RecalculatedBatchCacheDTO.java.html#L14" class="el_method">getTargetStageRecalculation()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a1"><a href="RecalculatedBatchCacheDTO.java.html#L9" class="el_method">canEqual(Object)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>