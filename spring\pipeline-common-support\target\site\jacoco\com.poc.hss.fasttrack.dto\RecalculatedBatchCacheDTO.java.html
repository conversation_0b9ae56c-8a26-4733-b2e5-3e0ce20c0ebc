<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RecalculatedBatchCacheDTO.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.dto</a> &gt; <span class="el_source">RecalculatedBatchCacheDTO.java</span></div><h1>RecalculatedBatchCacheDTO.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.dto;

import com.poc.hss.fasttrack.model.Unity2Component;
import lombok.Builder;
import lombok.Data;

import java.util.List;

<span class="pc bpc" id="L9" title="18 of 30 branches missed.">@Data</span>
<span class="pc" id="L10">@Builder</span>
public class RecalculatedBatchCacheDTO {
<span class="fc" id="L12">    private String batchId;</span>
<span class="fc" id="L13">    private List&lt;SourceStageRecalculation&gt; sourceStageRecalculationList;</span>
<span class="fc" id="L14">    private TargetStageRecalculation targetStageRecalculation;</span>

<span class="nc bnc" id="L16" title="All 46 branches missed.">    @Data</span>
<span class="pc" id="L17">    @Builder</span>
    public static class SourceStageRecalculation {
<span class="fc" id="L19">        private String name;</span>
<span class="fc" id="L20">        private Unity2Component stage;</span>
<span class="fc" id="L21">        private Long originalMessageOut;</span>
<span class="fc" id="L22">        private Long recalculatedMessageOut;</span>
<span class="fc" id="L23">        private Boolean updated;</span>
    }

<span class="pc bpc" id="L26" title="45 of 46 branches missed.">    @Data</span>
<span class="pc" id="L27">    @Builder</span>
    public static class TargetStageRecalculation {
<span class="fc" id="L29">        private String name;</span>
<span class="fc" id="L30">        private Unity2Component stage;</span>
<span class="fc" id="L31">        private Long originalMessageIn;</span>
<span class="fc" id="L32">        private Long recalculatedMessageIn;</span>
<span class="fc" id="L33">        private Boolean updated;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>