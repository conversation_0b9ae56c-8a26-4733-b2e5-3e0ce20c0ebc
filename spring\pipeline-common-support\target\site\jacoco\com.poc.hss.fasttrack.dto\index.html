<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">584 of 1,417</td><td class="ctr2">58%</td><td class="bar">146 of 190</td><td class="ctr2">23%</td><td class="ctr1">115</td><td class="ctr2">197</td><td class="ctr1">1</td><td class="ctr2">52</td><td class="ctr1">24</td><td class="ctr2">102</td><td class="ctr1">0</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a5"><a href="RecalculatedBatchCacheDTO$SourceStageRecalculation.html" class="el_class">RecalculatedBatchCacheDTO.SourceStageRecalculation</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="56" height="10" title="218" alt="218"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="37" alt="37"/></td><td class="ctr2" id="c8">14%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="86" height="10" title="46" alt="46"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">32</td><td class="ctr2" id="g1">39</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i2">7</td><td class="ctr1" id="j0">9</td><td class="ctr2" id="k1">16</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a7"><a href="RecalculatedBatchCacheDTO$TargetStageRecalculation.html" class="el_class">RecalculatedBatchCacheDTO.TargetStageRecalculation</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="55" height="10" title="213" alt="213"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="42" alt="42"/></td><td class="ctr2" id="c7">16%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="84" height="10" title="45" alt="45"/><img src="../jacoco-resources/greenbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="e3">2%</td><td class="ctr1" id="f1">31</td><td class="ctr2" id="g2">39</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j1">8</td><td class="ctr2" id="k2">16</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="KafkaMessageDTO.html" class="el_class">KafkaMessageDTO</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="55" alt="55"/><img src="../jacoco-resources/greenbar.gif" width="105" height="10" title="409" alt="409"/></td><td class="ctr2" id="c1">88%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="69" height="10" title="37" alt="37"/><img src="../jacoco-resources/greenbar.gif" width="50" height="10" title="27" alt="27"/></td><td class="ctr2" id="e1">42%</td><td class="ctr1" id="f2">30</td><td class="ctr2" id="g0">55</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k0">23</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="RecalculatedBatchCacheDTO.html" class="el_class">RecalculatedBatchCacheDTO</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="43" alt="43"/><img src="../jacoco-resources/greenbar.gif" width="32" height="10" title="127" alt="127"/></td><td class="ctr2" id="c4">74%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="33" height="10" title="18" alt="18"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">40%</td><td class="ctr1" id="f3">18</td><td class="ctr2" id="g3">27</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i4">5</td><td class="ctr1" id="j2">3</td><td class="ctr2" id="k3">12</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a1"><a href="KafkaMessageDTO$KafkaMessageDTOBuilder.html" class="el_class">KafkaMessageDTO.KafkaMessageDTOBuilder</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="19" alt="19"/><img src="../jacoco-resources/greenbar.gif" width="14" height="10" title="56" alt="56"/></td><td class="ctr2" id="c5">74%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">10</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k4">10</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a8"><a href="RecalculatedBatchCacheDTO$TargetStageRecalculation$TargetStageRecalculationBuilder.html" class="el_class">RecalculatedBatchCacheDTO.TargetStageRecalculation.TargetStageRecalculationBuilder</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="42" alt="42"/></td><td class="ctr2" id="c2">76%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">8</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k5">8</td><td class="ctr1" id="l5">0</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a6"><a href="RecalculatedBatchCacheDTO$SourceStageRecalculation$SourceStageRecalculationBuilder.html" class="el_class">RecalculatedBatchCacheDTO.SourceStageRecalculation.SourceStageRecalculationBuilder</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="13" alt="13"/><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="42" alt="42"/></td><td class="ctr2" id="c3">76%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">8</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">8</td><td class="ctr1" id="l6">0</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a4"><a href="RecalculatedBatchCacheDTO$RecalculatedBatchCacheDTOBuilder.html" class="el_class">RecalculatedBatchCacheDTO.RecalculatedBatchCacheDTOBuilder</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="10" alt="10"/><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="28" alt="28"/></td><td class="ctr2" id="c6">73%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">6</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k7">6</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a2"><a href="KafkaMessageFormat.html" class="el_class">KafkaMessageFormat</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="50" alt="50"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i1">10</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">3</td><td class="ctr1" id="l8">0</td><td class="ctr2" id="m8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>