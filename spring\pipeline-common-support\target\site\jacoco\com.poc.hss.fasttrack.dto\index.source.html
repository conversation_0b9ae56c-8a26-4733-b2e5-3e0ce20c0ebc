<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.dto</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <span class="el_package">com.poc.hss.fasttrack.dto</span></div><h1>com.poc.hss.fasttrack.dto</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">584 of 1,417</td><td class="ctr2">58%</td><td class="bar">146 of 190</td><td class="ctr2">23%</td><td class="ctr1">115</td><td class="ctr2">197</td><td class="ctr1">1</td><td class="ctr2">52</td><td class="ctr1">24</td><td class="ctr2">102</td><td class="ctr1">0</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><a href="RecalculatedBatchCacheDTO.java.html" class="el_source">RecalculatedBatchCacheDTO.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="510" alt="510"/><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="318" alt="318"/></td><td class="ctr2" id="c2">38%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="107" height="10" title="109" alt="109"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="13" alt="13"/></td><td class="ctr2" id="e2">10%</td><td class="ctr1" id="f0">84</td><td class="ctr2" id="g0">127</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j0">23</td><td class="ctr2" id="k0">66</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">6</td></tr><tr><td id="a0"><a href="KafkaMessageDTO.java.html" class="el_source">KafkaMessageDTO.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="74" alt="74"/><img src="../jacoco-resources/greenbar.gif" width="67" height="10" title="465" alt="465"/></td><td class="ctr2" id="c1">86%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="37" alt="37"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="27" alt="27"/></td><td class="ctr2" id="e1">42%</td><td class="ctr1" id="f1">31</td><td class="ctr2" id="g1">65</td><td class="ctr1" id="h1">0</td><td class="ctr2" id="i0">23</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">33</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">2</td></tr><tr><td id="a1"><a href="KafkaMessageFormat.java.html" class="el_source">KafkaMessageFormat.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="7" height="10" title="50" alt="50"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">10</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">3</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>