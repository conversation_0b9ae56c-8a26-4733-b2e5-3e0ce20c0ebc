<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheResult.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.model</a> &gt; <span class="el_source">CacheResult.java</span></div><h1>CacheResult.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.util.stream.Collectors;

<span class="pc bpc" id="L12" title="23 of 38 branches missed.">@Data</span>
<span class="pc" id="L13">@Builder</span>
<span class="fc" id="L14">@NoArgsConstructor</span>
<span class="fc" id="L15">@AllArgsConstructor</span>
public class CacheResult {
<span class="fc" id="L17">    private List&lt;SourceAdaptorResult&gt; sourceAdaptorResult = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L18">    private Map&lt;String, PipelineResult&gt; pipelineResult = new LinkedHashMap&lt;&gt;();</span>
<span class="fc" id="L19">    private List&lt;ConsumerAdaptorResult&gt; consumerAdaptorResult = new ArrayList&lt;&gt;();</span>
<span class="fc" id="L20">    private List&lt;CacheInfo&gt; cacheList = new ArrayList&lt;&gt;();</span>

<span class="fc" id="L22">    public enum DefinitionEnum {</span>
<span class="fc" id="L23">        MESSAGE_IN(&quot;message-in&quot;),</span>
<span class="fc" id="L24">        MESSAGE_OUT(&quot;message-out&quot;),</span>
<span class="fc" id="L25">        BATCH_COMPLETION_FLAG(&quot;batch-completion&quot;);</span>

        private final String value;

<span class="fc" id="L29">        DefinitionEnum(String value) {</span>
<span class="fc" id="L30">            this.value = value;</span>
<span class="fc" id="L31">        }</span>

        public String getValue() {
<span class="fc" id="L34">            return this.value;</span>
        }

        @JsonCreator
        public static DefinitionEnum fromValue(String text) {
<span class="fc bfc" id="L39" title="All 2 branches covered.">            for (DefinitionEnum s : DefinitionEnum.values()) {</span>
<span class="fc bfc" id="L40" title="All 2 branches covered.">                if (String.valueOf(s.value).equals(text)) {</span>
<span class="fc" id="L41">                    return s;</span>
                }
            }
<span class="fc" id="L44">            return null;</span>
        }
    }

<span class="nc bnc" id="L48" title="All 38 branches missed.">    @Data</span>
<span class="pc" id="L49">    @Builder(toBuilder = true)</span>
<span class="nc" id="L50">    @NoArgsConstructor</span>
<span class="fc" id="L51">    @AllArgsConstructor</span>
    public static class CacheInfo {
<span class="fc" id="L53">        String cacheGroup;</span>
<span class="fc" id="L54">        Unity2Component component;</span>
<span class="fc" id="L55">        String definition;</span>
<span class="fc" id="L56">        Object value;</span>
    }

<span class="nc bnc" id="L59" title="All 38 branches missed.">    @Data</span>
<span class="pc" id="L60">    @Builder</span>
<span class="nc" id="L61">    @NoArgsConstructor</span>
<span class="fc" id="L62">    @AllArgsConstructor</span>
    public static class SourceAdaptorResult {
<span class="fc" id="L64">        String name;</span>
<span class="fc" id="L65">        Long sourceAdaptorIn;</span>
<span class="fc" id="L66">        Long sourceAdaptorOut;</span>
<span class="fc" id="L67">        Boolean sourceAdaptorCompleted;</span>
    }

<span class="pc bnc" id="L70" title="All 70 branches missed.">    @Data</span>
<span class="pc" id="L71">    @Builder</span>
<span class="nc" id="L72">    @NoArgsConstructor</span>
<span class="fc" id="L73">    @AllArgsConstructor</span>
    public static class PipelineResult {
<span class="fc" id="L75">        String name;</span>
<span class="fc" id="L76">        Long transformIn;</span>
<span class="fc" id="L77">        Long transformOut;</span>
<span class="fc" id="L78">        Boolean transformCompleted;</span>
<span class="fc" id="L79">        Long accessIn;</span>
<span class="fc" id="L80">        Long accessOut;</span>
<span class="fc" id="L81">        Boolean accessCompleted;</span>
<span class="fc" id="L82">        List&lt;ConsumerAdaptorResult&gt; consumerAdaptors;</span>
    }

<span class="nc bnc" id="L85" title="All 30 branches missed.">    @Data</span>
<span class="pc" id="L86">    @Builder</span>
<span class="nc" id="L87">    @NoArgsConstructor</span>
<span class="fc" id="L88">    @AllArgsConstructor</span>
    public static class ConsumerAdaptorResult {
<span class="fc" id="L90">        String name;</span>
<span class="fc" id="L91">        Long consumerAdaptorIn;</span>
<span class="fc" id="L92">        Boolean consumerAdaptorCompleted;</span>
    }

    public String toString() {
<span class="fc" id="L96">        return &quot;-------------------------------------------------------\n&quot; +</span>
                &quot;Source Adaptor: \n&quot; +
<span class="fc" id="L98">                sourceAdaptorResult.stream()</span>
<span class="pc bpc" id="L99" title="1 of 2 branches missed.">                        .map(e -&gt; &quot;\t&quot; + (e.getSourceAdaptorIn() != null ? (e.getSourceAdaptorIn() + &quot; -&gt; &quot;) : &quot;&quot;)</span>
<span class="pc bpc" id="L100" title="1 of 2 branches missed.">                                + e.getName() + (Optional.ofNullable(e.getSourceAdaptorCompleted()).orElse(false) ? &quot;(Completed)&quot; : &quot;&quot;) + &quot; -&gt; &quot; + e.getSourceAdaptorOut())</span>
<span class="fc" id="L101">                        .collect(Collectors.joining(&quot;\n&quot;)) + &quot;\n&quot; +</span>
                &quot;-------------------------------------------------------\n&quot; +
                &quot;Pipeline: \n&quot; +
<span class="fc" id="L104">                pipelineResult.entrySet().stream()</span>
<span class="fc" id="L105">                        .map(Map.Entry::getValue)</span>
<span class="fc" id="L106">                        .map(e -&gt; e.getName() + &quot;\n&quot; +</span>
<span class="pc bpc" id="L107" title="1 of 2 branches missed.">                                &quot;\t&quot; + e.getTransformIn() + &quot; -&gt; Transform&quot; + (Optional.ofNullable(e.getTransformCompleted()).orElse(false) ? &quot;(Completed)&quot; : &quot;&quot;) + &quot; -&gt; &quot; + e.getTransformOut() + &quot;\n&quot; +</span>
<span class="pc bpc" id="L108" title="1 of 2 branches missed.">                                &quot;\t&quot; + e.getAccessIn() + &quot; -&gt; Access&quot; + (Optional.ofNullable(e.getAccessCompleted()).orElse(false) ? &quot;(Completed)&quot; : &quot;&quot;) + &quot; -&gt; &quot; + e.getAccessOut() +</span>
<span class="pc bpc" id="L109" title="1 of 2 branches missed.">                                (e.getConsumerAdaptors() == null ? &quot;&quot; : &quot;\n&quot; + e.getConsumerAdaptors().stream()</span>
<span class="pc bpc" id="L110" title="1 of 2 branches missed.">                                        .map(c -&gt; &quot;\t&quot; + c.getConsumerAdaptorIn() + &quot; -&gt; &quot; + &quot;ConsumerAdaptor:&quot; + c.getName() + (Optional.ofNullable(c.getConsumerAdaptorCompleted()).orElse(false) ? &quot;(Completed)&quot; : &quot;&quot;))</span>
<span class="fc" id="L111">                                        .collect(Collectors.joining(&quot;\n&quot;)))</span>
                        )
<span class="fc" id="L113">                        .collect(Collectors.joining(&quot;\n&quot;)) + &quot;\n&quot; +</span>
                &quot;-------------------------------------------------------\n&quot; +
<span class="fc bfc" id="L115" title="All 2 branches covered.">                (consumerAdaptorResult.size() == 0 ? &quot;&quot; : &quot;Consumer Adaptor: \n&quot; +</span>
<span class="fc" id="L116">                        consumerAdaptorResult.stream()</span>
<span class="pc bpc" id="L117" title="1 of 2 branches missed.">                                .map(e -&gt; &quot;\t&quot; + e.getConsumerAdaptorIn() + &quot; -&gt; &quot; + e.getName() + (Optional.ofNullable(e.getConsumerAdaptorCompleted()).orElse(false) ? &quot;(Completed)&quot; : &quot;&quot;))</span>
<span class="fc" id="L118">                                .collect(Collectors.joining(&quot;\n&quot;)) + &quot;\n&quot; +</span>
                        &quot;-------------------------------------------------------\n&quot;) +
                &quot;Cache Table: \n&quot; +
<span class="fc" id="L121">                String.format(&quot;%50s %100s %50s&quot;, &quot;Name&quot;, &quot;Definition&quot;, &quot;Value&quot;) + &quot;\n&quot; +</span>
<span class="fc" id="L122">                cacheList.stream()</span>
<span class="pc" id="L123">                        .sorted(Comparator.comparing(e -&gt; e.getComponent().ordinal() + e.getCacheGroup() + e.getDefinition()))</span>
<span class="fc" id="L124">                        .map(e -&gt; String.format(&quot;%50s %100s %50s&quot;, e.getCacheGroup() + &quot;:&quot; + e.getComponent(), e.getDefinition(), e.getValue()))</span>
<span class="fc" id="L125">                        .collect(Collectors.joining(&quot;\n&quot;)) + &quot;\n&quot; +</span>
                &quot;-------------------------------------------------------\n&quot;
        ;
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>