<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_class">CacheService</span></div><h1>CacheService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">362 of 790</td><td class="ctr2">54%</td><td class="bar">50 of 76</td><td class="ctr2">34%</td><td class="ctr1">34</td><td class="ctr2">59</td><td class="ctr1">65</td><td class="ctr2">154</td><td class="ctr1">5</td><td class="ctr2">21</td></tr></tfoot><tbody><tr><td id="a12"><a href="CacheService.java.html#L61" class="el_method">lambda$getCacheResult$7(String, CacheResult, CacheResult.CacheInfo)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="73" height="10" title="101" alt="101"/><img src="../jacoco-resources/greenbar.gif" width="46" height="10" title="64" alt="64"/></td><td class="ctr2" id="c15">38%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="82" height="10" title="11" alt="11"/><img src="../jacoco-resources/greenbar.gif" width="37" height="10" title="5" alt="5"/></td><td class="ctr2" id="e4">31%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h0">22</td><td class="ctr2" id="i0">35</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="CacheService.java.html#L106" class="el_method">lambda$getCacheResult$4(CacheResult, CacheResult.CacheInfo, String, CacheResponseV3)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="59" height="10" title="82" alt="82"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="CacheService.java.html#L158" class="el_method">lambda$getCacheResult$6(CacheResult, CacheResult.CacheInfo, CacheResult.ConsumerAdaptorResult.ConsumerAdaptorResultBuilder, CacheResponseV3)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="53" alt="53"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h2">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="CacheService.java.html#L140" class="el_method">lambda$getCacheResult$5(CacheResult, CacheResult.CacheInfo, CacheResult.ConsumerAdaptorResult.ConsumerAdaptorResultBuilder, CacheResponseV3)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="38" height="10" title="53" alt="53"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f3">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="CacheService.java.html#L86" class="el_method">lambda$getCacheResult$3(CacheResult, CacheResult.CacheInfo, String, CacheResponseV3)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="24" height="10" title="33" alt="33"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="49" alt="49"/></td><td class="ctr2" id="c14">59%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="7" alt="7"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="3" alt="3"/></td><td class="ctr2" id="e5">30%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h4">4</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="CacheService.java.html#L70" class="el_method">lambda$getCacheResult$2(CacheResult, CacheResult.CacheInfo, CacheResult.SourceAdaptorResult.SourceAdaptorResultBuilder, CacheResponseV3)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="16" height="10" title="22" alt="22"/><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="37" alt="37"/></td><td class="ctr2" id="c13">62%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">37%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h5">3</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="CacheService.java.html#L179" class="el_method">getBooleanValue(CacheResponseV3)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="12" alt="12"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a17"><a href="CacheService.java.html#L190" class="el_method">lambda$updateCache$8(Object)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="5" alt="5"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="CacheService.java.html#L59" class="el_method">lambda$getCacheResult$1(Predicate, CacheResult.CacheInfo)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="6" height="10" title="9" alt="9"/></td><td class="ctr2" id="c12">90%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="22" height="10" title="3" alt="3"/></td><td class="ctr2" id="e2">75%</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a20"><a href="CacheService.java.html#L187" class="el_method">updateCache(String, Unity2Component, String, String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="49" height="10" title="68" alt="68"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d7"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a15"><a href="CacheService.java.html#L218" class="el_method">lambda$resetCache$12(String, AtomicBoolean, CacheResult.CacheInfo)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/greenbar.gif" width="27" height="10" title="38" alt="38"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f10">0</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h10">0</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j10">0</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a18"><a href="CacheService.java.html#L202" class="el_method">resetCache(String, String)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="29" alt="29"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f11">0</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h11">0</td><td class="ctr2" id="i7">9</td><td class="ctr1" id="j11">0</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a16"><a href="CacheService.java.html#L207" class="el_method">lambda$resetCache$9(String)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="24" alt="24"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f12">0</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h12">0</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j12">0</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a5"><a href="CacheService.java.html#L50" class="el_method">lambda$getCacheResult$0(String)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/greenbar.gif" width="17" height="10" title="24" alt="24"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f13">0</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h13">0</td><td class="ctr2" id="i9">8</td><td class="ctr1" id="j13">0</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a2"><a href="CacheService.java.html#L44" class="el_method">getCacheResult(String, Predicate)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/greenbar.gif" width="16" height="10" title="23" alt="23"/></td><td class="ctr2" id="c5">100%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f14">0</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h14">0</td><td class="ctr2" id="i10">8</td><td class="ctr1" id="j14">0</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a14"><a href="CacheService.java.html#L223" class="el_method">lambda$resetCache$11(String, CacheResult.CacheInfo, String, AtomicBoolean, CacheResponseV3)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="21" alt="21"/></td><td class="ctr2" id="c6">100%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f15">0</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h15">0</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j15">0</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a13"><a href="CacheService.java.html#L216" class="el_method">lambda$resetCache$10(String, CacheResult.CacheInfo)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/greenbar.gif" width="10" height="10" title="15" alt="15"/></td><td class="ctr2" id="c7">100%</td><td class="bar" id="d8"><img src="../jacoco-resources/greenbar.gif" width="45" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f16">0</td><td class="ctr2" id="g7">4</td><td class="ctr1" id="h16">0</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j16">0</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a3"><a href="CacheService.java.html#L183" class="el_method">getLongValue(CacheResponseV3)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/greenbar.gif" width="8" height="10" title="12" alt="12"/></td><td class="ctr2" id="c8">100%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">0</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">0</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j17">0</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a4"><a href="CacheService.java.html#L40" class="el_method">init()</a></td><td class="bar" id="b18"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="8" alt="8"/></td><td class="ctr2" id="c9">100%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">0</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">0</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j18">0</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a19"><a href="CacheService.java.html#L23" class="el_method">static {...}</a></td><td class="bar" id="b19"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c10">100%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">0</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h19">0</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j19">0</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a0"><a href="CacheService.java.html#L25" class="el_method">CacheService()</a></td><td class="bar" id="b20"><img src="../jacoco-resources/greenbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">100%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">0</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h20">0</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j20">0</td><td class="ctr2" id="k20">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>