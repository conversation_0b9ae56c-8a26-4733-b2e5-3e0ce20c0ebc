<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CacheService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">CacheService.java</span></div><h1>CacheService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.client.api.CacheV3Api;
import com.poc.hss.fasttrack.client.model.*;
import com.poc.hss.fasttrack.constant.Constants;
import com.poc.hss.fasttrack.metric.factory.MetricFactory;
import com.poc.hss.fasttrack.metric.specification.MetricKey;
import com.poc.hss.fasttrack.model.CacheResult;
import com.poc.hss.fasttrack.model.Unity2Component;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Predicate;

<span class="fc" id="L23">@Slf4j</span>
@Component
<span class="fc" id="L25">public class CacheService {</span>
    @Autowired
    private MetricFactory metricFactory;

    @Autowired
    private CacheV3Api cacheV3Api;

    @Value(&quot;${cache.address:http://cache}/api/v3&quot;)
    private String cacheApiV3Address;

    @Autowired
    private ReconciliationService reconciliationService;

    @PostConstruct
    public void init() {
<span class="fc" id="L40">        cacheV3Api.getApiClient().setBasePath(cacheApiV3Address);</span>
<span class="fc" id="L41">    }</span>

    public CacheResult getCacheResult(String batchId, Predicate&lt;CacheResult.CacheInfo&gt; predicate) {
<span class="fc" id="L44">        Collection&lt;String&gt; cacheNames = cacheV3Api.getCacheNames();</span>
<span class="fc" id="L45">        CacheResult result = new CacheResult();</span>

<span class="fc" id="L47">        cacheNames.stream()</span>
<span class="fc" id="L48">                .sorted()</span>
<span class="fc" id="L49">                .map(e -&gt; {</span>
<span class="fc" id="L50">                    log.debug(&quot;CacheName: {}&quot;, e);</span>
<span class="fc" id="L51">                    String[] parts = e.split(&quot;:&quot;);</span>
<span class="fc" id="L52">                    String cacheGroup = parts[1];</span>
<span class="fc" id="L53">                    String component = parts[2];</span>
<span class="fc" id="L54">                    return CacheResult.CacheInfo.builder()</span>
<span class="fc" id="L55">                            .cacheGroup(cacheGroup)</span>
<span class="fc" id="L56">                            .component(Unity2Component.fromValue(component))</span>
<span class="fc" id="L57">                            .build();</span>
                })
<span class="pc bpc" id="L59" title="1 of 4 branches missed.">                .filter(cacheInfo -&gt; predicate == null || predicate.test(cacheInfo))</span>
<span class="fc" id="L60">                .forEach(cacheInfo -&gt; {</span>
<span class="fc" id="L61">                    final CachePageResponseV3 cachePageResponseV3 = cacheV3Api.searchCache(cacheInfo.getCacheGroup(), cacheInfo.getComponent().toString(), null, batchId, null, true, 0, 1000, null);</span>
<span class="fc" id="L62">                    final List&lt;CacheResponseV3&gt; dataList = cachePageResponseV3.getData();</span>
<span class="fc bfc" id="L63" title="All 2 branches covered.">                    if (dataList.size() == 0)</span>
<span class="fc" id="L64">                        return;</span>
<span class="fc" id="L65">                    String cacheGroup = cacheInfo.getCacheGroup();</span>
<span class="fc bfc" id="L66" title="All 2 branches covered.">                    if (cacheInfo.getComponent() == Unity2Component.SOURCE_ADAPTOR) {</span>
<span class="fc" id="L67">                        final CacheResult.SourceAdaptorResult.SourceAdaptorResultBuilder sourceAdaptorResultBuilder = CacheResult.SourceAdaptorResult.builder()</span>
<span class="fc" id="L68">                                .name(cacheGroup);</span>
<span class="fc" id="L69">                        dataList.forEach(response -&gt; {</span>
<span class="fc" id="L70">                                    result.getCacheList().add(cacheInfo.toBuilder()</span>
<span class="fc" id="L71">                                            .definition(response.getKey())</span>
<span class="pc bpc" id="L72" title="1 of 2 branches missed.">                                            .value(response.getKey().equals(Constants.BATCH_COMPLETION) ? getBooleanValue(response) : getLongValue(response))</span>
<span class="fc" id="L73">                                            .build());</span>
<span class="pc bpc" id="L74" title="1 of 2 branches missed.">                                    if (response.getKey().equals(Constants.MESSAGE_IN)) {</span>
<span class="nc" id="L75">                                        sourceAdaptorResultBuilder.sourceAdaptorIn(getLongValue(response));</span>
<span class="pc bpc" id="L76" title="1 of 2 branches missed.">                                    } else if (response.getKey().equals(Constants.MESSAGE_OUT)) {</span>
<span class="fc" id="L77">                                        sourceAdaptorResultBuilder.sourceAdaptorOut(getLongValue(response));</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">                                    } else if (response.getKey().equals(Constants.BATCH_COMPLETION)) {</span>
<span class="nc" id="L79">                                        sourceAdaptorResultBuilder.sourceAdaptorCompleted(getBooleanValue(response));</span>
                                    }
<span class="fc" id="L81">                                }</span>
                        );
<span class="fc" id="L83">                        result.getSourceAdaptorResult().add(sourceAdaptorResultBuilder.build());</span>
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">                    } else if (cacheInfo.getComponent() == Unity2Component.TRANSFORM) {</span>
<span class="fc" id="L85">                        dataList.forEach(response -&gt; {</span>
<span class="fc" id="L86">                                    result.getCacheList().add(cacheInfo.toBuilder()</span>
<span class="fc" id="L87">                                            .definition(response.getKey())</span>
<span class="pc bpc" id="L88" title="1 of 2 branches missed.">                                            .value(response.getKey().equals(Constants.BATCH_COMPLETION) ? getBooleanValue(response) : getLongValue(response))</span>
<span class="fc" id="L89">                                            .build());</span>
<span class="pc bpc" id="L90" title="1 of 2 branches missed.">                                    if (!result.getPipelineResult().containsKey(cacheGroup)) {</span>
<span class="fc" id="L91">                                        result.getPipelineResult().put(cacheGroup, CacheResult.PipelineResult.builder()</span>
<span class="fc" id="L92">                                                .name(cacheGroup)</span>
<span class="fc" id="L93">                                                .build());</span>
                                    }
<span class="pc bpc" id="L95" title="1 of 2 branches missed.">                                    if (response.getKey().equals(Constants.MESSAGE_IN)) {</span>
<span class="fc" id="L96">                                        result.getPipelineResult().get(cacheGroup).setTransformIn(getLongValue(response));</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">                                    } else if (response.getKey().equals(Constants.MESSAGE_OUT)) {</span>
<span class="nc" id="L98">                                        result.getPipelineResult().get(cacheGroup).setTransformOut(getLongValue(response));</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">                                    } else if (response.getKey().equals(Constants.BATCH_COMPLETION)) {</span>
<span class="nc" id="L100">                                        result.getPipelineResult().get(cacheGroup).setTransformCompleted(getBooleanValue(response));</span>
                                    }
<span class="fc" id="L102">                                }</span>
                        );
<span class="nc bnc" id="L104" title="All 2 branches missed.">                    } else if (cacheInfo.getComponent() == Unity2Component.ACCESS) {</span>
<span class="nc" id="L105">                        dataList.forEach(response -&gt; {</span>
<span class="nc" id="L106">                                    result.getCacheList().add(cacheInfo.toBuilder()</span>
<span class="nc" id="L107">                                            .definition(response.getKey())</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">                                            .value(response.getKey().equals(Constants.BATCH_COMPLETION) ? getBooleanValue(response) : getLongValue(response))</span>
<span class="nc" id="L109">                                            .build());</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">                                    if (!result.getPipelineResult().containsKey(cacheGroup)) {</span>
<span class="nc" id="L111">                                        result.getPipelineResult().put(cacheGroup, CacheResult.PipelineResult.builder()</span>
<span class="nc" id="L112">                                                .name(cacheGroup)</span>
<span class="nc" id="L113">                                                .build());</span>
                                    }
<span class="nc bnc" id="L115" title="All 2 branches missed.">                                    if (response.getKey().equals(Constants.MESSAGE_IN)) {</span>
<span class="nc" id="L116">                                        result.getPipelineResult().get(cacheGroup).setAccessIn(getLongValue(response));</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">                                    } else if (response.getKey().equals(Constants.MESSAGE_OUT)) {</span>
<span class="nc" id="L118">                                        result.getPipelineResult().get(cacheGroup).setAccessOut(getLongValue(response));</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">                                    } else if (response.getKey().equals(Constants.BATCH_COMPLETION)) {</span>
<span class="nc" id="L120">                                        result.getPipelineResult().get(cacheGroup).setAccessCompleted(getBooleanValue(response));</span>
                                    }
<span class="nc" id="L122">                                }</span>
                        );
<span class="nc bnc" id="L124" title="All 2 branches missed.">                    } else if (cacheInfo.getComponent() == Unity2Component.CONSUMER_ADAPTOR) {</span>
<span class="nc bnc" id="L125" title="All 2 branches missed.">                        if (cacheGroup.contains(&quot;.&quot;)) {</span>
<span class="nc" id="L126">                            final String[] split = cacheGroup.split(&quot;\\.&quot;);</span>
<span class="nc" id="L127">                            String pipelineName = split[0];</span>
<span class="nc" id="L128">                            String consumerAdaptorName = split[1];</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">                            if (!result.getPipelineResult().containsKey(pipelineName)) {</span>
<span class="nc" id="L130">                                result.getPipelineResult().put(pipelineName, CacheResult.PipelineResult.builder()</span>
<span class="nc" id="L131">                                        .name(pipelineName)</span>
<span class="nc" id="L132">                                        .build());</span>
                            }
<span class="nc bnc" id="L134" title="All 2 branches missed.">                            if (result.getPipelineResult().get(pipelineName).getConsumerAdaptors() == null) {</span>
<span class="nc" id="L135">                                result.getPipelineResult().get(pipelineName).setConsumerAdaptors(new ArrayList&lt;&gt;());</span>
                            }
<span class="nc" id="L137">                            final CacheResult.ConsumerAdaptorResult.ConsumerAdaptorResultBuilder consumerAdaptorResultBuilder = CacheResult.ConsumerAdaptorResult.builder()</span>
<span class="nc" id="L138">                                    .name(consumerAdaptorName);</span>
<span class="nc" id="L139">                            dataList.forEach(response -&gt; {</span>
<span class="nc" id="L140">                                        result.getCacheList().add(cacheInfo.toBuilder()</span>
<span class="nc" id="L141">                                                .definition(response.getKey())</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">                                                .value(response.getKey().equals(Constants.BATCH_COMPLETION) ? getBooleanValue(response) : getLongValue(response))</span>
<span class="nc" id="L143">                                                .build());</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">                                        if (response.getKey().equals(Constants.MESSAGE_IN)) {</span>
<span class="nc" id="L145">                                            consumerAdaptorResultBuilder.consumerAdaptorIn(getLongValue(response));</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">                                        } else if (response.getKey().equals(Constants.MESSAGE_OUT)) {</span>

<span class="nc bnc" id="L148" title="All 2 branches missed.">                                        } else if (response.getKey().equals(Constants.BATCH_COMPLETION)) {</span>
<span class="nc" id="L149">                                            consumerAdaptorResultBuilder.consumerAdaptorCompleted(getBooleanValue(response));</span>
                                        }
<span class="nc" id="L151">                                    }</span>
                            );
<span class="nc" id="L153">                            result.getPipelineResult().get(pipelineName).getConsumerAdaptors().add(consumerAdaptorResultBuilder.build());</span>
<span class="nc" id="L154">                        } else {</span>
<span class="nc" id="L155">                            final CacheResult.ConsumerAdaptorResult.ConsumerAdaptorResultBuilder consumerAdaptorResultBuilder = CacheResult.ConsumerAdaptorResult.builder()</span>
<span class="nc" id="L156">                                    .name(cacheGroup);</span>
<span class="nc" id="L157">                            dataList.forEach(response -&gt; {</span>
<span class="nc" id="L158">                                        result.getCacheList().add(cacheInfo.toBuilder()</span>
<span class="nc" id="L159">                                                .definition(response.getKey())</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">                                                .value(response.getKey().equals(Constants.BATCH_COMPLETION) ? getBooleanValue(response) : getLongValue(response))</span>
<span class="nc" id="L161">                                                .build());</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">                                        if (response.getKey().equals(Constants.MESSAGE_IN)) {</span>
<span class="nc" id="L163">                                            consumerAdaptorResultBuilder.consumerAdaptorIn(getLongValue(response));</span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">                                        } else if (response.getKey().equals(Constants.MESSAGE_OUT)) {</span>

<span class="nc bnc" id="L166" title="All 2 branches missed.">                                        } else if (response.getKey().equals(Constants.BATCH_COMPLETION)) {</span>
<span class="nc" id="L167">                                            consumerAdaptorResultBuilder.consumerAdaptorCompleted(getBooleanValue(response));</span>
                                        }
<span class="nc" id="L169">                                    }</span>
                            );
<span class="nc" id="L171">                            result.getConsumerAdaptorResult().add(consumerAdaptorResultBuilder.build());</span>
                        }
                    }
<span class="fc" id="L174">                });</span>
<span class="fc" id="L175">        return result;</span>
    }

    private Boolean getBooleanValue(CacheResponseV3 cache) {
<span class="nc" id="L179">        return Optional.ofNullable(cache.getValue()).map(Object::toString).map(Boolean::parseBoolean).orElse(false);</span>
    }

    private Long getLongValue(CacheResponseV3 cache) {
<span class="fc" id="L183">        return Optional.ofNullable(cache.getValue()).map(Object::toString).map(Long::parseLong).orElse(0L);</span>
    }

    public boolean updateCache(String group, Unity2Component component, String definition, String batchId, String value) {
<span class="fc bfc" id="L187" title="All 2 branches covered.">        if (definition.equals(Constants.MESSAGE_IN)) {</span>
<span class="fc" id="L188">            metricFactory.getMetricCounter(group, component, MetricKey.MESSAGE_IN, batchId).setValue(Long.parseLong(value));</span>
<span class="fc bfc" id="L189" title="All 2 branches covered.">        } else if (definition.startsWith(Constants.MESSAGE_OUT)) {</span>
<span class="pc" id="L190">            metricFactory.getMetricCounter(group, component, new MetricKey&lt;&gt;(definition, obj -&gt; Long.parseLong(obj.toString()), 0L), batchId).setValue(Long.parseLong(value));</span>
<span class="fc bfc" id="L191" title="All 2 branches covered.">        } else if (definition.equals(Constants.BATCH_COMPLETION)) {</span>
<span class="fc" id="L192">            metricFactory.getMetricValue(group, component, MetricKey.BATCH_COMPLETION_FLAG, batchId).setValue(Boolean.parseBoolean(value));</span>
        } else {
<span class="fc" id="L194">            log.error(&quot;Invalid definition&quot;);</span>
<span class="fc" id="L195">            return false;</span>
        }
<span class="fc" id="L197">        reconciliationService.reconcile(group, component.toString(), batchId);</span>
<span class="fc" id="L198">        return true;</span>
    }

    public boolean resetCache(String batchId, String pipeline) {
<span class="fc" id="L202">        final AtomicBoolean reset = new AtomicBoolean(false);</span>
<span class="fc" id="L203">        Collection&lt;String&gt; cacheNames = cacheV3Api.getCacheNames();</span>
<span class="fc" id="L204">        cacheNames.stream()</span>
<span class="fc" id="L205">                .sorted()</span>
<span class="fc" id="L206">                .map(e -&gt; {</span>
<span class="fc" id="L207">                    log.debug(&quot;CacheName: {}&quot;, e);</span>
<span class="fc" id="L208">                    String[] parts = e.split(&quot;:&quot;);</span>
<span class="fc" id="L209">                    String cacheGroup = parts[1];</span>
<span class="fc" id="L210">                    String component = parts[2];</span>
<span class="fc" id="L211">                    return CacheResult.CacheInfo.builder()</span>
<span class="fc" id="L212">                            .cacheGroup(cacheGroup)</span>
<span class="fc" id="L213">                            .component(Unity2Component.fromValue(component))</span>
<span class="fc" id="L214">                            .build();</span>
                })
<span class="fc bfc" id="L216" title="All 6 branches covered.">                .filter(cacheInfo -&gt; pipeline == null || cacheInfo.getCacheGroup().contains(pipeline) || cacheInfo.getComponent() == Unity2Component.SOURCE_ADAPTOR)</span>
<span class="fc" id="L217">                .forEach(cacheInfo -&gt; {</span>
<span class="fc" id="L218">                    String cacheName = cacheInfo.getCacheGroup() + &quot;:&quot; + cacheInfo.getComponent();</span>

<span class="fc" id="L220">                    final CachePageResponseV3 cachePageResponseV3 = cacheV3Api.searchCache(cacheInfo.getCacheGroup(), cacheInfo.getComponent().toString(), null, batchId, null, true, 0, 1000, null);</span>
<span class="fc" id="L221">                    final List&lt;CacheResponseV3&gt; dataList = cachePageResponseV3.getData();</span>
<span class="fc" id="L222">                    dataList.forEach(response -&gt; {</span>
<span class="fc" id="L223">                                log.info(&quot;Reset cache, name: {}, definition: {}&quot;, cacheName, response.getKey());</span>
<span class="fc" id="L224">                                cacheV3Api.evictCache(cacheInfo.getCacheGroup(), cacheInfo.getComponent().toString(), response.getKey(), batchId);</span>
<span class="fc" id="L225">                                reset.set(true);</span>
<span class="fc" id="L226">                            }</span>

                    );
<span class="fc" id="L229">                });</span>
<span class="fc" id="L230">        reconciliationService.reset(batchId);</span>
<span class="fc" id="L231">        return reset.get();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>