<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GenericKafkaService</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_class">GenericKafkaService</span></div><h1>GenericKafkaService</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,542 of 1,542</td><td class="ctr2">0%</td><td class="bar">146 of 146</td><td class="ctr2">0%</td><td class="ctr1">128</td><td class="ctr2">128</td><td class="ctr1">173</td><td class="ctr2">173</td><td class="ctr1">55</td><td class="ctr2">55</td></tr></tfoot><tbody><tr><td id="a50"><a href="GenericKafkaService.java.html#L116" class="el_method">scanAllMessagesWithResult(Consumer, LocalDateTime, LocalDateTime, Integer, Integer, Long, Long, String, String)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="238" alt="238"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h0">47</td><td class="ctr2" id="i0">47</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a48"><a href="GenericKafkaService.java.html#L184" class="el_method">scanAllMessagesWithCount(Consumer, LocalDateTime, LocalDateTime, Integer, Integer, Long, Long, String, Function, String)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="115" height="10" title="230" alt="230"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h1">45</td><td class="ctr2" id="i1">45</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a52"><a href="GenericKafkaService.java.html#L258" class="el_method">seekTo(Consumer, String, Integer, Long, Long)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="93" height="10" title="185" alt="185"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h2">21</td><td class="ctr2" id="i2">21</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a12"><a href="GenericKafkaService.java.html#L206" class="el_method">lambda$scanAllMessagesWithCount$26(Integer, Map, Map, Map, Long, int)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="65" alt="65"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d9"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a41"><a href="GenericKafkaService.java.html#L139" class="el_method">lambda$scanAllMessagesWithResult$7(Integer, Map, Map, Map, Long, int)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="32" height="10" title="65" alt="65"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d10"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h9">3</td><td class="ctr2" id="i9">3</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a46"><a href="GenericKafkaService.java.html#L47" class="el_method">resetOffset(Consumer, String, Integer, Long, Boolean, Boolean, Boolean, Boolean)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="30" height="10" title="61" alt="61"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a23"><a href="GenericKafkaService.java.html#L244" class="el_method">lambda$scanAllMessagesWithCount$37(Integer, Map, Map, Map, Long, int)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="52" alt="52"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="10" alt="10"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a34"><a href="GenericKafkaService.java.html#L170" class="el_method">lambda$scanAllMessagesWithResult$17(Integer, Map, Map, Map, Long, int)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="52" alt="52"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="10" alt="10"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f4">6</td><td class="ctr2" id="g4">6</td><td class="ctr1" id="h11">3</td><td class="ctr2" id="i11">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a49"><a href="GenericKafkaService.java.html#L104" class="el_method">scanAllMessagesWithCount(LocalDateTime, LocalDateTime, Integer, Integer, Long, Long, String, Function, String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="23" height="10" title="46" alt="46"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a51"><a href="GenericKafkaService.java.html#L88" class="el_method">scanAllMessagesWithResult(LocalDateTime, LocalDateTime, Integer, Integer, Long, Long, String, String)</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="45" alt="45"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d27"/><td class="ctr2" id="e27">n/a</td><td class="ctr1" id="f27">1</td><td class="ctr2" id="g27">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a54"><a href="GenericKafkaService.java.html#L289" class="el_method">waitForAssignment(Consumer, String)</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="22" height="10" title="45" alt="45"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a22"><a href="GenericKafkaService.java.html#L236" class="el_method">lambda$scanAllMessagesWithCount$36(Function, Map, KafkaMessageDTO)</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="28" alt="28"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d21"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a15"><a href="GenericKafkaService.java.html#L227" class="el_method">lambda$scanAllMessagesWithCount$29(Integer, Map, Map, KafkaMessageDTO)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="28" alt="28"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d11"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f11">3</td><td class="ctr2" id="g11">3</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a27"><a href="GenericKafkaService.java.html#L160" class="el_method">lambda$scanAllMessagesWithResult$10(Integer, Map, Map, KafkaMessageDTO)</a></td><td class="bar" id="b13"><img src="../jacoco-resources/redbar.gif" width="14" height="10" title="28" alt="28"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d12"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f12">3</td><td class="ctr2" id="g12">3</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a20"><a href="GenericKafkaService.java.html#L232" class="el_method">lambda$scanAllMessagesWithCount$34(Map, KafkaMessageDTO)</a></td><td class="bar" id="b14"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d7"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f7">4</td><td class="ctr2" id="g7">4</td><td class="ctr1" id="h19">1</td><td class="ctr2" id="i19">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a32"><a href="GenericKafkaService.java.html#L165" class="el_method">lambda$scanAllMessagesWithResult$15(Map, KafkaMessageDTO)</a></td><td class="bar" id="b15"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="26" alt="26"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d8"><img src="../jacoco-resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f8">4</td><td class="ctr2" id="g8">4</td><td class="ctr1" id="h20">1</td><td class="ctr2" id="i20">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a47"><a href="GenericKafkaService.java.html#L41" class="el_method">resetOffset(String, String, Integer, Long, Boolean, Boolean, Boolean, Boolean)</a></td><td class="bar" id="b16"><img src="../jacoco-resources/redbar.gif" width="9" height="10" title="18" alt="18"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d28"/><td class="ctr2" id="e28">n/a</td><td class="ctr1" id="f28">1</td><td class="ctr2" id="g28">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a24"><a href="GenericKafkaService.java.html#L252" class="el_method">lambda$scanAllMessagesWithCount$38(Map, Map, int)</a></td><td class="bar" id="b17"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d29"/><td class="ctr2" id="e29">n/a</td><td class="ctr1" id="f29">1</td><td class="ctr2" id="g29">1</td><td class="ctr1" id="h21">1</td><td class="ctr2" id="i21">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a13"><a href="GenericKafkaService.java.html#L220" class="el_method">lambda$scanAllMessagesWithCount$27(Map, Map, int)</a></td><td class="bar" id="b18"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d30"/><td class="ctr2" id="e30">n/a</td><td class="ctr1" id="f30">1</td><td class="ctr2" id="g30">1</td><td class="ctr1" id="h22">1</td><td class="ctr2" id="i22">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a35"><a href="GenericKafkaService.java.html#L178" class="el_method">lambda$scanAllMessagesWithResult$18(Map, Map, int)</a></td><td class="bar" id="b19"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d31"/><td class="ctr2" id="e31">n/a</td><td class="ctr1" id="f31">1</td><td class="ctr2" id="g31">1</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a42"><a href="GenericKafkaService.java.html#L153" class="el_method">lambda$scanAllMessagesWithResult$8(Map, Map, int)</a></td><td class="bar" id="b20"><img src="../jacoco-resources/redbar.gif" width="7" height="10" title="14" alt="14"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d32"/><td class="ctr2" id="e32">n/a</td><td class="ctr1" id="f32">1</td><td class="ctr2" id="g32">1</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a19"><a href="GenericKafkaService.java.html#L231" class="el_method">lambda$scanAllMessagesWithCount$33(Long, KafkaMessageDTO)</a></td><td class="bar" id="b21"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d13"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f13">3</td><td class="ctr2" id="g13">3</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a18"><a href="GenericKafkaService.java.html#L230" class="el_method">lambda$scanAllMessagesWithCount$32(Long, KafkaMessageDTO)</a></td><td class="bar" id="b22"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d14"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f14">3</td><td class="ctr2" id="g14">3</td><td class="ctr1" id="h26">1</td><td class="ctr2" id="i26">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a31"><a href="GenericKafkaService.java.html#L164" class="el_method">lambda$scanAllMessagesWithResult$14(Long, KafkaMessageDTO)</a></td><td class="bar" id="b23"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d15"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f15">3</td><td class="ctr2" id="g15">3</td><td class="ctr1" id="h27">1</td><td class="ctr2" id="i27">1</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a30"><a href="GenericKafkaService.java.html#L163" class="el_method">lambda$scanAllMessagesWithResult$13(Long, KafkaMessageDTO)</a></td><td class="bar" id="b24"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="13" alt="13"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d16"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f16">3</td><td class="ctr2" id="g16">3</td><td class="ctr1" id="h28">1</td><td class="ctr2" id="i28">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a1"><a href="GenericKafkaService.java.html#L303" class="el_method">checkOffset(String, String)</a></td><td class="bar" id="b25"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d33"/><td class="ctr2" id="e33">n/a</td><td class="ctr1" id="f33">1</td><td class="ctr2" id="g33">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a17"><a href="GenericKafkaService.java.html#L229" class="el_method">lambda$scanAllMessagesWithCount$31(String, KafkaMessageDTO)</a></td><td class="bar" id="b26"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d17"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g17">3</td><td class="ctr1" id="h29">1</td><td class="ctr2" id="i29">1</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a16"><a href="GenericKafkaService.java.html#L228" class="el_method">lambda$scanAllMessagesWithCount$30(Integer, KafkaMessageDTO)</a></td><td class="bar" id="b27"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d18"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g18">3</td><td class="ctr1" id="h30">1</td><td class="ctr2" id="i30">1</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a14"><a href="GenericKafkaService.java.html#L225" class="el_method">lambda$scanAllMessagesWithCount$28(Map, KafkaMessageDTO)</a></td><td class="bar" id="b28"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d34"/><td class="ctr2" id="e34">n/a</td><td class="ctr1" id="f34">1</td><td class="ctr2" id="g34">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a29"><a href="GenericKafkaService.java.html#L162" class="el_method">lambda$scanAllMessagesWithResult$12(String, KafkaMessageDTO)</a></td><td class="bar" id="b29"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d19"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h31">1</td><td class="ctr2" id="i31">1</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a28"><a href="GenericKafkaService.java.html#L161" class="el_method">lambda$scanAllMessagesWithResult$11(Integer, KafkaMessageDTO)</a></td><td class="bar" id="b30"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d20"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h32">1</td><td class="ctr2" id="i32">1</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a43"><a href="GenericKafkaService.java.html#L158" class="el_method">lambda$scanAllMessagesWithResult$9(Map, KafkaMessageDTO)</a></td><td class="bar" id="b31"><img src="../jacoco-resources/redbar.gif" width="6" height="10" title="12" alt="12"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d35"/><td class="ctr2" id="e35">n/a</td><td class="ctr1" id="f35">1</td><td class="ctr2" id="g35">1</td><td class="ctr1" id="h16">2</td><td class="ctr2" id="i16">2</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a3"><a href="GenericKafkaService.java.html#L313" class="el_method">getBatchId(KafkaMessageDTO)</a></td><td class="bar" id="b32"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="9" alt="9"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d36"/><td class="ctr2" id="e36">n/a</td><td class="ctr1" id="f36">1</td><td class="ctr2" id="g36">1</td><td class="ctr1" id="h12">3</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a11"><a href="GenericKafkaService.java.html#L208" class="el_method">lambda$scanAllMessagesWithCount$25(long)</a></td><td class="bar" id="b33"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d22"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h33">1</td><td class="ctr2" id="i33">1</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a40"><a href="GenericKafkaService.java.html#L141" class="el_method">lambda$scanAllMessagesWithResult$6(long)</a></td><td class="bar" id="b34"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d23"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h34">1</td><td class="ctr2" id="i34">1</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a8"><a href="GenericKafkaService.java.html#L197" class="el_method">lambda$scanAllMessagesWithCount$22(Map.Entry)</a></td><td class="bar" id="b35"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d24"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h35">1</td><td class="ctr2" id="i35">1</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a37"><a href="GenericKafkaService.java.html#L130" class="el_method">lambda$scanAllMessagesWithResult$3(Map.Entry)</a></td><td class="bar" id="b36"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d25"><img src="../jacoco-resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h36">1</td><td class="ctr2" id="i36">1</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a10"><a href="GenericKafkaService.java.html#L198" class="el_method">lambda$scanAllMessagesWithCount$24(Map.Entry)</a></td><td class="bar" id="b37"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h37">1</td><td class="ctr2" id="i37">1</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a9"><a href="GenericKafkaService.java.html#L198" class="el_method">lambda$scanAllMessagesWithCount$23(Map.Entry)</a></td><td class="bar" id="b38"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h38">1</td><td class="ctr2" id="i38">1</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a5"><a href="GenericKafkaService.java.html#L192" class="el_method">lambda$scanAllMessagesWithCount$19(Map.Entry)</a></td><td class="bar" id="b39"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h39">1</td><td class="ctr2" id="i39">1</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a39"><a href="GenericKafkaService.java.html#L131" class="el_method">lambda$scanAllMessagesWithResult$5(Map.Entry)</a></td><td class="bar" id="b40"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h40">1</td><td class="ctr2" id="i40">1</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a38"><a href="GenericKafkaService.java.html#L131" class="el_method">lambda$scanAllMessagesWithResult$4(Map.Entry)</a></td><td class="bar" id="b41"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h41">1</td><td class="ctr2" id="i41">1</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a25"><a href="GenericKafkaService.java.html#L125" class="el_method">lambda$scanAllMessagesWithResult$0(Map.Entry)</a></td><td class="bar" id="b42"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h42">1</td><td class="ctr2" id="i42">1</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a21"><a href="GenericKafkaService.java.html#L233" class="el_method">lambda$scanAllMessagesWithCount$35(KafkaMessageDTO)</a></td><td class="bar" id="b43"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h43">1</td><td class="ctr2" id="i43">1</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a33"><a href="GenericKafkaService.java.html#L166" class="el_method">lambda$scanAllMessagesWithResult$16(KafkaMessageDTO)</a></td><td class="bar" id="b44"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h44">1</td><td class="ctr2" id="i44">1</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a0"><a href="GenericKafkaService.java.html#L309" class="el_method">checkOffset(Consumer, String)</a></td><td class="bar" id="b45"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h45">1</td><td class="ctr2" id="i45">1</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a4"><a href="GenericKafkaService.java.html#L314" class="el_method">lambda$getBatchId$41(JsonObject)</a></td><td class="bar" id="b46"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h46">1</td><td class="ctr2" id="i46">1</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a53"><a href="GenericKafkaService.java.html#L28" class="el_method">static {...}</a></td><td class="bar" id="b47"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h47">1</td><td class="ctr2" id="i47">1</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a2"><a href="GenericKafkaService.java.html#L30" class="el_method">GenericKafkaService()</a></td><td class="bar" id="b48"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h48">1</td><td class="ctr2" id="i48">1</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a45"><a href="GenericKafkaService.java.html#L262" class="el_method">lambda$seekTo$40(Long, TopicPartition)</a></td><td class="bar" id="b49"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h49">1</td><td class="ctr2" id="i49">1</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a44"><a href="GenericKafkaService.java.html#L262" class="el_method">lambda$seekTo$39(TopicPartition)</a></td><td class="bar" id="b50"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h50">1</td><td class="ctr2" id="i50">1</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a7"><a href="GenericKafkaService.java.html#L194" class="el_method">lambda$scanAllMessagesWithCount$21(Long, TopicPartition)</a></td><td class="bar" id="b51"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h51">1</td><td class="ctr2" id="i51">1</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a6"><a href="GenericKafkaService.java.html#L194" class="el_method">lambda$scanAllMessagesWithCount$20(TopicPartition)</a></td><td class="bar" id="b52"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h52">1</td><td class="ctr2" id="i52">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a36"><a href="GenericKafkaService.java.html#L127" class="el_method">lambda$scanAllMessagesWithResult$2(Long, TopicPartition)</a></td><td class="bar" id="b53"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h53">1</td><td class="ctr2" id="i53">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a26"><a href="GenericKafkaService.java.html#L127" class="el_method">lambda$scanAllMessagesWithResult$1(TopicPartition)</a></td><td class="bar" id="b54"><img src="../jacoco-resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h54">1</td><td class="ctr2" id="i54">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>