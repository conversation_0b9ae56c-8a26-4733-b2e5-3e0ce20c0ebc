<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>GenericKafkaService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">GenericKafkaService.java</span></div><h1>GenericKafkaService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.config.ConsumerProvider;
import com.poc.hss.fasttrack.config.KafkaProducerManager;
import com.poc.hss.fasttrack.constant.Constants;
import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.kafka.util.KafkaUtil;
import com.poc.hss.fasttrack.util.ConversionHelperService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.Consumer;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.OffsetAndTimestamp;
import org.apache.kafka.common.TopicPartition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.LongStream;
import java.util.stream.StreamSupport;

<span class="nc" id="L28">@Slf4j</span>
@Component
<span class="nc" id="L30">public class GenericKafkaService {</span>
    @Autowired
    private KafkaProducerManager kafkaProducerManager;

    @Autowired
    private ConversionHelperService conversionHelperService;

    @Autowired
    private ConsumerProvider consumerProvider;

    public boolean resetOffset(String topic, String consumerGroup, Integer partition, Long offset, Boolean toLatest, Boolean toBeginning, Boolean ifNull, Boolean allPartitions) {
<span class="nc" id="L41">        try (Consumer consumer = consumerProvider.getConsumer(consumerGroup)) {</span>
<span class="nc" id="L42">            return resetOffset(consumer, topic, partition, offset, toLatest, toBeginning, ifNull, allPartitions);</span>
        }
    }

    public boolean resetOffset(Consumer&lt;Object, Object&gt; consumer, String topic, Integer partition, Long offset, Boolean toLatest, Boolean toBeginning, Boolean ifNull, Boolean allPartitions) {
<span class="nc" id="L47">        AtomicInteger resetDone = new AtomicInteger(0);</span>
<span class="nc" id="L48">        consumer.subscribe(Collections.singletonList(topic), new ConsumerRebalanceListener() {</span>

            @Override
            public void onPartitionsRevoked(Collection&lt;TopicPartition&gt; collection) {
<span class="nc" id="L52">            }</span>

            @Override
            public void onPartitionsAssigned(Collection&lt;TopicPartition&gt; collection) {
<span class="nc" id="L56">                final List&lt;KafkaUtil.Metric&gt; metrics = KafkaUtil.getMetrics(consumer, topic);</span>

<span class="nc bnc" id="L58" title="All 2 branches missed.">                for (TopicPartition tp : collection) {</span>
<span class="nc" id="L59">                    final KafkaUtil.Metric metric = metrics.get(tp.partition());</span>
<span class="nc" id="L60">                    Long newOffset = offset;</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">                    if (toLatest)</span>
<span class="nc" id="L62">                        newOffset = metric.getEndOffset();</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">                    else if (toBeginning)</span>
<span class="nc" id="L64">                        newOffset = metric.getBeginningOffset();</span>

<span class="nc bnc" id="L66" title="All 4 branches missed.">                    if (allPartitions || (tp.partition() == partition) &amp;&amp;</span>
<span class="nc bnc" id="L67" title="All 4 branches missed.">                            (!ifNull || metric.getLag() == null)) {</span>
<span class="nc" id="L68">                        log.info(&quot;Topic: {}, Partition: {}, Original Offset: {}, New Offset: {}&quot;, tp.topic(), tp.partition(), metric.getCommittedOffset(), newOffset);</span>
<span class="nc" id="L69">                        consumer.seek(tp, newOffset);</span>
<span class="nc" id="L70">                        consumer.commitAsync();</span>
                    } else {
<span class="nc" id="L72">                        log.info(&quot;Topic: {}, Partition: {}, Original Offset: {}, New Offset: {}&quot;, tp.topic(), tp.partition(), metric.getCommittedOffset(), &quot;unchanged&quot;);</span>
                    }
<span class="nc" id="L74">                    resetDone.incrementAndGet();</span>
<span class="nc" id="L75">                }</span>
<span class="nc" id="L76">            }</span>
        });
<span class="nc" id="L78">        int max = 12 * 5, count = 0;</span>
<span class="nc" id="L79">        int totalPartition = consumer.partitionsFor(topic).size();</span>
<span class="nc bnc" id="L80" title="All 4 branches missed.">        while (resetDone.get() &lt; totalPartition &amp;&amp; count++ &lt; max) {</span>
<span class="nc" id="L81">            log.info(&quot;Waiting partition assignment, check if the related pods are running, reset done: {}, total partitions: {}&quot;, resetDone.get(), totalPartition);</span>
<span class="nc" id="L82">            consumer.poll(Duration.ofSeconds(5));</span>
        }
<span class="nc bnc" id="L84" title="All 2 branches missed.">        return resetDone.get() == totalPartition;</span>
    }

    public List&lt;KafkaMessageDTO&gt; scanAllMessagesWithResult(LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword, String topic) {
<span class="nc" id="L88">        String consumerGroup = UUID.randomUUID().toString();</span>
<span class="nc" id="L89">        final Consumer&lt;String, Object&gt; consumer = consumerProvider.getConsumer(consumerGroup);</span>
<span class="nc" id="L90">        consumer.subscribe(Collections.singletonList(topic));</span>
<span class="nc" id="L91">        log.info(&quot;Start scanning message, Consumer Group: {}, Topic: {}, partition: {}&quot;, consumerGroup, topic, partition);</span>
        try {
<span class="nc" id="L93">            return scanAllMessagesWithResult(consumer, fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword, topic);</span>
        } finally {
<span class="nc" id="L95">            consumer.close();</span>
        }
    }

    public Map&lt;String, Long&gt; scanAllMessagesWithCount(LocalDateTime fromTimestamp, LocalDateTime toTimestamp,
                                                      Integer maxRecords, Integer partition,
                                                      Long fromOffset, Long toOffset,
                                                      String keyword, Function&lt;KafkaMessageDTO, String&gt; keyMappers,
                                                      String topic) {
<span class="nc" id="L104">        String consumerGroup = UUID.randomUUID().toString();</span>
<span class="nc" id="L105">        final Consumer&lt;String, Object&gt; consumer = consumerProvider.getConsumer(consumerGroup);</span>
<span class="nc" id="L106">        consumer.subscribe(Collections.singletonList(topic));</span>
<span class="nc" id="L107">        log.info(&quot;Start scanning message, Consumer Group: {}, Topic: {}, partition: {}&quot;, consumerGroup, topic, partition);</span>
        try {
<span class="nc" id="L109">            return scanAllMessagesWithCount(consumer, fromTimestamp, toTimestamp, maxRecords, partition, fromOffset, toOffset, keyword, keyMappers, topic);</span>
        } finally {
<span class="nc" id="L111">            consumer.close();</span>
        }
    }

    public List&lt;KafkaMessageDTO&gt; scanAllMessagesWithResult(Consumer&lt;String, Object&gt; consumer, LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword, String topic) {
<span class="nc bnc" id="L116" title="All 2 branches missed.">        if (keyword != null)</span>
<span class="nc" id="L117">            log.info(&quot;keyword: {}&quot;, keyword);</span>
<span class="nc" id="L118">        long start = System.currentTimeMillis();</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">        Long fromDateMills = fromTimestamp == null ? null : fromTimestamp.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">        Long toDateMills = toTimestamp == null ? null : toTimestamp.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();</span>

<span class="nc" id="L122">        Map&lt;Integer, Long&gt; initialOffsetMap = seekTo(consumer, topic, partition, fromOffset, fromDateMills);</span>
<span class="nc" id="L123">        Map&lt;Integer, Long&gt; currentOffsetMap = new HashMap&lt;&gt;(initialOffsetMap);</span>
<span class="nc" id="L124">        final Map&lt;Integer, Long&gt; endOffsets = consumer.endOffsets(consumer.assignment()).entrySet().stream()</span>
<span class="nc" id="L125">                .collect(Collectors.toMap(e -&gt; e.getKey().partition(), Map.Entry::getValue));</span>
<span class="nc bnc" id="L126" title="All 2 branches missed.">        final Map&lt;Integer, Long&gt; toDateOffsetMap = toDateMills == null ? null : consumer</span>
<span class="nc" id="L127">                .offsetsForTimes(consumer.assignment().stream().collect(Collectors.toMap(e -&gt; e, e -&gt; toDateMills)))</span>
<span class="nc" id="L128">                .entrySet()</span>
<span class="nc" id="L129">                .stream()</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">                .filter(e -&gt; e.getValue() != null)</span>
<span class="nc" id="L131">                .collect(Collectors.toMap(e -&gt; e.getKey().partition(), e -&gt; e.getValue().offset()));</span>

<span class="nc" id="L133">        int totalPartition = consumer.partitionsFor(topic).size();</span>
<span class="nc" id="L134">        List&lt;KafkaMessageDTO&gt; foundRecords = new ArrayList&lt;&gt;();</span>
        long different;
        while (true) {
<span class="nc" id="L137">            different = IntStream.range(0, totalPartition)</span>
<span class="nc" id="L138">                    .mapToLong(</span>
<span class="nc bnc" id="L139" title="All 2 branches missed.">                            e -&gt; LongStream.of(maxRecords == null ? -1 : maxRecords - (currentOffsetMap.get(e) - initialOffsetMap.get(e)),</span>
<span class="nc" id="L140">                                    endOffsets.get(e) - currentOffsetMap.get(e),</span>
<span class="nc bnc" id="L141" title="All 4 branches missed.">                                    toOffset == null ? -1 : toOffset - currentOffsetMap.get(e)).filter(f -&gt; f &gt;= 0).min().orElse(0)</span>
<span class="nc" id="L142">                    ).sum();</span>
<span class="nc" id="L143">            List&lt;ConsumerRecord&gt; records = StreamSupport</span>
<span class="nc" id="L144">                    .stream(consumer.poll(Duration.ofSeconds(10)).spliterator(), false)</span>
<span class="nc" id="L145">                    .collect(Collectors.toList());</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">            if (records.isEmpty()) {</span>
<span class="nc" id="L147">                log.info(&quot;No records polled, different: {}&quot;, different);</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">                if (different &gt; 16)</span>
<span class="nc" id="L149">                    continue;</span>
                else
                    break;
            }
<span class="nc" id="L153">            Long totalScanned = IntStream.range(0, totalPartition).mapToLong(e -&gt; currentOffsetMap.get(e) - initialOffsetMap.get(e)).sum();</span>
<span class="nc" id="L154">            log.debug(&quot;partition: {}, offset: {}, scanned: {}&quot;, records.get(0).partition(), records.get(0).offset(), totalScanned);</span>
<span class="nc" id="L155">            final List&lt;KafkaMessageDTO&gt; kafkaMessages = conversionHelperService.convertList(records, KafkaMessageDTO.class);</span>
<span class="nc" id="L156">            foundRecords.addAll(kafkaMessages.stream()</span>
<span class="nc" id="L157">                    .peek(rec -&gt; {</span>
<span class="nc" id="L158">                        currentOffsetMap.put(rec.getConsumerRecord().partition(), rec.getConsumerRecord().offset());</span>
<span class="nc" id="L159">                    })</span>
<span class="nc bnc" id="L160" title="All 4 branches missed.">                    .filter(rec -&gt; maxRecords == null || currentOffsetMap.get(rec.getConsumerRecord().partition()) - initialOffsetMap.get(rec.getConsumerRecord().partition()) &lt; maxRecords)</span>
<span class="nc bnc" id="L161" title="All 4 branches missed.">                    .filter(rec -&gt; partition == null || partition == rec.getConsumerRecord().partition())</span>
<span class="nc bnc" id="L162" title="All 4 branches missed.">                    .filter(rec -&gt; keyword == null || rec.getPayload().toString().contains(keyword))</span>
<span class="nc bnc" id="L163" title="All 4 branches missed.">                    .filter(rec -&gt; fromOffset == null || rec.getConsumerRecord().offset() &gt;= fromOffset)</span>
<span class="nc bnc" id="L164" title="All 4 branches missed.">                    .filter(rec -&gt; toOffset == null || rec.getConsumerRecord().offset() &lt;= toOffset)</span>
<span class="nc bnc" id="L165" title="All 6 branches missed.">                    .filter(rec -&gt; toDateOffsetMap == null || !toDateOffsetMap.containsKey(rec.getConsumerRecord().partition()) || rec.getConsumerRecord().offset() &lt;= toDateOffsetMap.get(rec.getConsumerRecord().partition()))</span>
<span class="nc" id="L166">                    .peek(e -&gt; log.debug(e.toString()))</span>
<span class="nc" id="L167">                    .collect(Collectors.toList()));</span>

<span class="nc" id="L169">            boolean allCompleted = IntStream.range(0, totalPartition).allMatch(</span>
<span class="nc bnc" id="L170" title="All 4 branches missed.">                    e -&gt; maxRecords != null &amp;&amp; currentOffsetMap.get(e) - initialOffsetMap.get(e) &gt; maxRecords ||</span>
<span class="nc bnc" id="L171" title="All 4 branches missed.">                            currentOffsetMap.get(e).equals(endOffsets.get(e) - 1) ||</span>
<span class="nc bnc" id="L172" title="All 2 branches missed.">                            toOffset != null &amp;&amp; currentOffsetMap.get(e) &gt; toOffset</span>
            );
<span class="nc bnc" id="L174" title="All 2 branches missed.">            if (allCompleted) {</span>
<span class="nc" id="L175">                break;</span>
            }
<span class="nc" id="L177">        }</span>
<span class="nc" id="L178">        Long totalScanned = IntStream.range(0, totalPartition).mapToLong(e -&gt; currentOffsetMap.get(e) - initialOffsetMap.get(e)).sum();</span>
<span class="nc" id="L179">        log.info(&quot;Total scanned size: {}, it takes {} ms&quot;, totalScanned, System.currentTimeMillis() - start);</span>
<span class="nc" id="L180">        return foundRecords;</span>
    }

    public Map&lt;String, Long&gt; scanAllMessagesWithCount(Consumer&lt;String, Object&gt; consumer, LocalDateTime fromTimestamp, LocalDateTime toTimestamp, Integer maxRecords, Integer partition, Long fromOffset, Long toOffset, String keyword, Function&lt;KafkaMessageDTO, String&gt; keyMappers, String topic) {
<span class="nc" id="L184">        long start = System.currentTimeMillis();</span>

<span class="nc bnc" id="L186" title="All 2 branches missed.">        Long fromDateMills = fromTimestamp == null ? null : fromTimestamp.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">        Long toDateMills = toTimestamp == null ? null : toTimestamp.atZone(TimeZone.getDefault().toZoneId()).toInstant().toEpochMilli();</span>

<span class="nc" id="L189">        Map&lt;Integer, Long&gt; initialOffsetMap = seekTo(consumer, topic, partition, fromOffset, fromDateMills);</span>
<span class="nc" id="L190">        Map&lt;Integer, Long&gt; currentOffsetMap = new HashMap&lt;&gt;(initialOffsetMap);</span>
<span class="nc" id="L191">        final Map&lt;Integer, Long&gt; endOffsets = consumer.endOffsets(consumer.assignment()).entrySet().stream()</span>
<span class="nc" id="L192">                .collect(Collectors.toMap(e -&gt; e.getKey().partition(), Map.Entry::getValue));</span>
<span class="nc bnc" id="L193" title="All 2 branches missed.">        final Map&lt;Integer, Long&gt; toDateOffsetMap = toDateMills == null ? null : consumer</span>
<span class="nc" id="L194">                .offsetsForTimes(consumer.assignment().stream().collect(Collectors.toMap(e -&gt; e, e -&gt; toDateMills)))</span>
<span class="nc" id="L195">                .entrySet()</span>
<span class="nc" id="L196">                .stream()</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">                .filter(e -&gt; e.getValue() != null)</span>
<span class="nc" id="L198">                .collect(Collectors.toMap(e -&gt; e.getKey().partition(), e -&gt; e.getValue().offset()));</span>

<span class="nc" id="L200">        int totalPartition = consumer.partitionsFor(topic).size();</span>
<span class="nc" id="L201">        Map&lt;String, Long&gt; resultMap = new HashMap&lt;&gt;();</span>
        long different;
        while (true) {
<span class="nc" id="L204">            different = IntStream.range(0, totalPartition)</span>
<span class="nc" id="L205">                    .mapToLong(</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">                            e -&gt; LongStream.of(maxRecords == null ? -1 : maxRecords - (currentOffsetMap.get(e) - initialOffsetMap.get(e)),</span>
<span class="nc" id="L207">                                    endOffsets.get(e) - currentOffsetMap.get(e),</span>
<span class="nc bnc" id="L208" title="All 4 branches missed.">                                    toOffset == null ? -1 : toOffset - currentOffsetMap.get(e)).filter(f -&gt; f &gt;= 0).min().orElse(0)</span>
<span class="nc" id="L209">                    ).sum();</span>
<span class="nc" id="L210">            List&lt;ConsumerRecord&gt; records = StreamSupport</span>
<span class="nc" id="L211">                    .stream(consumer.poll(Duration.ofSeconds(10)).spliterator(), false)</span>
<span class="nc" id="L212">                    .collect(Collectors.toList());</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">            if (records.isEmpty()) {</span>
<span class="nc" id="L214">                log.info(&quot;No records polled, different: {}&quot;, different);</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">                if (different &gt; 16)</span>
<span class="nc" id="L216">                    continue;</span>
                else
                    break;
            }
<span class="nc" id="L220">            Long totalScanned = IntStream.range(0, totalPartition).mapToLong(e -&gt; currentOffsetMap.get(e) - initialOffsetMap.get(e)).sum();</span>
<span class="nc" id="L221">            log.debug(&quot;partition: {}, offset: {}, scanned: {}&quot;, records.get(0).partition(), records.get(0).offset(), totalScanned);</span>
<span class="nc" id="L222">            final List&lt;KafkaMessageDTO&gt; kafkaMessages = conversionHelperService.convertList(records, KafkaMessageDTO.class);</span>
<span class="nc" id="L223">            kafkaMessages.stream()</span>
<span class="nc" id="L224">                    .peek(rec -&gt; {</span>
<span class="nc" id="L225">                        currentOffsetMap.put(rec.getConsumerRecord().partition(), rec.getConsumerRecord().offset());</span>
<span class="nc" id="L226">                    })</span>
<span class="nc bnc" id="L227" title="All 4 branches missed.">                    .filter(rec -&gt; maxRecords == null || currentOffsetMap.get(rec.getConsumerRecord().partition()) - initialOffsetMap.get(rec.getConsumerRecord().partition()) &lt; maxRecords)</span>
<span class="nc bnc" id="L228" title="All 4 branches missed.">                    .filter(rec -&gt; partition == null || partition == rec.getConsumerRecord().partition())</span>
<span class="nc bnc" id="L229" title="All 4 branches missed.">                    .filter(rec -&gt; keyword == null || rec.getPayload().toString().contains(keyword))</span>
<span class="nc bnc" id="L230" title="All 4 branches missed.">                    .filter(rec -&gt; fromOffset == null || rec.getConsumerRecord().offset() &gt;= fromOffset)</span>
<span class="nc bnc" id="L231" title="All 4 branches missed.">                    .filter(rec -&gt; toOffset == null || rec.getConsumerRecord().offset() &lt;= toOffset)</span>
<span class="nc bnc" id="L232" title="All 6 branches missed.">                    .filter(rec -&gt; toDateOffsetMap == null || !toDateOffsetMap.containsKey(rec.getConsumerRecord().partition()) || rec.getConsumerRecord().offset() &lt;= toDateOffsetMap.get(rec.getConsumerRecord().partition()))</span>
<span class="nc" id="L233">                    .peek(e -&gt; log.debug(e.toString()))</span>
<span class="nc" id="L234">                    .forEach(</span>
                            e -&gt; {
<span class="nc" id="L236">                                String key = keyMappers.apply(e);</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">                                if (!resultMap.containsKey(key))</span>
<span class="nc" id="L238">                                    resultMap.put(key, 0L);</span>
<span class="nc" id="L239">                                resultMap.put(key, resultMap.get(key) + 1);</span>
<span class="nc" id="L240">                            }</span>
                    );

<span class="nc" id="L243">            boolean allCompleted = IntStream.range(0, totalPartition).allMatch(</span>
<span class="nc bnc" id="L244" title="All 4 branches missed.">                    e -&gt; maxRecords != null &amp;&amp; currentOffsetMap.get(e) - initialOffsetMap.get(e) &gt; maxRecords ||</span>
<span class="nc bnc" id="L245" title="All 4 branches missed.">                            currentOffsetMap.get(e).equals(endOffsets.get(e) - 1) ||</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">                            toOffset != null &amp;&amp; currentOffsetMap.get(e) &gt; toOffset</span>
            );
<span class="nc bnc" id="L248" title="All 2 branches missed.">            if (allCompleted) {</span>
<span class="nc" id="L249">                break;</span>
            }
<span class="nc" id="L251">        }</span>
<span class="nc" id="L252">        Long totalScanned = IntStream.range(0, totalPartition).mapToLong(e -&gt; currentOffsetMap.get(e) - initialOffsetMap.get(e)).sum();</span>
<span class="nc" id="L253">        log.info(&quot;Total scanned size: {}, it takes {} ms&quot;, totalScanned, System.currentTimeMillis() - start);</span>
<span class="nc" id="L254">        return resultMap;</span>
    }

    private Map&lt;Integer, Long&gt; seekTo(Consumer&lt;String, Object&gt; consumer, String topic, Integer partition, Long fromOffset, Long fromDateMills) {
<span class="nc" id="L258">        long start = System.currentTimeMillis();</span>
<span class="nc" id="L259">        Map&lt;Integer, Long&gt; initialOffsetMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L260">        waitForAssignment(consumer, topic);</span>

<span class="nc bnc" id="L262" title="All 2 branches missed.">        final Map&lt;TopicPartition, OffsetAndTimestamp&gt; fromDateOffsetMap = fromDateMills == null ? null : consumer.offsetsForTimes(consumer.assignment().stream().collect(Collectors.toMap(e -&gt; e, e -&gt; fromDateMills)));</span>
<span class="nc" id="L263">        final Map&lt;TopicPartition, Long&gt; endOffsets = consumer.endOffsets(consumer.assignment());</span>
<span class="nc" id="L264">        final Map&lt;TopicPartition, Long&gt; beginningOffsets = consumer.beginningOffsets(consumer.assignment());</span>

<span class="nc bnc" id="L266" title="All 2 branches missed.">        for (TopicPartition tp : beginningOffsets.keySet()) {</span>
<span class="nc bnc" id="L267" title="All 2 branches missed.">            fromOffset = fromOffset == null ? -1 : fromOffset;</span>
<span class="nc bnc" id="L268" title="All 4 branches missed.">            Long offsetsForFromTimes = (fromDateOffsetMap == null || fromDateOffsetMap.get(tp) == null) ? -1 : fromDateOffsetMap.get(tp).offset();</span>
<span class="nc" id="L269">            Long beginningOffset = beginningOffsets.get(tp);</span>
<span class="nc" id="L270">            Long endOffset = endOffsets.get(tp);</span>
<span class="nc bnc" id="L271" title="All 4 branches missed.">            Boolean matchPartition = partition == null || tp.partition() == partition;</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">            final Long seekToOffset = Collections.max(Arrays.asList(Long.min(fromOffset, endOffset), offsetsForFromTimes, beginningOffset, matchPartition ? 0L : endOffset));</span>
<span class="nc" id="L273">            log.debug(&quot;Seek to Partition: {}, matchPartition: {}, fromOffset: {}, offsetsForTimes: {},&quot; +</span>
                            &quot; Beginning Offset: {}, End Offset: {},&quot; +
                            &quot; Seek To Offset: {}&quot;,
<span class="nc" id="L276">                    tp.partition(), matchPartition, fromOffset, offsetsForFromTimes,</span>
                    beginningOffset, endOffset,
                    seekToOffset);
<span class="nc bnc" id="L279" title="All 2 branches missed.">            if (seekToOffset != null) {</span>
<span class="nc" id="L280">                initialOffsetMap.put(tp.partition(), seekToOffset);</span>
<span class="nc" id="L281">                consumer.seek(tp, seekToOffset);</span>
            }
<span class="nc" id="L283">        }</span>
<span class="nc" id="L284">        log.debug(&quot;seekTo takes {} ms&quot;, System.currentTimeMillis() - start);</span>
<span class="nc" id="L285">        return initialOffsetMap;</span>
    }

    private void waitForAssignment(Consumer&lt;String, Object&gt; consumer, String topic) {
<span class="nc" id="L289">        long start = System.currentTimeMillis();</span>
<span class="nc" id="L290">        int totalPartition = consumer.partitionsFor(topic).size();</span>
<span class="nc" id="L291">        int trial = 0, maxTrial = 12;</span>
<span class="nc bnc" id="L292" title="All 4 branches missed.">        while (consumer.assignment().size() &lt; totalPartition &amp;&amp; trial++ &lt; maxTrial) {</span>
<span class="nc" id="L293">            consumer.poll(Duration.ofSeconds(5));</span>
        }
<span class="nc bnc" id="L295" title="All 2 branches missed.">        if (consumer.assignment().size() &lt; totalPartition) {</span>
<span class="nc" id="L296">            throw new RuntimeException(&quot;Timeout. Failed to get all partitions&quot;);</span>
        } else {
<span class="nc" id="L298">            log.debug(&quot;Waiting partition assignment - completed, Topic: {}, it takes {} ms&quot;, topic, System.currentTimeMillis() - start);</span>
        }
<span class="nc" id="L300">    }</span>

    public List&lt;KafkaUtil.Metric&gt; checkOffset(String topic, String consumerGroup) {
<span class="nc" id="L303">        try (Consumer consumer = consumerProvider.getConsumer(consumerGroup)) {</span>
<span class="nc" id="L304">            return checkOffset(consumer, topic);</span>
        }
    }

    public List&lt;KafkaUtil.Metric&gt; checkOffset(Consumer&lt;String, Object&gt; consumer, String batchTopic) {
<span class="nc" id="L309">        return KafkaUtil.getMetrics(consumer, batchTopic);</span>
    }

    public static String getBatchId(KafkaMessageDTO message) {
<span class="nc" id="L313">        return Optional.ofNullable(message.getPayload())</span>
<span class="nc" id="L314">                .map(e -&gt; e.getString(Constants.BATCH_ID))</span>
<span class="nc" id="L315">                .orElse(null);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>