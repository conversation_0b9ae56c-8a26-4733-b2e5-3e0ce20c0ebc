<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>RecalculationService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">RecalculationService.java</span></div><h1>RecalculationService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.constant.Constants;
import com.poc.hss.fasttrack.dto.KafkaMessageDTO;
import com.poc.hss.fasttrack.dto.RecalculatedBatchCacheDTO;
import com.poc.hss.fasttrack.kafka.util.KafkaUtil;
import com.poc.hss.fasttrack.model.CacheResult;
import com.poc.hss.fasttrack.model.ConsumerGroupInfo;
import com.poc.hss.fasttrack.model.TopicInfo;
import com.poc.hss.fasttrack.model.Unity2Component;
import com.poc.hss.fasttrack.util.BatchUtil;
import com.poc.hss.fasttrack.util.K8sUtil;
import com.poc.hss.fasttrack.util.SupportAttributesUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

<span class="nc" id="L22">@Slf4j</span>
@Component
<span class="nc" id="L24">public class RecalculationService {</span>
    @Autowired
    private GenericKafkaService genericKafkaService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private K8sUtil k8sUtil;

    public List&lt;RecalculatedBatchCacheDTO&gt; recalculateCache(String messageTopicInput,
                                                            String batchId, String consumerGroup, String namespace,
                                                            LocalDateTime fromTimestamp,
                                                            boolean showMessageLag,
                                                            boolean execute) {
<span class="nc" id="L39">        List&lt;RecalculatedBatchCacheDTO&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L40">        log.info(&quot;Consumer Group: {}&quot;, consumerGroup);</span>
<span class="nc" id="L41">        log.info(&quot;Message Topic: {}&quot;, messageTopicInput);</span>
<span class="nc" id="L42">        Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes = k8sUtil.getConfigMap(&quot;.*\\-support-attributes&quot;, namespace);</span>
<span class="nc" id="L43">        List&lt;String&gt; messageTopics = Arrays.asList(messageTopicInput.split(&quot;,&quot;));</span>
<span class="nc" id="L44">        final Map&lt;String, Map&lt;String, String&gt;&gt; sourceComponentAttributesMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L45">        messageTopics.forEach(messageTopic -&gt; {</span>
<span class="nc" id="L46">            final Map&lt;String, String&gt; attributes = SupportAttributesUtil.lookupByTargetTopic(messageTopic, false, supportAttributes);</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">            if (attributes != null)</span>
<span class="nc" id="L48">                sourceComponentAttributesMap.put(messageTopic, attributes);</span>
<span class="nc" id="L49">        });</span>

<span class="nc" id="L51">        final Map&lt;String, String&gt; targetComponentAttributes =</span>
<span class="nc" id="L52">                SupportAttributesUtil.lookupBySourceTopicAndConsumerGroup(messageTopics.get(0), false, consumerGroup, supportAttributes);</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">        if (log.isDebugEnabled()) {</span>
<span class="nc" id="L54">            log.debug(&quot;sourceComponentAttributesMap: {}&quot;, sourceComponentAttributesMap);</span>
<span class="nc" id="L55">            log.debug(&quot;targetComponentAttributes: {}&quot;, targetComponentAttributes);</span>
        }

<span class="nc" id="L58">        log.info(&quot;Message Topic List: {}&quot;, messageTopics);</span>

<span class="nc bnc" id="L60" title="All 2 branches missed.">        if (showMessageLag) {</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">            for (String messageTopic : messageTopics) {</span>
<span class="nc" id="L62">                boolean isMessageLag = false;</span>
<span class="nc" id="L63">                final List&lt;KafkaUtil.Metric&gt; messageMetrics = genericKafkaService.checkOffset(messageTopic, consumerGroup);</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">                for (KafkaUtil.Metric metric : messageMetrics) {</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">                    if (metric.getLag() &gt; 1) {</span>
<span class="nc" id="L66">                        log.info(&quot;Metric Partition: {}, Lag:{}&quot;, metric.getPartition(), metric.getLag());</span>
<span class="nc bnc" id="L67" title="All 2 branches missed.">                        long startOffset = metric.getCommittedOffset() == null ? metric.getBeginningOffset() : metric.getCommittedOffset();</span>
<span class="nc" id="L68">                        final List&lt;KafkaMessageDTO&gt; messages = genericKafkaService.scanAllMessagesWithResult(null, null, 1,</span>
<span class="nc" id="L69">                                metric.getPartition(), startOffset, null, null, messageTopic);</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">                        if (messages.size() &gt; 0) {</span>
<span class="nc" id="L71">                            log.info(&quot;Problem Message:\n{}&quot;, messages.get(0));</span>
<span class="nc" id="L72">                            isMessageLag = true;</span>
                        }
                    }
<span class="nc" id="L75">                }</span>

<span class="nc bnc" id="L77" title="All 2 branches missed.">                if (isMessageLag) {</span>
<span class="nc" id="L78">                    log.warn(&quot;There is lag in message topic: {}. Please fix the message lag first&quot;, messageTopic);</span>
                }
<span class="nc" id="L80">            }</span>
        }
<span class="nc" id="L82">        log.info(&quot;From Timestamp: {}&quot;, fromTimestamp);</span>

<span class="nc" id="L84">        Map&lt;String, Map&lt;String, Long&gt;&gt; batchTopicCountMap = new HashMap&lt;&gt;();</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">        for (String messageTopic : messageTopics) {</span>
<span class="nc" id="L86">            final Map&lt;String, Long&gt; batchCountPerTopicMap = genericKafkaService</span>
<span class="nc" id="L87">                    .scanAllMessagesWithCount(fromTimestamp, null, null, null, null, null, batchId, GenericKafkaService::getBatchId, messageTopic);</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">            if (batchId != null) {</span>
<span class="nc" id="L89">                final Long count = batchCountPerTopicMap.getOrDefault(batchId, 0L);</span>
<span class="nc" id="L90">                batchCountPerTopicMap.clear();</span>
<span class="nc" id="L91">                batchCountPerTopicMap.put(batchId, count);</span>
            }
<span class="nc" id="L93">            log.info(&quot;Total batch size: {}&quot;, batchCountPerTopicMap.size());</span>
<span class="nc" id="L94">            batchCountPerTopicMap.forEach((id, count) -&gt; {</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">                if (!batchTopicCountMap.containsKey(id))</span>
<span class="nc" id="L96">                    batchTopicCountMap.put(id, new HashMap&lt;&gt;());</span>
<span class="nc" id="L97">                batchTopicCountMap.get(id).put(messageTopic, count);</span>
<span class="nc" id="L98">            });</span>
<span class="nc" id="L99">        }</span>


<span class="nc" id="L102">        final TopicInfo topicInfo = BatchUtil.getTopicInfo(messageTopics.get(0));</span>
<span class="nc" id="L103">        final ConsumerGroupInfo consumerGroupInfo = BatchUtil.getConsumerGroupInfo(consumerGroup);</span>
<span class="nc" id="L104">        List&lt;Unity2Component&gt; nextComponents = BatchUtil.getNextComponents(topicInfo.getComponent(), messageTopics.get(0));</span>
<span class="nc" id="L105">        Map&lt;String, Boolean&gt; sourceUpdatedMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L106">        boolean targetUpdated = false;</span>
<span class="nc bnc" id="L107" title="All 6 branches missed.">        boolean isRoutingSourceAdaptor = targetComponentAttributes != null &amp;&amp; targetComponentAttributes.containsKey(&quot;componentType&quot;) &amp;&amp; targetComponentAttributes.get(&quot;componentType&quot;).equals(Unity2Component.SOURCE_ADAPTOR.toString());</span>
<span class="nc bnc" id="L108" title="All 6 branches missed.">        boolean isBlend = targetComponentAttributes != null &amp;&amp; targetComponentAttributes.containsKey(&quot;componentType&quot;) &amp;&amp; targetComponentAttributes.get(&quot;componentType&quot;).equals(Unity2Component.BLEND.toString());</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">        for (String tmpBatchId : batchTopicCountMap.keySet()) {</span>
<span class="nc" id="L110">            log.debug(&quot;Batch ID: {}&quot;, tmpBatchId);</span>
<span class="nc" id="L111">            Map&lt;String, Long&gt; topicCount = batchTopicCountMap.get(tmpBatchId);</span>

<span class="nc" id="L113">            final CacheResult cacheResult = cacheService.getCacheResult(tmpBatchId,</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">                    e -&gt; sourceComponentAttributesMap.entrySet().stream().anyMatch(entry -&gt; SupportAttributesUtil.isSourceComponentCache(e, entry.getValue(), topicInfo)) ||</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">                            SupportAttributesUtil.isTargetComponentCache(e, targetComponentAttributes, consumerGroupInfo, nextComponents)</span>
            );
<span class="nc" id="L117">            log.debug(&quot;cacheResult: {}&quot;, cacheResult);</span>

<span class="nc" id="L119">            Map&lt;String, Long&gt; topicMessageOutMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L120">            Long messageIn = null;</span>
<span class="nc" id="L121">            Map&lt;String, CacheResult.CacheInfo&gt; topicMessageOutCacheMap = new HashMap&lt;&gt;();</span>
<span class="nc" id="L122">            CacheResult.CacheInfo messageInCache = null;</span>

<span class="nc bnc" id="L124" title="All 2 branches missed.">            for (String messageTopic : topicCount.keySet()) {</span>
<span class="nc" id="L125">                final Map&lt;String, String&gt; sourceComponentAttributes = sourceComponentAttributesMap.get(messageTopic);</span>
<span class="nc bnc" id="L126" title="All 2 branches missed.">                for (CacheResult.CacheInfo cache : cacheResult.getCacheList()) {</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">                    if (cache.getComponent() == topicInfo.getComponent()</span>
<span class="nc bnc" id="L128" title="All 4 branches missed.">                            &amp;&amp; cache.getDefinition().startsWith(Constants.MESSAGE_OUT)</span>
<span class="nc bnc" id="L129" title="All 4 branches missed.">                            &amp;&amp; (!isRoutingSourceAdaptor || cache.getDefinition().contains(messageTopic))</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">                            &amp;&amp; (!isBlend || cache.getCacheGroup().equals(sourceComponentAttributes.get(&quot;cacheGroup&quot;)))</span>
                    ) {
<span class="nc" id="L132">                        topicMessageOutMap.put(messageTopic, (Long) cache.getValue());</span>
<span class="nc" id="L133">                        topicMessageOutCacheMap.put(messageTopic, cache);</span>

                    }
<span class="nc" id="L136">                }</span>
<span class="nc" id="L137">            }</span>

<span class="nc bnc" id="L139" title="All 2 branches missed.">            for (CacheResult.CacheInfo cache : cacheResult.getCacheList()) {</span>
<span class="nc bnc" id="L140" title="All 6 branches missed.">                if (nextComponents.stream().anyMatch(e -&gt; BatchUtil.convertToCacheComponent(e).equals(cache.getComponent())) &amp;&amp; cache.getDefinition().equals(Constants.MESSAGE_IN)</span>
<span class="nc bnc" id="L141" title="All 4 branches missed.">                        &amp;&amp; (targetComponentAttributes == null || !targetComponentAttributes.containsKey(&quot;componentType&quot;) || BatchUtil.convertToCacheComponent(targetComponentAttributes.get(&quot;componentType&quot;)).equals(cache.getComponent().toString()))) {</span>
<span class="nc" id="L142">                    messageIn = (Long) cache.getValue();</span>
<span class="nc" id="L143">                    messageInCache = cache;</span>
                }
<span class="nc" id="L145">            }</span>

<span class="nc" id="L147">            log.info(&quot;topicMessageOutCacheMap: {}, messageInCache: {}&quot;, topicMessageOutCacheMap, messageInCache);</span>

<span class="nc bnc" id="L149" title="All 2 branches missed.">            for (String messageTopic : topicCount.keySet()) {</span>
<span class="nc" id="L150">                Long count = topicCount.get(messageTopic);</span>
<span class="nc" id="L151">                final Long messageOut = topicMessageOutMap.get(messageTopic);</span>
<span class="nc" id="L152">                final CacheResult.CacheInfo messageOutCache = topicMessageOutCacheMap.get(messageTopic);</span>
<span class="nc bnc" id="L153" title="All 4 branches missed.">                if (messageOut != null &amp;&amp; messageOutCache != null) {</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">                    if (messageOut.equals(count)) {</span>
<span class="nc" id="L155">                        log.info(&quot;[Cache] batchId: {}, name: {}, definition: {}, out: {} = {}&quot;,</span>
<span class="nc" id="L156">                                tmpBatchId, messageOutCache.getCacheGroup() + &quot;:&quot; + messageOutCache.getComponent(), messageOutCache.getDefinition(), messageOut, count);</span>
                    } else {
<span class="nc" id="L158">                        log.info(&quot;[Cache] batchId: {}, name: {}, definition: {}, out: {} -&gt; {}&quot;,</span>
<span class="nc" id="L159">                                tmpBatchId, messageOutCache.getCacheGroup() + &quot;:&quot; + messageOutCache.getComponent(), messageOutCache.getDefinition(), messageOut, count);</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">                        if (execute) {</span>
<span class="nc" id="L161">                            cacheService.updateCache(messageOutCache.getCacheGroup(), messageOutCache.getComponent(), messageOutCache.getDefinition(), batchId, count.toString());</span>
<span class="nc" id="L162">                            sourceUpdatedMap.put(messageTopic, true);</span>
                        }
                    }
                }
<span class="nc" id="L166">            }</span>


<span class="nc" id="L169">            Long totalCount = topicCount.values().stream().mapToLong(e -&gt; e).sum();</span>
<span class="nc bnc" id="L170" title="All 4 branches missed.">            if (messageIn != null &amp;&amp; messageIn.equals(totalCount)) {</span>
<span class="nc" id="L171">                log.info(&quot;[Cache] batchId: {}, name: {}, definition: {}, in: {} = {}&quot;,</span>
<span class="nc" id="L172">                        tmpBatchId, messageInCache.getCacheGroup() + &quot;:&quot; + messageInCache.getComponent(), messageInCache.getDefinition(), messageIn, totalCount);</span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">            } else if (messageInCache != null) {</span>
<span class="nc" id="L174">                log.info(&quot;[Cache] batchId: {}, name: {}, definition: {}, in: {} -&gt; {}&quot;,</span>
<span class="nc" id="L175">                        tmpBatchId, messageInCache.getCacheGroup() + &quot;:&quot; + messageInCache.getComponent(), messageInCache.getDefinition(), messageIn, totalCount);</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">                if (execute) {</span>
<span class="nc" id="L177">                    cacheService.updateCache(messageInCache.getCacheGroup(), messageInCache.getComponent(), messageInCache.getDefinition(), batchId, totalCount.toString());</span>
<span class="nc" id="L178">                    targetUpdated = true;</span>
                }
            } else {
<span class="nc" id="L181">                log.info(&quot;[Cache] batchId: {}, name: {}, definition: {}, in: {} -&gt; {}&quot;,</span>
<span class="nc" id="L182">                        tmpBatchId, consumerGroupInfo.getResourceName() + &quot;:&quot; + BatchUtil.convertToCacheComponent(targetComponentAttributes.get(&quot;componentType&quot;)), Constants.MESSAGE_IN, messageIn, totalCount);</span>
<span class="nc bnc" id="L183" title="All 2 branches missed.">                if (execute) {</span>
<span class="nc" id="L184">                    cacheService.updateCache(consumerGroupInfo.getResourceName(), BatchUtil.convertToCacheComponent(Unity2Component.fromValue(targetComponentAttributes.get(&quot;componentType&quot;))), Constants.MESSAGE_IN, batchId, totalCount.toString());</span>
<span class="nc" id="L185">                    targetUpdated = true;</span>
                }
            }

<span class="nc" id="L189">            result.add(RecalculatedBatchCacheDTO.builder()</span>
<span class="nc" id="L190">                    .batchId(tmpBatchId)</span>
<span class="nc" id="L191">                    .sourceStageRecalculationList(</span>
<span class="nc" id="L192">                            topicCount.entrySet().stream().map(</span>
                                    e -&gt; {
<span class="nc" id="L194">                                        String messageTopic = e.getKey();</span>
<span class="nc" id="L195">                                        Long count = e.getValue();</span>
<span class="nc" id="L196">                                        final Long messageOut = topicMessageOutMap.get(messageTopic);</span>
<span class="nc" id="L197">                                        final CacheResult.CacheInfo messageOutCache = topicMessageOutCacheMap.get(messageTopic);</span>
<span class="nc" id="L198">                                        return RecalculatedBatchCacheDTO.SourceStageRecalculation.builder()</span>
<span class="nc" id="L199">                                                .name(messageOutCache.getCacheGroup())</span>
<span class="nc" id="L200">                                                .stage(messageOutCache.getComponent())</span>
<span class="nc" id="L201">                                                .originalMessageOut(messageOut)</span>
<span class="nc" id="L202">                                                .recalculatedMessageOut(count)</span>
<span class="nc" id="L203">                                                .updated(sourceUpdatedMap.getOrDefault(messageTopic, false))</span>
<span class="nc" id="L204">                                                .build();</span>
                                    }
<span class="nc" id="L206">                            ).collect(Collectors.toList())</span>
                    )
<span class="nc" id="L208">                    .targetStageRecalculation(RecalculatedBatchCacheDTO.TargetStageRecalculation.builder()</span>
<span class="nc bnc" id="L209" title="All 2 branches missed.">                            .name(messageInCache == null ? targetComponentAttributes.get(&quot;cacheGroup&quot;) : messageInCache.getCacheGroup())</span>
<span class="nc bnc" id="L210" title="All 2 branches missed.">                            .stage(messageInCache == null ? Unity2Component.fromValue(BatchUtil.convertToCacheComponent(targetComponentAttributes.get(&quot;componentType&quot;))) : messageInCache.getComponent())</span>
<span class="nc" id="L211">                            .originalMessageIn(messageIn)</span>
<span class="nc" id="L212">                            .recalculatedMessageIn(totalCount)</span>
<span class="nc" id="L213">                            .updated(targetUpdated)</span>
<span class="nc" id="L214">                            .build())</span>
<span class="nc" id="L215">                    .build());</span>
<span class="nc" id="L216">        }</span>
<span class="nc" id="L217">        return result;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>