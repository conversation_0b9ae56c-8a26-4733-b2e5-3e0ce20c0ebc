<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>ReconciliationService.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">ReconciliationService.java</span></div><h1>ReconciliationService.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.client.api.ReconciliationApi;
import com.poc.hss.fasttrack.client.model.ReconciliationRequest;
import com.poc.hss.fasttrack.client.model.ResetRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

<span class="fc" id="L13">@Slf4j</span>
@Component
<span class="fc" id="L15">public class ReconciliationService {</span>

    @Autowired
    private ReconciliationApi reconciliationApi;

    @Value(&quot;${cache.address:http://cache}/api/reconciliation&quot;)
    private String cacheAddress;

    @PostConstruct
    public void init() {
<span class="fc" id="L25">        reconciliationApi.getApiClient().setBasePath(cacheAddress);</span>
<span class="fc" id="L26">    }</span>

    public void reset(String batch){
<span class="fc" id="L29">        ResetRequest request = new ResetRequest();</span>
<span class="fc" id="L30">        request.setBatch(batch);</span>
<span class="fc" id="L31">        reconciliationApi.reset(request);</span>
<span class="fc" id="L32">    }</span>
    public void reconcile(String group, String component, String batch){
<span class="fc" id="L34">        ReconciliationRequest request = new ReconciliationRequest();</span>
<span class="fc" id="L35">        request.setGroup(group);</span>
<span class="fc" id="L36">        request.setComponent(component);</span>
<span class="fc" id="L37">        request.setBatch(batch);</span>
<span class="fc" id="L38">        reconciliationApi.reconcile(request);</span>
<span class="fc" id="L39">    }</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>