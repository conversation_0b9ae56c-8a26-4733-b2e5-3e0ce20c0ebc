<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.service</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <span class="el_package">com.poc.hss.fasttrack.service</span></div><h1>com.poc.hss.fasttrack.service</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,917 of 3,392</td><td class="ctr2">14%</td><td class="bar">304 of 330</td><td class="ctr2">7%</td><td class="ctr1">229</td><td class="ctr2">259</td><td class="ctr1">399</td><td class="ctr2">502</td><td class="ctr1">73</td><td class="ctr2">94</td><td class="ctr1">3</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a1"><a href="GenericKafkaService.html" class="el_class">GenericKafkaService</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="1,542" alt="1,542"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="146" alt="146"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">128</td><td class="ctr2" id="g0">128</td><td class="ctr1" id="h0">173</td><td class="ctr2" id="i0">173</td><td class="ctr1" id="j0">55</td><td class="ctr2" id="k0">55</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a3"><a href="RecalculationService.html" class="el_class">RecalculationService</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="66" height="10" title="859" alt="859"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="94" alt="94"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">57</td><td class="ctr2" id="g2">57</td><td class="ctr1" id="h1">143</td><td class="ctr2" id="i2">143</td><td class="ctr1" id="j1">10</td><td class="ctr2" id="k2">10</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="CacheService.html" class="el_class">CacheService</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="28" height="10" title="362" alt="362"/><img src="../jacoco-resources/greenbar.gif" width="33" height="10" title="428" alt="428"/></td><td class="ctr2" id="c1">54%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="50" alt="50"/><img src="../jacoco-resources/greenbar.gif" width="21" height="10" title="26" alt="26"/></td><td class="ctr2" id="e0">34%</td><td class="ctr1" id="f2">34</td><td class="ctr2" id="g1">59</td><td class="ctr1" id="h2">65</td><td class="ctr2" id="i1">154</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="GenericKafkaService$1.html" class="el_class">GenericKafkaService.new ConsumerRebalanceListener() {...}</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="154" alt="154"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="11" height="10" title="14" alt="14"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">10</td><td class="ctr2" id="g3">10</td><td class="ctr1" id="h3">19</td><td class="ctr2" id="i3">19</td><td class="ctr1" id="j3">3</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a4"><a href="ReconciliationService.html" class="el_class">ReconciliationService</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="47" alt="47"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l4">0</td><td class="ctr2" id="m4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>