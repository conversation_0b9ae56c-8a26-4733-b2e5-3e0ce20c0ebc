<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.service</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <span class="el_package">com.poc.hss.fasttrack.service</span></div><h1>com.poc.hss.fasttrack.service</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,917 of 3,392</td><td class="ctr2">14%</td><td class="bar">304 of 330</td><td class="ctr2">7%</td><td class="ctr1">229</td><td class="ctr2">259</td><td class="ctr1">399</td><td class="ctr2">502</td><td class="ctr1">73</td><td class="ctr2">94</td><td class="ctr1">3</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a1"><a href="GenericKafkaService.java.html" class="el_source">GenericKafkaService.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="1,696" alt="1,696"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="160" alt="160"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">138</td><td class="ctr2" id="g0">138</td><td class="ctr1" id="h0">191</td><td class="ctr2" id="i0">191</td><td class="ctr1" id="j0">58</td><td class="ctr2" id="k0">58</td><td class="ctr1" id="l0">2</td><td class="ctr2" id="m0">2</td></tr><tr><td id="a2"><a href="RecalculationService.java.html" class="el_source">RecalculationService.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="859" alt="859"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="94" alt="94"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">57</td><td class="ctr2" id="g2">57</td><td class="ctr1" id="h1">143</td><td class="ctr2" id="i2">143</td><td class="ctr1" id="j1">10</td><td class="ctr2" id="k2">10</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="CacheService.java.html" class="el_source">CacheService.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="25" height="10" title="362" alt="362"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="428" alt="428"/></td><td class="ctr2" id="c1">54%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="50" alt="50"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="26" alt="26"/></td><td class="ctr2" id="e0">34%</td><td class="ctr1" id="f2">34</td><td class="ctr2" id="g1">59</td><td class="ctr1" id="h2">65</td><td class="ctr2" id="i1">154</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k1">21</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a3"><a href="ReconciliationService.java.html" class="el_source">ReconciliationService.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="3" height="10" title="47" alt="47"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>