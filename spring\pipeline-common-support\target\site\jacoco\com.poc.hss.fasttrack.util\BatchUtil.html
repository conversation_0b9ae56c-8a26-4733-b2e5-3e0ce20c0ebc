<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchUtil</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_class">BatchUtil</span></div><h1>BatchUtil</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">16 of 152</td><td class="ctr2">89%</td><td class="bar">4 of 23</td><td class="ctr2">82%</td><td class="ctr1">5</td><td class="ctr2">22</td><td class="ctr1">6</td><td class="ctr2">43</td><td class="ctr1">2</td><td class="ctr2">7</td></tr></tfoot><tbody><tr><td id="a4"><a href="BatchUtil.java.html#L76" class="el_method">getNextComponent(Unity2Component)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="36" height="10" title="13" alt="13"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="53" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h0">5</td><td class="ctr2" id="i3">5</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="BatchUtil.java.html#L13" class="el_method">BatchUtil()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f1">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="BatchUtil.java.html#L15" class="el_method">getTopicInfo(String)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="43" alt="43"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d4"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f2">0</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i0">14</td><td class="ctr1" id="j2">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="BatchUtil.java.html#L88" class="el_method">getNextComponents(Unity2Component, String)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/greenbar.gif" width="108" height="10" title="39" alt="39"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d1"><img src="../jacoco-resources/greenbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="e1">100%</td><td class="ctr1" id="f3">0</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><a href="BatchUtil.java.html#L60" class="el_method">getConsumerGroupInfo(String)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/greenbar.gif" width="75" height="10" title="27" alt="27"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d5"><img src="../jacoco-resources/greenbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i1">9</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="BatchUtil.java.html#L50" class="el_method">convertToCacheComponent(String)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/greenbar.gif" width="44" height="10" title="16" alt="16"/></td><td class="ctr2" id="c3">100%</td><td class="bar" id="d2"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="3" alt="3"/></td><td class="ctr2" id="e3">100%</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">4</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a2"><a href="BatchUtil.java.html#L40" class="el_method">convertToCacheComponent(Unity2Component)</a></td><td class="bar" id="b6"><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="11" alt="11"/></td><td class="ctr2" id="c4">100%</td><td class="bar" id="d3"><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="3" alt="3"/></td><td class="ctr2" id="e4">100%</td><td class="ctr1" id="f6">0</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h6">0</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k6">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>