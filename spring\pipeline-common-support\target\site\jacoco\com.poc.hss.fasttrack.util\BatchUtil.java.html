<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>BatchUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">BatchUtil.java</span></div><h1>BatchUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import com.poc.hss.fasttrack.model.ConsumerGroupInfo;
import com.poc.hss.fasttrack.model.TopicInfo;
import com.poc.hss.fasttrack.model.Unity2Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

<span class="nc" id="L13">public class BatchUtil {</span>
    public static TopicInfo getTopicInfo(String batchTopic) {
<span class="fc" id="L15">        Pattern pattern = Pattern.compile(&quot;unity2\\-&quot; +</span>
                &quot;([A-Z]+)\\-&quot; +
                &quot;(.+)\\-&quot; + //projectName
                &quot;(transform|access|source\\-adaptor|blend|fusion|access\\-event|fusion\\-event)\\-&quot; +
                &quot;(.+)\\-&quot; + //resourceName + key + version
                &quot;(out|out.*)&quot;);
<span class="fc" id="L21">        final Matcher matcher = pattern.matcher(batchTopic);</span>
<span class="fc bfc" id="L22" title="All 2 branches covered.">        if (matcher.find()) {</span>
<span class="fc" id="L23">            return TopicInfo.builder()</span>
<span class="fc" id="L24">                    .env(matcher.group(1))</span>
<span class="fc" id="L25">                    .projectName(matcher.group(2))</span>
<span class="fc" id="L26">                    .component(convertToCacheComponent(Unity2Component.fromValue(matcher.group(3)</span>
<span class="fc" id="L27">                            .replace(&quot;-&quot;, &quot;_&quot;)</span>
<span class="fc" id="L28">                            .replace(&quot;access-event&quot;, &quot;access&quot;)</span>
<span class="fc" id="L29">                            .replace(&quot;fusion-event&quot;, &quot;fusion&quot;)</span>
<span class="fc" id="L30">                            .toUpperCase())))</span>
<span class="fc" id="L31">                    .resourceNameKeyVersion(matcher.group(4))</span>
<span class="fc" id="L32">                    .build();</span>
        } else {
<span class="fc" id="L34">            return null;</span>
        }

    }

    public static Unity2Component convertToCacheComponent(Unity2Component component) {
<span class="fc bfc" id="L40" title="All 3 branches covered.">        switch (component) {</span>
            case BLEND:
<span class="fc" id="L42">                return Unity2Component.TRANSFORM;</span>
            case FUSION:
<span class="fc" id="L44">                return Unity2Component.ACCESS;</span>
        }
<span class="fc" id="L46">        return component;</span>
    }

    public static String convertToCacheComponent(String component) {
<span class="fc bfc" id="L50" title="All 3 branches covered.">        switch (component) {</span>
            case &quot;BLEND&quot;:
<span class="fc" id="L52">                return Unity2Component.TRANSFORM.toString();</span>
            case &quot;FUSION&quot;:
<span class="fc" id="L54">                return Unity2Component.ACCESS.toString();</span>
        }
<span class="fc" id="L56">        return component;</span>
    }

    public static ConsumerGroupInfo getConsumerGroupInfo(String consumerGroup) {
<span class="fc" id="L60">        Pattern pattern = Pattern.compile(&quot;(.+)\\-&quot; + //projectName</span>
                &quot;([A-Z0-9]+)\\-&quot; +
                &quot;(.+)&quot;); //resourceName
<span class="fc" id="L63">        final Matcher matcher = pattern.matcher(consumerGroup);</span>
<span class="fc bfc" id="L64" title="All 2 branches covered.">        if (matcher.find()) {</span>
<span class="fc" id="L65">            return ConsumerGroupInfo.builder()</span>
<span class="fc" id="L66">                    .projectName(matcher.group(1))</span>
<span class="fc" id="L67">                    .env(matcher.group(2))</span>
<span class="fc" id="L68">                    .resourceName(matcher.group(3))</span>
<span class="fc" id="L69">                    .build();</span>
        } else {
<span class="fc" id="L71">            return null;</span>
        }
    }

    private Unity2Component getNextComponent(Unity2Component component) {
<span class="nc bnc" id="L76" title="All 4 branches missed.">        switch (component) {</span>
            case SOURCE_ADAPTOR:
<span class="nc" id="L78">                return Unity2Component.TRANSFORM;</span>
            case TRANSFORM:
<span class="nc" id="L80">                return Unity2Component.ACCESS;</span>
            case ACCESS:
<span class="nc" id="L82">                return Unity2Component.CONSUMER_ADAPTOR;</span>
        }
<span class="nc" id="L84">        return null;</span>
    }

    public static List&lt;Unity2Component&gt; getNextComponents(Unity2Component component, String batchTopic) {
<span class="fc bfc" id="L88" title="All 5 branches covered.">        switch (component) {</span>
            case SOURCE_ADAPTOR:
<span class="fc" id="L90">                return Arrays.asList(Unity2Component.SOURCE_ADAPTOR, Unity2Component.TRANSFORM);</span>
            case TRANSFORM:
<span class="fc" id="L92">                return Collections.singletonList(Unity2Component.ACCESS);</span>
            case BLEND:
<span class="fc" id="L94">                return Collections.singletonList(Unity2Component.FUSION);</span>
            case ACCESS:
<span class="fc bfc" id="L96" title="All 4 branches covered.">                return (batchTopic.contains(&quot;access-event&quot;) || batchTopic.contains(&quot;fusion-event&quot;)) ? Collections.singletonList(Unity2Component.TRANSFORM) : Collections.singletonList(Unity2Component.CONSUMER_ADAPTOR);</span>
        }
<span class="fc" id="L98">        return Collections.emptyList();</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>