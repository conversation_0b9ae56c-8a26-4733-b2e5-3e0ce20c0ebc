<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SupportAttributesUtil</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_class">SupportAttributesUtil</span></div><h1>SupportAttributesUtil</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">195 of 549</td><td class="ctr2">64%</td><td class="bar">59 of 120</td><td class="ctr2">50%</td><td class="ctr1">41</td><td class="ctr2">70</td><td class="ctr1">30</td><td class="ctr2">87</td><td class="ctr1">3</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a0"><a href="SupportAttributesUtil.java.html#L32" class="el_method">isSourceComponentCache(CacheResult.CacheInfo, Map, TopicInfo)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="68" alt="68"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="18" alt="18"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g2">10</td><td class="ctr1" id="h0">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="SupportAttributesUtil.java.html#L18" class="el_method">isTargetComponentCache(CacheResult.CacheInfo, Map, ConsumerGroupInfo, List)</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="77" height="10" title="65" alt="65"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="80" height="10" title="16" alt="16"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">9</td><td class="ctr2" id="g4">9</td><td class="ctr1" id="h1">9</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="SupportAttributesUtil.java.html#L83" class="el_method">lookupBySourceTopicAndConsumerGroup(String, boolean, String, Map)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="54" height="10" title="46" alt="46"/><img src="../jacoco-resources/greenbar.gif" width="59" height="10" title="50" alt="50"/></td><td class="ctr2" id="c6">52%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="70" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="40" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">36%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">12</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j3">0</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="SupportAttributesUtil.java.html#L128" class="el_method">lookupByTargetTopic(String, boolean, Map)</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="5" alt="5"/><img src="../jacoco-resources/greenbar.gif" width="95" height="10" title="80" alt="80"/></td><td class="ctr2" id="c4">94%</td><td class="bar" id="d4"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="70" height="10" title="14" alt="14"/></td><td class="ctr2" id="e2">77%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">10</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i2">14</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="SupportAttributesUtil.java.html#L47" class="el_method">lookupBySourceTopicAndConsumerGroup(String, String, Map)</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="115" height="10" title="97" alt="97"/></td><td class="ctr2" id="c3">96%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="20" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="100" height="10" title="20" alt="20"/></td><td class="ctr2" id="e1">83%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g0">13</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i0">17</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="SupportAttributesUtil.java.html#L72" class="el_method">lookupByDeployment(String, Map)</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="4" alt="4"/><img src="../jacoco-resources/greenbar.gif" width="35" height="10" title="30" alt="30"/></td><td class="ctr2" id="c5">88%</td><td class="bar" id="d5"><img src="../jacoco-resources/redbar.gif" width="10" height="10" title="2" alt="2"/><img src="../jacoco-resources/greenbar.gif" width="30" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">75%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a9"><a href="SupportAttributesUtil.java.html#L16" class="el_method">SupportAttributesUtil()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a7"><a href="SupportAttributesUtil.java.html#L107" class="el_method">lookupMessageTopicByBatchTopic(List, List)</a></td><td class="bar" id="b7"><img src="../jacoco-resources/greenbar.gif" width="104" height="10" title="88" alt="88"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"><img src="../jacoco-resources/redbar.gif" width="5" height="10" title="1" alt="1"/><img src="../jacoco-resources/greenbar.gif" width="65" height="10" title="13" alt="13"/></td><td class="ctr2" id="e0">92%</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g5">8</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a2"><a href="SupportAttributesUtil.java.html#L124" class="el_method">lambda$lookupMessageTopicByBatchTopic$0(String)</a></td><td class="bar" id="b8"><img src="../jacoco-resources/greenbar.gif" width="5" height="10" title="5" alt="5"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><a href="SupportAttributesUtil.java.html#L15" class="el_method">static {...}</a></td><td class="bar" id="b9"><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c2">100%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">0</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">0</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">0</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>