<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>SupportAttributesUtil.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-support</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.util</a> &gt; <span class="el_source">SupportAttributesUtil.java</span></div><h1>SupportAttributesUtil.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.util;

import com.poc.hss.fasttrack.model.CacheResult;
import com.poc.hss.fasttrack.model.ConsumerGroupInfo;
import com.poc.hss.fasttrack.model.TopicInfo;
import com.poc.hss.fasttrack.model.Unity2Component;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

<span class="fc" id="L15">@Slf4j</span>
<span class="nc" id="L16">public class SupportAttributesUtil {</span>
    public static boolean isTargetComponentCache(CacheResult.CacheInfo e, Map&lt;String, String&gt; targetComponentAttributes, ConsumerGroupInfo consumerGroupInfo, List&lt;Unity2Component&gt; nextComponents) {
<span class="nc bnc" id="L18" title="All 2 branches missed.">        if (log.isDebugEnabled()) {</span>
<span class="nc" id="L19">            log.debug(&quot;Cache Info: {}&quot;, e);</span>
<span class="nc" id="L20">            log.debug(&quot;targetComponentAttributes: {}&quot;, targetComponentAttributes);</span>
<span class="nc" id="L21">            log.debug(&quot;consumerGroupInfo: {}&quot;, consumerGroupInfo);</span>
<span class="nc" id="L22">            log.debug(&quot;nextComponents: {}&quot;, nextComponents);</span>
        }
<span class="nc bnc" id="L24" title="All 6 branches missed.">        if (targetComponentAttributes != null &amp;&amp; targetComponentAttributes.containsKey(&quot;cacheGroup&quot;) &amp;&amp; targetComponentAttributes.containsKey(&quot;componentType&quot;)) {</span>
<span class="nc bnc" id="L25" title="All 2 branches missed.">            return e.getCacheGroup().equals(targetComponentAttributes.get(&quot;cacheGroup&quot;))</span>
<span class="nc bnc" id="L26" title="All 2 branches missed.">                    &amp;&amp; e.getComponent().toString().equals(BatchUtil.convertToCacheComponent(targetComponentAttributes.get(&quot;componentType&quot;)));</span>
        }
<span class="nc bnc" id="L28" title="All 4 branches missed.">        return (consumerGroupInfo.getResourceName().contains(e.getCacheGroup()) &amp;&amp; nextComponents.contains(e.getComponent()));</span>
    }

    public static boolean isSourceComponentCache(CacheResult.CacheInfo e, Map&lt;String, String&gt; sourceComponentAttributes, TopicInfo topicInfo) {
<span class="nc bnc" id="L32" title="All 2 branches missed.">        if (log.isDebugEnabled()) {</span>
<span class="nc" id="L33">            log.debug(&quot;Cache Info: {}&quot;, e);</span>
<span class="nc" id="L34">            log.debug(&quot;sourceComponentAttributes: {}&quot;, sourceComponentAttributes);</span>
<span class="nc" id="L35">            log.debug(&quot;topicInfo: {}&quot;, topicInfo);</span>
        }
<span class="nc bnc" id="L37" title="All 6 branches missed.">        if (sourceComponentAttributes != null &amp;&amp; sourceComponentAttributes.containsKey(&quot;cacheGroup&quot;) &amp;&amp; sourceComponentAttributes.containsKey(&quot;componentType&quot;)) {</span>
<span class="nc bnc" id="L38" title="All 2 branches missed.">            return e.getCacheGroup().equals(sourceComponentAttributes.get(&quot;cacheGroup&quot;))</span>
<span class="nc bnc" id="L39" title="All 2 branches missed.">                    &amp;&amp; e.getComponent().toString().equals(BatchUtil.convertToCacheComponent(sourceComponentAttributes.get(&quot;componentType&quot;)));</span>
        }

<span class="nc bnc" id="L42" title="All 4 branches missed.">        return (topicInfo.getResourceNameKeyVersion().equals(e.getCacheGroup()) || topicInfo.getResourceNameKeyVersion().equals(e.getCacheGroup() + &quot;-v1-2-0&quot;))</span>
<span class="nc bnc" id="L43" title="All 2 branches missed.">                &amp;&amp; topicInfo.getComponent() == e.getComponent();</span>
    }

    public static Map&lt;String, String&gt; lookupBySourceTopicAndConsumerGroup(String inputTopic, String consumerGroup, Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes) {
<span class="fc bfc" id="L47" title="All 2 branches covered.">        for (Map&lt;String, String&gt; value : supportAttributes.values()) {</span>
<span class="pc bpc" id="L48" title="1 of 2 branches missed.">            if (log.isDebugEnabled())</span>
<span class="nc" id="L49">                log.debug(&quot;value: {}&quot;, value);</span>
<span class="pc bpc" id="L50" title="1 of 6 branches missed.">            if (consumerGroup == null || value.containsKey(&quot;consumerGroup&quot;) &amp;&amp; value.get(&quot;consumerGroup&quot;).equals(consumerGroup)) {</span>
<span class="fc" id="L51">                String sourceBatchTopicSuffix = value.getOrDefault(&quot;sourceBatchTopicSuffix&quot;, &quot;-batch&quot;);</span>
<span class="fc bfc" id="L52" title="All 2 branches covered.">                if (value.containsKey(&quot;sourceTopic&quot;)) {</span>
<span class="fc bfc" id="L53" title="All 2 branches covered.">                    if (value.get(&quot;sourceTopic&quot;).equals(inputTopic) ||</span>
<span class="fc bfc" id="L54" title="All 2 branches covered.">                            (value.get(&quot;sourceTopic&quot;) + sourceBatchTopicSuffix).equals(inputTopic)) {</span>
<span class="fc" id="L55">                        return value;</span>
                    }
<span class="pc bpc" id="L57" title="1 of 2 branches missed.">                } else if (value.containsKey(&quot;sourceTopics&quot;)) {</span>
<span class="fc" id="L58">                    final String[] sourceTopics = value.get(&quot;sourceTopics&quot;).split(&quot;,&quot;);</span>
<span class="pc bpc" id="L59" title="1 of 2 branches missed.">                    for (String sourceTopic : sourceTopics) {</span>
<span class="fc bfc" id="L60" title="All 2 branches covered.">                        if (sourceTopic.equals(inputTopic) ||</span>
<span class="fc bfc" id="L61" title="All 2 branches covered.">                                (sourceTopic + sourceBatchTopicSuffix).equals(inputTopic)) {</span>
<span class="fc" id="L62">                            return value;</span>
                        }
                    }
                }
            }
<span class="fc" id="L67">        }</span>
<span class="fc" id="L68">        return null;</span>
    }

    public static Map&lt;String, String&gt; lookupByDeployment(String deployment, Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes) {
<span class="fc bfc" id="L72" title="All 2 branches covered.">        for (Map&lt;String, String&gt; value : supportAttributes.values()) {</span>
<span class="pc bpc" id="L73" title="1 of 2 branches missed.">            if (log.isDebugEnabled())</span>
<span class="nc" id="L74">                log.debug(&quot;value: {}&quot;, value);</span>
<span class="pc bpc" id="L75" title="1 of 4 branches missed.">            if (value.containsKey(&quot;deployment&quot;) &amp;&amp; value.get(&quot;deployment&quot;).equals(deployment)) {</span>
<span class="fc" id="L76">                return value;</span>
            }
<span class="fc" id="L78">        }</span>
<span class="fc" id="L79">        return null;</span>
    }

    public static Map&lt;String, String&gt; lookupBySourceTopicAndConsumerGroup(String batchTopic, boolean isBatch, String consumerGroup, Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes) {
<span class="pc bpc" id="L83" title="1 of 2 branches missed.">        for (Map&lt;String, String&gt; value : supportAttributes.values()) {</span>
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">            if (log.isDebugEnabled())</span>
<span class="nc" id="L85">                log.debug(&quot;value: {}&quot;, value);</span>
<span class="pc bpc" id="L86" title="2 of 4 branches missed.">            if (value.containsKey(&quot;consumerGroup&quot;) &amp;&amp; value.get(&quot;consumerGroup&quot;).equals(consumerGroup)) {</span>
<span class="fc" id="L87">                String sourceBatchTopicSuffix = value.getOrDefault(&quot;sourceBatchTopicSuffix&quot;, &quot;-batch&quot;);</span>
<span class="pc bpc" id="L88" title="1 of 2 branches missed.">                if (value.containsKey(&quot;sourceTopic&quot;)) {</span>
<span class="pc bpc" id="L89" title="1 of 4 branches missed.">                    if ((value.get(&quot;sourceTopic&quot;) + (isBatch ? sourceBatchTopicSuffix : &quot;&quot;)).equals(batchTopic)) {</span>
<span class="fc" id="L90">                        return value;</span>
                    }
<span class="nc bnc" id="L92" title="All 2 branches missed.">                } else if (value.containsKey(&quot;sourceTopics&quot;)) {</span>
<span class="nc" id="L93">                    final String[] sourceTopics = value.get(&quot;sourceTopics&quot;).split(&quot;,&quot;);</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">                    for (String sourceTopic : sourceTopics) {</span>
<span class="nc bnc" id="L95" title="All 4 branches missed.">                        if ((sourceTopic + (isBatch ? sourceBatchTopicSuffix : &quot;&quot;)).equals(batchTopic)) {</span>
<span class="nc" id="L96">                            return value;</span>
                        }
                    }

                }
            }
<span class="nc" id="L102">        }</span>
<span class="nc" id="L103">        return null;</span>
    }

    public static List&lt;String&gt; lookupMessageTopicByBatchTopic(List&lt;String&gt; batchTopics, List&lt;Map&lt;String, String&gt;&gt; sourceComponentAttributesList) {
<span class="fc" id="L107">        List&lt;String&gt; resultList = new ArrayList&lt;&gt;();</span>
<span class="fc bfc" id="L108" title="All 2 branches covered.">        if (CollectionUtils.isNotEmpty(sourceComponentAttributesList)) {</span>
<span class="fc bfc" id="L109" title="All 2 branches covered.">            for (Map&lt;String, String&gt; sourceComponentAttributes : sourceComponentAttributesList) {</span>
<span class="fc" id="L110">                String targetBatchTopicSuffix = sourceComponentAttributes.getOrDefault(&quot;targetBatchTopicSuffix&quot;, &quot;-batch&quot;);</span>
<span class="fc bfc" id="L111" title="All 2 branches covered.">                if (sourceComponentAttributes.containsKey(&quot;targetTopic&quot;)) {</span>
<span class="fc" id="L112">                    resultList.add(sourceComponentAttributes.get(&quot;targetTopic&quot;));</span>
<span class="pc bpc" id="L113" title="1 of 2 branches missed.">                } else if (sourceComponentAttributes.containsKey(&quot;targetTopics&quot;)) {</span>
<span class="fc" id="L114">                    final String[] targetTopics = sourceComponentAttributes.get(&quot;targetTopics&quot;).split(&quot;,&quot;);</span>
<span class="fc bfc" id="L115" title="All 2 branches covered.">                    for (String targetTopic : targetTopics) {</span>
<span class="fc bfc" id="L116" title="All 2 branches covered.">                        if (batchTopics.contains(targetTopic + targetBatchTopicSuffix)) {</span>
<span class="fc" id="L117">                            resultList.add(targetTopic);</span>
                        }
                    }
                }
<span class="fc" id="L121">            }</span>
<span class="fc" id="L122">            return resultList;</span>
        }
<span class="fc bfc" id="L124" title="All 2 branches covered.">        return CollectionUtils.isEmpty(batchTopics) ? null : batchTopics.stream().map(batchTopic -&gt; batchTopic.replace(&quot;-batch&quot;, &quot;&quot;)).collect(Collectors.toList());</span>
    }

    public static Map&lt;String, String&gt; lookupByTargetTopic(String topic, boolean isBatch, Map&lt;String, Map&lt;String, String&gt;&gt; supportAttributes) {
<span class="fc bfc" id="L128" title="All 2 branches covered.">        for (Map&lt;String, String&gt; value : supportAttributes.values()) {</span>
<span class="pc bpc" id="L129" title="1 of 2 branches missed.">            if (log.isDebugEnabled())</span>
<span class="nc" id="L130">                log.debug(&quot;value: {}&quot;, value);</span>
<span class="fc" id="L131">            String targetBatchTopicSuffix = value.getOrDefault(&quot;targetBatchTopicSuffix&quot;, &quot;-batch&quot;);</span>
<span class="fc bfc" id="L132" title="All 2 branches covered.">            if (value.containsKey(&quot;targetTopic&quot;)) {</span>
<span class="fc bfc" id="L133" title="All 4 branches covered.">                if ((value.get(&quot;targetTopic&quot;) + (isBatch ? targetBatchTopicSuffix : &quot;&quot;)).equals(topic)) {</span>
<span class="fc" id="L134">                    return value;</span>
                }
<span class="pc bpc" id="L136" title="1 of 2 branches missed.">            } else if (value.containsKey(&quot;targetTopics&quot;)) {</span>
<span class="fc" id="L137">                final String[] targetTopics = value.get(&quot;targetTopics&quot;).split(&quot;,&quot;);</span>
<span class="pc bpc" id="L138" title="1 of 2 branches missed.">                for (String targetTopic : targetTopics) {</span>
<span class="pc bpc" id="L139" title="1 of 4 branches missed.">                    if ((targetTopic + (isBatch ? targetBatchTopicSuffix : &quot;&quot;)).equals(topic)) {</span>
<span class="fc" id="L140">                        return value;</span>
                    }
                }

            }
<span class="fc" id="L145">        }</span>
<span class="fc" id="L146">        return null;</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>