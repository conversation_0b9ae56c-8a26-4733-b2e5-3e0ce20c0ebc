C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\src\main\java\com\poc\hss\fasttrack\kafka\KafkaTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\src\main\java\com\poc\hss\fasttrack\kafka\KafkaConsumerContainer.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\src\main\java\com\poc\hss\fasttrack\kafka\KafkaTestUtils.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\src\main\java\com\poc\hss\fasttrack\kafka\KafkaAdminMetadataContainer.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\src\main\java\com\poc\hss\fasttrack\kafka\KafkaListenerContainerErrorHandlerTestConfiguration.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test-kafka\src\main\java\com\poc\hss\fasttrack\kafka\MultiPartitionKafkaTest.java
