C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\utils\MessageTestUtils.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\utils\OAuth2TestUtils.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\config\TestRestTemplateNoRedirectionConfiguration.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\BaseTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\annotation\DisableTestRestTemplateRedirection.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\SpringMvcSecurityTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\utils\GraphQLTestUtil.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\config\JwtDecoderTestConfiguration.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\utils\TimedTask.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\SpringWebTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\SpringWebSecurityTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\client\NoRedirectionClientHttpRequestFactory.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\SpringMvcTest.java
C:\eddiewtchoi\workspace\ide\git\spring\pipeline-common-test\src\main\java\com\poc\hss\fasttrack\test\MockShutdownManagerTestConfig.java
