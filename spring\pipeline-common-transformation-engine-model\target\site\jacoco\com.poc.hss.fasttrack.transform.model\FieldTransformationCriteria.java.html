<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldTransformationCriteria.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_source">FieldTransformationCriteria.java</span></div><h1>FieldTransformationCriteria.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.transform.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

<span class="nc bnc" id="L14" title="All 30 branches missed.">@Data</span>
<span class="nc" id="L15">@Builder</span>
<span class="nc" id="L16">@NoArgsConstructor</span>
<span class="nc" id="L17">@AllArgsConstructor</span>
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FieldTransformationCriteria {

<span class="nc" id="L21">    private List&lt;String&gt; inputFields;</span>
<span class="nc" id="L22">    private List&lt;FieldTransformationOperation&gt; operations;</span>
<span class="nc" id="L23">	private OperationMode operationMode;</span>

<span class="nc bnc" id="L25" title="All 62 branches missed."> 	@Data</span>
<span class="nc" id="L26"> 	@Builder</span>
<span class="nc" id="L27">    @NoArgsConstructor</span>
<span class="nc" id="L28">    @AllArgsConstructor</span>
 	public static class FieldTransformationOperation {
<span class="nc" id="L30"> 		private String type;</span>
<span class="nc" id="L31"> 		private List&lt;String&gt; formatters;</span>
<span class="nc" id="L32">		private List&lt;String&gt; fromMultiple;</span>
<span class="nc" id="L33"> 		private String from;</span>
<span class="nc" id="L34"> 		private String as;</span>
<span class="nc" id="L35"> 		private String delimiter;</span>
<span class="nc" id="L36"> 		private Map&lt;String, String&gt; args;</span>
 	}

<span class="fc" id="L39">	public enum FormatterType {</span>
<span class="fc" id="L40">		UPPERCASE(&quot;UPPERCASE&quot;),</span>
<span class="fc" id="L41">		LOWERCASE(&quot;LOWERCASE&quot;),</span>
<span class="fc" id="L42">		TRIM(&quot;TRIM&quot;);</span>

		private String value;

<span class="fc" id="L46">		FormatterType(String value) {</span>
<span class="fc" id="L47">			this.value = value;</span>
<span class="fc" id="L48">		}</span>

		@Override
		@JsonValue
		public String toString() {
<span class="fc" id="L53">			return String.valueOf(value);</span>
		}

		@JsonCreator
		public static FormatterType fromValue(String text) {
<span class="fc bfc" id="L58" title="All 2 branches covered.">			for (FormatterType b : FormatterType.values()) {</span>
<span class="fc bfc" id="L59" title="All 2 branches covered.">				if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L60">					return b;</span>
				}
			}
<span class="fc" id="L63">			return null;</span>
		}
	}

<span class="fc" id="L67">	public enum OperationType {</span>
<span class="fc" id="L68">		RENAME(&quot;RENAME&quot;),</span>
<span class="fc" id="L69">		NOOP(&quot;NOOP&quot;),</span>
<span class="fc" id="L70">		CONCAT(&quot;CONCAT&quot;);</span>

		private String value;

<span class="fc" id="L74">		OperationType(String value) {</span>
<span class="fc" id="L75">			this.value = value;</span>
<span class="fc" id="L76">		}</span>

		@Override
		@JsonValue
		public String toString() {
<span class="fc" id="L81">			return String.valueOf(value);</span>
		}

		@JsonCreator
		public static OperationType fromValue(String text) {
<span class="fc bfc" id="L86" title="All 2 branches covered.">			for (OperationType b : OperationType.values()) {</span>
<span class="fc bfc" id="L87" title="All 2 branches covered.">				if (String.valueOf(b.value).equals(text)) {</span>
<span class="fc" id="L88">					return b;</span>
				}
			}
<span class="fc" id="L91">			return null;</span>
		}
	}

<span class="nc" id="L95">	public enum OperationMode {</span>
<span class="nc" id="L96">		MERGE(&quot;MERGE&quot;),</span>
<span class="nc" id="L97">		REPLACE(&quot;REPLACE&quot;);</span>

		private String value;

<span class="nc" id="L101">		OperationMode(String value) {</span>
<span class="nc" id="L102">			this.value = value;</span>
<span class="nc" id="L103">		}</span>

		@Override
		@JsonValue
		public String toString() {
<span class="nc" id="L108">			return String.valueOf(value);</span>
		}

		@JsonCreator
		public static OperationType fromValue(String text) {
<span class="nc bnc" id="L113" title="All 2 branches missed.">			for (OperationType b : OperationType.values()) {</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">				if (String.valueOf(b.value).equals(text)) {</span>
<span class="nc" id="L115">					return b;</span>
				}
			}
<span class="nc" id="L118">			return null;</span>
		}
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>