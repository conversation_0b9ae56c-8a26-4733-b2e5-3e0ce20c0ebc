<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_source">LookupRequest.java</span></div><h1>LookupRequest.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.transform.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

<span class="nc bnc" id="L8" title="All 78 branches missed.">@Data</span>
<span class="nc" id="L9">@Builder(toBuilder = true)</span>
public class LookupRequest {
<span class="nc" id="L11">    private String accessSchemaName;</span>
<span class="nc" id="L12">    private String criteria;</span>
<span class="nc" id="L13">    private List&lt;String&gt; fields;</span>
<span class="nc" id="L14">    private List&lt;SortRequest&gt; sorts;</span>
<span class="nc" id="L15">    private Integer offset;</span>
<span class="nc" id="L16">    private Integer limit;</span>
<span class="nc" id="L17">    private List&lt;FieldAggregation&gt; fieldAggregations;</span>
<span class="nc" id="L18">    private Boolean distinct;</span>
<span class="nc" id="L19">    private List&lt;String&gt; groups;</span>

<span class="nc bnc" id="L21" title="All 30 branches missed.">    @Data</span>
<span class="nc" id="L22">    @Builder</span>
    public static class FieldAggregation {
<span class="nc" id="L24">        private String function;</span>
<span class="nc" id="L25">        private String field;</span>
<span class="nc" id="L26">        private String rename;</span>
    }

<span class="nc bnc" id="L29" title="All 22 branches missed.">    @Data</span>
<span class="nc" id="L30">    @Builder</span>
    public static class SortRequest {
<span class="nc" id="L32">        private String field;</span>
<span class="nc" id="L33">        private SortDirection direction;</span>
    }

<span class="fc" id="L36">    public enum SortDirection {</span>
<span class="fc" id="L37">        ASC,</span>
<span class="fc" id="L38">        DESC</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>