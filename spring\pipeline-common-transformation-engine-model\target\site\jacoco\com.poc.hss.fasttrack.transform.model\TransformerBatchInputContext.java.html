<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TransformerBatchInputContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_source">TransformerBatchInputContext.java</span></div><h1>TransformerBatchInputContext.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.transform.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

<span class="nc bnc" id="L9" title="All 16 branches missed.">@EqualsAndHashCode(callSuper = true)</span>
<span class="nc" id="L10">@Data</span>
<span class="nc" id="L11">@SuperBuilder</span>
public class TransformerBatchInputContext extends TransformerInputContext {
<span class="nc" id="L13">    private List&lt;TransformerRecord&gt; records;</span>

    public static TransformerBatchInputContextBuilder&lt;?, ?&gt; from(TransformerBatchInputContext context) {
<span class="nc" id="L16">        return TransformerBatchInputContext.builder()</span>
<span class="nc" id="L17">                .records(context.getRecords())</span>
<span class="nc" id="L18">                .lookupService(context.getLookupService())</span>
<span class="nc" id="L19">                .customApiService(context.getCustomApiService())</span>
<span class="nc" id="L20">                .restTemplate(context.getRestTemplate())</span>
<span class="nc" id="L21">                .batchService(context.getBatchService())</span>
<span class="nc" id="L22">                .env(context.getEnv())</span>
<span class="nc" id="L23">                .meterRegistry(context.getMeterRegistry())</span>
<span class="nc" id="L24">                .jdbcTemplate(context.getJdbcTemplate())</span>
<span class="nc" id="L25">                .togglzManager(context.getTogglzManager());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>