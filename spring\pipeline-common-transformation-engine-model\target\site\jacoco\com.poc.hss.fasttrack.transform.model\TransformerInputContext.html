<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TransformerInputContext</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_class">TransformerInputContext</span></div><h1>TransformerInputContext</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">363 of 363</td><td class="ctr2">0%</td><td class="bar">70 of 70</td><td class="ctr2">0%</td><td class="ctr1">48</td><td class="ctr2">48</td><td class="ctr1">10</td><td class="ctr2">10</td><td class="ctr1">13</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a1"><a href="TransformerInputContext.java.html#L14" class="el_method">equals(Object)</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="157" alt="157"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="54" alt="54"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">28</td><td class="ctr2" id="g0">28</td><td class="ctr1" id="h0">1</td><td class="ctr2" id="i0">1</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a10"><a href="TransformerInputContext.java.html#L14" class="el_method">hashCode()</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="90" height="10" title="118" alt="118"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="35" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h1">1</td><td class="ctr2" id="i1">1</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a12"><a href="TransformerInputContext.java.html#L15" class="el_method">TransformerInputContext(TransformerInputContext.TransformerInputContextBuilder)</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="26" height="10" title="35" alt="35"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">1</td><td class="ctr1" id="h2">1</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="TransformerInputContext.java.html#L14" class="el_method">toString()</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="19" height="10" title="26" alt="26"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="TransformerInputContext.java.html#L17" class="el_method">getLookupService()</a></td><td class="bar" id="b4"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="TransformerInputContext.java.html#L18" class="el_method">getCustomApiService()</a></td><td class="bar" id="b5"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a8"><a href="TransformerInputContext.java.html#L19" class="el_method">getRestTemplate()</a></td><td class="bar" id="b6"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">1</td><td class="ctr2" id="i6">1</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="TransformerInputContext.java.html#L20" class="el_method">getBatchService()</a></td><td class="bar" id="b7"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">1</td><td class="ctr2" id="i7">1</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="TransformerInputContext.java.html#L21" class="el_method">getEnv()</a></td><td class="bar" id="b8"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a7"><a href="TransformerInputContext.java.html#L22" class="el_method">getMeterRegistry()</a></td><td class="bar" id="b9"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a5"><a href="TransformerInputContext.java.html#L23" class="el_method">getJdbcTemplate()</a></td><td class="bar" id="b10"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a9"><a href="TransformerInputContext.java.html#L24" class="el_method">getTogglzManager()</a></td><td class="bar" id="b11"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">1</td><td class="ctr2" id="i11">1</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="TransformerInputContext.java.html#L14" class="el_method">canEqual(Object)</a></td><td class="bar" id="b12"><img src="../jacoco-resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>