<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TransformerInputContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_source">TransformerInputContext.java</span></div><h1>TransformerInputContext.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.transform.model;

import com.poc.hss.fasttrack.service.BatchService;
import com.poc.hss.fasttrack.service.CustomApiService;
import com.poc.hss.fasttrack.service.LookupService;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.Data;
import lombok.experimental.SuperBuilder;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.client.RestTemplate;
import org.togglz.core.manager.FeatureManager;

<span class="nc bnc" id="L14" title="All 70 branches missed.">@Data</span>
<span class="nc" id="L15">@SuperBuilder</span>
public abstract class TransformerInputContext {
<span class="nc" id="L17">    private final LookupService lookupService;</span>
<span class="nc" id="L18">    private final CustomApiService customApiService;</span>
<span class="nc" id="L19">    private final RestTemplate restTemplate;</span>
<span class="nc" id="L20">    private final BatchService batchService;</span>
<span class="nc" id="L21">    private final Environment env;</span>
<span class="nc" id="L22">    private final MeterRegistry meterRegistry;</span>
<span class="nc" id="L23">    private final JdbcTemplate jdbcTemplate;</span>
<span class="nc" id="L24">    private final FeatureManager togglzManager;</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>