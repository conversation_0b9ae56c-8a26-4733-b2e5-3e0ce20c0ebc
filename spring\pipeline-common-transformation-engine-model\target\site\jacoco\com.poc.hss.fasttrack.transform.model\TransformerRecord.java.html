<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TransformerRecord.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_source">TransformerRecord.java</span></div><h1>TransformerRecord.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.transform.model;

import com.poc.hss.fasttrack.enums.DataPersistMode;
import io.vertx.core.json.JsonObject;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

import java.time.LocalDateTime;

<span class="nc" id="L10">@SuperBuilder</span>
@Getter
public class TransformerRecord {
<span class="nc" id="L13">    private final String id;</span>
<span class="nc" id="L14">    private final Boolean deleted;</span>
<span class="nc" id="L15">    private final String traceId;</span>
<span class="nc" id="L16">    private final String source;</span>
<span class="nc" id="L17">    private final JsonObject data;</span>
<span class="nc" id="L18">    private final JsonObject metadata;</span>
<span class="nc" id="L19">    private final String sql;</span>
<span class="nc" id="L20">    private final String sqlExecutionMode;</span>
<span class="nc" id="L21">    private final String batchId;</span>
<span class="nc" id="L22">    private final LocalDateTime receivedDate;</span>
<span class="nc" id="L23">    private final String sourceComponent;</span>
<span class="nc" id="L24">    private final String sourceGroup;</span>
<span class="nc" id="L25">    private final String kafkaPartitionKey;</span>
<span class="nc" id="L26">    private final JsonObject operationMap;</span>
<span class="nc" id="L27">    private final DataPersistMode dataPersistMode;</span>
<span class="nc" id="L28">    private final Exception exception;</span>
<span class="nc" id="L29">    private final String b3TraceId;</span>

    public static TransformerRecordBuilder&lt;?, ?&gt; from(TransformerRecord transformerRecord) {
<span class="nc" id="L32">        return builder()</span>
<span class="nc" id="L33">                .id(transformerRecord.id)</span>
<span class="nc" id="L34">                .deleted(transformerRecord.deleted)</span>
<span class="nc" id="L35">                .traceId(transformerRecord.traceId)</span>
<span class="nc" id="L36">                .source(transformerRecord.source)</span>
<span class="nc" id="L37">                .data(transformerRecord.data)</span>
<span class="nc" id="L38">                .metadata(transformerRecord.metadata)</span>
<span class="nc" id="L39">                .sql(transformerRecord.sql)</span>
<span class="nc" id="L40">                .sqlExecutionMode(transformerRecord.getSqlExecutionMode())</span>
<span class="nc" id="L41">                .batchId(transformerRecord.batchId)</span>
<span class="nc" id="L42">                .receivedDate(transformerRecord.receivedDate)</span>
<span class="nc" id="L43">                .sourceComponent(transformerRecord.sourceComponent)</span>
<span class="nc" id="L44">                .sourceGroup(transformerRecord.sourceGroup)</span>
<span class="nc" id="L45">                .kafkaPartitionKey(transformerRecord.kafkaPartitionKey)</span>
<span class="nc" id="L46">                .operationMap(transformerRecord.operationMap)</span>
<span class="nc" id="L47">                .dataPersistMode(transformerRecord.dataPersistMode)</span>
<span class="nc" id="L48">                .exception(transformerRecord.exception)</span>
<span class="nc" id="L49">                .b3TraceId(transformerRecord.b3TraceId);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>