<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>TransformerSingleInputContext.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-engine-model</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.transform.model</a> &gt; <span class="el_source">TransformerSingleInputContext.java</span></div><h1>TransformerSingleInputContext.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.transform.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

<span class="nc bnc" id="L7" title="All 16 branches missed.">@EqualsAndHashCode(callSuper = true)</span>
<span class="nc" id="L8">@Data</span>
<span class="nc" id="L9">@SuperBuilder</span>
public class TransformerSingleInputContext extends TransformerInputContext {
<span class="nc" id="L11">    private TransformerRecord record;</span>

    public static TransformerSingleInputContextBuilder&lt;?, ?&gt; from(TransformerSingleInputContext context) {
<span class="nc" id="L14">        return TransformerSingleInputContext.builder()</span>
<span class="nc" id="L15">                .record(context.getRecord())</span>
<span class="nc" id="L16">                .lookupService(context.getLookupService())</span>
<span class="nc" id="L17">                .customApiService(context.getCustomApiService())</span>
<span class="nc" id="L18">                .restTemplate(context.getRestTemplate())</span>
<span class="nc" id="L19">                .batchService(context.getBatchService())</span>
<span class="nc" id="L20">                .env(context.getEnv())</span>
<span class="nc" id="L21">                .meterRegistry(context.getMeterRegistry())</span>
<span class="nc" id="L22">                .togglzManager(context.getTogglzManager());</span>
    }

    public static TransformerSingleInputContextBuilder&lt;?, ?&gt; from(TransformerBatchInputContext context, TransformerRecord record) {
<span class="nc" id="L26">        return TransformerSingleInputContext.builder()</span>
<span class="nc" id="L27">                .record(record)</span>
<span class="nc" id="L28">                .lookupService(context.getLookupService())</span>
<span class="nc" id="L29">                .customApiService(context.getCustomApiService())</span>
<span class="nc" id="L30">                .restTemplate(context.getRestTemplate())</span>
<span class="nc" id="L31">                .batchService(context.getBatchService())</span>
<span class="nc" id="L32">                .env(context.getEnv())</span>
<span class="nc" id="L33">                .meterRegistry(context.getMeterRegistry())</span>
<span class="nc" id="L34">                .togglzManager(context.getTogglzManager());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>