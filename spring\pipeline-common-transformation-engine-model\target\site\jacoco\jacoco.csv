GRO<PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUCTION_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON>CH_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,<PERSON><PERSON>OD_MISSED,METHOD_COVERED
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerBatchInputContext.TransformerBatchInputContextBuilder,16,0,0,0,1,0,3,0,3,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerBatchInputContext.TransformerBatchInputContextBuilderImpl,7,0,0,0,1,0,2,0,2,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria.OperationType,0,62,0,4,0,12,0,6,0,4
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerSingleInputContext,157,0,16,0,22,0,18,0,10,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,Unity2DslOperationSingleFieldInput,214,0,38,0,6,0,33,0,14,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria.FieldTransformationOperation,346,0,62,0,11,0,52,0,21,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerInputContext.TransformerInputContextBuilder,77,0,0,0,1,0,10,0,10,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerSingleInputContext.TransformerSingleInputContextBuilderImpl,7,0,0,0,1,0,2,0,2,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerRecord.TransformerRecordBuilder,147,0,0,0,1,0,19,0,19,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,CustomApiRequest,229,0,38,0,6,0,34,0,15,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest.SortRequest,126,0,22,0,4,0,21,0,10,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,Unity2DslOperationInput,171,0,30,0,5,0,27,0,12,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria.FieldTransformationCriteriaBuilder,39,0,0,0,1,0,6,0,6,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerRecord,179,0,0,0,36,0,20,0,20,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerRecord.TransformerRecordBuilderImpl,7,0,0,0,1,0,2,0,2,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest.SortDirection,0,15,0,0,0,3,0,1,0,1
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,Unity2DslOperationMultiFieldInput,215,0,38,0,6,0,33,0,14,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,Unity2DslOperationSingleFieldInput.Unity2DslOperationSingleFieldInputBuilder,48,0,0,0,1,0,7,0,7,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria,174,0,30,0,7,0,28,0,13,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria.OperationMode,55,0,4,0,11,0,6,0,4,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest,461,0,78,0,11,0,64,0,25,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,CustomApiRequest.CustomApiRequestBuilder,47,0,0,0,1,0,7,0,7,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,Unity2DslOperationMultiFieldInput.Unity2DslOperationMultiFieldInputBuilder,49,0,0,0,1,0,7,0,7,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest.FieldAggregation,168,0,30,0,5,0,27,0,12,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerInputContext,363,0,70,0,10,0,48,0,13,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerSingleInputContext.TransformerSingleInputContextBuilder,16,0,0,0,1,0,3,0,3,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,TransformerBatchInputContext,129,0,16,0,14,0,17,0,9,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest.SortRequest.SortRequestBuilder,28,0,0,0,1,0,5,0,5,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest.LookupRequestBuilder,94,0,0,0,1,0,12,0,12,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria.FieldTransformationOperation.FieldTransformationOperationBuilder,75,0,0,0,1,0,10,0,10,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,FieldTransformationCriteria.FormatterType,0,62,0,4,0,12,0,6,0,4
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,Unity2DslOperationInput.Unity2DslOperationInputBuilder,39,0,0,0,1,0,6,0,6,0
pipeline-common-transformation-engine-model,com.poc.hss.fasttrack.transform.model,LookupRequest.FieldAggregation.FieldAggregationBuilder,36,0,0,0,1,0,6,0,6,0
