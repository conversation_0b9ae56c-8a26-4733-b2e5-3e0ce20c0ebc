import com.poc.hss.fasttrack.transform.model.Unity2DslOperationMultiFieldInput
import com.poc.hss.fasttrack.transform.operation.MultipleInputUnity2DslOperation
import org.apache.commons.lang3.StringUtils

import java.util.stream.Collectors
import java.util.stream.IntStream

class LeftPad extends MultipleInputUnity2DslOperation {
    @Override
    protected String doOperation(Unity2DslOperationMultiFieldInput input) {
        def source = input.getSource()
        def fromFields = input.getFromFields()
        def args = input.getArgs()
        def padChar = args.get("padChar"," ")
        def delimiter = args.get("delimiter","-")
        def sizeArr = args.get("size").split(",")
        return IntStream.range(0, fromFields.size())
                .mapToObj(i -> StringUtils.leftPad(String.valueOf(source.getValue(fromFields.get(i))), Integer.parseInt(sizeArr[i]), padChar))
                .collect(Collectors.joining(delimiter));
    }
}