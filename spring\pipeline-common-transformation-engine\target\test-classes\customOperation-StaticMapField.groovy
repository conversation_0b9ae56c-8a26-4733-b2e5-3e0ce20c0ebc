import com.poc.hss.fasttrack.transform.model.Unity2DslOperationSingleFieldInput
import com.poc.hss.fasttrack.transform.operation.SingleInputUnity2DslOperation

class StaticMapField extends SingleInputUnity2DslOperation {
    @Override
    protected String doOperation(Unity2DslOperationSingleFieldInput input) {
        def source = input.getSource()
        def from = input.getFromField()
        def args = input.getArgs()
        String value = String.valueOf(source.getValue(from));
        switch(value){
            case args.get("criteria1"):
                return String.valueOf(source.getValue(args.get("field1")));
            case args.get("criteria2"):
                return String.valueOf(source.getValue(args.get("field2")));
            case args.get("criteria3"):
                return String.valueOf(source.getValue(args.get("field3")));
        }
        return value
    }
}