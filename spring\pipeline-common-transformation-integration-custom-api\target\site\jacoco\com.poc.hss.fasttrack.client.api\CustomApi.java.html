<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-custom-api</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.api</a> &gt; <span class="el_source">CustomApi.java</span></div><h1>CustomApi.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.client.api;

import com.poc.hss.fasttrack.client.ApiClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component(&quot;com.poc.hss.fasttrack.client.api.CustomApi&quot;)
public class CustomApi {
    private ApiClient apiClient;

    public CustomApi() {
<span class="nc" id="L26">        this(new ApiClient());</span>
<span class="nc" id="L27">    }</span>

    @Autowired
<span class="fc" id="L30">    public CustomApi(ApiClient apiClient) {</span>
<span class="fc" id="L31">        this.apiClient = apiClient;</span>
<span class="fc" id="L32">    }</span>

    public ApiClient getApiClient() {
<span class="fc" id="L35">        return apiClient;</span>
    }

    public void setApiClient(ApiClient apiClient) {
<span class="nc" id="L39">        this.apiClient = apiClient;</span>
<span class="nc" id="L40">    }</span>

    public&lt;T&gt; T query(String queryName, Map&lt;String, String&gt; headers, Integer page, Map&lt;String, String&gt; params, Class&lt;T&gt; to) throws RestClientException {

        // verify the required parameter 'accessSchemaName' is set
<span class="pc bpc" id="L45" title="1 of 2 branches missed.">        if (queryName == null) {</span>
<span class="nc" id="L46">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'queryName' when calling query&quot;);</span>
        }
        // create path and map variables
<span class="fc" id="L49">        final Map&lt;String, Object&gt; uriVariables = new HashMap&lt;String, Object&gt;();</span>
<span class="fc" id="L50">        uriVariables.put(&quot;queryName&quot;, queryName);</span>
<span class="fc" id="L51">        String path = UriComponentsBuilder.fromPath(&quot;/custom/{queryName}&quot;).buildAndExpand(uriVariables).toUriString();</span>
        
<span class="fc" id="L53">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="fc" id="L54">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="fc" id="L55">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="fc" id="L57">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="fc" id="L60">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="fc" id="L61">        final String[] contentTypes = { </span>
            &quot;application/json&quot;
         };
<span class="fc" id="L64">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="fc" id="L66">        String[] authNames = new String[] {  };</span>

<span class="fc" id="L68">        ParameterizedTypeReference&lt;T&gt; returnType = new ParameterizedTypeReference&lt;T&gt;() {};</span>
<span class="fc" id="L69">        return apiClient.invokeAPI(path, HttpMethod.GET, queryParams, null, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>