<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomQueryResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-custom-api</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">CustomQueryResponse.java</span></div><h1>CustomQueryResponse.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.client.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

<span class="nc bnc" id="L11" title="All 22 branches missed.">@Data</span>
<span class="pc" id="L12">@Builder</span>
public class CustomQueryResponse {

<span class="fc" id="L15">    private List&lt;Map&lt;String, Object&gt;&gt; resultMessages;</span>
<span class="nc" id="L16">    private PageInfo pageInfo;</span>

<span class="nc bnc" id="L18" title="All 18 branches missed.">    @Data</span>
<span class="pc" id="L19">    @Builder</span>
<span class="fc" id="L20">    @AllArgsConstructor</span>
<span class="nc" id="L21">    @NoArgsConstructor</span>
    public static class PageInfo {
<span class="nc" id="L23">        private int nextPageIdx;</span>
<span class="nc" id="L24">        private int prePageIdx;</span>
<span class="nc" id="L25">        private int currentPageIdx;</span>
<span class="nc" id="L26">        private int pageSize;</span>
<span class="nc" id="L27">        private int totalNumberOfRecords;</span>
<span class="nc" id="L28">        private int totalPage;</span>
    }

}

</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>