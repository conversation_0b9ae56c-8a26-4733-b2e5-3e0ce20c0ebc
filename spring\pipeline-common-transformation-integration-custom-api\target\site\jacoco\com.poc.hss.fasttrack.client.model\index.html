<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.client.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-custom-api</a> &gt; <span class="el_package">com.poc.hss.fasttrack.client.model</span></div><h1>com.poc.hss.fasttrack.client.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">306 of 417</td><td class="ctr2">26%</td><td class="bar">40 of 40</td><td class="ctr2">0%</td><td class="ctr1">46</td><td class="ctr2">63</td><td class="ctr1">10</td><td class="ctr2">14</td><td class="ctr1">26</td><td class="ctr2">43</td><td class="ctr1">0</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a2"><a href="CustomQueryResponse$PageInfo.html" class="el_class">CustomQueryResponse.PageInfo</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="104" height="10" title="173" alt="173"/><img src="../jacoco-resources/greenbar.gif" width="15" height="10" title="25" alt="25"/></td><td class="ctr2" id="c2">12%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="98" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">26</td><td class="ctr2" id="g0">28</td><td class="ctr1" id="h0">8</td><td class="ctr2" id="i0">10</td><td class="ctr1" id="j0">17</td><td class="ctr2" id="k0">19</td><td class="ctr1" id="l0">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="CustomQueryResponse.html" class="el_class">CustomQueryResponse</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="67" height="10" title="111" alt="111"/><img src="../jacoco-resources/greenbar.gif" width="9" height="10" title="16" alt="16"/></td><td class="ctr2" id="c3">12%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="22" alt="22"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">18</td><td class="ctr2" id="g1">21</td><td class="ctr1" id="h1">2</td><td class="ctr2" id="i1">4</td><td class="ctr1" id="j1">7</td><td class="ctr2" id="k1">10</td><td class="ctr1" id="l1">0</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="CustomQueryResponse$PageInfo$PageInfoBuilder.html" class="el_class">CustomQueryResponse.PageInfo.PageInfoBuilder</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="8" height="10" title="14" alt="14"/><img src="../jacoco-resources/greenbar.gif" width="29" height="10" title="49" alt="49"/></td><td class="ctr2" id="c0">77%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">1</td><td class="ctr2" id="g2">9</td><td class="ctr1" id="h2">0</td><td class="ctr2" id="i2">1</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">9</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="CustomQueryResponse$CustomQueryResponseBuilder.html" class="el_class">CustomQueryResponse.CustomQueryResponseBuilder</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="4" height="10" title="8" alt="8"/><img src="../jacoco-resources/greenbar.gif" width="12" height="10" title="21" alt="21"/></td><td class="ctr2" id="c1">72%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">5</td><td class="ctr1" id="h3">0</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>