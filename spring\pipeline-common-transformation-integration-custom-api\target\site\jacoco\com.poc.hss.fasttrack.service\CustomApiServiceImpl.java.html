<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>CustomApiServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-custom-api</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">CustomApiServiceImpl.java</span></div><h1>CustomApiServiceImpl.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.backoff.BackoffException;
import com.poc.hss.fasttrack.backoff.BackoffTask;
import com.poc.hss.fasttrack.backoff.BackoffTaskFactory;
import com.poc.hss.fasttrack.backoff.strategy.ExponentialBackoffStrategy;
import com.poc.hss.fasttrack.client.api.CustomApi;
import com.poc.hss.fasttrack.client.model.CustomQueryResponse;
import com.poc.hss.fasttrack.transform.model.CustomApiRequest;
import io.vertx.core.json.JsonObject;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L25">public class CustomApiServiceImpl implements CustomApiService {</span>

<span class="fc" id="L27">    private static final Logger logger = LoggerFactory.getLogger(CustomApiServiceImpl.class);</span>

    @Autowired
    private CustomApi customApi;

    @Value(&quot;${customApi.address:http://custom-api}/api&quot;)
    private String customApiAddress;

    @PostConstruct
    private void init() {
<span class="fc" id="L37">        customApi.getApiClient().setBasePath(customApiAddress);</span>
<span class="fc" id="L38">    }</span>

    @Override
    public JsonObject queryOne(CustomApiRequest request) {
<span class="nc" id="L42">        return queryOne(request, false);</span>
    }

    @Override
    public JsonObject queryOne(CustomApiRequest request, boolean required) {
<span class="nc" id="L47">        return queryOne(request, required, 30000);</span>
    }

    @Override
    public JsonObject queryOne(CustomApiRequest request, boolean required, long timeout) {
        try {
<span class="nc" id="L53">            return queryOne(request, required, false, timeout);</span>
<span class="nc" id="L54">        } catch (BackoffException e) {</span>
<span class="nc" id="L55">            return null;</span>
        }
    }

    @Override
    public JsonObject queryOne(CustomApiRequest request, boolean required, boolean doThrow) throws BackoffException {
<span class="nc" id="L61">        return queryOne(request, required, doThrow, 30000);</span>
    }

    @Override
    public JsonObject queryOne(CustomApiRequest request, boolean required, boolean doThrow, long timeout) throws BackoffException {
<span class="nc" id="L66">        logger.info(&quot;Single query for request {}, required={}&quot;, request, required);</span>

<span class="nc bnc" id="L68" title="All 4 branches missed.">        BackoffTask&lt;JsonObject&gt; backoff = BackoffTaskFactory.create(ExponentialBackoffStrategy.getInstance(), res -&gt; !required || Objects.nonNull(res));</span>

        try {
<span class="nc" id="L71">            return backoff.get(</span>
<span class="nc" id="L72">                    () -&gt; queryList(</span>
<span class="nc" id="L73">                            request.toBuilder()</span>
<span class="nc" id="L74">                                    .page(0)</span>
<span class="nc" id="L75">                                    .build()</span>
<span class="nc" id="L76">                    ).stream().findFirst().orElse(null),</span>
<span class="nc" id="L77">                    Duration.ofMillis(timeout)</span>
            );
<span class="nc" id="L79">        } catch (BackoffException e) {</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">            if (doThrow)</span>
<span class="nc" id="L81">                throw e;</span>
            else {
<span class="nc" id="L83">                logger.error(&quot;Backoff timeout for request {}&quot;, request);</span>
<span class="nc" id="L84">                return null;</span>
            }
        }
    }

    @Override
    public List&lt;JsonObject&gt; queryList(CustomApiRequest request) {
<span class="fc" id="L91">        logger.info(&quot;Query for request {}&quot;, request);</span>
<span class="fc" id="L92">        CustomQueryResponse response = customApi.query(request.getQueryName(), request.getHeaders(), request.getPage(), request.getParams(), CustomQueryResponse.class);</span>

<span class="fc" id="L94">        return CollectionUtils.emptyIfNull(response.getResultMessages())</span>
<span class="fc" id="L95">                .stream()</span>
<span class="fc" id="L96">                .map(JsonObject::new)</span>
<span class="fc" id="L97">                .collect(Collectors.toList());</span>
    }

    @Override
    public JsonObject queryListAsResponseJsonObject(CustomApiRequest request) {
<span class="fc" id="L102">        logger.info(&quot;Query for request {}&quot;, request);</span>
<span class="fc" id="L103">        String response = customApi.query(request.getQueryName(), request.getHeaders(), request.getPage(), request.getParams(), String.class);</span>

<span class="fc" id="L105">        return new JsonObject(response);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>