G<PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUC<PERSON>ON_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON>CH_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,LINE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,<PERSON>THOD_MISSED,METHOD_COVERED
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.service,CustomApiServiceImpl,82,66,6,0,20,13,10,5,7,5
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.client.api,CustomApi.new ParameterizedTypeReference() {...},0,3,0,0,0,1,0,1,0,1
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.client.api,CustomApi,16,84,1,1,5,18,3,3,2,3
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.client.model,CustomQueryResponse.CustomQueryResponseBuilder,8,21,0,0,0,1,1,4,1,4
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.client.model,CustomQueryResponse.PageInfo,173,25,18,0,8,2,26,2,17,2
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.client.model,CustomQueryResponse.PageInfo.PageInfoBuilder,14,49,0,0,0,1,1,8,1,8
pipeline-common-transformation-integration-custom-api,com.poc.hss.fasttrack.client.model,CustomQueryResponse,111,16,22,0,2,2,18,3,7,3
