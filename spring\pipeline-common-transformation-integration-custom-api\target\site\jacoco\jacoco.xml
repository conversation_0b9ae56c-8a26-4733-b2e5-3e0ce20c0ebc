<?xml version="1.0" encoding="UTF-8" standalone="yes"?><!DOCTYPE report PUBLIC "-//JACOCO//DTD Report 1.1//EN" "report.dtd"><report name="pipeline-common-transformation-integration-custom-api"><sessioninfo id="H344L0L63UFKL6Z-d30a472e" start="1752782343228" dump="1752782349770"/><package name="com/poc/hss/fasttrack/service"><class name="com/poc/hss/fasttrack/service/CustomApiServiceImpl" sourcefilename="CustomApiServiceImpl.java"><method name="&lt;init&gt;" desc="()V" line="25"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="init" desc="()V" line="37"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="2"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="queryOne" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;)Lio/vertx/core/json/JsonObject;" line="42"><counter type="INSTRUCTION" missed="5" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryOne" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;Z)Lio/vertx/core/json/JsonObject;" line="47"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryOne" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;ZJ)Lio/vertx/core/json/JsonObject;" line="53"><counter type="INSTRUCTION" missed="10" covered="0"/><counter type="LINE" missed="3" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryOne" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;ZZ)Lio/vertx/core/json/JsonObject;" line="61"><counter type="INSTRUCTION" missed="7" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryOne" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;ZZJ)Lio/vertx/core/json/JsonObject;" line="66"><counter type="INSTRUCTION" missed="31" covered="0"/><counter type="BRANCH" missed="2" covered="0"/><counter type="LINE" missed="9" covered="0"/><counter type="COMPLEXITY" missed="2" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="queryList" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;)Ljava/util/List;" line="91"><counter type="INSTRUCTION" missed="0" covered="28"/><counter type="LINE" missed="0" covered="6"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="queryListAsResponseJsonObject" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;)Lio/vertx/core/json/JsonObject;" line="102"><counter type="INSTRUCTION" missed="0" covered="23"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="lambda$queryOne$1" desc="(Lcom/poc/hss/fasttrack/transform/model/CustomApiRequest;)Lio/vertx/core/json/JsonObject;" line="72"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="5" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="lambda$queryOne$0" desc="(ZLio/vertx/core/json/JsonObject;)Z" line="68"><counter type="INSTRUCTION" missed="9" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;clinit&gt;" desc="()V" line="27"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="82" covered="66"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="20" covered="13"/><counter type="COMPLEXITY" missed="10" covered="5"/><counter type="METHOD" missed="7" covered="5"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="CustomApiServiceImpl.java"><line nr="25" mi="0" ci="3" mb="0" cb="0"/><line nr="27" mi="0" ci="4" mb="0" cb="0"/><line nr="37" mi="0" ci="7" mb="0" cb="0"/><line nr="38" mi="0" ci="1" mb="0" cb="0"/><line nr="42" mi="5" ci="0" mb="0" cb="0"/><line nr="47" mi="6" ci="0" mb="0" cb="0"/><line nr="53" mi="7" ci="0" mb="0" cb="0"/><line nr="54" mi="1" ci="0" mb="0" cb="0"/><line nr="55" mi="2" ci="0" mb="0" cb="0"/><line nr="61" mi="7" ci="0" mb="0" cb="0"/><line nr="66" mi="6" ci="0" mb="0" cb="0"/><line nr="68" mi="14" ci="0" mb="4" cb="0"/><line nr="71" mi="8" ci="0" mb="0" cb="0"/><line nr="72" mi="1" ci="0" mb="0" cb="0"/><line nr="73" mi="2" ci="0" mb="0" cb="0"/><line nr="74" mi="2" ci="0" mb="0" cb="0"/><line nr="75" mi="1" ci="0" mb="0" cb="0"/><line nr="76" mi="8" ci="0" mb="0" cb="0"/><line nr="77" mi="1" ci="0" mb="0" cb="0"/><line nr="79" mi="1" ci="0" mb="0" cb="0"/><line nr="80" mi="2" ci="0" mb="2" cb="0"/><line nr="81" mi="2" ci="0" mb="0" cb="0"/><line nr="83" mi="4" ci="0" mb="0" cb="0"/><line nr="84" mi="2" ci="0" mb="0" cb="0"/><line nr="91" mi="0" ci="4" mb="0" cb="0"/><line nr="92" mi="0" ci="14" mb="0" cb="0"/><line nr="94" mi="0" ci="4" mb="0" cb="0"/><line nr="95" mi="0" ci="2" mb="0" cb="0"/><line nr="96" mi="0" ci="1" mb="0" cb="0"/><line nr="97" mi="0" ci="3" mb="0" cb="0"/><line nr="102" mi="0" ci="4" mb="0" cb="0"/><line nr="103" mi="0" ci="14" mb="0" cb="0"/><line nr="105" mi="0" ci="5" mb="0" cb="0"/><counter type="INSTRUCTION" missed="82" covered="66"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="20" covered="13"/><counter type="COMPLEXITY" missed="10" covered="5"/><counter type="METHOD" missed="7" covered="5"/><counter type="CLASS" missed="0" covered="1"/></sourcefile><counter type="INSTRUCTION" missed="82" covered="66"/><counter type="BRANCH" missed="6" covered="0"/><counter type="LINE" missed="20" covered="13"/><counter type="COMPLEXITY" missed="10" covered="5"/><counter type="METHOD" missed="7" covered="5"/><counter type="CLASS" missed="0" covered="1"/></package><package name="com/poc/hss/fasttrack/client/api"><class name="com/poc/hss/fasttrack/client/api/CustomApi$1" sourcefilename="CustomApi.java"><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/client/api/CustomApi;)V" line="68"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/client/api/CustomApi" sourcefilename="CustomApi.java"><method name="&lt;init&gt;" desc="()V" line="26"><counter type="INSTRUCTION" missed="6" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(Lcom/poc/hss/fasttrack/client/ApiClient;)V" line="30"><counter type="INSTRUCTION" missed="0" covered="6"/><counter type="LINE" missed="0" covered="3"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getApiClient" desc="()Lcom/poc/hss/fasttrack/client/ApiClient;" line="35"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="setApiClient" desc="(Lcom/poc/hss/fasttrack/client/ApiClient;)V" line="39"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="2" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="query" desc="(Ljava/lang/String;Ljava/util/Map;Ljava/lang/Integer;Ljava/util/Map;Ljava/lang/Class;)Ljava/lang/Object;" line="45"><counter type="INSTRUCTION" missed="6" covered="75"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="1" covered="14"/><counter type="COMPLEXITY" missed="1" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><counter type="INSTRUCTION" missed="16" covered="84"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="5" covered="18"/><counter type="COMPLEXITY" missed="3" covered="3"/><counter type="METHOD" missed="2" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="CustomApi.java"><line nr="26" mi="5" ci="0" mb="0" cb="0"/><line nr="27" mi="1" ci="0" mb="0" cb="0"/><line nr="30" mi="0" ci="2" mb="0" cb="0"/><line nr="31" mi="0" ci="3" mb="0" cb="0"/><line nr="32" mi="0" ci="1" mb="0" cb="0"/><line nr="35" mi="0" ci="3" mb="0" cb="0"/><line nr="39" mi="3" ci="0" mb="0" cb="0"/><line nr="40" mi="1" ci="0" mb="0" cb="0"/><line nr="45" mi="0" ci="2" mb="1" cb="1"/><line nr="46" mi="6" ci="0" mb="0" cb="0"/><line nr="49" mi="0" ci="4" mb="0" cb="0"/><line nr="50" mi="0" ci="5" mb="0" cb="0"/><line nr="51" mi="0" ci="6" mb="0" cb="0"/><line nr="53" mi="0" ci="4" mb="0" cb="0"/><line nr="54" mi="0" ci="4" mb="0" cb="0"/><line nr="55" mi="0" ci="4" mb="0" cb="0"/><line nr="57" mi="0" ci="7" mb="0" cb="0"/><line nr="60" mi="0" ci="5" mb="0" cb="0"/><line nr="61" mi="0" ci="7" mb="0" cb="0"/><line nr="64" mi="0" ci="5" mb="0" cb="0"/><line nr="66" mi="0" ci="3" mb="0" cb="0"/><line nr="68" mi="0" ci="8" mb="0" cb="0"/><line nr="69" mi="0" ci="14" mb="0" cb="0"/><counter type="INSTRUCTION" missed="16" covered="87"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="5" covered="18"/><counter type="COMPLEXITY" missed="3" covered="4"/><counter type="METHOD" missed="2" covered="4"/><counter type="CLASS" missed="0" covered="2"/></sourcefile><counter type="INSTRUCTION" missed="16" covered="87"/><counter type="BRANCH" missed="1" covered="1"/><counter type="LINE" missed="5" covered="18"/><counter type="COMPLEXITY" missed="3" covered="4"/><counter type="METHOD" missed="2" covered="4"/><counter type="CLASS" missed="0" covered="2"/></package><package name="com/poc/hss/fasttrack/client/model"><class name="com/poc/hss/fasttrack/client/model/CustomQueryResponse$CustomQueryResponseBuilder" sourcefilename="CustomQueryResponse.java"><method name="&lt;init&gt;" desc="()V" line="12"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="resultMessages" desc="(Ljava/util/List;)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$CustomQueryResponseBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="pageInfo" desc="(Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo;)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$CustomQueryResponseBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse;" line="12"><counter type="INSTRUCTION" missed="0" covered="8"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="12"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="8" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="4"/><counter type="METHOD" missed="1" covered="4"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo" sourcefilename="CustomQueryResponse.java"><method name="builder" desc="()Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getNextPageIdx" desc="()I" line="23"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getPrePageIdx" desc="()I" line="24"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getCurrentPageIdx" desc="()I" line="25"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getPageSize" desc="()I" line="26"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTotalNumberOfRecords" desc="()I" line="27"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="getTotalPage" desc="()I" line="28"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setNextPageIdx" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setPrePageIdx" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setCurrentPageIdx" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setPageSize" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setTotalNumberOfRecords" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setTotalPage" desc="(I)V" line="18"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="18"><counter type="INSTRUCTION" missed="63" covered="0"/><counter type="BRANCH" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="18"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="18"><counter type="INSTRUCTION" missed="48" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="18"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="&lt;init&gt;" desc="(IIIIII)V" line="20"><counter type="INSTRUCTION" missed="0" covered="21"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="&lt;init&gt;" desc="()V" line="21"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="173" covered="25"/><counter type="BRANCH" missed="18" covered="0"/><counter type="LINE" missed="8" covered="2"/><counter type="COMPLEXITY" missed="26" covered="2"/><counter type="METHOD" missed="17" covered="2"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder" sourcefilename="CustomQueryResponse.java"><method name="&lt;init&gt;" desc="()V" line="19"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="nextPageIdx" desc="(I)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="prePageIdx" desc="(I)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="currentPageIdx" desc="(I)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="pageSize" desc="(I)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="totalNumberOfRecords" desc="(I)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="totalPage" desc="(I)Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo$PageInfoBuilder;" line="19"><counter type="INSTRUCTION" missed="0" covered="5"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="build" desc="()Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo;" line="19"><counter type="INSTRUCTION" missed="0" covered="16"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="toString" desc="()Ljava/lang/String;" line="19"><counter type="INSTRUCTION" missed="14" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="14" covered="49"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="1" covered="8"/><counter type="METHOD" missed="1" covered="8"/><counter type="CLASS" missed="0" covered="1"/></class><class name="com/poc/hss/fasttrack/client/model/CustomQueryResponse" sourcefilename="CustomQueryResponse.java"><method name="&lt;init&gt;" desc="(Ljava/util/List;Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo;)V" line="12"><counter type="INSTRUCTION" missed="0" covered="9"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="builder" desc="()Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$CustomQueryResponseBuilder;" line="12"><counter type="INSTRUCTION" missed="0" covered="4"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getResultMessages" desc="()Ljava/util/List;" line="15"><counter type="INSTRUCTION" missed="0" covered="3"/><counter type="LINE" missed="0" covered="1"/><counter type="COMPLEXITY" missed="0" covered="1"/><counter type="METHOD" missed="0" covered="1"/></method><method name="getPageInfo" desc="()Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo;" line="16"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setResultMessages" desc="(Ljava/util/List;)V" line="11"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="setPageInfo" desc="(Lcom/poc/hss/fasttrack/client/model/CustomQueryResponse$PageInfo;)V" line="11"><counter type="INSTRUCTION" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="equals" desc="(Ljava/lang/Object;)Z" line="11"><counter type="INSTRUCTION" missed="55" covered="0"/><counter type="BRANCH" missed="18" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="10" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="canEqual" desc="(Ljava/lang/Object;)Z" line="11"><counter type="INSTRUCTION" missed="3" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="hashCode" desc="()I" line="11"><counter type="INSTRUCTION" missed="34" covered="0"/><counter type="BRANCH" missed="4" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="3" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><method name="toString" desc="()Ljava/lang/String;" line="11"><counter type="INSTRUCTION" missed="8" covered="0"/><counter type="LINE" missed="1" covered="0"/><counter type="COMPLEXITY" missed="1" covered="0"/><counter type="METHOD" missed="1" covered="0"/></method><counter type="INSTRUCTION" missed="111" covered="16"/><counter type="BRANCH" missed="22" covered="0"/><counter type="LINE" missed="2" covered="2"/><counter type="COMPLEXITY" missed="18" covered="3"/><counter type="METHOD" missed="7" covered="3"/><counter type="CLASS" missed="0" covered="1"/></class><sourcefile name="CustomQueryResponse.java"><line nr="11" mi="108" ci="0" mb="22" cb="0"/><line nr="12" mi="8" ci="34" mb="0" cb="0"/><line nr="15" mi="0" ci="3" mb="0" cb="0"/><line nr="16" mi="3" ci="0" mb="0" cb="0"/><line nr="18" mi="152" ci="0" mb="18" cb="0"/><line nr="19" mi="14" ci="53" mb="0" cb="0"/><line nr="20" mi="0" ci="21" mb="0" cb="0"/><line nr="21" mi="3" ci="0" mb="0" cb="0"/><line nr="23" mi="3" ci="0" mb="0" cb="0"/><line nr="24" mi="3" ci="0" mb="0" cb="0"/><line nr="25" mi="3" ci="0" mb="0" cb="0"/><line nr="26" mi="3" ci="0" mb="0" cb="0"/><line nr="27" mi="3" ci="0" mb="0" cb="0"/><line nr="28" mi="3" ci="0" mb="0" cb="0"/><counter type="INSTRUCTION" missed="306" covered="111"/><counter type="BRANCH" missed="40" covered="0"/><counter type="LINE" missed="10" covered="4"/><counter type="COMPLEXITY" missed="46" covered="17"/><counter type="METHOD" missed="26" covered="17"/><counter type="CLASS" missed="0" covered="4"/></sourcefile><counter type="INSTRUCTION" missed="306" covered="111"/><counter type="BRANCH" missed="40" covered="0"/><counter type="LINE" missed="10" covered="4"/><counter type="COMPLEXITY" missed="46" covered="17"/><counter type="METHOD" missed="26" covered="17"/><counter type="CLASS" missed="0" covered="4"/></package><counter type="INSTRUCTION" missed="404" covered="264"/><counter type="BRANCH" missed="47" covered="1"/><counter type="LINE" missed="35" covered="35"/><counter type="COMPLEXITY" missed="59" covered="26"/><counter type="METHOD" missed="35" covered="26"/><counter type="CLASS" missed="0" covered="7"/></report>