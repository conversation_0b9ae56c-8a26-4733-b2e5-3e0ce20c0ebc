<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupApi.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.api</a> &gt; <span class="el_source">LookupApi.java</span></div><h1>LookupApi.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.client.api;

import com.poc.hss.fasttrack.client.ApiClient;
import com.poc.hss.fasttrack.client.model.LookupRequest;
import com.poc.hss.fasttrack.client.model.LookupResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;
import org.springframework.web.util.UriComponentsBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component(&quot;com.poc.hss.fasttrack.client.api.LookupApi&quot;)
public class LookupApi {
    private ApiClient apiClient;

    public LookupApi() {
<span class="nc" id="L28">        this(new ApiClient());</span>
<span class="nc" id="L29">    }</span>

    @Autowired
<span class="fc" id="L32">    public LookupApi(ApiClient apiClient) {</span>
<span class="fc" id="L33">        this.apiClient = apiClient;</span>
<span class="fc" id="L34">    }</span>

    public ApiClient getApiClient() {
<span class="fc" id="L37">        return apiClient;</span>
    }

    public void setApiClient(ApiClient apiClient) {
<span class="nc" id="L41">        this.apiClient = apiClient;</span>
<span class="nc" id="L42">    }</span>

    /**
     * Lookup by criteria
     * 
     * &lt;p&gt;&lt;b&gt;200&lt;/b&gt; - OK
     * @param accessSchemaName Access schema name
     * @param body The body parameter
     * @return LookupResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupResponse lookup(String accessSchemaName, LookupRequest body) throws RestClientException {
<span class="fc" id="L54">        Object postBody = body;</span>
        // verify the required parameter 'accessSchemaName' is set
<span class="pc bpc" id="L56" title="1 of 2 branches missed.">        if (accessSchemaName == null) {</span>
<span class="nc" id="L57">            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, &quot;Missing the required parameter 'accessSchemaName' when calling lookup&quot;);</span>
        }
        // create path and map variables
<span class="fc" id="L60">        final Map&lt;String, Object&gt; uriVariables = new HashMap&lt;String, Object&gt;();</span>
<span class="fc" id="L61">        uriVariables.put(&quot;accessSchemaName&quot;, accessSchemaName);</span>
<span class="fc" id="L62">        String path = UriComponentsBuilder.fromPath(&quot;/lookup/{accessSchemaName}&quot;).buildAndExpand(uriVariables).toUriString();</span>
        
<span class="fc" id="L64">        final MultiValueMap&lt;String, String&gt; queryParams = new LinkedMultiValueMap&lt;String, String&gt;();</span>
<span class="fc" id="L65">        final HttpHeaders headerParams = new HttpHeaders();</span>
<span class="fc" id="L66">        final MultiValueMap&lt;String, Object&gt; formParams = new LinkedMultiValueMap&lt;String, Object&gt;();</span>

<span class="fc" id="L68">        final String[] accepts = { </span>
            &quot;application/json&quot;
         };
<span class="fc" id="L71">        final List&lt;MediaType&gt; accept = apiClient.selectHeaderAccept(accepts);</span>
<span class="fc" id="L72">        final String[] contentTypes = { </span>
            &quot;application/json&quot;
         };
<span class="fc" id="L75">        final MediaType contentType = apiClient.selectHeaderContentType(contentTypes);</span>

<span class="fc" id="L77">        String[] authNames = new String[] {  };</span>

<span class="fc" id="L79">        ParameterizedTypeReference&lt;LookupResponse&gt; returnType = new ParameterizedTypeReference&lt;LookupResponse&gt;() {};</span>
<span class="fc" id="L80">        return apiClient.invokeAPI(path, HttpMethod.POST, queryParams, postBody, headerParams, formParams, accept, contentType, authNames, returnType);</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>