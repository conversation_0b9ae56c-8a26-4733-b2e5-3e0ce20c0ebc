<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>Error.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">Error.java</span></div><h1>Error.java</h1><pre class="source lang-java linenums">/*
 * Data service
 * This API includes necessary endpoints to fetch the data requried for lookup service
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Objects;
/**
 * Error
 */


<span class="nc" id="L24">public class Error {</span>
<span class="nc" id="L25">  @JsonProperty(&quot;code&quot;)</span>
  private String code = null;

<span class="nc" id="L28">  @JsonProperty(&quot;message&quot;)</span>
  private String message = null;

  public Error code(String code) {
<span class="nc" id="L32">    this.code = code;</span>
<span class="nc" id="L33">    return this;</span>
  }

   /**
   * Get code
   * @return code
  **/
  @Schema(description = &quot;&quot;)
  public String getCode() {
<span class="nc" id="L42">    return code;</span>
  }

  public void setCode(String code) {
<span class="nc" id="L46">    this.code = code;</span>
<span class="nc" id="L47">  }</span>

  public Error message(String message) {
<span class="nc" id="L50">    this.message = message;</span>
<span class="nc" id="L51">    return this;</span>
  }

   /**
   * Get message
   * @return message
  **/
  @Schema(description = &quot;&quot;)
  public String getMessage() {
<span class="nc" id="L60">    return message;</span>
  }

  public void setMessage(String message) {
<span class="nc" id="L64">    this.message = message;</span>
<span class="nc" id="L65">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L70" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L71">      return true;</span>
    }
<span class="nc bnc" id="L73" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L74">      return false;</span>
    }
<span class="nc" id="L76">    Error error = (Error) o;</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">    return Objects.equals(this.code, error.code) &amp;&amp;</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">        Objects.equals(this.message, error.message);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L83">    return Objects.hash(code, message);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L89">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L90">    sb.append(&quot;class Error {\n&quot;);</span>
    
<span class="nc" id="L92">    sb.append(&quot;    code: &quot;).append(toIndentedString(code)).append(&quot;\n&quot;);</span>
<span class="nc" id="L93">    sb.append(&quot;    message: &quot;).append(toIndentedString(message)).append(&quot;\n&quot;);</span>
<span class="nc" id="L94">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L95">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L103" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L104">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L106">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>