<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>FieldAggregation.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">FieldAggregation.java</span></div><h1>FieldAggregation.java</h1><pre class="source lang-java linenums">/*
 * Data lookup service
 * This API includes necessary endpoints to fetch the data requried for lookup service
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
/**
 * FieldAggregation
 */


<span class="nc" id="L26">public class FieldAggregation {</span>
<span class="nc" id="L27">  @JsonProperty(&quot;function&quot;)</span>
  private String function = null;

<span class="nc" id="L30">  @JsonProperty(&quot;field&quot;)</span>
  private String field = null;

<span class="nc" id="L33">  @JsonProperty(&quot;rename&quot;)</span>
  private String rename = null;

  public FieldAggregation function(String function) {
<span class="nc" id="L37">    this.function = function;</span>
<span class="nc" id="L38">    return this;</span>
  }

   /**
   * Get function
   * @return function
  **/
  @Schema(description = &quot;&quot;)
  public String getFunction() {
<span class="nc" id="L47">    return function;</span>
  }

  public void setFunction(String function) {
<span class="nc" id="L51">    this.function = function;</span>
<span class="nc" id="L52">  }</span>

  public FieldAggregation field(String field) {
<span class="nc" id="L55">    this.field = field;</span>
<span class="nc" id="L56">    return this;</span>
  }

   /**
   * Get field
   * @return field
  **/
  @Schema(description = &quot;&quot;)
  public String getField() {
<span class="nc" id="L65">    return field;</span>
  }

  public void setField(String field) {
<span class="nc" id="L69">    this.field = field;</span>
<span class="nc" id="L70">  }</span>

  public FieldAggregation rename(String rename) {
<span class="nc" id="L73">    this.rename = rename;</span>
<span class="nc" id="L74">    return this;</span>
  }

   /**
   * Get rename
   * @return rename
  **/
  @Schema(description = &quot;&quot;)
  public String getRename() {
<span class="nc" id="L83">    return rename;</span>
  }

  public void setRename(String rename) {
<span class="nc" id="L87">    this.rename = rename;</span>
<span class="nc" id="L88">  }</span>


  @Override
  public boolean equals(Object o) {
<span class="nc bnc" id="L93" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L94">      return true;</span>
    }
<span class="nc bnc" id="L96" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L97">      return false;</span>
    }
<span class="nc" id="L99">    FieldAggregation fieldAggregation = (FieldAggregation) o;</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">    return Objects.equals(this.function, fieldAggregation.function) &amp;&amp;</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">        Objects.equals(this.field, fieldAggregation.field) &amp;&amp;</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">        Objects.equals(this.rename, fieldAggregation.rename);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L107">    return Objects.hash(function, field, rename);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L113">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L114">    sb.append(&quot;class FieldAggregation {\n&quot;);</span>
    
<span class="nc" id="L116">    sb.append(&quot;    function: &quot;).append(toIndentedString(function)).append(&quot;\n&quot;);</span>
<span class="nc" id="L117">    sb.append(&quot;    field: &quot;).append(toIndentedString(field)).append(&quot;\n&quot;);</span>
<span class="nc" id="L118">    sb.append(&quot;    rename: &quot;).append(toIndentedString(rename)).append(&quot;\n&quot;);</span>
<span class="nc" id="L119">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L120">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
<span class="nc bnc" id="L128" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L129">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L131">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>