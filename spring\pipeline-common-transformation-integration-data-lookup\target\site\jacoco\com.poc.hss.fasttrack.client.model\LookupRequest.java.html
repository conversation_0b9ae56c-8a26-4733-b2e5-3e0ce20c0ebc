<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupRequest.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">LookupRequest.java</span></div><h1>LookupRequest.java</h1><pre class="source lang-java linenums">/*
 * Data lookup service
 * This API includes necessary endpoints to fetch the data requried for lookup service
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
/**
 * LookupRequest
 */


<span class="fc" id="L26">public class LookupRequest {</span>
<span class="fc" id="L27">  @JsonProperty(&quot;criteria&quot;)</span>
  private String criteria = null;

<span class="fc" id="L30">  @JsonProperty(&quot;fields&quot;)</span>
  private List&lt;String&gt; fields = null;

<span class="fc" id="L33">  @JsonProperty(&quot;orders&quot;)</span>
  private List&lt;String&gt; orders = null;

<span class="fc" id="L36">  @JsonProperty(&quot;limit&quot;)</span>
  private Integer limit = null;

<span class="fc" id="L39">  @JsonProperty(&quot;offset&quot;)</span>
  private Integer offset = null;

<span class="fc" id="L42">  @JsonProperty(&quot;groups&quot;)</span>
  private List&lt;String&gt; groups = null;

<span class="fc" id="L45">  @JsonProperty(&quot;fieldAggregations&quot;)</span>
  private List&lt;FieldAggregation&gt; fieldAggregations = null;

<span class="fc" id="L48">  @JsonProperty(&quot;distinct&quot;)</span>
  private Boolean distinct = null;

  public LookupRequest criteria(String criteria) {
<span class="fc" id="L52">    this.criteria = criteria;</span>
<span class="fc" id="L53">    return this;</span>
  }

   /**
   * Get criteria
   * @return criteria
  **/
  @Schema(description = &quot;&quot;)
  public String getCriteria() {
<span class="nc" id="L62">    return criteria;</span>
  }

  public void setCriteria(String criteria) {
<span class="nc" id="L66">    this.criteria = criteria;</span>
<span class="nc" id="L67">  }</span>

  public LookupRequest fields(List&lt;String&gt; fields) {
<span class="fc" id="L70">    this.fields = fields;</span>
<span class="fc" id="L71">    return this;</span>
  }

  public LookupRequest addFieldsItem(String fieldsItem) {
<span class="nc bnc" id="L75" title="All 2 branches missed.">    if (this.fields == null) {</span>
<span class="nc" id="L76">      this.fields = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L78">    this.fields.add(fieldsItem);</span>
<span class="nc" id="L79">    return this;</span>
  }

   /**
   * Get fields
   * @return fields
  **/
  @Schema(description = &quot;&quot;)
  public List&lt;String&gt; getFields() {
<span class="nc" id="L88">    return fields;</span>
  }

  public void setFields(List&lt;String&gt; fields) {
<span class="nc" id="L92">    this.fields = fields;</span>
<span class="nc" id="L93">  }</span>

  public LookupRequest orders(List&lt;String&gt; orders) {
<span class="fc" id="L96">    this.orders = orders;</span>
<span class="fc" id="L97">    return this;</span>
  }

  public LookupRequest addOrdersItem(String ordersItem) {
<span class="nc bnc" id="L101" title="All 2 branches missed.">    if (this.orders == null) {</span>
<span class="nc" id="L102">      this.orders = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L104">    this.orders.add(ordersItem);</span>
<span class="nc" id="L105">    return this;</span>
  }

   /**
   * Fields to order by (in format of [field_name]:[asc|desc])
   * @return orders
  **/
  @Schema(example = &quot;username:asc&quot;, description = &quot;Fields to order by (in format of [field_name]:[asc|desc])&quot;)
  public List&lt;String&gt; getOrders() {
<span class="nc" id="L114">    return orders;</span>
  }

  public void setOrders(List&lt;String&gt; orders) {
<span class="nc" id="L118">    this.orders = orders;</span>
<span class="nc" id="L119">  }</span>

  public LookupRequest limit(Integer limit) {
<span class="fc" id="L122">    this.limit = limit;</span>
<span class="fc" id="L123">    return this;</span>
  }

   /**
   * Get limit
   * @return limit
  **/
  @Schema(description = &quot;&quot;)
  public Integer getLimit() {
<span class="nc" id="L132">    return limit;</span>
  }

  public void setLimit(Integer limit) {
<span class="nc" id="L136">    this.limit = limit;</span>
<span class="nc" id="L137">  }</span>

  public LookupRequest offset(Integer offset) {
<span class="fc" id="L140">    this.offset = offset;</span>
<span class="fc" id="L141">    return this;</span>
  }

   /**
   * Get offset
   * @return offset
  **/
  @Schema(description = &quot;&quot;)
  public Integer getOffset() {
<span class="nc" id="L150">    return offset;</span>
  }

  public void setOffset(Integer offset) {
<span class="nc" id="L154">    this.offset = offset;</span>
<span class="nc" id="L155">  }</span>

  public LookupRequest groups(List&lt;String&gt; groups) {
<span class="fc" id="L158">    this.groups = groups;</span>
<span class="fc" id="L159">    return this;</span>
  }

  public LookupRequest addGroupsItem(String groupsItem) {
<span class="nc bnc" id="L163" title="All 2 branches missed.">    if (this.groups == null) {</span>
<span class="nc" id="L164">      this.groups = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L166">    this.groups.add(groupsItem);</span>
<span class="nc" id="L167">    return this;</span>
  }

   /**
   * Get groups
   * @return groups
  **/
  @Schema(description = &quot;&quot;)
  public List&lt;String&gt; getGroups() {
<span class="nc" id="L176">    return groups;</span>
  }

  public void setGroups(List&lt;String&gt; groups) {
<span class="nc" id="L180">    this.groups = groups;</span>
<span class="nc" id="L181">  }</span>

  public LookupRequest fieldAggregations(List&lt;FieldAggregation&gt; fieldAggregations) {
<span class="fc" id="L184">    this.fieldAggregations = fieldAggregations;</span>
<span class="fc" id="L185">    return this;</span>
  }

  public LookupRequest addFieldAggregationsItem(FieldAggregation fieldAggregationsItem) {
<span class="nc bnc" id="L189" title="All 2 branches missed.">    if (this.fieldAggregations == null) {</span>
<span class="nc" id="L190">      this.fieldAggregations = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L192">    this.fieldAggregations.add(fieldAggregationsItem);</span>
<span class="nc" id="L193">    return this;</span>
  }

   /**
   * Take precedence over fields if name conflicts, use together with group
   * @return fieldAggregations
  **/
  @Schema(description = &quot;Take precedence over fields if name conflicts, use together with group&quot;)
  public List&lt;FieldAggregation&gt; getFieldAggregations() {
<span class="nc" id="L202">    return fieldAggregations;</span>
  }

  public void setFieldAggregations(List&lt;FieldAggregation&gt; fieldAggregations) {
<span class="nc" id="L206">    this.fieldAggregations = fieldAggregations;</span>
<span class="nc" id="L207">  }</span>

  public LookupRequest distinct(Boolean distinct) {
<span class="fc" id="L210">    this.distinct = distinct;</span>
<span class="fc" id="L211">    return this;</span>
  }

   /**
   * Get distinct
   * @return distinct
  **/
  @Schema(description = &quot;&quot;)
  public Boolean isDistinct() {
<span class="nc" id="L220">    return distinct;</span>
  }

  public void setDistinct(Boolean distinct) {
<span class="nc" id="L224">    this.distinct = distinct;</span>
<span class="nc" id="L225">  }</span>


  @Override
  public boolean equals(Object o) {
<span class="nc bnc" id="L230" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L231">      return true;</span>
    }
<span class="nc bnc" id="L233" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L234">      return false;</span>
    }
<span class="nc" id="L236">    LookupRequest lookupRequest = (LookupRequest) o;</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">    return Objects.equals(this.criteria, lookupRequest.criteria) &amp;&amp;</span>
<span class="nc bnc" id="L238" title="All 2 branches missed.">        Objects.equals(this.fields, lookupRequest.fields) &amp;&amp;</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">        Objects.equals(this.orders, lookupRequest.orders) &amp;&amp;</span>
<span class="nc bnc" id="L240" title="All 2 branches missed.">        Objects.equals(this.limit, lookupRequest.limit) &amp;&amp;</span>
<span class="nc bnc" id="L241" title="All 2 branches missed.">        Objects.equals(this.offset, lookupRequest.offset) &amp;&amp;</span>
<span class="nc bnc" id="L242" title="All 2 branches missed.">        Objects.equals(this.groups, lookupRequest.groups) &amp;&amp;</span>
<span class="nc bnc" id="L243" title="All 2 branches missed.">        Objects.equals(this.fieldAggregations, lookupRequest.fieldAggregations) &amp;&amp;</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">        Objects.equals(this.distinct, lookupRequest.distinct);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L249">    return Objects.hash(criteria, fields, orders, limit, offset, groups, fieldAggregations, distinct);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L255">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L256">    sb.append(&quot;class LookupRequest {\n&quot;);</span>
    
<span class="nc" id="L258">    sb.append(&quot;    criteria: &quot;).append(toIndentedString(criteria)).append(&quot;\n&quot;);</span>
<span class="nc" id="L259">    sb.append(&quot;    fields: &quot;).append(toIndentedString(fields)).append(&quot;\n&quot;);</span>
<span class="nc" id="L260">    sb.append(&quot;    orders: &quot;).append(toIndentedString(orders)).append(&quot;\n&quot;);</span>
<span class="nc" id="L261">    sb.append(&quot;    limit: &quot;).append(toIndentedString(limit)).append(&quot;\n&quot;);</span>
<span class="nc" id="L262">    sb.append(&quot;    offset: &quot;).append(toIndentedString(offset)).append(&quot;\n&quot;);</span>
<span class="nc" id="L263">    sb.append(&quot;    groups: &quot;).append(toIndentedString(groups)).append(&quot;\n&quot;);</span>
<span class="nc" id="L264">    sb.append(&quot;    fieldAggregations: &quot;).append(toIndentedString(fieldAggregations)).append(&quot;\n&quot;);</span>
<span class="nc" id="L265">    sb.append(&quot;    distinct: &quot;).append(toIndentedString(distinct)).append(&quot;\n&quot;);</span>
<span class="nc" id="L266">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L267">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
<span class="nc bnc" id="L275" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L276">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L278">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>