<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupResponse.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.client.model</a> &gt; <span class="el_source">LookupResponse.java</span></div><h1>LookupResponse.java</h1><pre class="source lang-java linenums">/*
 * Data service
 * This API includes necessary endpoints to fetch the data requried for lookup service
 *
 * OpenAPI spec version: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 */

package com.poc.hss.fasttrack.client.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
/**
 * LookupResponse
 */


<span class="fc" id="L27">public class LookupResponse {</span>
<span class="fc" id="L28">  @JsonProperty(&quot;data&quot;)</span>
  private List&lt;Map&lt;String, Object&gt;&gt; data = null;

<span class="fc" id="L31">  @JsonProperty(&quot;errors&quot;)</span>
  private List&lt;Error&gt; errors = null;

  public LookupResponse data(List&lt;Map&lt;String, Object&gt;&gt; data) {
<span class="fc" id="L35">    this.data = data;</span>
<span class="fc" id="L36">    return this;</span>
  }

  public LookupResponse addDataItem(Map&lt;String, Object&gt; dataItem) {
<span class="nc bnc" id="L40" title="All 2 branches missed.">    if (this.data == null) {</span>
<span class="nc" id="L41">      this.data = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L43">    this.data.add(dataItem);</span>
<span class="nc" id="L44">    return this;</span>
  }

   /**
   * Get data
   * @return data
  **/
  @Schema(description = &quot;&quot;)
  public List&lt;Map&lt;String, Object&gt;&gt; getData() {
<span class="fc" id="L53">    return data;</span>
  }

  public void setData(List&lt;Map&lt;String, Object&gt;&gt; data) {
<span class="nc" id="L57">    this.data = data;</span>
<span class="nc" id="L58">  }</span>

  public LookupResponse errors(List&lt;Error&gt; errors) {
<span class="nc" id="L61">    this.errors = errors;</span>
<span class="nc" id="L62">    return this;</span>
  }

  public LookupResponse addErrorsItem(Error errorsItem) {
<span class="nc bnc" id="L66" title="All 2 branches missed.">    if (this.errors == null) {</span>
<span class="nc" id="L67">      this.errors = new ArrayList&lt;&gt;();</span>
    }
<span class="nc" id="L69">    this.errors.add(errorsItem);</span>
<span class="nc" id="L70">    return this;</span>
  }

   /**
   * Get errors
   * @return errors
  **/
  @Schema(description = &quot;&quot;)
  public List&lt;Error&gt; getErrors() {
<span class="nc" id="L79">    return errors;</span>
  }

  public void setErrors(List&lt;Error&gt; errors) {
<span class="nc" id="L83">    this.errors = errors;</span>
<span class="nc" id="L84">  }</span>


  @Override
  public boolean equals(java.lang.Object o) {
<span class="nc bnc" id="L89" title="All 2 branches missed.">    if (this == o) {</span>
<span class="nc" id="L90">      return true;</span>
    }
<span class="nc bnc" id="L92" title="All 4 branches missed.">    if (o == null || getClass() != o.getClass()) {</span>
<span class="nc" id="L93">      return false;</span>
    }
<span class="nc" id="L95">    LookupResponse lookupResponse = (LookupResponse) o;</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">    return Objects.equals(this.data, lookupResponse.data) &amp;&amp;</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">        Objects.equals(this.errors, lookupResponse.errors);</span>
  }

  @Override
  public int hashCode() {
<span class="nc" id="L102">    return Objects.hash(data, errors);</span>
  }


  @Override
  public String toString() {
<span class="nc" id="L108">    StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L109">    sb.append(&quot;class LookupResponse {\n&quot;);</span>
    
<span class="nc" id="L111">    sb.append(&quot;    data: &quot;).append(toIndentedString(data)).append(&quot;\n&quot;);</span>
<span class="nc" id="L112">    sb.append(&quot;    errors: &quot;).append(toIndentedString(errors)).append(&quot;\n&quot;);</span>
<span class="nc" id="L113">    sb.append(&quot;}&quot;);</span>
<span class="nc" id="L114">    return sb.toString();</span>
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
<span class="nc bnc" id="L122" title="All 2 branches missed.">    if (o == null) {</span>
<span class="nc" id="L123">      return &quot;null&quot;;</span>
    }
<span class="nc" id="L125">    return o.toString().replace(&quot;\n&quot;, &quot;\n    &quot;);</span>
  }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>