<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>com.poc.hss.fasttrack.client.model</title><script type="text/javascript" src="../jacoco-resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <span class="el_package">com.poc.hss.fasttrack.client.model</span></div><h1>com.poc.hss.fasttrack.client.model</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">773 of 857</td><td class="ctr2">9%</td><td class="bar">74 of 74</td><td class="ctr2">0%</td><td class="ctr1">96</td><td class="ctr2">108</td><td class="ctr1">169</td><td class="ctr2">200</td><td class="ctr1">59</td><td class="ctr2">71</td><td class="ctr1">2</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a2"><a href="LookupRequest.java.html" class="el_source">LookupRequest.java</a></td><td class="bar" id="b0"><img src="../jacoco-resources/redbar.gif" width="100" height="10" title="342" alt="342"/><img src="../jacoco-resources/greenbar.gif" width="19" height="10" title="67" alt="67"/></td><td class="ctr2" id="c0">16%</td><td class="bar" id="d0"><img src="../jacoco-resources/redbar.gif" width="120" height="10" title="32" alt="32"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">40</td><td class="ctr2" id="g0">49</td><td class="ctr1" id="h0">69</td><td class="ctr2" id="i0">94</td><td class="ctr1" id="j0">24</td><td class="ctr2" id="k0">33</td><td class="ctr1" id="l2">0</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="FieldAggregation.java.html" class="el_source">FieldAggregation.java</a></td><td class="bar" id="b1"><img src="../jacoco-resources/redbar.gif" width="48" height="10" title="164" alt="164"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../jacoco-resources/redbar.gif" width="52" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">21</td><td class="ctr2" id="g1">21</td><td class="ctr1" id="h1">38</td><td class="ctr2" id="i1">38</td><td class="ctr1" id="j1">14</td><td class="ctr2" id="k1">14</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="LookupResponse.java.html" class="el_source">LookupResponse.java</a></td><td class="bar" id="b2"><img src="../jacoco-resources/redbar.gif" width="41" height="10" title="140" alt="140"/><img src="../jacoco-resources/greenbar.gif" width="4" height="10" title="17" alt="17"/></td><td class="ctr2" id="c1">10%</td><td class="bar" id="d1"><img src="../jacoco-resources/redbar.gif" width="60" height="10" title="16" alt="16"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">18</td><td class="ctr2" id="g2">21</td><td class="ctr1" id="h2">32</td><td class="ctr2" id="i2">38</td><td class="ctr1" id="j3">10</td><td class="ctr2" id="k2">13</td><td class="ctr1" id="l3">0</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a0"><a href="Error.java.html" class="el_source">Error.java</a></td><td class="bar" id="b3"><img src="../jacoco-resources/redbar.gif" width="37" height="10" title="127" alt="127"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../jacoco-resources/redbar.gif" width="45" height="10" title="12" alt="12"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">17</td><td class="ctr2" id="g3">17</td><td class="ctr1" id="h3">30</td><td class="ctr2" id="i3">30</td><td class="ctr1" id="j2">11</td><td class="ctr2" id="k3">11</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>