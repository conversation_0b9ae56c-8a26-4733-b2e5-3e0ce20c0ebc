<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="en"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../jacoco-resources/report.css" type="text/css"/><link rel="shortcut icon" href="../jacoco-resources/report.gif" type="image/gif"/><title>LookupServiceImpl.java</title><link rel="stylesheet" href="../jacoco-resources/prettify.css" type="text/css"/><script type="text/javascript" src="../jacoco-resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../jacoco-sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">pipeline-common-transformation-integration-data-lookup</a> &gt; <a href="index.source.html" class="el_package">com.poc.hss.fasttrack.service</a> &gt; <span class="el_source">LookupServiceImpl.java</span></div><h1>LookupServiceImpl.java</h1><pre class="source lang-java linenums">package com.poc.hss.fasttrack.service;

import com.poc.hss.fasttrack.backoff.BackoffException;
import com.poc.hss.fasttrack.backoff.BackoffTask;
import com.poc.hss.fasttrack.backoff.BackoffTaskFactory;
import com.poc.hss.fasttrack.backoff.strategy.ExponentialBackoffStrategy;
import com.poc.hss.fasttrack.client.api.LookupApi;
import com.poc.hss.fasttrack.client.model.FieldAggregation;
import com.poc.hss.fasttrack.client.model.LookupResponse;
import com.poc.hss.fasttrack.transform.model.LookupRequest;
import io.vertx.core.json.JsonObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
<span class="fc" id="L27">public class LookupServiceImpl implements LookupService {</span>

<span class="fc" id="L29">    private static final Logger logger = LoggerFactory.getLogger(LookupServiceImpl.class);</span>

    @Autowired
    private LookupApi lookupApi;

    @Value(&quot;${dataLookup.address:http://data-lookup}/api&quot;)
    private String dataLookupAddress;

    @PostConstruct
    private void init() {
<span class="fc" id="L39">        lookupApi.getApiClient().setBasePath(dataLookupAddress);</span>
<span class="fc" id="L40">    }</span>

    @Override
    public JsonObject queryOne(LookupRequest request) {
<span class="fc" id="L44">        return queryOne(request, false);</span>
    }

    @Override
    public JsonObject queryOne(LookupRequest request, boolean required) {
<span class="fc" id="L49">        return queryOne(request, required, 30000);</span>
    }

    @Override
    public JsonObject queryOne(LookupRequest request, boolean required, long timeout) {
        try {
<span class="fc" id="L55">            return queryOne(request, required, false, timeout);</span>
<span class="nc" id="L56">        } catch (BackoffException e) {</span>
<span class="nc" id="L57">            return null;</span>
        }
    }

    @Override
    public JsonObject queryOne(LookupRequest request, boolean required, boolean doThrow) throws BackoffException {
<span class="nc" id="L63">        return queryOne(request, required, doThrow, 30000);</span>
    }

    @Override
    public JsonObject queryOne(LookupRequest request, boolean required, boolean doThrow, long timeout) throws BackoffException {
<span class="pc bpc" id="L68" title="1 of 2 branches missed.">        if (logger.isDebugEnabled())</span>
<span class="nc" id="L69">            logger.debug(&quot;Single lookup for request {}, required={}&quot;, request, required);</span>

<span class="fc bfc" id="L71" title="All 4 branches covered.">        BackoffTask&lt;JsonObject&gt; backoff = BackoffTaskFactory.create(ExponentialBackoffStrategy.getInstance(), res -&gt; !required || Objects.nonNull(res));</span>

        try {
<span class="fc" id="L74">            return backoff.get(</span>
<span class="fc" id="L75">                    () -&gt; queryList(</span>
<span class="fc" id="L76">                            request.toBuilder()</span>
<span class="fc" id="L77">                                    .offset(0)</span>
<span class="fc" id="L78">                                    .limit(1)</span>
<span class="fc" id="L79">                                    .build()</span>
<span class="fc" id="L80">                    ).stream().findFirst().orElse(null),</span>
<span class="fc" id="L81">                    Duration.ofMillis(timeout)</span>
            );
<span class="fc" id="L83">        } catch (BackoffException e) {</span>
<span class="pc bpc" id="L84" title="1 of 2 branches missed.">            if (doThrow)</span>
<span class="nc" id="L85">                throw e;</span>
            else {
<span class="fc" id="L87">                logger.error(&quot;Backoff timeout for request {}&quot;, request);</span>
<span class="fc" id="L88">                return null;</span>
            }
        }
    }

    @Override
    public List&lt;JsonObject&gt; queryList(LookupRequest request) {
<span class="pc bpc" id="L95" title="1 of 2 branches missed.">        if (logger.isDebugEnabled())</span>
<span class="nc" id="L96">            logger.debug(&quot;List lookup for request {}&quot;, request);</span>

<span class="fc" id="L98">        LookupResponse response = lookupApi.lookup(</span>
<span class="fc" id="L99">                request.getAccessSchemaName(),</span>
                new com.poc.hss.fasttrack.client.model.LookupRequest()
<span class="fc" id="L101">                        .criteria(request.getCriteria())</span>
<span class="fc" id="L102">                        .fields(request.getFields())</span>
<span class="fc" id="L103">                        .orders(</span>
<span class="fc" id="L104">                                ListUtils.emptyIfNull(request.getSorts())</span>
<span class="fc" id="L105">                                        .stream()</span>
<span class="pc" id="L106">                                        .map(sort -&gt; String.format(&quot;%s:%s&quot;, sort.getField(), sort.getDirection()))</span>
<span class="fc" id="L107">                                        .collect(Collectors.toList())</span>
                        )
<span class="fc" id="L109">                        .groups(request.getGroups())</span>
<span class="fc" id="L110">                        .distinct(request.getDistinct())</span>
<span class="fc" id="L111">                        .fieldAggregations(</span>
<span class="fc" id="L112">                                ListUtils.emptyIfNull(request.getFieldAggregations())</span>
<span class="fc" id="L113">                                        .stream()</span>
<span class="pc" id="L114">                                        .map(agg -&gt; new FieldAggregation()</span>
<span class="nc" id="L115">                                                .field(agg.getField())</span>
<span class="nc" id="L116">                                                .function(agg.getFunction())</span>
<span class="nc" id="L117">                                                .rename(agg.getRename())</span>
                                        )
<span class="fc" id="L119">                                        .collect(Collectors.toList())</span>
                        )
<span class="fc" id="L121">                        .offset(request.getOffset())</span>
<span class="fc" id="L122">                        .limit(request.getLimit())</span>
        );

<span class="fc" id="L125">        return CollectionUtils.emptyIfNull(response.getData())</span>
<span class="fc" id="L126">                .stream()</span>
<span class="fc" id="L127">                .map(JsonObject::new)</span>
<span class="fc" id="L128">                .collect(Collectors.toList());</span>
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.jacoco.org/jacoco">JaCoCo</a> 0.8.12.202403310830</span></div></body></html>