<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>,INSTRUC<PERSON><PERSON>_MISSED,INSTRUCTION_COVERED,<PERSON><PERSON>CH_MISSED,<PERSON><PERSON><PERSON>_COVERED,LINE_MISSED,<PERSON>INE_COVERED,COMPLEXITY_MISSED,COMPLEXITY_COVERED,<PERSON><PERSON>OD_MISSED,METHOD_COVERED
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.service,LookupServiceImpl,50,145,3,7,9,44,6,12,3,10
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.client.api,LookupApi.new ParameterizedTypeReference() {...},0,3,0,0,0,1,0,1,0,1
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.client.api,LookupApi,16,87,1,1,5,19,3,3,2,3
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.client.model,Error,127,0,12,0,30,0,17,0,11,0
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.client.model,LookupRequest,342,67,32,0,69,25,40,9,24,9
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.client.model,FieldAggregation,164,0,14,0,38,0,21,0,14,0
pipeline-common-transformation-integration-data-lookup,com.poc.hss.fasttrack.client.model,LookupResponse,140,17,16,0,32,6,18,3,10,3
