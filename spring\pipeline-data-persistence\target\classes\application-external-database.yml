PIPELINE: "pipeline-data-persistence"
spring:
  config:
    import: application-jasypt.yml
  application:
    name: pipeline-data-persistence
  datasource:
    url: "*****************************************"
    username: "postgres"
    password: ""
  jpa:
    show-sql: false
    generate-ddl: true
    properties:
      hibernate:
        dialect: "org.hibernate.dialect.PostgreSQLDialect"
        hbm2ddl:
          auto: "none"
jdbcConfig:
  dataSourceName: "PG-Ext"
externalDataSourceJdbcConfigs:
  PG-Ext:
    url: "**********************************************"
    username: "<EMAIL>"
    password: ""
kafkaConfig:
  auto.offset.reset: "earliest"
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
  group.id: "${kafka.consumer.group:spring-poc-dev}"
  schema.registry.url: "https://hkl20146687.hc.cloud.hk.hsbc:8082/"
  security.protocol: "SSL"
  ssl.keystore.location: "C:/hss/apps/certs/env/dev/unity-microservices.jks"
  ssl.keystore.password:
  ssl.truststore.location: "C:/hss/apps/certs/env/dev/unity-microservices.ts"
  ssl.truststore.password:
management:
  endpoints:
    web:
      exposure:
        include: "health,prometheus"
  metrics:
    tags:
      application: "${spring.application.name}"
cache:
  provider: "kafka"
  address: "http://cache"
  topic: "unity2-DEV-core-health-check-cache-metric"
projectId: "8c7ab7aa_810e_4573_971e_cc9ca8f1ab90"
accesses:
  - persistenceEnabled: true
    sourceTopic: "unity2-DEV-core-health-check-transform-demo-ghss-out"
    tableName: "TestPoll"
    columns:
      - name: "_trace_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: []
      - name: "_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: []
    script: ""
    scriptEnabled: false
    retention:
      housekeepingSchedule: "0 * * * * *"
      retentionEnabled: true
    sourceBatchTopicSuffix: "-batch"
    targetBatchTopicSuffix: "-batch"
    ddlAllowed: true
logging:
  level:
    com.poc.hss.fasttrack: TRACE