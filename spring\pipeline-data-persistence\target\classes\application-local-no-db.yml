spring:
  config:
    import: application-jasypt.yml
  application:
    name: pipeline-data-persistence
#  datasource:
#    url: *****************************************
#    username: postgres
#    password: password
#  jpa:
#    show-sql: false
#    generate-ddl: true
#    properties:
#      hibernate:
#        default_schema: access
#        dialect: org.hibernate.dialect.PostgreSQLDialect
#        hbm2ddl:
#          auto: update
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration
      - org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration
kafkaConfig:
  bootstrap.servers: hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094
  group.id: ${kafka.consumer.group:spring-poc-dev}
  schema.registry.url: http://hkl20146687.hc.cloud.hk.hsbc:8081/
  auto.offset.reset: earliest
  security.protocol: SSL
  ssl.keystore.location: C:/hss/apps/certs/env/dev/unity-microservices.jks
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: C:/hss/apps/certs/env/dev/unity-microservices.ts
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
elkConfig:
  hosts: "hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045"
  environment: "DEV"
pipelineName: local-pipeline
projectId: test-schema
consumerAdaptorTopic: csv-batch-test-data-persistence-out-topic
accesses:
  - sourceTopic: "csv-batch-test-transformer-out-topic"
    persistenceEnabled: false
    tableName: "TEST"
    columns:
      - name: "TEST"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "_trace_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "created_time_stamp"
        dataType: "timestamp"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "created_by"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "updated_time_stamp"
        dataType: "timestamp"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "updated_by"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "deleted_by"
        dataType: "text"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "_batch_id"
        dataType: "text"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "deleted_time_stamp"
        dataType: "timestamp"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "c1"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "c2"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "c3"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
cache:
  provider: memory
  address: http://localhost:8088
  group: test-pipeline
  component: ACCESS