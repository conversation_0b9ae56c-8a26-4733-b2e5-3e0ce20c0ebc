spring:
  config:
    import: application-jasypt.yml
  application:
    name: pipeline-data-persistence
  datasource:
    url: *****************************************
    username: postgres
    password: password
  jpa:
    show-sql: false
    generate-ddl: false
    properties:
      hibernate:
        default_schema: access
        dialect: org.hibernate.dialect.PostgreSQLDialect
        hbm2ddl:
          auto: update
kafkaConfig:
  bootstrap.servers: hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094
  group.id: ${KAFKA_CONSUMER_GROUP:spring-poc-sit}
  schema.registry.url: http://hkl20146687.hc.cloud.hk.hsbc:8081/
  auto.offset.reset: earliest
  security.protocol: SSL
  ssl.keystore.location: C:/hss/apps/certs/env/dev/unity-microservices.jks
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: C:/hss/apps/certs/env/dev/unity-microservices.ts
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
pipelineName: local-pipeline
projectId: test-schema
consumerAdaptorTopic: performance-test-access-out-10-5000-10-tmp-${KAFKA_CONSUMER_GROUP}
businessEventTopic: performance-test-access-event-out-10-5000-10-tmp-${KAFKA_CONSUMER_GROUP}
accesses:
  - sourceTopic: "performance-test-transformer-out-10-5000-10"
    sourceBatchTopicSuffix: "-batch"
    targetBatchTopicSuffix: "-batch"
    persistenceEnabled: true
    tableName: "performance-test"
    columns:
      - name: "column1"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "column2"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "column3"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "created_time_stamp"
        dataType: "timestamp"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "created_by"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "updated_time_stamp"
        dataType: "timestamp"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "updated_by"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "deleted_by"
        dataType: "text"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "_batch_id"
        dataType: "text"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "deleted_time_stamp"
        dataType: "timestamp"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric-${USERNAME}
PIPELINE: test-pipeline
logging:
  level:
    com.poc.hss.fasttrack.utils.MetricUtils: DEBUG
    com.poc.hss.fasttrack.step.StepExecutor: DEBUG