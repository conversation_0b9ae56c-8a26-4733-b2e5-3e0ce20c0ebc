spring:
  config:
    import: application-jasypt.yml
  application:
    name: pipeline-data-persistence
  datasource:
    url: *****************************************
    username: postgres
    password: password
  jpa:
    show-sql: false
    generate-ddl: false
    properties:
      hibernate:
        default_schema: access
        dialect: org.hibernate.dialect.PostgreSQLDialect
        hbm2ddl:
          auto: update
kafkaConfig:
  bootstrap.servers: "localhost:13333"
  group.id: ${kafka.consumer.group:project-DEV-local}
  auto.offset.reset: earliest
elkConfig:
  hosts: "hkl20160038.hc.cloud.hk.hsbc:5045,hkl20160039.hc.cloud.hk.hsbc:5045,hkl20160040.hc.cloud.hk.hsbc:5045"
  environment: "DEV"
pipelineName: local
projectId: test-schema
consumerAdaptorTopic: unity2-local-test-a-out
businessEventTopic: unity2-local-test-event-a-out
accesses:
  - sourceTopic: "unity2-local-test-t-out"
    sourceBatchTopicSuffix: "-batch"
    targetBatchTopicSuffix: "-batch"
    persistenceEnabled: true
    tableName: "TEST"
    columns:
      - name: "TEST"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "_trace_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "_id"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "created_time_stamp"
        dataType: "timestamp"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "created_by"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "updated_time_stamp"
        dataType: "timestamp"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "updated_by"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "deleted_by"
        dataType: "text"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "_batch_id"
        dataType: "text"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "deleted_time_stamp"
        dataType: "timestamp"
        isIndexed: true
        isMultiple: false
        contentSchema: [ ]
      - name: "c1"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "c2"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
      - name: "c3"
        dataType: "text"
        isIndexed: false
        isMultiple: false
        contentSchema: [ ]
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric
PIPELINE: local