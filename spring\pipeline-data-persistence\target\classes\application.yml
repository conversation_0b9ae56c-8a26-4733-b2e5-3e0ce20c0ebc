server:
  port: 8082
  shutdown: graceful
spring:
  application:
    name: pipeline-data-persistence
  config:
    import: optional:application-zipkin.yml,optional:configtree:/hss/apps/secrets/
  datasource:
    hikari:
      poolName: pipeline-data-persistence-connection-pool
      minimumIdle: 1
cache:
  group: ${PIPELINE}
  component: ACCESS
unity2:
  auto-configuration:
    auto-bean-disposal: true
management:
  health:
    redis:
      enabled: false
