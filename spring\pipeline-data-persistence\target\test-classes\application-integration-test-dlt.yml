spring:
  datasource:
    url: *****************************************
    username: postgres
    password: password
  jpa:
    properties:
      hibernate:
        default_schema: pipeline_data_test
        dialect: org.hibernate.dialect.PostgreSQLDialect
        hbm2ddl:
          auto: none
logging:
  level:
    org.springframework.jdbc.core: INFO
kafkaConfig:
  bootstrap.servers: localhost:13333
  auto.offset.reset: earliest
  group.id: test-group-id
projectId: "pipeline-data-unit-test"
consumerAdaptorTopic: "test-consumer-adaptor-topic"
PIPELINE: test-pipeline
pipelineName: test-pipeline
accesses:
  - sourceTopic: unit-test-poc-dev
    deadLetterTopic: unit-test-poc-dev-dlt
    persistenceEnabled: true
    tableName: "UNT_Test"
    columns:
      - name: "_id"
        dataType: "text"
        isIndexed: false
      - name: "_trace_id"
        dataType: "text"
        isIndexed: false
      - name: "created_by"
        dataType: "text"
        isIndexed: false
      - name: "created_time_stamp"
        dataType: "timestamp"
        isIndexed: false
      - name: "updated_by"
        dataType: "text"
        isIndexed: false
      - name: "updated_time_stamp"
        dataType: "timestamp"
        isIndexed: false
      - name: "TEXT1"
        dataType: "text"
        isIndexed: true
      - name: "NUL"
        dataType: "text"
        isIndexed: false
      - name: "Unicode"
        dataType: "text"
        isIndexed: false
        defaultValue: foo
      - name: "NUMBER1"
        dataType: "numeric"
        isIndexed: false
        defaultValue: 1.23
      - name: "number2"
        dataType: "numeric"
        isIndexed: true
      - name: "DATE1"
        dataType: "date"
        isIndexed: false
      - name: "date2"
        dataType: "date"
      - name: "DATETIME1"
        dataType: "text"
        isIndexed: false
      - name: "dateTime2"
        dataType: "text"
        isIndexed: false
      - name: "content"
        dataType: "jsonb"
        isIndexed: true
      - name: "contentArray"
        dataType: "jsonb"
        isIndexed: true
        isMultiple: true
        contentSchema:
          - name: "name"
            dataType: "text"
            isMulitple: false
            isIndexed: false
          - name: "parents"
            dataType: "text"
            isMulitple: true
            isIndexed: false
      - name: "textArray"
        dataType: "text"
        isMultiple: true
        isIndexed: false
      - name: "numberArray"
        dataType: "numeric"
        isMultiple: true
        isIndexed: false
      - name: "_sql"
        dataType: "text"
        isMultiple: false
        isIndexed: false
      - name: "_sql_execution_mode"
        dataType: "text"
        isMultiple: false
        isIndexed: false
      - name: "_batch_id"
        dataType: "text"
        isMultiple: false
        isIndexed: false
    script: "SET SCHEMA 'pipeline_data_unit_test';CREATE OR REPLACE VIEW \"test_view1\" AS (SELECT \"_batch_id\" FROM \"UNT_Test\");CREATE OR REPLACE VIEW \"test_view2\" AS (SELECT \"_batch_id\" FROM \"UNT_Test\" WHERE \"_id\" = '1');CREATE INDEX IF NOT EXISTS \"idx_test1\" ON \"UNT_Test\" (\"_batch_id\");CREATE INDEX IF NOT EXISTS \"idx_test2\" ON \"UNT_Test\" (\"_id\",\"_batch_id\");"
    scriptEnabled: true
dbType: Postgres