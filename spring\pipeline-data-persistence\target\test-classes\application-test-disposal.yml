spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL
    username: sa
    password: password
  h2:
    console:
      enabled: true
logging:
  level:
    org.springframework.jdbc.core: INFO
kafkaConfig:
  bootstrap.servers: localhost:13333
  auto.offset.reset: earliest
  group.id: test-group-id
projectId: "pipeline-data-unit-test"
consumerAdaptorTopic: "test-consumer-adaptor-topic"
PIPELINE: test-pipeline
pipelineName: test-pipeline
accesses:
  - sourceTopic: test_in_topic
    persistenceEnabled: false
    tableName: "UNT_Test"
    columns:
      - name: "_id"
        dataType: "text"
        isIndexed: false
  - sourceTopic: test_in_topic_2
    persistenceEnabled: false
    tableName: "UNT_Test_2"
    columns:
      - name: "_id"
        dataType: "text"
        isIndexed: false