spring:
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=PostgreSQL
    username: sa
    password: password
logging:
  level:
    org.springframework.jdbc.core: INFO
    com.poc.hss.fasttrack: DEBUG
    org.apache: WARN
    org.springframework: WARN
    state.change.logger: WARN
    kafka: WARN
kafkaConfig:
  bootstrap.servers: localhost:13333
  auto.offset.reset: earliest
  group.id: test-group-id
projectId: "pipeline-data-unit-test"
PIPELINE: test-pipeline
pipelineName: test-pipeline
accesses:
  - sourceTopic: test_in_topic
    sourceBatchTopicSuffix: "-batch"
    targetBatchTopicSuffix: "-batch"
    persistenceEnabled: true
    tableName: "UNT_Test"
    retention:
      retentionScript: "DELETE FROM \"pipeline_data_unit_test\".\"UNT_Test\" WHERE created_time_stamp < NOW() - INTERVAL '1' DAY;"
      housekeepingSchedule: 0/5 * * * * *
      retentionEnabled: true
    columns:
      - name: "_id"
        dataType: "text"
        isIndexed: false
      - name: "_trace_id"
        dataType: "text"
        isIndexed: false
      - name: "created_by"
        dataType: "text"
        isIndexed: false
      - name: "created_time_stamp"
        dataType: "timestamp"
        isIndexed: false
      - name: "updated_by"
        dataType: "text"
        isIndexed: false
      - name: "updated_time_stamp"
        dataType: "timestamp"
        isIndexed: false
      - name: "_batch_id"
        dataType: "text"
        isIndexed: false
      - name: "TEXT1"
        dataType: "text"
        isIndexed: true
      - name: "NUL"
        dataType: "text"
        isIndexed: false
      - name: "Unicode"
        dataType: "text"
        isIndexed: false
      - name: "NUMBER1"
        dataType: "numeric"
        isIndexed: false
      - name: "number2"
        dataType: "numeric"
        isIndexed: true
      - name: "DATE1"
        dataType: "date"
        isIndexed: false
      - name: "date2"
        dataType: "date"
      - name: "DATETIME1"
        dataType: "text"
        isIndexed: false
      - name: "dateTime2"
        dataType: "text"
        isIndexed: false
      - name: "content"
        dataType: "jsonb"
        isIndexed: true
      - name: "textArray"
        dataType: "text"
        isMultiple: true
        isIndexed: false
      - name: "numberArray"
        dataType: "numeric"
        isMultiple: true
        isIndexed: false
    script: "CREATE OR REPLACE VIEW \"pipeline_data_unit_test\".\"test_view1\" AS (\tSELECT \"_batch_id\" FROM \"pipeline_data_unit_test\".\"UNT_Test\");CREATE OR REPLACE VIEW \"pipeline_data_unit_test\".\"test_view2\" AS (SELECT \"_batch_id\" FROM \"pipeline_data_unit_test\".\"UNT_Test\" WHERE \"_id\" = '1');CREATE INDEX IF NOT EXISTS \"idx_test1\" ON \"pipeline_data_unit_test\".\"UNT_Test\" (\"_batch_id\");CREATE INDEX IF NOT EXISTS \"idx_test2\" ON \"pipeline_data_unit_test\".\"UNT_Test\" (\"_id\",\"_batch_id\");"
    scriptEnabled: true
dbType: H2