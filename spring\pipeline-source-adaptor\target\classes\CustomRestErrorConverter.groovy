import com.poc.hss.fasttrack.exception.Unity2RestResponseException
import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import io.vertx.core.json.Json
import io.vertx.core.json.JsonObject
import org.apache.commons.collections4.CollectionUtils
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.messaging.Message
import org.springframework.vault.support.JsonMapFlattener

import java.util.stream.Collectors

class CustomRestErrorConverter implements OutboundMessageConverter<byte[]> {

    private static final Logger logger = LoggerFactory.getLogger(CustomRestErrorConverter.class)
    @Override
    List<Map<String, Object>> transform(Message<byte[]> source) {
        List<Map<String, Object>> outputList = new ArrayList<>()
        final String payload = new String(source.getPayload())
        final Object json = Json.decodeValue(payload)
        if(logger.isDebugEnabled()){
            logger.debug("payload....{}", payload)
            logger.debug("flatten Object... {}", JsonMapFlattener.flattenToStringMap(((JsonObject) json).getMap()))
        }
        Map<String, String> flattenInputMap = JsonMapFlattener.flatten(((JsonObject) json).getMap())

        Set<String> mandatoryFields = Arrays.asList("Field1","Field2","Field4","Field8")
        Set<String> missingFields = mandatoryFields.stream()
            .filter(mandatoryField -> !flattenInputMap.keySet().contains(mandatoryField))
            .collect(Collectors.toList())

        if (CollectionUtils.isNotEmpty(missingFields)) {
            throw new Unity2RestResponseException(HttpStatus.BAD_REQUEST, "Missing mandatory fields: " + missingFields.stream().collect(Collectors.joining(",")))
        }
        return outputList;
    }
}
