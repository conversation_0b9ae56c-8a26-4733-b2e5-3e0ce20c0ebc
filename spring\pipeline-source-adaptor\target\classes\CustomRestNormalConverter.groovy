import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import io.vertx.core.json.Json
import io.vertx.core.json.JsonArray
import io.vertx.core.json.JsonObject
import lombok.extern.slf4j.Slf4j
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.messaging.Message
import org.springframework.messaging.support.MessageBuilder
import org.springframework.vault.support.JsonMapFlattener

import java.util.stream.Collectors

class CustomRestNormalConverter implements OutboundMessageConverter<byte[]> {

    private static final Logger logger = LoggerFactory.getLogger(CustomRestNormalConverter.class)
    @Override
    List<Map<String, Object>> transform(Message<byte[]> source) {
        List<Map<String, Object>> outputList = new ArrayList<>()
        Map<String, Object> outputMap = new HashMap<>();
        final String payload = new String(source.getPayload());
        final Object json = Json.decodeValue(payload);
        if(logger.isDebugEnabled()){
            logger.debug("payload....{}", payload)
            logger.debug("flatten Object... {}", JsonMapFlattener.flattenToStringMap(((JsonObject) json).getMap()))
        }
        Map<String, String> flattenInputMap = JsonMapFlattener.flatten(((JsonObject) json).getMap());
        outputMap.put("data", flattenInputMap)
        outputList.add(outputMap)
        return outputList;
    }
}
