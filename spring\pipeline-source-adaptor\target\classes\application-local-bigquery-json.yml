sourceAdaptorConfig:
  projectId: "82e89249-eef0-4959-9e2b-6f28a6371da4"
  name: "test-bq-sa"
  sourceAdaptor:
    sourceChannel: "BIG_QUERY"
    sourceDataFormat: "JSON"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "unity2-DEV-core-source-adaptor-test-bq-sa-out"
    sourceDataKeyFields:
      - "current"
      - "site"
    additionalProperties:
      env: "DEV"
      mqConfig:
        host: ""
        port: 0
        channel: ""
        queueManager: ""
        cipherSuite: ""
        appName: ""
        queueName: ""
        keyStore: ""
        keyPassword: ""
      bigQueryConfig:
        projectId: "hsbc-9087302-unity-dev"
        datasetName: "unity2_test_bq"
        tableName: "bq-kms-test"
        kmsKeyName: ""
        queryFields: ["field1_out", "field2_out", "field3_out", "field4_out"]
      sourceKafkaConfig:
        sourceKafkaProperties: {}