spring:
  config:
    import: application-jasypt.yml
sourceAdaptorConfig:
  projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
  name: "test-source-adaptor"
  sourceAdaptor:
    sourceChannel: "KAFKA"
    sourceDataFormat: "JSON"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "unity2-local-test-sa-out-2"
    additionalProperties:
      sourceKafkaConfig:
        sourceKafkaProperties:
          bootstrap.servers: "hkl20146688.hc.cloud.hk.hsbc:9094"
          group.id: "spring-poc-nick"
          schema.registry.url: "http://hkl20146688.hc.cloud.hk.hsbc:8081/"
          auto.offset.reset: "earliest"
          security.protocol: "SSL"
          ssl.keystore.location: "C:/hss/apps/certs/env/dev/unity-microservices.jks"
          ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
          ssl.truststore.location: "C:/hss/apps/certs/env/dev/unity-microservices.ts"
          ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
        sourceTopic: "unity2-local-test-sa-in-2"
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric-2
unity2:
  kafka:
    transactional: true