spring:
  config:
    import: application-jasypt.yml
sourceAdaptorConfig:
  projectId: "holding-api"
  name: "holding-api-mq-xml"
  sourceAdaptor:
    sourceChannel: "MQ"
    sourceDataFormat: "XML"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "unity2-DEV-CSHOLDING-source-adaptor-gcs-instrument"
    sourceDataKeyFields:
      - "ISIN"
    additionalProperties:
      mqConfig:
        host: hkwmqt-dhsag1.it.hk.hibm.hsbc
        port: 12305
        channel: HU1C.HK.C6
        queueManager: DHKLHHSAG1
        cipherSuite: TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA384
        appName: UNITY-MQ-ADAPTER
        queueName: HK_HSAG.HK_HU1C.HK_HB1C.BANDRESPHSBC.D00
        keyStore: /hss/apps/certs/env/uat/HU1C.jks
        keyPassword: ENC(4bX8bpoMyNsfpDsObr0yEOV7WoFP4PTsqYDJV+lAjoSHukf2tg7VHNwNCWNSTkkE)
    inputParsingConfig:
      xmlParsingConfig:
        exprMap:
          ISIN: "/Document/Scty/SctyInstrmId/ISIN/text()"
          Sedol: "/Document/Scty/SctyInstrmId/OthrId[Tp/Cd='SEDL']/Id/text()"
          Cusip: "/Document/Scty/SctyInstrmId/OthrId[Tp/Cd='CUSP']/Id/text()"
          Description: "/Document/Scty/SctyInstrmId/Desc/text()"
          CcyCode: "/Document/Scty/SctyDtls/Ccy/text()"
          CountryOfInc: "/Document/Scty/SctyDtls/CtryOfInc/text()"
          IssueType: "/Document/Scty/SctyDtls/SecTp/Tp/text()"
          IssueTypeDesc: "/Document/Scty/SctyDtls/SecTp/Desc/text()"
          IssueCountryCode: "/Document/Scty/SctyDtls/SrcCtry/text()"
          QuantityType: "/Document/Scty/SctyDtls/QtyTp/text()"
kafkaConfig:
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
  group.id: "spring-source-adaptor-security-main-api"
  schema.registry.url: "http://hkl20146687.hc.cloud.hk.hsbc:8081/"
  auto.offset.reset: "earliest"
  security.protocol: "SSL"
  ssl.keystore.location: "C:/hss/apps/certs/env/dev/unity-microservices.jks"
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: "C:/hss/apps/certs/env/dev/unity-microservices.ts"
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
