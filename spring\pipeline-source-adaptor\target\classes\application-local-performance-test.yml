spring:
  config:
    import: application-jasypt.yml
kafkaConfig:
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
  group.id: "${KAFKA_CONSUMER_GROUP:spring-poc-sit}"
  schema.registry.url: "http://hkl20146688.hc.cloud.hk.hsbc:8081/"
  auto.offset.reset: "earliest"
  security.protocol: "SSL"
  ssl.keystore.location: "C:/hss/apps/certs/env/dev/unity-microservices.jks"
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: "C:/hss/apps/certs/env/dev/unity-microservices.ts"
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
sourceAdaptorConfig:
  projectId: "8c7ab7aa-810e-4573-971e-cc9ca8f1ab90"
  name: "hk-hsiitnp"
  sourceAdaptor:
    sourceChannel: "KAFKA"
    sourceDataFormat: "JSON"
    sourceDataKeyFields:
      - "_id"
    additionalProperties:
      env: "DEV"
      sourceKafkaConfig:
        sourceKafkaProperties:
          security.protocol: "SSL"
          schema.registry.url: "http://hkl20146688.hc.cloud.hk.hsbc:8081/"
          ssl.truststore.location: "C:/hss/apps/certs/unity-microservices.ts"
          ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
          group.id: "${KAFKA_CONSUMER_GROUP:spring-poc-sit}"
          ssl.keystore.location: "C:/hss/apps/certs/unity-microservices.jks"
          bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
          ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
          auto.offset.reset: "earliest"
        sourceTopic: "performance-test-out-10-5000-10"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "performance-test-source-adaptor-out-10-5000-10-tmp-${KAFKA_CONSUMER_GROUP}"
    convertValuesToString: true
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric-${USERNAME}
logging:
  level:
    org.apache.kafka.clients.producer.internals.ProducerBatch: TRACE