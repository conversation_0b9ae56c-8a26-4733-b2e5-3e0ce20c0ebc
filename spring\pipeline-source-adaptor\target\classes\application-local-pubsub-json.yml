spring:
  config:
    import: application-jasypt.yml
kafkaConfig:
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
  group.id: "core-local-sa-pubsub"
  schema.registry.url: "http://hkl20146687.hc.cloud.hk.hsbc:8081/"
  auto.offset.reset: "earliest"
  security.protocol: "SSL"
  ssl.keystore.location: "/hss/apps/certs/env/dev/unity-microservices.jks"
  ssl.keystore.password:
  ssl.truststore.location: "/hss/apps/certs/env/dev/unity-microservices.ts"
  ssl.truststore.password:
cache:
  provider: "memory"
sourceAdaptorConfig:
  projectId: "8c7ab7aa-810e-4573-971e-cc9ca8f1ab90"
  name: "fx-pubsub-src"
  sourceAdaptor:
    sourceChannel: "PUB_SUB"
    sourceDataFormat: "JSON"
    mode: "PIPELINE"
    additionalProperties:
      env: "DEV"
      pubSubConfig:
        projectId: "hsbc-dev"
        subscriptionName: "env_subscription"
        sourceTopic: "none"
        location: "asia-east2"
        kmsKeyName: "serviceaccount.json"
    sourceDataKeyFields:
      - "mid"
    kafkaPartitionKeyFields: []
    idFormatOverride: ""
    convertValuesToString: true
    routingConfig:
      mode: "DEFAULT"
      defaultTargetTopic: "unity2-DEV-core-health-check-source-adaptor-fx-pubsub"
      batchTopicSuffix: "-batch"