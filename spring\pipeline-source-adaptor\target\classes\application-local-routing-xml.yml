spring:
  config:
    import: application-jasypt.yml
sourceAdaptorConfig:
  projectId: "8c7ab7aa-810e-4573-971e-cc9ca8f1ab90"
  name: "topic-holding"
  sourceAdaptor:
    sourceChannel: "ROUTING_SOURCE_ADAPTOR"
    sourceDataFormat: "XML"
    mode: "PIPELINE"
    additionalProperties:
      env: "DEV"
      sourceKafkaConfig:
        sourceKafkaProperties:
          security.protocol: "SSL"
          schema.registry.url: "http://hkl20146687.hc.cloud.hk.hsbc:8081/"
          ssl.truststore.location: "C:/hss/apps/certs/unity-microservices.ts"
          ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
          group.id: "spring-poc-sit"
          ssl.keystore.location: "C:/hss/apps/certs/unity-microservices.jks"
          bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
          ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
          auto.offset.reset: "earliest"
        sourceTopic: "topic-holding"
    sourceDataKeyFields:
      - "AcctId"
    convertValuesToString: true
    inputParsingConfig:
      xmlParsingConfig:
        exprMap:
          sequenceNumber: "/Document/Pos/sequenceNumber/text()"
          SrcSysNm: "/Document/Pos/SrcSysId/SrcSysNm/text()"
          CtryCd: "/Document/Pos/SrcSysId/CtryCd/text()"
          AcctId: "/Document/Pos/PosId/AcctId/Nb/text()"
    routingConfig:
      mode: "DEFAULT"
      defaultTargetTopic: "topic-holding-out"
kafkaConfig:
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094"
  group.id: "spring-source-adaptor-security-main-api"
  schema.registry.url: "http://hkl20146687.hc.cloud.hk.hsbc:8081/"
  auto.offset.reset: "earliest"
  security.protocol: "SSL"
  ssl.keystore.location: "C:/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: "C:/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
cache:
  provider: memory
  address: http://localhost:8088
logging:
  level:
    com.poc.hss.fasttrack: DEBUG