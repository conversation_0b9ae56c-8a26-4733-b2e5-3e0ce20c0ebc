spring:
  config:
    import: application-jasypt.yml
kafkaConfig:
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094"
  group.id: ${kafka.consumer.group:spring-poc-dev}
  schema.registry.url: "http://hkl20146687.hc.cloud.hk.hsbc:8081/"
  auto.offset.reset: "earliest"
  security.protocol: "SSL"
  ssl.keystore.location: "C:/hss/apps/certs/env/dev/unity-microservices.jks"
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: "C:/hss/apps/certs/env/dev/unity-microservices.ts"
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
sourceAdaptorConfig:
  projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
  name: "source-adaptor-sftp-csv"
  sourceAdaptor:
    sourceChannel: SFTP
    sourceDataFormat: CSV
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "csv-batch-test-source-adaptor-out-topic"
      batchTopicSuffix: "-batch"
    batchConfig:
      idPattern: "{yyyyMMdd}-HSBC1"
      poller:
        fixedDelay: 30000
        cron: "0/30 * * * * *"
    sourceDataKeyFields:
      - "c1"
      - "c2"
      - "c3"
    additionalProperties:
      sftpConfig:
        host: hkl20072440.hc.cloud.hk.hsbc
        port: 22
        userName: otcustody
        privateKey: C:/hss/apps/secrets/public_key.ppk
        remoteDirectory: /tmp/sftp-test
        fileNamePattern: ".+\\.csv"
    inputParsingConfig:
      csvParsingConfig:
        headerSectionStartRow: -1
        dataSectionStartRow: 1
cache:
  provider: rest
  address: http://localhost:8088