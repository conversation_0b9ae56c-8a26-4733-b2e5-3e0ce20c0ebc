sourceAdaptorConfig:
  projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
  name: "test-source-adaptor"
  sourceAdaptor:
    sourceChannel: SFTP
    sourceDataFormat: CSV
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "unity2-local-test-sa-out"
      batchTopicSuffix: "-batch"
    sourceDataKeyFields:
      - "c1"
      - "c2"
    additionalProperties:
      sftpConfig:
        host: hkl20072440.hc.cloud.hk.hsbc
        port: 22
        userName: otcustody
        privateKey: C:/hss/apps/secrets/public_key.pem
        remoteDirectory: /tmp/sftp-test
        fileNamePattern: ".+\\.csv"
    inputParsingConfig:
      csvParsingConfig:
        headerSectionStartRow: -1
        dataSectionStartRow: 1
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric
logging:
  level:
    com.poc.hss.fasttrack: DEBUG