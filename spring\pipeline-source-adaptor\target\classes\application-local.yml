spring:
  config:
    import: application-jasypt.yml
kafkaConfig:
  bootstrap.servers: "localhost:13333"
  group.id: "${KAFKA_CONSUMER_GROUP:spring-poc-sit}"
  auto.offset.reset: "earliest"
sourceAdaptorConfig:
  projectId: "8c7ab7aa-810e-4573-971e-cc9ca8f1ab90"
  name: "local"
  sourceAdaptor:
    sourceChannel: SFTP
    sourceDataFormat: CSV
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "unity2-local-test-sa-out"
      batchTopicSuffix: "-batch"
    sourceDataKeyFields:
      - "c1"
      - "c2"
    additionalProperties:
      sftpConfig:
        host: hkl20072440.hc.cloud.hk.hsbc
        port: 22
        userName: otcustody
        privateKey: C:/hss/apps/secrets/public_key.pem
        remoteDirectory: /tmp/sftp-test
        fileNamePattern: ".+\\.csv"
    inputParsingConfig:
      csvParsingConfig:
        headerSectionStartRow: -1
        dataSectionStartRow: 1
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric
