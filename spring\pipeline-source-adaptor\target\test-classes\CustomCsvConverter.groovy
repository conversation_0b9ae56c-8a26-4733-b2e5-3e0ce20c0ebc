import com.opencsv.CSVReader
import com.opencsv.CSVReaderBuilder
import com.poc.hss.fasttrack.constants.MessageHeaderConstants
import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import org.springframework.messaging.Message

import java.util.stream.Collectors
import java.util.stream.IntStream
import java.util.stream.StreamSupport

class CustomCsvConverter implements OutboundMessageConverter<File> {
    
    @Override
    public List<Map<String, Object>> transform(Message<File> msg) {
        Map<String, String> header = readHeaderSection(msg);
        List<Map<String, Object>> data = readDataSection(msg);
        
        return data.stream().map(rec -> {
            rec.putAll(header);
            Map<String, Object> map = new HashMap<>();
            map.put("data", rec);
            map.putIfAbsent(MessageHeaderConstants.FILENAME, msg.getHeaders().get("file_name"));
            return map;
        }).collect(Collectors.toList());
    }

    private Map<String, String> readHeaderSection(Message<File> msg) {
        int headerSectionStartRow = 1;
        int dataSectionStartRow = 5;
        if (headerSectionStartRow < 1)
            return Collections.emptyMap();

        FileReader fileReader = new FileReader(msg.getPayload());
        CSVReader csvReader = new CSVReaderBuilder(fileReader)
                .withSkipLines(headerSectionStartRow - 1)
                .build();

        Map<String, String> header = new HashMap<>();

        while (csvReader.peek().length == 2 && csvReader.getRecordsRead() < dataSectionStartRow) {
            String[] record = csvReader.readNext();
            header.putIfAbsent(record[0], record[1]);
        }

        fileReader.close();
        return header;
    }

    private List<Map<String, Object>> readDataSection(Message<File> msg) {
        int dataSectionStartRow = 5;
        FileReader fileReader = new FileReader(msg.getPayload());
        CSVReader csvReader = new CSVReaderBuilder(fileReader)
                .withSkipLines(dataSectionStartRow - 1)
                .build();

        boolean isDataSectionHasHeader = true;
        String[] dataSectionHeaders = csvReader.readNext();

        List<Map<String, Object>> data = StreamSupport.stream(csvReader.spliterator(), false)
                .map(row ->
                        IntStream.range(0, row.length).boxed()
                                .collect(Collectors.toMap(
                                        i -> dataSectionHeaders[i],
                                        i -> (Object) row[i]
                                ))
                )
                .collect(Collectors.toList());

        fileReader.close();
        return data;
    }
}