import com.poc.hss.fasttrack.config.SourceAdaptorConfig
import com.poc.hss.fasttrack.constant.Constants
import com.poc.hss.fasttrack.constants.MessageHeaderConstants
import com.poc.hss.fasttrack.outbound.converter.CsvFileOutboundConverter
import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import org.springframework.messaging.Message

import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class CustomCsvBatchConverter implements OutboundMessageConverter<File> {
    CsvFileOutboundConverter delegate

    CustomCsvBatchConverter() {
        SourceAdaptorConfig sourceAdaptorConfig = new SourceAdaptorConfig()
        sourceAdaptorConfig.sourceAdaptor = new SourceAdaptorConfig.SourceAdaptor()
        sourceAdaptorConfig.sourceAdaptor.inputParsingConfig = new SourceAdaptorConfig.InputParsingConfig()
        sourceAdaptorConfig.sourceAdaptor.inputParsingConfig.csvParsingConfig = new SourceAdaptorConfig.CsvParsingConfig()
        sourceAdaptorConfig.sourceAdaptor.inputParsingConfig.csvParsingConfig.headerSectionStartRow = -1
        sourceAdaptorConfig.sourceAdaptor.inputParsingConfig.csvParsingConfig.dataSectionStartRow = 1
        delegate = new CsvFileOutboundConverter(sourceAdaptorConfig)
    }

    @Override
    List<Map<String, Object>> transform(Message<File> msg) {
        List<Map<String, Object>> transformed = delegate.transform(msg)
        transformed.stream().forEach(
                e -> e.putIfAbsent(Constants.BATCH_ID,
                        LocalDateTime.parse((String) msg.getHeaders().get(MessageHeaderConstants.RECEIVE_TIME))
                                .format(DateTimeFormatter.ofPattern("yyyyMMdd")))
        )
        return transformed
    }
}