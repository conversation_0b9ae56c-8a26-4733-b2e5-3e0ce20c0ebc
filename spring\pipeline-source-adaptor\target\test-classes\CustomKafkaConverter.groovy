import com.poc.hss.fasttrack.constant.Constants
import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import io.vertx.core.json.JsonObject
import org.springframework.kafka.support.KafkaNull
import org.springframework.messaging.Message

class CustomKafkaConverter implements OutboundMessageConverter<Object> {

    private static final String SPRING_INTEGRATION_KAFKA_KEY_HEADER = "kafka_receivedMessageKey";

    @Override
    List<Map<String, Object>> transform(Message<Object> source) {
        if (source.getPayload() == KafkaNull.INSTANCE) {
            return Collections.singletonList(
                    new JsonObject(source.getHeaders().get(SPRING_INTEGRATION_KAFKA_KEY_HEADER).toString())
                            .put(Constants.DELETED, true)
                            .getMap()
            );
        } else {
            JsonObject jsonObject = new JsonObject(source.getPayload().toString());
            Map<String, Object> valueMap = jsonObject.getMap();
            Map<String, Object> result = new HashMap<>();
            result.put(Constants.DATA, valueMap)
            return Collections.singletonList(result);
        }
    }
}
