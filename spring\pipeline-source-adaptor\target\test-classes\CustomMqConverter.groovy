import com.poc.hss.fasttrack.constant.Constants
import com.poc.hss.fasttrack.enums.XmlDataFormat
import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import org.springframework.messaging.Message
import org.w3c.dom.Document
import org.w3c.dom.NodeList
import org.xml.sax.InputSource

import javax.xml.parsers.DocumentBuilder
import javax.xml.parsers.DocumentBuilderFactory
import javax.xml.xpath.*

class CustomMqConverter implements OutboundMessageConverter<Object> {
    private final XPathFactory xPathfactory = XPathFactory.newInstance();
    private final XPath xpath = xPathfactory.newXPath();

    @Override
    List<Map<String, Object>> transform(Message<Object> source) {
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(new InputSource(new StringReader(source.getPayload())));

        Map<String, Object> valueMap = new HashMap<>();
        valueMap.put("ISIN", evaluateXPath(document, "/Document/Scty/SctyInstrmId/ISIN/text()", XmlDataFormat.TEXT))
        valueMap.put("Sedol", evaluateXPath(document, "/Document/Scty/SctyInstrmId/OthrId[Tp/Cd='SEDL']/Id/text()", XmlDataFormat.TEXT))
        valueMap.put("Cusip", evaluateXPath(document, "/Document/Scty/SctyInstrmId/OthrId[Tp/Cd='CUSP']/Id/text()", XmlDataFormat.TEXT))
        valueMap.put("Description", evaluateXPath(document, "/Document/Scty/SctyInstrmId/Desc/text()", XmlDataFormat.TEXT))
        valueMap.put("CcyCode", evaluateXPath(document, "/Document/Scty/SctyDtls/Ccy/text()", XmlDataFormat.TEXT))
        valueMap.put("CountryOfInc", evaluateXPath(document, "/Document/Scty/SctyDtls/CtryOfInc/text()", XmlDataFormat.TEXT))
        valueMap.put("IssueType", evaluateXPath(document, "/Document/Scty/SctyDtls/SecTp/Tp/text()", XmlDataFormat.TEXT))
        valueMap.put("IssueTypeDesc", evaluateXPath(document, "/Document/Scty/SctyDtls/SecTp/Desc/text()", XmlDataFormat.TEXT))
        valueMap.put("IssueCountryCode", evaluateXPath(document, "/Document/Scty/SctyDtls/SrcCtry/text()", XmlDataFormat.TEXT))
        valueMap.put("QuantityType", evaluateXPath(document, "/Document/Scty/SctyDtls/QtyTp/text()", XmlDataFormat.TEXT))
        Map<String, Object> message = new HashMap<>();
        message.put(Constants.DATA, valueMap);
        return Collections.singletonList(message)

    }
    private Object evaluateXPath(Document document, String xpathExpression, XmlDataFormat dataFormat) {
        try {
            XPathExpression expr = xpath.compile(xpathExpression);
            NodeList nodes = (NodeList) expr.evaluate(document, XPathConstants.NODESET);

            if (dataFormat == XmlDataFormat.LIST) {
                List<String> values = new ArrayList<>();
                for (int i = 0; i < nodes.getLength(); i++) {
                    values.add(nodes.item(i) != null ? nodes.item(i).getNodeValue() : "");
                }
                return values;
            }
            return nodes.item(0) != null ? nodes.item(0).getNodeValue() : "";
        } catch (XPathExpressionException e) {
            log.error("Error when parsing xml: ", e);
            return dataFormat == XmlDataFormat.LIST ? Collections.EMPTY_LIST : "";
        }
    }
}
