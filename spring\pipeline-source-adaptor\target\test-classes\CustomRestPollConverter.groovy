import com.poc.hss.fasttrack.outbound.converter.OutboundMessageConverter
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.messaging.Message

class CustomRestPollConverter implements OutboundMessageConverter<byte[]> {

    private static final Logger logger = LoggerFactory.getLogger(CustomRestPollConverter.class)

    @Override
    List<Map<String, Object>> transform(Message<byte[]> source) {
        List<Map<String, Object>> outputList = new ArrayList<>()
        logger.info("payload: {}", source.getPayload())
        final String payload = new String(source.getPayload())
        def array = new JsonObject(payload).getJsonArray("data")
        for (Object item : array.getList())
            outputList.add(((JsonObject) item).getMap())
        return outputList;
    }
}
