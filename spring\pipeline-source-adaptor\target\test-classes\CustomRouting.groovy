import com.poc.hss.fasttrack.outbound.router.CustomRouterInterface
import io.vertx.core.json.JsonObject

class CustomRouting implements CustomRouterInterface {
    @Override
    String getTopic(JsonObject msg) {
        msg = msg.getJsonObject("data");
        if (msg.getString("holdingSrcSysNm").equals("GCSF1") ||
                msg.getString("holdingSrcSysNm").equals("GCSF5")
        )
            return "holding";
        else if (msg.getString("tradeSrcSysNm").equals("GCSF1") ||
                msg.getString("tradeSrcSysNm").equals("GCSF5")
        )
            return "trade";
        else
            return "default";
    }
}