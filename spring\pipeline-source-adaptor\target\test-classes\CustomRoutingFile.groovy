import com.poc.hss.fasttrack.constants.MessageHeaderConstants
import com.poc.hss.fasttrack.outbound.router.CustomRouterInterface
import io.vertx.core.json.JsonObject

class CustomRoutingFile implements CustomRouterInterface {
    @Override
    String getTopic(JsonObject msg) {
        if (msg.getString(MessageHeaderConstants.FILENAME) == "data-routing-main.csv")
            return "main";
        else if (msg.getString(MessageHeaderConstants.FILENAME) == "data-routing-lookup.csv")
            return "lookup";
        else
            return "default";
    }
}