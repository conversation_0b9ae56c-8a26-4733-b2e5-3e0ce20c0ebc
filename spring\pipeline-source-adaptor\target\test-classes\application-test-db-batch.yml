sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: DB
    sourceDataFormat: JSON
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - column1
    additionalProperties:
      dbConfig:
        query: select column1 from table_a
        url: *****************************************
        username: postgres
        password: password
    batchConfig:
      idPattern: "{yyyyMMdd}"
      poller:
        cron: "0 * * * * *"