sourceAdaptorConfig:
  projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
  name: "sam_source_adaptor-source"
  sourceAdaptor:
    sourceChannel: "KAFKA"
    sourceDataFormat: "JSON"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - column1
      - column2
    idFormatOverride: "%3d_%s"
    additionalProperties:
      sourceKafkaConfig:
        sourceKafkaProperties:
          bootstrap.servers: "localhost:13333"
          auto.offset.reset: "earliest"
          group.id: "spring-poc-nick"
        sourceTopic: "test_topic"
    batchConfig:
      idPattern: "{yyyyMMdd}"
      poller:
        fixedDelay: 1000