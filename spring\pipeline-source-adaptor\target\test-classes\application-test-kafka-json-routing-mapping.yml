sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: "KAFKA"
    sourceDataFormat: "JSON"
    mode: ROUTING
    routingConfig:
      mode: MAPPING
      mappingConfig:
        - key: column1
          value: holding
          targetTopic: topic-holding
        - key: column1
          value: trade
          targetTopic: topic-trade
      defaultTargetTopic: "topic-default"
    sourceDataKeyFields:
      - column1
    additionalProperties:
      sourceKafkaConfig:
        sourceKafkaProperties:
          bootstrap.servers: "localhost:13333"
          auto.offset.reset: "earliest"
          group.id: "spring-poc-nick"
        sourceTopic: "test_topic"

