sourceAdaptorConfig:
  projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
  name: "sam_source_adaptor-source"
  sourceAdaptor:
    sourceChannel: "ROUTING_SOURCE_ADAPTOR"
    sourceDataFormat: "JSON"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - header1
      - header2
      - header3
      - column1
    additionalProperties:
      sourceKafkaConfig:
        sourceKafkaProperties:
          bootstrap.servers: "localhost:13333"
          auto.offset.reset: "earliest"
          group.id: "spring-poc-nick"
        sourceTopic: "test_topic"
