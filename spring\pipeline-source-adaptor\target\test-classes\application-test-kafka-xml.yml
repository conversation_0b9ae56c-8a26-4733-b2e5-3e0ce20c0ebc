sourceAdaptorConfig:
  projectId: "32026d8c-6539-4f63-9df7-81a7cd7ee52a"
  name: "sam_source_adaptor-source"
  sourceAdaptor:
    sourceChannel: "KAFKA"
    sourceDataFormat: "XML"
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - AcctId
    additionalProperties:
      sourceKafkaConfig:
        sourceKafkaProperties:
          bootstrap.servers: "localhost:13333"
          auto.offset.reset: "earliest"
          group.id: "spring-poc-nick"
        sourceTopic: "test_topic"
    inputParsingConfig:
      xmlParsingConfig:
        exprMap:
          sequenceNumber: "/Document/Pos/sequenceNumber/text()"
          SrcSysNm: "/Document/Pos/SrcSysId/SrcSysNm/text()"
          CtryCd: "/Document/Pos/SrcSysId/CtryCd/text()"
          AcctId: "/Document/Pos/PosId/AcctId/Nb/text()"
