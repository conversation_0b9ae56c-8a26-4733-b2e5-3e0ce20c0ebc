sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: "MQ"
    sourceDataFormat: "XML"
    mode: ROUTING
    routingConfig:
      mode: CUSTOM
      customRoutingConfig:
        routerName: "CustomRouting.groovy"
        targetTopicMap:
          trade: topic-trade
          holding: topic-holding
          default: topic-default
      defaultTargetTopic: "topic-default"
    sourceDataKeyFields:
      - holdingSrcSysNm
      - tradeSrcSysNm
      - holdingSeq
      - tradeSeq
    additionalProperties:
      mqConfig:
        queueName: test.queue
    inputParsingConfig:
      xmlParsingConfig:
        passSourceInPayload: true
        exprMap:
          holdingSrcSysNm: "/Document/Pos/SrcSysId/SrcSysNm/text()"
          tradeSrcSysNm: "/Document/Trad/SrcSysId/SrcSysNm/text()"
          holdingSeq: "/Document/Pos/sequenceNumber/text()"
          tradeSeq: "/Document/Trad/sequenceNumber/text()"

