spring:
  config:
    import: application-jasypt.yml
kafkaConfig:
  bootstrap.servers: "hkl20146687.hc.cloud.hk.hsbc:9094"
  group.id: "spring-poc"
  schema.registry.url: "http://hkl20146687.hc.cloud.hk.hsbc:8081/"
  auto.offset.reset: "earliest"
  security.protocol: "SSL"
  ssl.keystore.location: "C:/hss/apps/certs/unity-microservices.jks"
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: "C:/hss/apps/certs/unity-microservices.ts"
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: "MQ"
    sourceDataFormat: "XML"
    mode: ROUTING
    routingConfig:
      mode: MAPPING
      mappingConfig:
        - key: holdingSrcSysNm
          value: GCSF1
          targetTopic: topic-holding
        - key: holdingSrcSysNm
          value: GCSF5
          targetTopic: topic-holding
        - key: tradeSrcSysNm
          value: GCSF1
          targetTopic: topic-trade
        - key: tradeSrcSysNm
          value: GCSF5
          targetTopic: topic-trade
      defaultTargetTopic: "topic-default"
    sourceDataKeyFields:
      - holdingSrcSysNm
      - tradeSrcSysNm
      - holdingSeq
      - tradeSeq
    additionalProperties:
      mqConfig:
        queueName: test.queue
    inputParsingConfig:
      xmlParsingConfig:
        passSourceInPayload: true
        exprMap:
          holdingSrcSysNm: "/Document/Pos/SrcSysId/SrcSysNm/text()"
          tradeSrcSysNm: "/Document/Trad/SrcSysId/SrcSysNm/text()"
          holdingSeq: "/Document/Pos/sequenceNumber/text()"
          tradeSeq: "/Document/Trad/sequenceNumber/text()"