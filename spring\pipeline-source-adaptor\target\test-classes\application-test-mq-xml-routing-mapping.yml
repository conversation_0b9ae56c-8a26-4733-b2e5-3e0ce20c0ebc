sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: "MQ"
    sourceDataFormat: "XML"
    mode: ROUTING
    routingConfig:
      mode: MAPPING
      mappingConfig:
        - key: holdingSrcSysNm
          value: GCSF1
          targetTopic: topic-holding
        - key: holdingSrcSysNm
          value: GCSF5
          targetTopic: topic-holding
        - key: tradeSrcSysNm
          value: GCSF1
          targetTopic: topic-trade
        - key: tradeSrcSysNm
          value: GCSF5
          targetTopic: topic-trade
      defaultTargetTopic: "topic-default"
    sourceDataKeyFields:
      - holdingSrcSysNm
      - tradeSrcSysNm
      - holdingSeq
      - tradeSeq
    additionalProperties:
      mqConfig:
        queueName: test.queue
    inputParsingConfig:
      xmlParsingConfig:
        passSourceInPayload: true
        exprMap:
          holdingSrcSysNm: "/Document/Pos/SrcSysId/SrcSysNm/text()"
          tradeSrcSysNm: "/Document/Trad/SrcSysId/SrcSysNm/text()"
          holdingSeq: "/Document/Pos/sequenceNumber/text()"
          tradeSeq: "/Document/Trad/sequenceNumber/text()"

