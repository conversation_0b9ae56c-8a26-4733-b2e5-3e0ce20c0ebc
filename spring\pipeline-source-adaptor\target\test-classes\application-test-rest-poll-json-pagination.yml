sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: REST_POLL
    sourceDataFormat: JSON
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - "id"
    additionalProperties:
      restPollConfig:
        url: http://localhost:9999/test-poll
        httpMethod: GET
        oauth2Config:
          scopes: scope
          url: http://localhost:8080/oauth2/token
          clientId: client-id
          clientSecret: client-secret
          grantType: client_credentials
        payload: limit={limit}&offset={offset}&date={yyyyMMdd}
        limit: 50
        proxyConfig:
          proxyUrl: "apxy1.hk.hsbc"
          proxyPort: "18084"
    batchConfig:
      fixedDelay: 60000