sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: SFTP
    sourceDataFormat: CSV
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - column1
    additionalProperties:
      sftpConfig:
        host: localhost
        port: 20022
        userName: username
        password: password
        remoteDirectory: /files
        fileNamePattern: "data\\.csv"
    inputParsingConfig:
      csvParsingConfig:
        headerSectionStartRow: 1
        dataSectionStartRow: 5
    batchConfig:
      idPattern: "{yyyyMMdd}"
      poller:
        cron: "0 * * * * *"