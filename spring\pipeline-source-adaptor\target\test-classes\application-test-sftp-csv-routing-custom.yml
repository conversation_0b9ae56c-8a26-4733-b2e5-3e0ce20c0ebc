sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: "SFTP"
    sourceDataFormat: "CSV"
    mode: ROUTING
    routingConfig:
      mode: CUSTOM
      customRoutingConfig:
        routerName: "CustomRoutingFile.groovy"
        targetTopicMap:
          main: topic-main
          lookup: topic-lookup
          default: topic-default
      defaultTargetTopic: "topic-default"
    sourceDataKeyFields:
      - column1
    additionalProperties:
      sftpConfig:
        host: localhost
        port: 20022
        userName: username
        password: password
        remoteDirectory: /files
        fileNamePattern: "data\\-routing\\-.+\\.csv"
    inputParsingConfig:
      csvParsingConfig:
        headerSectionStartRow: -1
        dataSectionStartRow: 1

