sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: SFTP
    sourceDataFormat: CUSTOM
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - column1
    additionalProperties:
      sftpConfig:
        host: localhost
        port: 20022
        userName: username
        password: password
        remoteDirectory: /files
        fileNamePattern: "data\\.csv"
    inputParsingConfig:
      customParsingConfig:
        converterName: "CustomCsvConverter.groovy"