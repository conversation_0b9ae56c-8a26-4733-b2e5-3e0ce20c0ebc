sourceAdaptorConfig:
  sourceAdaptor:
    sourceChannel: SFTP
    sourceDataFormat: FIX_LENGTH_FLAT_FILE
    routingConfig:
      mode: DEFAULT
      defaultTargetTopic: "outbound_topic"
    sourceDataKeyFields:
      - column1
    additionalProperties:
      sftpConfig:
        host: localhost
        port: 20022
        userName: username
        password: password
        remoteDirectory: /files
        fileNamePattern: "data-fixedLength\\.txt"
    inputParsingConfig:
      fixedLengthParsingConfig:
        dataSectionStartRow: 1
        headerDefinitions:
          - headerName: GROUPID
            length: 10
            dataFormat: INTEGER
          - headerName: ENTITYID
            length: 10
            dataFormat: INTEGER
          - headerName: GROUP
            length: 4
            dataFormat: TEXT
          - headerName: ENTITY
            length: 4
            dataFormat: TEXT
          - headerName: CUSTOMERNAME
            length: 120
            dataFormat: TEXT
          - headerName: CUSTOMERSTATUS
            length: 15
            dataFormat: TEXT
          - headerName: Type
            length: 50
            dataFormat: TEXT
          - headerName: LOGOFILENAME
            length: 100
            dataFormat: TEXT
          - headerName: PRIMARYRM
            length: 35
            dataFormat: TEXT
          - headerName: SECONDARYRM
            length: 35
            dataFormat: TEXT
          - headerName: GROUPRM
            length: 35
            dataFormat: TEXT
          - headerName: CIB_IBH_RM
            length: 35
            dataFormat: TEXT
          - headerName: COUNTRYCODE
            length: 2
            dataFormat: TEXT
          - headerName: COUNTRYFULLNAME
            length: 35
            dataFormat: TEXT
          - headerName: ORIGINCODE
            length: 2
            dataFormat: TEXT
          - headerName: ORIGINFULLNAME
            length: 35
            dataFormat: TEXT
          - headerName: HSBCGROUP
            length: 1
            dataFormat: TEXT
          - headerName: CLASSCODE
            length: 2
            dataFormat: TEXT
          - headerName: SECTORCODE
            length: 5
            dataFormat: TEXT