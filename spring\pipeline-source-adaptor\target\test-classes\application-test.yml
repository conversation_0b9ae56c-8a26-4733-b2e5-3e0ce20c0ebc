kafkaConfig:
  bootstrap.servers: "localhost:13333"
  auto.offset.reset: earliest
sourceAdaptorConfig:
  projectId: "test-project-id"
  name: "test-project-name"
  sourceAdaptor:
    routingConfig:
      batchTopicSuffix: "-batch"
unity2:
  integration:
    shutdown:
      timeout:
        ms: 1000
logging:
  level:
    com.poc.hss.fasttrack: INFO
    org.apache: WARN
    org.springframework: WARN
    state.change.logger: WARN
    kafka: WARN
