import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

import java.util.concurrent.TimeUnit

class NoopTransformer extends AbstractTransformer {
    @Override
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        long start = System.currentTimeMillis()
        try {
            System.out.println(System.getenv());
            System.out.println(System.getProperty("testProperty"));

            return context.getRecords()
        }
        finally {
            context.getMeterRegistry().timer("batchTransform").record(System.currentTimeMillis() - start, TimeUnit.MILLISECONDS)
        }
    }
}