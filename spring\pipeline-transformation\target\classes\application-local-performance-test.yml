spring:
  config:
    import: application-jasypt.yml
pipelineName: test-pipeline-name
kafkaConfig:
  bootstrap.servers: hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094
  group.id: ${KAFKA_CONSUMER_GROUP:spring-poc-dev}
  schema.registry.url: http://hkl20146687.hc.cloud.hk.hsbc:8081/
  auto.offset.reset: earliest
  security.protocol: SSL
  ssl.keystore.location: C:/hss/apps/certs/env/dev/unity-microservices.jks
  ssl.keystore.password: ENC(P6fxbEuR7IIcFtwME7AsAKmE0IPEEe82z2RACsQ4dJ6IJQhACXEdwqAhvjTCPJQzvyKH3lHphECznhuk5V81SQ==)
  ssl.truststore.location: C:/hss/apps/certs/env/dev/unity-microservices.ts
  ssl.truststore.password: ENC(Tjbt18l41muuRlkaOMtNW+LtjDWgO5yD2VQExHeLGB9zt6RHFYyBWswVgpuq75iD8ZAZR2oCo7Apnn7SOB6mWA==)
transformations:
  - sourceTopics:
      - "performance-test-source-adaptor-out-10-5000-10"
    transformers:
      - "NoopTransformer.groovy"
    targetTopic: "performance-test-transformer-out-10-5000-10-tmp-${KAFKA_CONSUMER_GROUP}"
    sourceBatchTopicSuffix: "-batch"
    targetBatchTopicSuffix: "-batch"
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric-20220811-1
PIPELINE: test-pipeline
logging:
  level:
    com.poc.hss.fasttrack.step.StepExecutor: DEBUG