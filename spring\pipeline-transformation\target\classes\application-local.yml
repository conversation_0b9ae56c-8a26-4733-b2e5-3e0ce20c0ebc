spring:
  config:
    import: application-jasypt.yml
  datasource:
    url: "*****************************************"
    username: ""
    password: ""
pipelineName: test-pipeline-name
kafkaConfig:
  bootstrap.servers: "localhost:13333"
  group.id: ${KAFKA_CONSUMER_GROUP:project-DEV-local}
  auto.offset.reset: earliest
transformations:
  - sourceTopics:
      - "unity2-local-test-sa-out"
    transformers:
      - "NoopTransformer.groovy"
    targetTopic: "unity2-DEV-test"
    sourceBatchTopicSuffix: "-batch"
    targetBatchTopicSuffix: "-batch"
cache:
  provider: kafka
  address: http://localhost:8088
  topic: unity2-local-cache-metric
PIPELINE: local