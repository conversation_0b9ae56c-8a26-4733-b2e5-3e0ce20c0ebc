server:
  port: 8084
  shutdown: graceful

spring:
  application:
    name: pipeline-transformation
  config:
    import: optional:application-zipkin.yml,optional:configtree:/hss/apps/secrets/

cache:
  group: ${PIPELINE}
  component: TRANSFORM

unity2:
  auto-configuration:
    auto-bean-disposal: true

togglz:
  enabled: true
  console:
    enabled: false
management:
  endpoints:
    web:
      exposure:
        include: "togglz"
  health:
    redis:
      enabled: false
