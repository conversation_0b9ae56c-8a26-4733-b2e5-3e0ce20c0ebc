import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

class BatchTransformer extends AbstractTransformer {
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        def result = new ArrayList<>()

        context.getRecords().forEach(rec -> {
            def data = rec.getData().copy()
            data.put("newField", Helper.value())
            data.put("env",context.getEnv().getProperty("env"))
            result.add(TransformerRecord.from(rec).data(data).build())
            result.add(TransformerRecord.from(rec).data(data).build())
        })

        return result
    }
}