import com.poc.hss.fasttrack.service.CustomApiService
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.CustomApiRequest
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

import java.util.stream.Collectors

class CustomApiTransformer extends AbstractTransformer {
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        def result = new ArrayList<>()
        def ids = context.getRecords().stream().map(r -> r.getData().getValue("sourceId")).collect(Collectors.joining(","))
        Map<String, String> params = new HashMap<>()
        params.put("ids", ids)
        CustomApiRequest request = CustomApiRequest.builder()
        .queryName("labelQuery")
        .params(params)
                .build()
        CustomApiService customApiService = context.getCustomApiService()
        Map<String, String> labelMap = customApiService.queryList(request).stream().collect(Collectors.toMap({ json -> json.getString("_id") }, { json -> json.getString("label") }))
        context.getRecords().forEach(rec -> {
            def data = rec.getData().copy()
            data.put("label", labelMap.get(rec.getId()))
            result.add(TransformerRecord.from(rec).data(data).build())
        })
        return result
    }
}
