import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerSingleInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

class CustomSqlTransformer extends AbstractTransformer {
    List<TransformerRecord> transform(TransformerSingleInputContext context) {
        return Collections.singletonList(
                TransformerRecord.from(context.getRecord())
                        .sql("insert into table (a, b) values ('1', '2'); ")
                        .sqlExecutionMode("ASYNCHRONOUS")
                        .build()
        )
    }
}