{"inputFields": ["NPNCPD", "NPSDLC"], "operations": [{"type": "RENAME", "from": "NPNCPD", "formatters": ["TRIM"], "as": "output1"}, {"type": "CONCAT", "formatters": ["TRIM"], "fromMultiple": ["NPNCPD", "NPSDLC"], "as": "output2", "args": {"delimiter": ","}}, {"type": "MapValue", "formatters": ["TRIM", "CamelCase"], "from": "NPNCPD", "as": "output3", "args": {"schema": "test_table", "keyField": "key", "valueField": "label"}}]}