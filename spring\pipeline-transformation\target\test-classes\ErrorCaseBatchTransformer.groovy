import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

class ErrorCaseBatchTransformer extends AbstractTransformer {
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        return Collections.singletonList(
                TransformerRecord.from(context.getRecords().first())
                        .exception(new RuntimeException())
                        .build()
        )
    }
}