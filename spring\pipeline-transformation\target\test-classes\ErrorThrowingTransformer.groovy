import com.poc.hss.fasttrack.backoff.BackoffException
import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaException
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

class ErrorThrowingTransformer extends AbstractTransformer {
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        if (context.getRecords().any { rec -> rec.getData().getString("THROW") == "OUTSIDE_RETRY" })
            throw new Unity2KafkaException()

        if (context.getRecords().any { rec -> rec.getData().getString("THROW") == "OUTSIDE_NON_RETRY" })
            throw new Exception()

        if (context.getRecords().any { rec -> rec.getData().getString("THROW") == "OUTSIDE_BACKOFF" })
            throw new BackoffException("Timeout")

        List<TransformerRecord> result = new ArrayList<>()
        for (TransformerRecord rec : context.getRecords()) {
            if (rec.getData().getString("THROW") == "INSIDE_NON_RETRY") {
                result.add(TransformerRecord.from(rec).exception(new Exception()).build())
                return result
            } else if (rec.getData().getString("THROW") == "INSIDE_RETRY") {
                result.add(TransformerRecord.from(rec).exception(new Unity2KafkaException()).build())
                return result
            } else if (rec.getData().getString("THROW") == "INSIDE_BACKOFF") {
                result.add(TransformerRecord.from(rec).exception(new BackoffException("Timeout")).build())
                return result
            } else {
                result.add(TransformerRecord.from(rec).build())
            }
        }
        return result
    }
}
