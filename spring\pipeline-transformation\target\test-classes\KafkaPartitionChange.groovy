import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerSingleInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

class KafkaPartitionChange extends AbstractTransformer {
    List<TransformerRecord> transform(TransformerSingleInputContext context) {
        return Collections.singletonList(
                TransformerRecord
                        .from(context.getRecord())
                        .kafkaPartitionKey("my-updated-custom-key")
                        .build()
        );
    }
}