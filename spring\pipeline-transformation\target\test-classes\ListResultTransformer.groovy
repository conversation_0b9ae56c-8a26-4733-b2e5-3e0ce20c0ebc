import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerSingleInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

class ListResultTransformer extends AbstractTransformer {
    List<TransformerRecord> transform(TransformerSingleInputContext context) {
        Integer previous = Helper.get("test")
        Integer current = previous + 1
        Helper.put("test", current)
        return Collections.singletonList(
                TransformerRecord.from(context.getRecord())
                        .data(
                                context.getRecord().getData()
                                        .put("newField", Helper.value())
                                        .put("previous", "" + previous)
                                        .put("current", "" + current)
                        )
                        .build()
        );
    }
}