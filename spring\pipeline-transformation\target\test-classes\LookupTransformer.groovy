import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord

import java.util.stream.Collectors

class LookupTransformer extends AbstractTransformer {
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        def result = new ArrayList<>()
        def ids = context.getRecords().stream().map(r -> r.getData().getValue("sourceId")).collect(Collectors.toList())
        LookupRequest entitlementLookupRequest = LookupRequest.builder()
                .accessSchemaName("TABLE_NAME")
                .criteria(String.format("_id in ('%s')", String.join("','", ids)))
                .fields(Arrays.asList("_id", "label"))
                .build()
        LookupService lookupService = context.getLookupService()
        Map<String, String> labelMap = lookupService.queryList(entitlementLookupRequest).stream().collect(Collectors.toMap({ json -> json.getString("_id") }, { json -> json.getString("label") }))

        context.getRecords().forEach(rec -> {
            def data = rec.getData().copy()
            data.put("label", labelMap.get(rec.getId()))
            result.add(TransformerRecord.from(rec).data(data).build())
        })

        return result
    }
}
