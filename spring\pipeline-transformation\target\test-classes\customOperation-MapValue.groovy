import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.Unity2DslOperationSingleFieldInput
import com.poc.hss.fasttrack.transform.operation.SingleInputUnity2DslOperation

import java.util.stream.Collectors

class MapValue extends SingleInputUnity2DslOperation {
    @Override
    protected String doOperation(Unity2DslOperationSingleFieldInput input) {
        def source = input.getSource()
        def from = input.getFromField()
        def args = input.getArgs()
        def lookupService = input.getLookupService()
        String value = source.getString(from);
        def queryList = lookupService.queryList(LookupRequest.builder()
                .accessSchemaName(args.get("schema"))
                .fields(Arrays.asList(args.get("keyField"), args.get("valueField")))
                .build())
        def labelMap = queryList.stream().collect(Collectors.toMap(e -> e.getString(args.get("keyField")), e -> e.getString(args.get("valueField"))))
        return labelMap.get(value)
    }
}