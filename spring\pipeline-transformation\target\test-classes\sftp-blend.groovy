import com.poc.hss.fasttrack.kafka.exception.Unity2KafkaException
import com.poc.hss.fasttrack.service.LookupService
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.LookupRequest
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class SftpBlendTransformer extends AbstractTransformer {
    Logger logger = LoggerFactory.getLogger("SftpBlendTransformer")

    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        List<TransformerRecord> result = new ArrayList<>()
        if (context.getRecords().size() > 0) {
            String batchId = context.getRecords().get(0).getBatchId()
            // wait for status SUCCESS appear
            // throw Unity2KafkaException to retry the transformation after 30 seconds cannot get the SUCCESS status
            lookupStatus(context, batchId)

            context.getRecords().forEach(rec -> {
                JsonObject mainRec = null
                JsonObject lookupRec = null
                JsonObject patchRec = null
                logger.info("rec.getSource(): " + rec.getSource())
                logger.info("rec.getId(): " + rec.getId())
                if (rec.getSource() == "sftp") {
                    mainRec = rec.getData().copy()
                    lookupRec = lookup(context, "sftp-lookup", rec.getId(), batchId)
                } else if (rec.getSource() == "sftp-lookup") {
                    mainRec = lookup(context, "sftp", rec.getId(), batchId)
                    lookupRec = rec.getData().copy()
                } else if (rec.getSource() == "sftp-patch") {
                    patchRec = rec.getData().copy()
                }
                logger.info("mainRec: " + mainRec + ", lookupRec: " + lookupRec + ", patchRec: " + patchRec)
                //If not both appear, then skip it; the message will be generated when both message arrived
                if (mainRec && lookupRec) {
                    mainRec.put("lookup1", lookupRec.getValue("lookup1"))
                    mainRec.put("lookup2", lookupRec.getValue("lookup2"))
                    result.add(TransformerRecord.from(rec)
                            .data(mainRec)
//                            .sourceGroup("sftp,sftp-lookup,sftp-patch") //used for reconciliation, it is concatenated pipeline name
                            .build())
                }
                if (patchRec) {
                    result.add(TransformerRecord.from(rec)
                            .data(patchRec)
//                            .sourceGroup("sftp,sftp-lookup,sftp-patch") //used for reconciliation, it is concatenated pipeline name
                            .build())
                }
            })
        }
        return result
    }

    static JsonObject lookup(TransformerBatchInputContext context, String schema, String id, String batchId) {
        LookupRequest request = LookupRequest.builder()
                .accessSchemaName(schema)
                .criteria(String.format("_id = '%s' and _batch_id = '%s'", id, batchId))
        //Not specifying the fields will return all columns
                .build()
        LookupService lookupService = context.getLookupService()
        return lookupService.queryOne(request)
    }

    static JsonObject lookupStatus(TransformerBatchInputContext context, String id) {
        LookupRequest statusRequest = LookupRequest.builder()
                .accessSchemaName("sftp-status")
                .criteria(String.format("_id = '%s' and status= '%s'", id, "SUCCESS"))
                .fields(Arrays.asList("_id", "date", "status"))
                .build()
        LookupService lookupService = context.getLookupService()
        //If it is required, it will retry until 30 sec with exponential retry
        JsonObject statusObj = lookupService.queryOne(statusRequest, true)
        //If it is still empty, then return Unity2KafkaException to force a rollback, no message will be lost
        if (statusObj == null)
            throw new Unity2KafkaException("Still unable to get complete status")
        return statusObj
    }
}