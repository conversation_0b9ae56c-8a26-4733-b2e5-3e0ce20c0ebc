import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonObject

class SftpJsonTransformer extends AbstractTransformer {
    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        def result = new ArrayList<>()

        context.getRecords().forEach(rec -> {
            try{
                def data = rec.getData().copy()
                data.put("jsonField", new JsonObject().put("column1",data.getValue("column1"))
                        .put("column2",data.getValue("column2"))
                        .put("column3",data.getValue("column3")))
                result.add(TransformerRecord.from(rec).data(data).build())
            }
            catch(Exception e){
                result.add(TransformerRecord.from(rec).exception(e).build())
            }
        })

        return result
    }
}