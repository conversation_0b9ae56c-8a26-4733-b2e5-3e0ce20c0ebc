{"inputFields": ["column1", "column2", "column3", "header1", "header2", "header3", "newField", "increment"], "operations": [{"type": "NOOP", "from": "column1", "formatters": []}, {"type": "NOOP", "from": "newField", "formatters": []}, {"type": "NOOP", "from": "column2", "formatters": []}, {"type": "NOOP", "from": "column3", "formatters": []}, {"type": "NOOP", "from": "header1", "formatters": []}, {"type": "NOOP", "from": "header2", "formatters": []}, {"type": "NOOP", "from": "header3", "formatters": []}, {"formatters": ["UPPERCASE"], "type": "RENAME", "from": "column1", "as": "column1upper"}, {"formatters": ["LOWERCASE"], "type": "RENAME", "from": "column1", "as": "column1lower"}, {"formatters": ["TRIM"], "type": "RENAME", "from": "column1", "as": "column1trim"}, {"formatters": ["CamelCase"], "type": "RENAME", "from": "column1", "as": "column1camel"}, {"formatters": [], "type": "StaticMapValue", "from": "column1", "as": "column1<PERSON><PERSON>", "args": {"criteria1": "row1column1", "value1": "row1column1new", "criteria2": "row2column1", "value2": "row2column1new", "criteria3": "row3column1", "value3": "row3column1new"}}, {"type": "NOOP", "from": "increment", "formatters": []}]}