import com.poc.hss.fasttrack.constant.Constants
import com.poc.hss.fasttrack.enums.AccessOperator
import com.poc.hss.fasttrack.transform.AbstractTransformer
import com.poc.hss.fasttrack.transform.model.TransformerBatchInputContext
import com.poc.hss.fasttrack.transform.model.TransformerRecord
import io.vertx.core.json.JsonArray
import io.vertx.core.json.JsonObject
import org.apache.kafka.common.serialization.StringSerializer
import org.apache.kafka.common.utils.Utils
import org.slf4j.Logger
import org.slf4j.LoggerFactory

class SftpTransformer extends AbstractTransformer {
    Logger logger = LoggerFactory.getLogger("SftpTransformer")

    List<TransformerRecord> batchTransform(TransformerBatchInputContext context) {
        def result = new ArrayList<>()
        //Store only one record per partition per batch id
        def recMap = new HashMap<String, Map<Integer, TransformerRecord>>()
        context.getRecords().forEach(rec -> {
            try {
                if (rec.getBatchId() != null) {
                    if (!recMap.containsKey(rec.getBatchId()))
                        recMap.put(rec.getBatchId(), new HashMap<>())
                    //Calculate the partition
                    int numPartitions = 8
                    String outputTopic = "unity2-" + context.getEnv().getProperty("ENV") + "-core-health-check-transform-sftp-out"
                    String key = Optional.ofNullable(rec.getKafkaPartitionKey()).orElse(rec.getId())
                    byte[] serializedKey = new StringSerializer().serialize(outputTopic, key)
                    int partition = Utils.toPositive(Utils.murmur2(serializedKey)) % (numPartitions)

                    recMap.get(rec.getBatchId()).put(partition, rec);
                }
                def data = rec.getData().copy()
                data.put("newField", Helper.value())
                data.put("env", context.getEnv().getProperty("env"))

                data.put("increment", "1")
                data.put("newField_changed", "true")
                JsonObject operationMap = new JsonObject()
                operationMap.put("increment", AccessOperator.INCREMENT.toString())
                operationMap.put("newField_changed", new JsonObject()
                        .put(Constants.OPERATION_MAP_OPERATOR, AccessOperator.NOT_EQUAL.toString())
                        .put(Constants.OPERATION_MAP_FIELDS, new JsonArray(Arrays.asList("newField", "newField")))
                )

                if (data.getString("action") == "split") {
                    result.add(TransformerRecord.from(rec).data(data).operationMap(operationMap).build())
                    result.add(TransformerRecord.from(rec).data(data).operationMap(operationMap).build())
                } else if (data.getString("action") == "skip") {
                } else if (data.getString("action") == "error") {
                    throw new RuntimeException("error")
                } else {
                    result.add(TransformerRecord.from(rec).data(data).operationMap(operationMap).build())
                }
            }
            catch (Exception e) {
                result.add(TransformerRecord.from(rec).exception(e).build())
            }
        })

        //send SQL per partition per batch
        //So, it guarantees the sql will be executed after the messages are persisted in each partition

        //Certainly, if the sql is not for the whole batch, you do not need to do it per partition
        //E.g. If you have some records in (without batch or across batch) and you want to do a sum of them
        //then, you just need to assign those records to the same partition_key, and add the sql with that partition key as well
        for (String batchId : recMap.keySet()) {
            def partitionRecMap = recMap.get(batchId)
            def sql = "update \"8c7ab7aa_810e_4573_971e_cc9ca8f1ab90\".\"sftp\" set total = " +
                    "(select count(1) from \"8c7ab7aa_810e_4573_971e_cc9ca8f1ab90\".\"sftp\" where _batch_id='" + batchId + "')" +
                    " where _batch_id='" + batchId + "'";
            logger.info("sql: {}", sql)
            for (TransformerRecord rec : partitionRecMap.values())
                result.add(TransformerRecord.from(rec)
                        .sql(sql)
                        .build())
        }
        return result
    }
}