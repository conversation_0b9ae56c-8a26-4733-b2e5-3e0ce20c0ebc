{"name": "Example Test Case - Kafka to Kafka scenario", "id": "example-test-case-kafka-to-kafka", "type": "FUNCTIONAL", "sourceChannelConfigList": [{"name": "tf-kafka-to-kafka-source", "channel": "KAFKA", "dataFormat": "JSON", "kafkaChannelConfig": {"topic": "tf_kafka_to_kafka_source_in", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "acks": "all", "retries": 0, "batch.size": 16384, "linger.ms": 1, "buffer.memory": 33554432, "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-confluent.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-confluent.ts", "ssl.truststore.password": ""}, "inputDataList": [{"fileName": "kafka-to-kafka-input.json", "messageBulkPublishConfig": null}]}}], "dataPersistenceValidationList": [{"name": "tf-kafka-to-kafka-schema", "dataPersistenceConnectionConfig": {"url": "*****************************************", "username": "postgres", "password": "", "driver": "org.postgresql.Driver"}, "dbName": "test_framework", "tableName": "tf_kafka_to_kafka", "validationConfig": {"idField": "userId", "timeout": 30, "cleanUpData": false}, "data": [{"_id": "9711417", "userId": "9711417", "firstname": "TestSitCustom", "lastname": "TestSitCustom2", "fullname": "TestSitCustom3"}]}], "customApiValidationList": [{"name": "tf-kafka-to-kafka-custom-api", "customApiConfig": {"url": "http://localhost:8088/api/custom/tf-kafka-to-kafka", "headers": null, "queryParams": null, "hssProfile": {"authServer": null, "clientId": null, "clientSecret": null}, "proxy": null}, "validationConfig": {"timeout": 10, "idField": "_id"}, "expectedResponseCode": 200, "expectedResultMessages": [{"_id": "9711417", "userId": "9711417", "firstname": "TestSitCustom", "lastname": "TestSitCustom2", "fullname": "TestSitCustom3"}], "expectedPageInfo": {"nextPageIdx": 1, "prePageIdx": 1, "currentPageIdx": 1, "pageSize": 100, "totalNumberOfRecords": 1, "totalPage": 1}}], "consumerChannelValidationList": [{"name": "tf-kafka-to-kafka-consumer", "channel": "KAFKA", "dataFormat": "JSON", "validationConfig": {"timeout": 300}, "kafkaChannelConfig": {"topic": "tf_kafka_to_kafka_consumer_out", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-confluent.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-confluent.ts", "ssl.truststore.password": "", "auto.offset.reset": "latest"}, "dataFiles": ["kafka-to-kafka-output.json"]}}]}