[{"_id": "9711417", "userId": "9711417", "firstname": "TestSit", "lastname": "Custom", "fullname": "TestSitCustom", "birthday": "1988-06-21", "email": "<EMAIL>", "phone": "283-272-085", "street": "Budapester Strasse", "houseNo": "72", "apartmentNo": "16", "zipCode": "27-412", "city": "Kirchtimke", "country": "<PERSON><PERSON>", "role": "technician"}, {"_id": "9711418", "userId": "9711418", "firstname": "TestSit", "lastname": "Custom2", "fullname": "TestSitCustom2", "birthday": "1989-06-21", "email": "<EMAIL>", "phone": "283-272-086", "street": "Budapester Strasse", "houseNo": "73", "apartmentNo": "17", "zipCode": "27-413", "city": "Kirchtimke", "country": "Alb", "role": "technician"}, {"_id": "9711419", "userId": "9711419", "firstname": "TestSit", "lastname": "Custom3", "fullname": "TestSitCustom3", "birthday": "1990-06-21", "email": "<EMAIL>", "phone": "283-272-087", "street": "Budapester Strasse", "houseNo": "74", "apartmentNo": "18", "zipCode": "27-414", "city": "Kirchtimke", "country": "Mon", "role": "technician"}]