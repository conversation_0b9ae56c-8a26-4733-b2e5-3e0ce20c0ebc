{"name": "Example Test Case - Kafka to PubSub scenario", "id": "example-test-case-kafka-to-pubsub", "type": "FUNCTIONAL", "preTest": {"connectionConfig": {"url": "*****************************************", "username": "postgres", "password": "", "driver": "org.postgresql.Driver"}, "scriptFileName": "init.sql"}, "sourceChannelConfigList": [{"name": "tf-kafka-to-pubsub-source", "channel": "KAFKA", "dataFormat": "JSON", "kafkaChannelConfig": {"topic": "tf_kafka_to_pubsub_source_in", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "acks": "all", "retries": 0, "batch.size": 16384, "linger.ms": 1, "buffer.memory": 33554432, "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-confluent.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-confluent.ts", "ssl.truststore.password": ""}, "inputDataList": [{"fileName": "kafka-to-pubsub-input.json", "messageBulkPublishConfig": null}]}}], "dataPersistenceValidationList": [{"name": "tf-kafka-to-pubsub-schema", "dataPersistenceConnectionConfig": {"url": "*****************************************", "username": "postgres", "password": "", "driver": "org.postgresql.Driver"}, "dbName": "test_framework", "tableName": "tf_kafka_to_pubsub", "validationConfig": {"idField": "_id", "timeout": 30, "cleanUpData": false}, "data": [{"_id": "9711417", "userId": "9711417", "firstname": "TestSit", "lastname": "Custom", "fullname": "TestSitCustom"}]}], "customApiValidationList": [{"name": "tf-kafka-to-pubsub-custom-api", "customApiConfig": {"url": "http://localhost:8088/api/custom/tf-kafka-to-pubsub", "headers": null, "queryParams": null, "hssProfile": {"authServer": null, "clientId": null, "clientSecret": null}, "proxy": null}, "validationConfig": {"timeout": 10, "idField": "_id"}, "expectedResponseCode": 200, "expectedResultMessages": [{"_id": "9711417", "userId": "9711417", "firstname": "TestSit", "lastname": "Custom", "fullname": "TestSitCustom"}, {"_id": "9711418", "userId": "9711418", "firstname": "TestSit", "lastname": "Custom2", "fullname": "TestSitCustom2"}, {"_id": "9711419", "userId": "9711419", "firstname": "TestSit", "lastname": "Custom3", "fullname": "TestSitCustom3"}], "expectedPageInfo": {"nextPageIdx": 1, "prePageIdx": 1, "currentPageIdx": 1, "pageSize": 100, "totalNumberOfRecords": 3, "totalPage": 1}}], "consumerChannelValidationList": [{"name": "tf-kafka-to-pubsub-consumer", "channel": "PUB_SUB", "dataFormat": "JSON", "validationConfig": {"timeout": 300}, "pubSubChannelConfig": {"projectName": "hsbc-9087302-unity-dev", "topicName": "tf_kafka_to_pubsub_consumer_out", "dataFiles": ["kafka-to-pubsub-output.json"]}}]}