{"name": "Example Test Case - test-framework", "id": "example-test-case-test-framework", "type": "FUNCTIONAL", "preTest": {"connectionConfig": {"url": "**********************************************", "username": "<EMAIL>", "password": "", "driver": "org.postgresql.Driver"}, "scriptFileName": "init.sql"}, "postTest": {"connectionConfig": {"url": "**********************************************", "username": "<EMAIL>", "password": "", "driver": "org.postgresql.Driver"}, "scriptFileName": "cleanup.sql"}, "sourceChannelConfigList": [{"name": "tf-kafka-json", "channel": "KAFKA", "dataFormat": "JSON", "kafkaChannelConfig": {"topic": "tf-dev-kafka-json-source-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "acks": "all", "retries": 0, "batch.size": 16384, "linger.ms": 1, "buffer.memory": 33554432, "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": ""}, "inputDataList": [{"fileName": "kafka-source-json.json", "messageBulkPublishConfig": null}]}}, {"name": "tf-kafka-xml", "channel": "KAFKA", "dataFormat": "XML", "kafkaChannelConfig": {"topic": "tf-dev-kafka-xml-source-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "acks": "all", "retries": 0, "batch.size": 16384, "linger.ms": 1, "buffer.memory": 33554432, "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": ""}, "inputDataList": [{"fileName": "kafka-source-xml.xml", "messageBulkPublishConfig": null}]}}], "dataPersistenceValidationList": [{"name": "tf-schema", "dataPersistenceConnectionConfig": {"url": "**********************************************", "username": "<EMAIL>", "password": "", "driver": "org.postgresql.Driver"}, "dbName": "d9ebd1f6_8501_4acd_8b10_0c5ebbf10a8b", "tableName": "tf-schema", "validationConfig": {"idField": "_id", "timeout": 300}, "data": [{"_id": "ITEM1", "id": "item1", "field_1": "item1_field1", "field_2_upper": "ITEM1_FIELD2", "field_3_upper": "ITEM1_FIELD3"}, {"_id": "ITEM2", "id": "item2", "field_1": "item2_field1", "field_2_upper": "ITEM2_FIELD2", "field_3_upper": "ITEM2_FIELD3"}, {"_id": "ITEM3", "id": "item3", "field_1": "item3_field1", "field_2_upper": "ITEM3_FIELD2", "field_3_upper": "ITEM3_FIELD3"}]}], "customApiValidationList": [{"name": "tf-custom-api", "customApiConfig": {"url": "https://api.testframework-dev.vpc3.hsbc-9087302-unity-dev.dev.gcp.cloud.hk.hsbc/api/custom/tf-custom-api", "headers": null, "queryParams": null, "hssProfile": {"authServer": null, "clientId": null, "clientSecret": null}, "proxy": null}, "validationConfig": {"timeout": 10, "idField": "id"}, "expectedResponseCode": 200, "expectedResultMessages": [{"id": "test_init", "field_1": "Field 1", "field_2_upper": "FIELD 2", "field_3_upper": "FIELD 3"}, {"id": "item1", "field_1": "item1_field1", "field_2_upper": "ITEM1_FIELD2", "field_3_upper": "ITEM1_FIELD3"}, {"id": "item2", "field_1": "item2_field1", "field_2_upper": "ITEM2_FIELD2", "field_3_upper": "ITEM2_FIELD3"}, {"id": "item3", "field_1": "item3_field1", "field_2_upper": "ITEM3_FIELD2", "field_3_upper": "ITEM3_FIELD3"}], "expectedPageInfo": {"nextPageIdx": 1, "prePageIdx": 1, "currentPageIdx": 1, "pageSize": 10, "totalNumberOfRecords": 4, "totalPage": 1}}], "consumerChannelValidationList": [{"name": "tf-kafka-json", "channel": "KAFKA", "dataFormat": "JSON", "validationConfig": {"timeout": 300}, "kafkaChannelConfig": {"topic": "tf-dev-kafka-json-consumer-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": "", "auto.offset.reset": "latest"}, "dataFiles": ["kafka-consumer-json.json"]}}, {"name": "tf-kafka-xml", "channel": "KAFKA", "dataFormat": "XML", "validationConfig": {"timeout": 300}, "xmlConfig": {"rootKey": "root", "itemKey": "items"}, "kafkaChannelConfig": {"topic": "tf-dev-kafka-xml-consumer-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": "", "auto.offset.reset": "latest"}, "dataFiles": ["kafka-consumer-xml.xml"]}}]}