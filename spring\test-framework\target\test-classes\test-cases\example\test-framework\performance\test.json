{"name": "Example Test Case - test-framework", "id": "example-test-case-test-framework", "type": "PERFORMANCE", "preTest": null, "postTest": null, "sourceChannelConfigList": [{"name": "tf-kafka-json", "channel": "KAFKA", "dataFormat": "JSON", "kafkaChannelConfig": {"topic": "tf-dev-kafka-json-source-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "acks": "all", "retries": 0, "batch.size": 16384, "linger.ms": 1, "buffer.memory": 33554432, "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "example", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": "example"}, "inputDataList": [{"fileName": "kafka-source-json.json", "messageBulkPublishConfig": {"numberOfBatches": 10, "numberOfMessagesPerBatch": 1000, "pauseInSeconds": 2, "dataKeyFields": ["id"]}}]}}], "dataPersistenceValidationList": [{"name": "tf-schema", "dataPersistenceConnectionConfig": {"url": "**********************************************", "username": "<EMAIL>", "password": "example", "driver": "org.postgresql.Driver"}, "dbName": "d9ebd1f6_8501_4acd_8b10_0c5ebbf10a8b", "tableName": "tf-schema", "validationConfig": {"timeout": 60000}}], "customApiValidationList": [], "consumerChannelValidationList": [{"name": "tf-kafka-json", "channel": "KAFKA", "dataFormat": "JSON", "validationConfig": {"timeout": 60000}, "kafkaChannelConfig": {"topic": "tf-dev-kafka-json-consumer-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "example", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": "example", "auto.offset.reset": "latest"}, "consumerGroup": "test-framework-pt-consumer-group"}}, {"name": "tf-kafka-xml", "channel": "KAFKA", "dataFormat": "XML", "validationConfig": {"timeout": 60000}, "xmlConfig": {"rootKey": "root", "itemKey": "items"}, "kafkaChannelConfig": {"topic": "tf-dev-kafka-xml-consumer-topic", "connectionConfig": {"bootstrap.servers": "hkl20146687.hc.cloud.hk.hsbc:9094,hkl20146688.hc.cloud.hk.hsbc:9094,hkl20152296.hc.cloud.hk.hsbc:9094", "security.protocol": "SSL", "ssl.keystore.location": "C:/hss/apps/certs/env/dev/unity-microservices.jks", "ssl.keystore.password": "example", "ssl.truststore.location": "C:/hss/apps/certs/env/dev/unity-microservices.ts", "ssl.truststore.password": "example", "auto.offset.reset": "latest"}, "consumerGroup": "test-framework-pt-consumer-group"}}]}