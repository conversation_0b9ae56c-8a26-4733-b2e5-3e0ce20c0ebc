import os
import time
from prometheus_client import start_http_server, Gauge
from kubernetes import client, config
from kubernetes.watch import Watch
from kubernetes.client.exceptions import ApiException
from urllib3.exceptions import ProtocolError
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Prometheus metrics
scaledobject_state = Gauge('keda_scaledobject_state', 'KEDA ScaledObject State', ['scaledobject_name', 'state'])
scaledobject_ready = Gauge('keda_scaledobject_ready', 'KEDA ScaledObject Ready State', ['scaledobject_name'])
scaledobject_active = Gauge('keda_scaledobject_active', 'KEDA ScaledObject Active State', ['scaledobject_name'])
scaledobject_paused = Gauge('keda_scaledobject_paused', 'KEDA ScaledObject Paused State', ['scaledobject_name'])

def get_current_namespace():
    try:
        with open('/var/run/secrets/kubernetes.io/serviceaccount/namespace', 'r') as f:
            namespace = f.read().strip()
            logging.info(f"Detected namespace from service account: {namespace}")
            return namespace
    except FileNotFoundError:
        logging.warning("Namespace file not found, defaulting to 'default' namespace.")
        return "default"

def get_scaledobject_status():
    try:
        # Load Kubernetes configuration
        try:
            config.load_kube_config()
        except config.ConfigException:
            logging.info("Kube config not found, falling back to in-cluster configuration.")
            config.load_incluster_config()

        v1 = client.CustomObjectsApi()
        namespace = get_current_namespace()

        # Fetch ScaledObjects with timeout
        scaledobjects = v1.list_namespaced_custom_object(
            group="keda.sh",
            version="v1alpha1",
            plural="scaledobjects",
            namespace=namespace,
            _request_timeout=30  # 30 second timeout
        )

        status_list = []
        items = scaledobjects.get("items", [])
        logging.debug(f"Found {len(items)} ScaledObjects in namespace {namespace}")

        for item in items:
            try:
                name = item["metadata"]["name"]
                conditions = item.get("status", {}).get("conditions", [])
                ready_state = next((cond["status"] for cond in conditions if cond["type"] == "Ready"), "Unknown")
                active_state = next((cond["status"] for cond in conditions if cond["type"] == "Active"), "Unknown")
                paused_state = next((cond["status"] for cond in conditions if cond["type"] == "Paused"), "Unknown")
                paused_state = "False" if paused_state == "Unknown" else paused_state

                if os.getenv("LOG_LEVEL") == "DEBUG":
                    logging.debug(f"ScaledObject: {name}, Ready: {ready_state}, Active: {active_state}, Paused: {paused_state}")

                status_list.append((name, ready_state, active_state, paused_state))
            except KeyError as ke:
                logging.warning(f"Missing key in ScaledObject data: {ke}")
                continue
            except Exception as ie:
                logging.error(f"Error processing ScaledObject {item.get('metadata', {}).get('name', 'unknown')}: {ie}")
                continue

        logging.info(f"Successfully processed {len(status_list)} ScaledObjects")
        return status_list

    except ApiException as e:
        logging.error(f"Kubernetes API error fetching ScaledObject status: {e}")
        return []
    except Exception as e:
        logging.error(f"Unexpected error fetching ScaledObject status: {e}")
        return []

def update_metrics():
    try:
        scaledobject_status = get_scaledobject_status()

        # Track currently active ScaledObjects
        active_scaledobjects = set(name for name, _, _, _ in scaledobject_status)
        logging.debug(f"Active ScaledObjects: {active_scaledobjects}")

        # Update metrics for active ScaledObjects
        for name, ready_state, active_state, paused_state in scaledobject_status:
            try:
                scaledobject_state.labels(scaledobject_name=name, state=ready_state).set(1)
                scaledobject_ready.labels(scaledobject_name=name).set(1 if ready_state == "True" else 0)
                scaledobject_active.labels(scaledobject_name=name).set(1 if active_state == "True" else 0)
                scaledobject_paused.labels(scaledobject_name=name).set(1 if paused_state == "True" else 0)
            except Exception as e:
                logging.error(f"Error updating metrics for ScaledObject {name}: {e}")

        # Remove metrics for ScaledObjects no longer present
        try:
            metrics_to_remove = []
            for label in list(scaledobject_state._metrics.keys()):
                if label[0] not in active_scaledobjects:  # Access the first element of the tuple
                    metrics_to_remove.append(label)

            for label in metrics_to_remove:
                try:
                    scaledobject_state.remove(*label)
                    scaledobject_ready.remove(*label)
                    scaledobject_active.remove(*label)
                    scaledobject_paused.remove(*label)
                    logging.info(f"Removed metrics for deleted ScaledObject: {label[0]}")
                except Exception as e:
                    logging.warning(f"Error removing metrics for label {label}: {e}")

        except Exception as e:
            logging.error(f"Error cleaning up old metrics: {e}")

        logging.debug(f"Metrics update completed for {len(scaledobject_status)} ScaledObjects")

    except Exception as e:
        logging.error(f"Critical error in update_metrics: {e}")

# Watch for changes to ScaledObjects with robust error handling
if __name__ == "__main__":
    # Start Prometheus HTTP server on port 8080
    start_http_server(8080)
    logging.info("Started Prometheus HTTP server on port 8080")

    # Initialize Kubernetes client
    try:
        config.load_kube_config()
        logging.info("Loaded kube config from file")
    except config.ConfigException:
        logging.info("Kube config not found, falling back to in-cluster configuration.")
        config.load_incluster_config()

    v1 = client.CustomObjectsApi()
    namespace = get_current_namespace()
    logging.info(f"Monitoring KEDA ScaledObjects in namespace: {namespace}")

    resource_version = None  # Initialize resource version
    retry_count = 0
    max_retries = 5
    base_retry_delay = 5  # seconds

    # Initial metrics update
    update_metrics()
    logging.info("Initial metrics update completed")

    while True:
        watch = Watch()  # Create new watch instance for each iteration
        try:
            logging.info(f"Starting watch stream (resource_version: {resource_version})")

            # Start the watch stream with the latest resource version
            for event in watch.stream(v1.list_namespaced_custom_object,
                                      group="keda.sh",
                                      version="v1alpha1",
                                      plural="scaledobjects",
                                      namespace=namespace,
                                      resource_version=resource_version,
                                      timeout_seconds=300):  # 5 minute timeout

                logging.info(f"Event: {event['type']} - {event['object']['metadata']['name']}")
                resource_version = event['object']['metadata']['resourceVersion']  # Update resource version
                update_metrics()
                retry_count = 0  # Reset retry count on successful event

        except ApiException as e:
            if e.status == 410:  # Resource version expired
                logging.warning("Resource version expired, fetching latest resource version...")
                try:
                    scaledobjects = v1.list_namespaced_custom_object(
                        group="keda.sh",
                        version="v1alpha1",
                        plural="scaledobjects",
                        namespace=namespace
                    )
                    resource_version = scaledobjects['metadata']['resourceVersion']  # Get latest resource version
                    logging.info(f"Updated resource version to: {resource_version}")
                    retry_count = 0  # Reset retry count
                    continue
                except Exception as inner_e:
                    logging.error(f"Failed to fetch latest resource version: {inner_e}")
                    retry_count += 1
            else:
                logging.error(f"API Exception: {e}")
                retry_count += 1

        except ProtocolError as e:
            logging.warning(f"Protocol error (connection terminated): {e}")
            retry_count += 1

        except Exception as e:
            logging.error(f"Unexpected error: {e}")
            retry_count += 1

        finally:
            # Always close the watch to clean up resources
            try:
                watch.stop()
            except:
                pass

        # Implement exponential backoff for retries
        if retry_count > 0:
            if retry_count >= max_retries:
                logging.error(f"Max retries ({max_retries}) exceeded. Resetting retry count and continuing...")
                retry_count = 0
                resource_version = None  # Reset resource version to start fresh

            retry_delay = min(base_retry_delay * (2 ** (retry_count - 1)), 60)  # Cap at 60 seconds
            logging.info(f"Retrying in {retry_delay} seconds (attempt {retry_count}/{max_retries})")
            time.sleep(retry_delay)

            # Update metrics during retry to maintain data freshness
            update_metrics()