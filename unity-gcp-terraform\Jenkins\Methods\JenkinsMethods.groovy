import groovy.lang.IntRange
import java.util.*
import java.util.Date
import java.util.Locale
import jenkins.model.*
import hudson.model.ParameterDefinition
import groovy.json.*
import java.nio.file.*
import com.cloudbees.hudson.plugins.folder.*;
import com.cloudbees.hudson.plugins.folder.properties.*;
import org.jenkinsci.plugins.plaincredentials.impl.FileCredentialsImpl
import com.cloudbees.hudson.plugins.folder.properties.FolderCredentialsProvider.FolderCredentialsProperty;
import com.cloudbees.plugins.credentials.impl.*;
import com.cloudbees.plugins.credentials.*;
import com.cloudbees.plugins.credentials.domains.*;
import java.io.File
import groovy.io.FileType
import java.text.SimpleDateFormat
import groovy.json.JsonOutput;
import groovy.json.JsonBuilder;

class JenkinsMethods implements Serializable {

    // Uncategorized Variables
    def v_jsonObject
    def v_workspace
    def v_configJSONObject
    def v_gcpProjectId
    def v_gcpBucket
    def v_gcpBucketAppDep
    def v_gcpCredentialId
    def v_environment
    def v_clusterName   
    def v_clusterRegion 
    def v_clusterProject
    def v_kubectlProxy  
    def v_salocation
    def v_cmek_project
    def v_gcpBuildServiceAccount
    def v_gkeKeyFile
    def v_stage3KeyFile
    def v_kubeConfig
    def v_network
    def v_dockerconf
    def v_region
    def v_projectEnv
    def v_envSuffix

    def initializeEnvironment(def script="", def v_envFolder, def v_team="") {
       
        // Intialize GCP variables
        
            v_gcpProjectId           = v_configJSONObject.gcp."${v_envFolder}"["project_id"]            
            v_gcpBucket              = v_configJSONObject.gcp."${v_envFolder}"["storage_bucket"]
            v_gcpBucketAppDep        = v_configJSONObject.gcp."${v_envFolder}"["storage_bucket_app_dependencies"]
            v_gcpCredentialId        = v_configJSONObject.gcp."${v_envFolder}"["credential_id"]
            v_environment            = v_configJSONObject.gcp."${v_envFolder}"["environment"]
            v_clusterName            = v_configJSONObject.gcp."${v_envFolder}"["clusterName"]
            v_clusterRegion          = v_configJSONObject.gcp."${v_envFolder}"["clusterRegion"]
            v_clusterProject         = v_configJSONObject.gcp."${v_envFolder}"["clusterProject"]
            v_kubectlProxy           = v_configJSONObject.gcp."${v_envFolder}"["kubectlProxy"]
            v_envSuffix              = v_configJSONObject.gcp."${v_envFolder}"["envSuffix"]
            v_region                 = v_configJSONObject.gcp."${v_envFolder}"["region"]
            v_salocation             = v_configJSONObject.gcp."${v_envFolder}"["image_builder_sa_file"]
            v_cmek_project           = v_configJSONObject.gcp."${v_envFolder}"["cmek_project"]
            v_gcpBuildServiceAccount = v_configJSONObject.gcp."${v_envFolder}"["gcpBuildServiceAccount"]
            v_network                = v_configJSONObject.gcp."${v_envFolder}"["network"]
            v_gkeKeyFile             = v_configJSONObject.file_path["gke_service_account_json_file"]
            v_stage3KeyFile          = v_configJSONObject.file_path["image_builder_service_account_json_file"]
            v_kubeConfig             = v_configJSONObject["kubeconfig_path"]
            v_dockerconf             = v_configJSONObject["dockerconf_path"]
    }

    def createFileJSONObject(def script, def v_fileName, def v_returnPojo) {
        if(script.fileExists(v_fileName)) 
        {
            v_jsonObject = script.readJSON file: "${v_fileName}", returnPojo: "${v_returnPojo}"
        }
        else
        {
            script.error "File \'$v_fileName\' does not exist!"
        }
        
        return v_jsonObject
    }

    def preDeployment (def script, def v_folder) {

        def dir = v_folder.toString().split("/").last()

        switch(dir) {
            case "k8":
                script.sh "gsutil cp ${v_gcpBucket}/runtime-gke/runtime-gke.json ."
                if(script.fileExists(v_kubeConfig)) { script.sh "rm -f ${v_kubeConfig}" }
                if(script.fileExists("${v_dockerconf}/config.json")) { script.sh "rm -f ${v_dockerconf}/config.json" }
                script.sh "mkdir -p ${v_workspace}/Terraform/Environments/${v_environment}/configs/GKE_Namespace/Certs"
                script.sh "mkdir -p ${v_workspace}/Terraform/Environments/${v_environment}/configs/GKE_Namespace/Secrets"
                script.sh "gsutil -m cp -r ${v_gcpBucketAppDep}/certs/env/gcp/* ./Terraform/Environments/${v_environment}/configs/GKE_Namespace/Certs/"
                script.sh "gsutil -m cp -r ${v_gcpBucketAppDep}/secrets/env/gcp/* ./Terraform/Environments/${v_environment}/configs/GKE_Namespace/Secrets/"             
                generateKubectlInfo(script, v_network)
                generateDockerconf(script)
                updateGkeConfig(script)
                break;
            case "cluster":
                script.sh "mkdir -p ${v_workspace}/Terraform/Environments/${v_environment}/configs/Proxy_config/Certs"
                script.sh "gsutil cp ${v_gcpBucket}/proxy_certs/* ${v_workspace}/Terraform/Environments/${v_environment}/configs/Proxy_config/Certs"
                break;
            default:
                script.echo "No steps found for given folder, proceed with standard path" 
                break;  
        }
    }


    def postDeployment (def script, def module, def operation) {

        def dir = module.toString().split("/").last()

        switch(dir) {
            case "k8":
                workloadIdentityBind(script,dir,operation) 
                PgAuditCreate(script,operation)
            default:
                break;
        }
    }

    def terraformInit(def script, def folder) {

        script.echo "Initializing Terraform"
        script.sh """
                   cd Terraform/Environments/${v_environment}/${folder}/; 
                   if [[ ! -z "${v_gcpProjectId}" ]]; then
                     export GOOGLE_PROJECT=${v_gcpProjectId}; 
                   fi
                   if [[ -f "${v_salocation}" ]]; then
                     export GOOGLE_CREDENTIALS=${v_salocation}; 
                   fi
                   /bin/terraform init -get-plugins=false"""

    }


    def terraformPlan(def script, def v_map) {

        def planOutput

    // Create plan
        script.echo "Creating Terraform Plan"

        try {
            if(v_map.default_vars){
                planOutput = script.sh (
                            script: """export GOOGLE_PROJECT="${v_gcpProjectId}" ;
                                       if [[ ! -z "${v_salocation}" ]]; then
                                         export GOOGLE_CREDENTIALS=${v_salocation}; 
                                       fi
                                       cd Terraform/Environments/${v_environment}/${v_map.folder}/; 
                                       /bin/terraform plan -out=tfplan -var-file=./${v_map.tfvars.toLowerCase()}.tfvars -var-file=../${v_map.default_vars}.tfvars -compact-warnings -parallelism=5""",
                            returnStdout: true
                        ).trim()
            }else{
                planOutput = script.sh (
                            script: """export GOOGLE_PROJECT="${v_gcpProjectId}" ;
                                       if [[ ! -z "${v_salocation}" ]]; then
                                         export GOOGLE_CREDENTIALS=${v_salocation}; 
                                       fi
                                       cd Terraform/Environments/${v_environment}/${v_map.folder}/; 
                                       /bin/terraform plan -out=tfplan -var-file=./${v_map.tfvars.toLowerCase()}.tfvars -compact-warnings -parallelism=5""",
                            returnStdout: true
                        ).trim()
            }
        } catch (Exception e) {
            script.error "Exception occurred. Below are the details\n\n${e}"
            script.currentBuild.result = 'FAILED'
        } 

        script.echo "Plan Output ${planOutput}"
    }

  def terraformCreate(def script, def v_map) {

        try {
            script.echo "Applying Terraform Plan"
            script.sh """export GOOGLE_PROJECT="${v_gcpProjectId}" ;
                         if [[ ! -z "${v_salocation}" ]]; then
                           export GOOGLE_CREDENTIALS=${v_salocation}; 
                         fi
                         cd Terraform/Environments/${v_environment}/${v_map.folder}/;                          
                         /bin/terraform apply -compact-warnings -input=false -parallelism=5 tfplan"""                        
        } catch (Exception e) {
            script.error "Exception occurred. Below are the details\n\n${e}"
            script.currentBuild.result = 'FAILED'
        }
    }   


    def terraformDemise(def script, def v_map) {

         try {
            script.echo "Destroying Terraform Managed Infrastructure"
            if(v_map.default_vars){
                script.sh """export GOOGLE_PROJECT="${v_gcpProjectId}" ;
                             if [[ ! -z "${v_salocation}" ]]; then
                               export GOOGLE_CREDENTIALS=${v_salocation}; 
                             fi
                            cd Terraform/Environments/${v_environment}/${v_map.folder}/; 
                            /bin/terraform destroy -var-file=./${v_map.tfvars.toLowerCase()}.tfvars -var-file=../${v_map.default_vars}.tfvars -compact-warnings -auto-approve -parallelism=5
                            """                           
            }else{
                script.sh """export GOOGLE_PROJECT="${v_gcpProjectId}" ;
                             if [[ ! -z "${v_salocation}" ]]; then
                               export GOOGLE_CREDENTIALS=${v_salocation}; 
                             fi
                             cd Terraform/Environments/${v_environment}/${v_map.folder}/; /bin/terraform destroy -var-file=./${v_map.tfvars.toLowerCase()}.tfvars -compact-warnings -auto-approve -parallelism=5
                             """                           
            }
        } catch (Exception e) {
            script.error "Exception occurred. Below are the details\n\n${e}"
            script.currentBuild.result = 'FAILED'
        }
    }


    def rotateMachines(def script, def selectedModule, def network){
        switch(selectedModule) {
            case "Kubectl Proxy":
                def proxyKubectl = script.sh (
                            script: """gcloud compute instance-groups managed list --format 'value(NAME)' --filter="NAME:gke-kubectl-${network}" """ ,
                            returnStdout: true
                        ).trim()
                if (proxyKubectl?.trim()) {                    
                    script.sh "gcloud compute instance-groups managed rolling-action replace ${proxyKubectl}  --region=asia-east2 --replacement-method=substitute --max-surge=3 --max-unavailable=0"    
                }       
                else{
                    throw new Exception("No proxy found with prefix name: gke-kubectl-${network}")
                }
                break
            case "GKE Connection Proxy":
                def proxyConcat = script.sh (
                            script: """gcloud compute instance-groups managed list --format 'value(NAME)' --filter="NAME:gke-proxy-${network}" """ ,
                            returnStdout: true
                        ).trim()
                if (proxyConcat?.trim()) {
                    script.sh "gcloud compute instance-groups managed rolling-action replace ${proxyConcat}  --region=asia-east2 --replacement-method=substitute --max-surge=3 --max-unavailable=0"   
                }       
                else{
                    throw new Exception("No proxy found with prefix name: gke-proxy-${network}")
                }
                break
            case "Cloud SQL Proxy":
                def proxyConcat = script.sh (
                            script: """gcloud compute instance-groups managed list --format 'value(NAME)' --filter="NAME:gce-cloudsqlproxy" | grep ${network}""" ,
                            returnStdout: true
                        ).trim()
                if (proxyConcat?.trim()) {
                    script.sh "gcloud compute instance-groups managed rolling-action replace ${proxyConcat}  --region=asia-east2 --replacement-method=substitute --max-surge=3 --max-unavailable=0"   
                }       
                else{
                    throw new Exception("No proxy found with prefix name: gke-proxy-${network}")
                }
                break
            case "All Proxies":
                def proxyAll = script.sh (
                            script: """gcloud compute instance-groups managed list --format 'value(NAME)' --filter='NAME:gke-proxy-${network} OR NAME:gke-kubectl-${network} OR NAME:gce-cloudsqlproxy OR NAME:hsbc-core-proxy OR NAME:hsbc-core-scanner'""" ,
                            returnStdout: true
                        ).trim()
                if (proxyAll?.trim()) {
                    String [] items = proxyAll.replaceAll("\n", ",").split("\\s*,\\s*");
                    for(item in Arrays.asList(items)){                      
                        script.sh "gcloud compute instance-groups managed rolling-action replace ${item}  --region=asia-east2 --replacement-method=substitute --max-surge=3 --max-unavailable=0"
                } 
                }       
                else{
                    throw new Exception("No proxy found with prefix names: ${network}")
                }
                break           
            default:
                script.echo "No proxy module selected to roation!"
                break

        }
    
    }

    def workloadIdentityBind (def script, def module, def step) {

        def v_service_account = script.sh (
                        script: "cd Terraform/Environments/${v_environment}/${module}/; /bin/terraform output service_account_details",
                            returnStdout: true
                        ).trim()
        
        String [] clean_list = v_service_account.replaceAll("\\s","").replaceAll("\\[","").replaceAll("\\]","").split("\\s*,\\s*");

         switch(step) {
            case "Test Deployment":

                script.echo "Created workloadIdentity Binding for serviceAccounts: ${clean_list}\""

            break
            case "Create enviroment":

                for(item in Arrays.asList(clean_list)){ 
                                    script.sh "gcloud iam service-accounts add-iam-policy-binding --role=\"roles/iam.workloadIdentityUser\" --member=\"serviceAccount:${v_gcpProjectId}.svc.id.goog[${item}]\" runtime-gke@${v_gcpProjectId}.iam.gserviceaccount.com"
                }

            break
            default:
                script.echo "No additional steps required"
            break
        }
    }

    def PgAuditCreate(def script, def step) {

        def env = v_environment.toLowerCase()

        script.echo "Replace instance string in Debug pod"
        script.sh "chmod 755 ./Scripts/bash/pgAudit.sh; ./Scripts/bash/pgAudit.sh ${v_gcpProjectId} ${v_region} ${env}"

        def v_kubeCtlLib = script.sh (
                script: """gcloud compute addresses list --filter="NAME:gke-kubectl-${v_network}" --format 'value(ADDRESS)'""" ,
                returnStdout: true
        ).trim()

        switch(step){
            case "Create enviroment":
                script.sh "export HTTPS_PROXY=\"${v_kubeCtlLib}:3128\"; export KUBECONFIG=${v_kubeConfig}; kubectl apply -f ./Scripts/yamls/DebugPod.yaml"
            break
            case "Test Deployment":
                script.sh "export HTTPS_PROXY=\"${v_kubeCtlLib}:3128\"; export KUBECONFIG=${v_kubeConfig}; kubectl get namespaces"
                script.sh "sed -n -e '/# <DEPLOYMENT_SECTION>/,\$p' ./Scripts/yamls/DebugPod.yaml"
            break
            default:
                script.echo "No additional steps required"
            break
        }
    }

    
    def generateKubectlInfo(def script, def v_network){

        script.sh """echo 'GENERATING KUBECONFIG WITH PROXY-URL'"""

        def v_clusterName = script.sh (
                script: """gcloud container clusters list --filter="NAME:gke-t2-${v_network}" --region=asia-east2 --format 'value(NAME)'""" ,
                returnStdout: true
        ).trim()

        def v_kubeCtlLib = script.sh (
                script: """gcloud compute addresses list --filter="NAME:gke-kubectl-${v_network}" --format 'value(ADDRESS)'""" ,
                returnStdout: true
        ).trim()

        script.sh """export KUBECONFIG=${v_kubeConfig}; 
                     gcloud container clusters get-credentials ${v_clusterName} --region=asia-east2"""

        List<String> kubeconfigLines = script.readFile(v_kubeConfig).split("\n")
        List<String> updatedKubeconfigLines = new ArrayList<>()
        kubeconfigLines.each {
            updatedKubeconfigLines.add(it)
            if (it.contains("server: https")){
                String serverAddressLine = it
                int leadingSpacesLength = 0
                while(serverAddressLine[leadingSpacesLength] == " "){
                    leadingSpacesLength++
                }
                String proxyUrlLine = (" " * leadingSpacesLength) + "proxy-url: http://${v_kubeCtlLib}:3128"
                updatedKubeconfigLines.add(proxyUrlLine)
            }
        }
        script.writeFile(file: v_kubeConfig, text: updatedKubeconfigLines.join("\n"))
    }

    def generateDockerconf(def script){

        script.sh """echo 'GENERATING Docker Credentials'"""

        script.sh "export DOCKER_CONFIG=${v_dockerconf}; cat ./runtime-gke.json | docker login --username=_json_key --password-stdin https://gcr.io"
    }

    def updateGkeConfig(def script){

        script.echo "Export sql outputs to accuire dababase details"

        terraformInit(script, "cloud_sql")

        script.sh """export GOOGLE_PROJECT="${v_gcpProjectId}"; cd Terraform/Environments/${v_environment}/cloud_sql/; /bin/terraform output --json > ../../../../Scripts/python/sql_output.json"""

        script.echo "Update Gke config script with data from sql output file"

        script.sh "python3 ${v_workspace}/Scripts/python/create_gke_secrets.py \"${v_workspace}/Scripts/python/sql_output.json\" \"${v_workspace}/Terraform/Environments/${v_environment}/json_configs/gke_configuration.json\" "

    }
  
}
return new JenkinsMethods();
