import hudson.model.ParameterDefinition
import hudson.model.JobProperty
import groovy.json.*
import jenkins.model.*
import java.io.*
import java.io.File
import groovy.io.FileType
def AGENT = "unity-dev-jenkins-agent"

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    
    parameters {
        string(
            name: 'deployment_metadata',
            description: 'JSON metadata containing deployment information',
            defaultValue: '{"unityReleaseVersion":"PROD-v202507171108","gitCommitHash":"11ae31423af474e1957c871761efbfcbaffbcc93","deploymentArtifactURL":"https://nexus302.systems.uk.hsbc:8081/nexus/repository/raw-alm-dev_n3p/com/mss/unity/deployment/gke-deployment-PROD-v202507171108.zip","deploymentArtifactChecksum":"d50b0898ad0c30251e52763c0d1a3ce9","env":"prod","projectName":"core","extendedUrl":""}'
        )
        string(
            name: 'cr_number',
            description: 'Change Request number (prefixed with CHG)',
            defaultValue: 'CHG0000000'
        )
    }

    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    echo "Validating input parameters..."

                    // Validate deployment_metadata is valid JSON
                    try {
                        def jsonSlurper = new JsonSlurper()
                        def metadata = jsonSlurper.parseText(params.deployment_metadata)
                        echo "✓ deployment_metadata is valid JSON"

                        // Check if required fields exist
                        if (!metadata.unityReleaseVersion) {
                            error("deployment_metadata missing required field: unityReleaseVersion")
                        }
                        if (!metadata.gitCommitHash) {
                            error("deployment_metadata missing required field: gitCommitHash")
                        }
                        echo "✓ Required fields present in deployment_metadata"

                    } catch (Exception e) {
                        error("Invalid JSON in deployment_metadata parameter: ${e.getMessage()}")
                    }

                    // Validate cr_number is prefixed with CHG
                    if (!params.cr_number.startsWith('CHG')) {
                        error("cr_number must be prefixed with 'CHG'. Current value: ${params.cr_number}")
                    }
                    echo "✓ cr_number is properly prefixed with CHG"

                    echo "All parameter validations passed successfully"
                }
            }
        }

        stage('Parse Deployment Metadata') {
            steps {
                script {
                    echo "Parsing deployment metadata..."
                    def jsonSlurper = new JsonSlurper()
                    def metadata = jsonSlurper.parseText(params.deployment_metadata)

                    // Extract required values
                    env.unityReleaseVersion = metadata.unityReleaseVersion
                    env.gitCommitHash = metadata.gitCommitHash

                    echo "Unity Release Version: ${env.unityReleaseVersion}"
                    echo "Git Commit Hash: ${env.gitCommitHash}"
                    echo "CR Number: ${params.cr_number}"
                }
            }
        }
        
        stage('Clone GKE Deployment Repository') {
            steps {
                script {
                    echo "Cloning gke-deployment repository with commit: ${env.gitCommitHash}"
                    sh """
                        rm -rf gke-deployment
                        git clone https://stash.hk.hsbc/scm/unity-i15n-poc/gke-deployment.git
                        cd gke-deployment
                        git checkout ${env.gitCommitHash}
                    """
                }
            }
        }
        
        stage('Execute ICE Artifact Helper') {
            steps {
                script {
                    withCredentials([string(credentialsId: 'ICE_AUTH_TOKEN', variable: 'ICE_AUTH_TOKEN')]) {
                        echo "Executing ICE artifact helper script..."
                        sh """
                            export _ice_credential_=\${ICE_AUTH_TOKEN}
                            cd gke-deployment 
                            wget https://stash.hk.hsbc/projects/UNITY-I15N-POC/repos/ice/raw/helper/update-lttd-by-api.bash?at=refs%2Fheads%2Fmaster -O update-lttd-by-api.bash
                            bash update-lttd-by-api.bash ${env.unityReleaseVersion} ${params.cr_number}
                        """
                    }
                }
            }
        }
    }
   
    post {
        unsuccessful {
            mail bcc: '',
                body: "[Jenkins Job]:${env.JOB_NAME}\n[url]:${env.JOB_URL}\n[status]:[failed]\nRegards DevOps Team",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "[Jenkins][ICE Artifact Helper][${env.JOB_NAME}][failed] Job status notification",
                to: '<EMAIL>,<EMAIL>'
        }
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}
