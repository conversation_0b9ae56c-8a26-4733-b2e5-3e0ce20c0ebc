import hudson.model.ParameterDefinition
import hudson.model.JobProperty
import groovy.json.*
import jenkins.model.*
import java.io.*
import java.io.File
import groovy.io.FileType
def AGENT = "unity-dev-jenkins-agent"

pipeline {    
    agent { label env.AGENT_LABEL ?: AGENT }
    
    parameters {
        string(
            name: 'deployment_metadata',
            description: 'JSON metadata containing deployment information',
            defaultValue: '{"unityReleaseVersion":"PROD-v202507171108","gitCommitHash":"11ae31423af474e1957c871761efbfcbaffbcc93","deploymentArtifactURL":"https://nexus302.systems.uk.hsbc:8081/nexus/repository/raw-alm-dev_n3p/com/mss/unity/deployment/gke-deployment-PROD-v202507171108.zip","deploymentArtifactChecksum":"d50b0898ad0c30251e52763c0d1a3ce9","env":"prod","projectName":"core","extendedUrl":""}'
        )
        string(
            name: 'cr_number',
            description: 'Change Request number(s) - single CR (CHG0000000) or CSV format (CHG0000001,CHG0000002,CHG0000003)',
            defaultValue: 'CHG0000000'
        )
    }

    stages {
        stage('Validate Parameters') {
            steps {
                script {
                    try {
                        echo "Validating input parameters..."

                        // Validate deployment_metadata is valid JSON
                        try {
                            def jsonSlurper = new JsonSlurper()
                            def metadata = jsonSlurper.parseText(params.deployment_metadata)
                            echo "✓ deployment_metadata is valid JSON"

                            // Check if required fields exist
                            if (!metadata.unityReleaseVersion) {
                                error("deployment_metadata missing required field: unityReleaseVersion")
                            }
                            if (!metadata.gitCommitHash) {
                                error("deployment_metadata missing required field: gitCommitHash")
                            }
                            echo "✓ Required fields present in deployment_metadata"

                        } catch (Exception e) {
                            error("Invalid JSON in deployment_metadata parameter: ${e.getMessage()}")
                        }

                        // Validate cr_number(s) - handle both single and CSV format
                        def crNumbers = params.cr_number.split(',').collect { it.trim() }
                        crNumbers.each { crNumber ->
                            if (!crNumber.startsWith('CHG')) {
                                error("All CR numbers must be prefixed with 'CHG'. Invalid value: ${crNumber}")
                            }
                        }
                        echo "✓ All CR numbers are properly prefixed with CHG: ${crNumbers.join(', ')}"

                        // Store parsed CR numbers for later use
                        env.CR_NUMBERS = crNumbers.join(',')

                        echo "All parameter validations passed successfully"

                    } catch (Exception e) {
                        error("Parameter validation failed: ${e.getMessage()}")
                    }
                }
            }
        }

        stage('Parse Deployment Metadata') {
            steps {
                script {
                    try {
                        echo "Parsing deployment metadata..."
                        def jsonSlurper = new JsonSlurper()
                        def metadata = jsonSlurper.parseText(params.deployment_metadata)

                        // Extract required values
                        env.unityReleaseVersion = metadata.unityReleaseVersion
                        env.gitCommitHash = metadata.gitCommitHash

                        echo "Unity Release Version: ${env.unityReleaseVersion}"
                        echo "Git Commit Hash: ${env.gitCommitHash}"
                        echo "CR Numbers: ${env.CR_NUMBERS}"

                    } catch (Exception e) {
                        error("Failed to parse deployment metadata: ${e.getMessage()}")
                    }
                }
            }
        }
        
        stage('Clone GKE Deployment Repository') {
            steps {
                script {
                    try {
                        echo "Cloning gke-deployment repository with commit: ${env.gitCommitHash}"
                        sh """
                            rm -rf gke-deployment
                            mkdir gke-deployment
                            cd gke-deployment
                            git init
                            git remote add origin https://stash.hk.hsbc/scm/unity-i15n-poc/gke-deployment.git
                            git fetch --depth 1 origin ${env.gitCommitHash}:refs/remotes/origin/target-commit
                            git checkout -b target-branch origin/target-commit
                        """
                        echo "✓ Successfully cloned repository at commit: ${env.gitCommitHash}"

                    } catch (Exception e) {
                        error("Failed to clone gke-deployment repository: ${e.getMessage()}")
                    }
                }
            }
        }
        
        stage('Execute ICE Artifact Helper') {
            steps {
                script {
                    try {
                        def crNumbers = env.CR_NUMBERS.split(',')
                        echo "Processing ${crNumbers.size()} CR number(s): ${crNumbers.join(', ')}"

                        withCredentials([string(credentialsId: 'ICE_AUTH_TOKEN', variable: 'ICE_AUTH_TOKEN')]) {
                            // Download the script once
                            echo "Downloading ICE artifact helper script..."
                            sh """
                                export _ice_credential_=\${ICE_AUTH_TOKEN}
                                cd gke-deployment
                                wget https://stash.hk.hsbc/projects/UNITY-I15N-POC/repos/ice/raw/helper/update-lttd-by-api.bash?at=refs%2Fheads%2Fmaster -O update-lttd-by-api.bash
                                chmod +x update-lttd-by-api.bash
                            """

                            // Execute script for each CR number
                            crNumbers.eachWithIndex { crNumber, index ->
                                echo "Processing CR ${index + 1}/${crNumbers.size()}: ${crNumber}"
                                try {
                                    sh """
                                        export _ice_credential_=\${ICE_AUTH_TOKEN}
                                        cd gke-deployment
                                        bash update-lttd-by-api.bash ${env.unityReleaseVersion} ${crNumber}
                                    """
                                    echo "✓ Successfully processed CR: ${crNumber}"
                                } catch (Exception crError) {
                                    echo "✗ Failed to process CR: ${crNumber} - ${crError.getMessage()}"
                                    // Continue with next CR instead of failing entire pipeline
                                    currentBuild.result = 'UNSTABLE'
                                }
                            }
                        }

                        echo "✓ ICE artifact helper script execution completed for all CR numbers"

                    } catch (Exception e) {
                        error("Failed to execute ICE artifact helper script: ${e.getMessage()}")
                    }
                }
            }
        }
    }
   
    post {
        unsuccessful {
            mail bcc: '',
                body: "[Jenkins Job]:${env.JOB_NAME}\n[url]:${env.JOB_URL}\n[status]:[failed]\nRegards DevOps Team",
                cc: '',
                from: 'unity_cloud_devops@noreply',
                replyTo: '',
                subject: "[Jenkins][ICE Artifact Helper][${env.JOB_NAME}][failed] Job status notification",
                to: '<EMAIL>,<EMAIL>'
        }
        cleanup {
            script {
                cleanWs()
            }
        }
    }
}
