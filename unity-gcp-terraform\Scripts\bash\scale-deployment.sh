#!/bin/bash

function print_usage(){
  echo bash $(basename $0) options
  echo options:
  echo --replicas
  echo --vpc
  echo [--namespace=ns-namespace1-apps,ns-namespace2-apps,...]
  echo bash $(basename $0)--vpc=vpc3 --replicas=0 --namespace=ns-uniconn-dev-apps,ns-tpc-uat-apps
}

function options() {
while [ "$1" != "" ];
do
  _param_=`printf "%s\n" $1 | awk -F= '{print $1}'`
  _value_=`printf "%s\n" $1 | sed 's/^[^=]*=//g'`
  if [[ $_value_ == $_param_ ]]; then
    shift
    _value_=$1
  fi
  case ${_param_} in
  --namespace)
    namespace="$(echo ${_value_}|sed 's|,|\||g')"
    ;;
  --exclude-namespace)
    exclude_namespace="$(echo ${_value_}|sed 's|,|\||g')"
    ;;
  --replicas)
    replicas="${_value_}"
    ;;
  --vpc)
    vpc="${_value_}"
    ;;
  --setup-kubeconfig)
    setup_kubeconfig="${_value_}"
    ;;      
  --help)
    print_usage
    exit
    ;;
  esac
  shift;
done
}

function setup_kubeconfig(){
  set -x 
  export KUBECONFIG=${root}/kubeconfig.conf
  gcloud container clusters list --format='value(name,zone)' | grep ${vpc} | while read cluster region ; do 
  gcloud container clusters get-credentials ${cluster} --region=${region}
  done
  export HTTPS_PROXY=$(gcloud compute addresses list --filter="NAME:gke-kubectl-${vpc}" --format='value(ADDRESS)'):3128
  set +x
}

function constructor(){
root=$(readlink -f $(dirname $0))
setup_kubeconfig=1
namespace=".*"
export no_proxy=${no_proxy},systems.uk.hsbc
}

function set_pause_keda_scaledobject(){  
  local _namespace_=$1
  local _pause_=$2
  local _false_=true  
  [[ ${_pause_} == "true" ]] && _false_=false
  #SKIP cases where KEDA is not fully setup
  if [[ $(kubectl get triggerauthentications.keda.sh -n ${_namespace_} | wc -l) -gt 0 ]]; then 
    kubectl get scaledobject -n ${_namespace_} -o json | jq -r ".items[] | select(.metadata.annotations[\"autoscaling.keda.sh/paused\"] == \"${_false_}\") | .metadata.name" | xargs -P5 -I{} kubectl annotate scaledobject {} autoscaling.keda.sh/paused="${_pause_}" --overwrite  -n ${_namespace_} ;
  fi 
}

function poll_kafka_status(){  
  local _namespace_=$1
  local _timer_=${2:-300}
  local _timer_count_=0
  if kubectl get statefulset/kafka -n ${_namespace_} &> /dev/null ; then 
    while [[ ${readiness_count:-0} -lt 3 && ${_timer_count_} -lt ${_timer_} ]]; do       
      readiness_count=$(kubectl get statefulset/kafka -o jsonpath='{.status.readyReplicas}' -n ${_namespace_}) 
      _timer_count_=$((++_timer_count_))
      echo kafka pod count: ${readiness_count}
      sleep 1               
    done
  fi
}
###############################################
# main
###############################################
constructor
options "$@"

echo namespace:filter:${namespace}

[[ ${setup_kubeconfig} -eq 1 ]] && setup_kubeconfig
if [[ -z "${exclude_namespace}" ]]; then 
  kubectl get namespace --all-namespaces -ojson | jq -r '.items | map(select(.metadata.name | test("ns-.*apps") or test("keda"))| .metadata.name )' | \
  jq -r '.[]' | egrep "${namespace}" | while read namespace ; 
  do
    if [[ ${replicas} -eq 1 ]]; then
      app_env=$(echo $namespace | cut -f3 -d-)             
      if kubectl get deployment/keda-operator &> /dev/null ; then 
        kubectl scale deployment/keda-operator --replicas=2 || true 
      fi
      kubectl scale statefulset --replicas=3 --namespace=$namespace ctrl || true
      kubectl scale statefulset --replicas=3 --namespace=$namespace kafka || true
      kubectl scale statefulset --replicas=1 --namespace=$namespace schema-registry || true
      poll_kafka_status $namespace
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=core || true
      set_pause_keda_scaledobject $namespace false      
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=app-core || true      
      #use auto scaler to start dev | sit 
      #[[ $(echo ${app_env} | egrep -v "dev|sit") ]] && kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-runtime || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=app-support || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=app-runtime || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category!=app-core,service-category!=app-runtime,service-category!=app-support,service-category!=core || true
      dbpod=`kubectl get deployment --namespace=$namespace -ojson |  jq -r '.items | map(select(.spec.template.spec.containers[].image | test(".*gce-proxy.*"))| .metadata.name )'|jq -r '.[]'|wc -l`
      timeToSleep=`jq -n $dbpod*0.34`
      sleep $timeToSleep
    else
      if kubectl get deployment/keda-operator &> /dev/null ; then 
        kubectl scale deployment/keda-operator --replicas=0 -n $namespace|| true
      fi      
      set_pause_keda_scaledobject $namespace true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-support || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-runtime || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-core || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=core || true     
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category!=app-core,service-category!=app-runtime,service-category!=app-support,service-category!=core || true
      kubectl scale statefulset --replicas=${replicas} --namespace=$namespace  -l service-category=app-support || true
    fi
  done 
else
  kubectl get namespace --all-namespaces -ojson | jq -r '.items | map(select(.metadata.name | test("ns-.*apps") or test("keda"))| .metadata.name )' | \
  jq -r '.[]' | egrep "${namespace}" | egrep -v "${exclude_namespace}" | while read namespace ; 
  do 
    echo namespace:${namespace}
    if [[ ${replicas} -eq 1 ]]; then
      app_env=$(echo $namespace | cut -f3 -d-)
      if kubectl get deployment/keda-operator &> /dev/null ; then 
        kubectl scale deployment/keda-operator --replicas=2 || true 
      fi
      kubectl scale statefulset --replicas=3 --namespace=$namespace ctrl || true
      kubectl scale statefulset --replicas=3 --namespace=$namespace kafka || true
      kubectl scale statefulset --replicas=1 --namespace=$namespace schema-registry || true
      poll_kafka_status $namespace
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=core || true
      set_pause_keda_scaledobject $namespace false
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=app-core || true      
      #use auto scaler to start dev | sit 
      #[[ $(echo ${app_env} | egrep -v "dev|sit") ]] && kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-runtime || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=app-support || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category=app-runtime || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace -l service-category!=app-core,service-category!=app-runtime,service-category!=app-support,service-category!=core || true
      dbpod=`kubectl get deployment --namespace=$namespace -ojson |  jq -r '.items | map(select(.spec.template.spec.containers[].image | test(".*gce-proxy.*"))| .metadata.name )'|jq -r '.[]'|wc -l`
      timeToSleep=`jq -n $dbpod*0.34`
      sleep $timeToSleep
    else
      if kubectl get deployment/keda-operator &> /dev/null ; then 
        kubectl scale deployment/keda-operator --replicas=0 -n $namespace|| true
      fi
      set_pause_keda_scaledobject $namespace true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-support || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-runtime || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=app-core || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category=core || true
      kubectl scale deployment --replicas=${replicas} --namespace=$namespace  -l service-category!=app-core,service-category!=app-runtime,service-category!=app-support,service-category!=core || true
      kubectl scale statefulset --replicas=${replicas} --namespace=$namespace  -l service-category=app-support || true
    fi
  done 
fi
