{% set shared_vpc_project = gcp_shared_vpc[0].split('/')[1] %}
{
  "prereqs": [
    {
      "file": "gke/addons.json",
      "source": {
        "gatekeeper": {
          "read_only_root_filesystem": {
            "excluded_namespaces": [
              "support-pod"
            ]
          },
          "gcr_projects": [
            "cloudsql-docker",
            "cloud-sql-connectors"
          ],
          "allowed_image_prefixes": [
            "gcr.io/{{gcp_project}}/",
            "asia-east2-docker.pkg.dev/{{gcp_project}}/",
            "us-docker.pkg.dev/hsbc-9087302-unity-dev/gcr.io",
            "us-docker.pkg.dev/hsbc-11465671-unity-dev/gcr.io",
            "gcr.io/hsbc-9087302-unity-dev/",
            "gcr.io/cloudsql-docker/",
            "gcr.io/cloud-sql-connectors/"
          ]
        }
      }
    },
    {
      "file": "gke/cluster.json",
      "source": {
        "name": "devcls",
        "region": "asia-east2",
        "machine_type": "e2-standard-8",
        "total_max_node_count": 9,
        "tags": [
          "fwtag-all-dev-out-alm-github",
          "fwtag-cloudsql-access",
          "fwtag-proxy-access"
        ],
        "node_pools": {
            "spot": {
                "machine_type": "e2-standard-4",
                "total_max_node_count": 20,
                "spot": true
            }
        },
        "cluster_autoscaling": {
        "autoscaling_profile": "OPTIMIZE_UTILIZATION"
        }
      }
    },
    {
      "file": "gke/rbac.json",
      "source": {
        "cluster_role_bindings": {
          "hgke-admin": [
            {
              "kind": "User",
              "name": "gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "kind": "User",
              "name": "<EMAIL>"
            }
          ]
        }
      }
    },
    {
      "file": "sot/service_account_roles.auto.tfvars.json",
      "source": {
          "service_accounts_role_bindings_version": 1,
          "service_accounts_role_bindings": {
              "gce-stage3-image-builder": [
                  {
                      "role": "roles/iam.serviceAccountUser",
                      "members": [
                          "group:gcp.{{gcp_project}}.<EMAIL>",
                          "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
                      ]
                  },
                  {
                      "role": "roles/iam.serviceAccountTokenCreator",
                      "members": [
                          "group:gcp.{{gcp_project}}.<EMAIL>"
                      ]
                  },
                  {
                      "role": "roles/iam.serviceAccountKeyAdmin",
                      "members": [
                          "group:gcp.{{gcp_project}}.<EMAIL>",
                          "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
                      ]
                  }
              ]
          }
      }   
    },     
    {
      "file": "sot/project_vars.auto.tfvars.json",
      "source": {
        "gcp_project_name": "{{gcp_project}}",
        "gcp_region": "{{gcp_project_region}}",
        "gcp_zone": "{{gcp_project_zone}}",
        "vpc_host_project": "{{shared_vpc_project}}"
      }      
    },    
    {
      "file": "sot/service_accounts.auto.tfvars.json",
      "source": {
        "service_accounts": {
          "runtime-gke": "runtime user for namespace workload",
          "gce-stage3-image-builder": "Automation Service account for Deployment - {{gcp_project}}"
        }
      }
    },
    {
      "file": "sot/shared_vpc_users.auto.tfvars.json",
      "source": {
        "shared_vpc_users": {
            {% for vpc in gcp_shared_vpc %}      "{{vpc}}": [
            "serviceAccount:{{gcp_project_number}}@cloudservices.gserviceaccount.com",
            "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
          ]{{ "," if not loop.last else "" }}
          {% endfor %}
        }
      }
    },
    {
      "file": "sot/project_apis.auto.tfvars.json",
      "source": {
        "enabled_api_services": [
          "servicenetworking.googleapis.com",
          "sqladmin.googleapis.com",
          "cloudresourcemanager.googleapis.com",
          "container.googleapis.com"
        ]
      }
    },
    {
      "file": "sot/project_roles.auto.tfvars.json",
      "source": {
        "standard_role_bindings": {
          "roles/compute.networkUser": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/container.viewer": [            
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "principal": "serviceAccount:<EMAIL>"
            }
          ],
          "roles/iam.serviceAccountAdmin": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            }
          ],
          "roles/logging.admin": [
            {
                "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            },
            {
                "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            }           
          ],
          "roles/logging.viewer": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            },
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            }
          ],
          "roles/monitoring.metricWriter": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/monitoring.viewer": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "principal": "serviceAccount:runtime-gke@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/essentialcontacts.admin": [
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            }
          ],
          "roles/essentialcontacts.viewer": [
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            }
          ],
          "roles/cloudsql.admin": [
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            },
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/cloudsql.client": [
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            },
            {
              "principal": "serviceAccount:runtime-gke@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/cloudsql.instanceUser": [
            {
              "principal": "serviceAccount:runtime-gke@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/cloudsql.instanceAdmin": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],          
          "roles/storage.admin": [
            {
                "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            }
          ],
          "roles/storage.objectAdmin": [
              {
                  "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
              },
              {
                  "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
              }
          ],
          "roles/storage.objectViewer": [
              {
                  "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
              }
          ],
          "roles/logging.configWriter": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            }
          ],
          "roles/container.serviceAgent": [
           {
              "principal": "serviceAccount:service-{{gcp_project_number}}@container-engine-robot.iam.gserviceaccount.com"
           }
          ],
          "roles/monitoring.editor": [
            {
              "principal": "serviceAccount:gce-stage3-image-builder@{{gcp_project}}.iam.gserviceaccount.com"
            },
            {
              "principal": "group:gcp.{{gcp_project}}.<EMAIL>"
            }
          ]
        }
      }
    }
  ]
}
