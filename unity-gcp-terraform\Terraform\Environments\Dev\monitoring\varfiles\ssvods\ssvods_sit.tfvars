key               = "gcp"
unit              = "1"
label_value_type  = "STRING"
label_description = "metric for gcp instance"
api_key           = "ssvods-sit-key"
team_name         = "ssvods-sit"

channel_recipients = {
    display_name  = "ssvods_Sit_Monitoring_recipients" 
    type = "webhook_basicauth"
    username = "f0851c183aac47568707a0d29697ab1f"
    url = "https://if-api.hsbc.co.uk/api/alertapi-baseauth?@aboutSource=GCP"
    password = "2e43F52B6701457fAf6431afA3F26Ad0"
}

//TODO: fill in
logs                = {
  custom_api_err = {
    name                = "ssvods_Sit_Custom_API_internal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR500\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    labels              = []
    label_extractors    = {}
  },
  pipeline_app_err = {
    name                = "ssvods_Sit_Pipeline_application_fatal_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"UNY2ERR999\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "Unexpected error has caused the Unity 2.0 ETL pipelines to fail - container would be restarted"
    labels              = []
    label_extractors    = {}
  },
  pub_sub_app_err = {
    name                = "ssvods_Sit_Pipeline_application_pub_sub_error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"Error while publishing PubSub message\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "PubSub error happened for consumer adaptor"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_error = {
    name                = "ssvods_Sit_Pipeline_Holding_Error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer Error transforming message\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer error"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_error = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS core-holding-eod-v3 pipeline processing error\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3 Pipeline Processing error"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_gce = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_GCE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site GCE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site GCE"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_gcuk = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_GCUK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site GCUK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site GCUK"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_ae = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_AE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site AE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site AE"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_au = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site AU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site AU"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_bh = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_BH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site BH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site BH"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_hk = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site HK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site HK"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_id = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_ID"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site ID\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site ID"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_in = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_IN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site IN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site IN"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_jp = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_JP"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site JP\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site JP"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_my = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_MY"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site MY\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site MY"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_nz = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_NZ"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site NZ\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site NZ"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_ph = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site PH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site PH"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_sg = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_SG"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site SG\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site SG"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_th = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_TH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site TH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site TH"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_tw = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_TW"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site TW\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site TW"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_vn = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_VN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site VN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site VN"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_om = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_OM"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site OM\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site OM"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_pipeline_volume_lk = {
    name                = "ssvods_Sit_Pipeline_Holding_Volume_LK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingV3Transformer received valid msg for site LK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingV3Transformer received valid msg for site LK"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_gce = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_GCE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site GCE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site GCE"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_gcuk = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_GCUK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site GCUK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site GCUK"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_ae = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_AE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site AE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site AE"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_au = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site AU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site AU"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_bh = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_BH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site BH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site BH"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_hk = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site HK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site HK"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_id = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_ID"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site ID\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site ID"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_in = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_IN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site IN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site IN"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_jp = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_JP"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site JP\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site JP"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_my = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_MY"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site MY\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site MY"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_nz = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_NZ"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site NZ\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site NZ"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_ph = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site PH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site PH"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_sg = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_SG"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site SG\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site SG"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_th = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_TH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site TH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site TH"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_tw = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_TW"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site TW\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site TW"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_vn = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_VN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site VN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site VN"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_om = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_OM"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site OM\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site OM"
    labels              = []
    label_extractors    = {}
  },
  ods_holding_eod_pipeline_volume_lk = {
    name                = "ssvods_Sit_Pipeline_Holding_EOD_Volume_LK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS CoreHoldingEODV3Transformer received valid msg for site LK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreHoldingEODV3Transformer received valid msg for site LK"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_error = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Error"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll Error transforming message\" and resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll error"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_ALL = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_ALL"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll- Trade total message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_AE = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_AE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site AE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll AE Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_AU = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site AU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll AU Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_BH = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_BH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site BH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll BH Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_HK = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site HK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll HK Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_ID = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_ID"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site ID\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll ID Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_JP = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_JP"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site JP\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll JP Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_MY = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_MY"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site MY\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll MY Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_NZ = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_NZ"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site NZ\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll NZ Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_PH = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site PH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll PH Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_SG = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_SG"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site SG\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll SG Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_TH = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_TH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site TH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll TH Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_TW = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_TW"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site TW\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll TW Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_VN = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_VN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site VN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll VN Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_OM = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_OM"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site OM\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll OM Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_LK = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_LK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site LK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll LK Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_BD = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_BD"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site BD\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll BD Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_all_pipeline_volume_MU = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_ALL_Volume_MU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformerAll received valid msg for site MU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformerAll MU Trade message count"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_all = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_ALL"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS CoreUdmGcsTransaction received msg for ALL"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_uk = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_UK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site UK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site UK"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_fr = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_FR"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site FR\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site FR"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_ae = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_AE"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site AE\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site AE"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_au = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_AU"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site AU\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site AU"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_bh = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_BH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site BH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site BH"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_hk = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_HK"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site HK\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site HK"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_jp = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_JP"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site JP\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site JP"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_my = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_MY"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site MY\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site MY"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_nz = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_NZ"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site NZ\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site NZ"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_ph = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_PH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site PH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site PH"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_qa = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_QA"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site QA\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site QA"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_sg = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_SG"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site SG\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site SG"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_th = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_TH"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site TH\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site TH"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_tw = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_TW"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site TW\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site TW"
    labels              = []
    label_extractors    = {}
  },
  ods_tradestatus_pipeline_volume_vn = {
    name                = "ssvods_Sit_Pipeline_Tradestatus_Volume_VN"
    filter              = "resource.type:\"k8s_container\" AND jsonPayload.message: \"ODS UdmToODSTradeStatusTransformer received valid msg for site VN\" AND resource.labels.namespace_name: \""
    metric_kind         = "DELTA"
    value_type          = "INT64"
    unit                = "1"
    display_name        = "ODS UdmToODSTradeStatusTransformer received valid msg for site VN"
    labels              = []
    label_extractors    = {}
  }
}

metrics = {
  Sql_Connections = {
    name                = "SQL Connections - Google Cloud CloudSQl PostGreSQL - Connections"
    object_name          = "sqlcon"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/connections\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_MAX"
    threshold           = 20
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_cpu_utilization = {
    name                = "SQL CPU Utilization - Google Cloud CloudSQl PostGreSQL - SQL CPU Utilization"
    object_name          = "sqlcpu"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.9
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_io_reads = {
    name                = "SQL Disk I/O Reads > 120 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Reads"
    object_name          = "sqldicread"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/read_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 120
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_io_writes = {
    name                = "SQL Disk I/O Writes > 90 /s - Google Cloud CloudSQl PostGreSQL - SQL Disk I/O Writes"
    object_name          = "sqldiscwrite"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/write_ops_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 90
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_disk_utilization = {
    name                = "SQL Disk Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Disk Utilization"
    object_name          = "sqldiscut"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/disk/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_memory_utilization = {
    name                = "SQL Memory Utilization  - Google Cloud CloudSQl PostGreSQL - SQL Memory Utilization"
    object_name          = "sqlmem"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/memory/utilization\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_PERCENT_CHANGE"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     =  ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Sql_send_byte_rate = {
    name                = "SQL Byte Rate > 80MBytes/s - Google Cloud CloudSQl PostGreSQL - SQL Byte Rate"
    object_name          = "sqlbytesrate"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/network/sent_bytes_count\" AND resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 80000000
    trigger_count       = 1
    group_by_fields     = ["resource.label.database_id"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_cpu = {
    name                = "GKE Container CPU - Google GKE Container - GKE Container CPU utilization"
    object_name          = "gkecpu"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/cpu/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
  },  
  Gke_containers_memory = {
    name                = "GKE Container Memory - Google GKE Container - GKE Container memory limit utilization"
    object_name          = "gkemem"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/memory/limit_utilization\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "300s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 0.8
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
  },
  Gke_containers_restarts = {
    name                = "GKE Container Restarts - Google GKE Container - GKE Container Restarts"
    object_name          = "gkerestarts"
    alert_severity       = "WARNING"     
    combiner            = "OR"
    filter              = "metric.type=\"kubernetes.io/container/restart_count\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "120s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_SUM"
    alignment_period    = "120s"
    per_series_aligner  = "ALIGN_RATE"
    threshold           = 3
    trigger_count       = 1
    group_by_fields     = ["resource.label.container_name"]
    api_key             = ""
    enabled             = true
   },
 Custom_API_internal_error = {
    name                = "Custom API internal error - Unexcepted error was raised during the invocation of a custom API"
    object_name          = "custapierr"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/ssvods_Sit_Custom_API_internal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = true
  },
   Pipeline_api_internal_error = {
    name                = "Pipeline application fatal error - Unexpected error has caused the Unity 2.0 ETL pipelines to fail"
    object_name          = "pipapiinterr"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"logging.googleapis.com/user/ssvods_Sit_Pipeline_application_fatal_error\" AND resource.type=\"k8s_container\" AND resource.label.namespace_name=has_substring(\""
    duration            = "60s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= "REDUCE_COUNT"
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_DELTA"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = ["metric.labels.log","resource.label.namespace_name"]
    api_key             = ""
    enabled             = true
  },
   Sql_server_is_down = {
    name                = "CloudSQL is Down - Unexpected error caused Cloud SQL to be down"
    object_name          = "sql-server-is-down"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "0s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
  Sql_server_available_for_failover = {
    name                = "CloudSQL is Down - Database Server failover unavaliable"
    object_name          = "sql-failover-is-down"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/up\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
  Sql_server_uptime = {
    name                = "CloudSQL is Down - Database Server down for more than 5 min"
    object_name          = "sql-uptime"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/uptime\" resource.type=\"cloudsql_database\" AND resource.label.database_id=has_substring(\""
    duration            = "300s"
    comparison          = "COMPARISON_LT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_MEAN"
    threshold           = 1
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  },
    Sql_server_state = {
    name                = "CloudSQL failed - Database Server is in state failed for 3 minutes"
    object_name          = "sql-state"
    alert_severity       = "WARNING"
    combiner            = "OR"
    filter              = "metric.type=\"cloudsql.googleapis.com/database/instance_state\" resource.type=\"cloudsql_database\" AND metric.label.state=\"FAILED\" AND resource.label.database_id=has_substring(\""
    duration            = "180s"
    comparison          = "COMPARISON_GT"
    cross_series_reducer= ""
    alignment_period    = "60s"
    per_series_aligner  = "ALIGN_COUNT_TRUE"
    threshold           = 0
    trigger_count       = 1
    group_by_fields     = []
    api_key             = ""
    enabled             = true
  }
}