{{- $target := default "GCP" .Values.target -}}
{{- if .Values.ingress.enabled }}
{{- $replicaCount := .Values.instances | int -}}
{{- range $ord := until $replicaCount -}}
apiVersion: v1
kind: Service
metadata:
  name: lb-{{ include "app.name" $ }}-{{ $ord }}
  {{- if eq $target "GCP" }}
  annotations:
    cloud.google.com/load-balancer-type: "Internal"
  {{- end }}  
  {{- if eq $target "ALI" }}
  annotations:
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-address-type: "intranet"
    {{- if $.Values.services.loadbalancer.switch }}    
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-vswitch-id: {{ $.Values.services.loadbalancer.switch | quote }}
    {{- end }}
    {{- if $.Values.services.loadbalancer.address }}    
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-ip: {{ $.Values.services.loadbalancer.address.kafka_0 | quote }}
    {{- end }}
  {{- end }}  
  labels:
{{- include "i15n-helmchart.labels" $ | nindent 4 }}  
spec:
  {{- if eq $target "ALI" }}
  type: LoadBalancer
  {{- else }}
  type: ClusterIP
  {{- end }}   
  selector: 
    {{- include "app.selectorLabels" $ | nindent 4 }}
    statefulset.kubernetes.io/pod-name: kafka-{{ $ord }}
  ports:
  - name: external
    port: {{ $.Values.servicePorts.external.port }}
    protocol: {{ $.Values.servicePorts.external.protocol }}
    targetPort: external
---
{{- end -}} 
{{- end -}} 
{{- if and .Values.services (hasKey .Values.services "loadbalancer") (get .Values.services "loadbalancer") }}
apiVersion: v1
kind: Service
metadata:
  name: lb-static-{{ include "app.name" $ }}-{{ $ord }}
  {{- if eq $target "GCP" }}
  annotations:
    cloud.google.com/load-balancer-type: "Internal"
  {{- end }}  
  {{- if eq $target "ALI" }}
  annotations:
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-address-type: "intranet"
    {{- if $.Values.services.loadbalancer.switch }}    
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-vswitch-id: {{ $.Values.services.loadbalancer.switch | quote }}
    {{- end }}
    {{- if $.Values.services.loadbalancer.address }}    
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-ip: {{ $.Values.services.loadbalancer.address.kafka_0 | quote }}
    {{- end }}
  {{- end }}  
  labels:
{{- include "i15n-helmchart.labels" $ | nindent 4 }}  
spec:
  type: LoadBalancer
  {{- if $.Values.services.loadbalancer.address }}
  loadBalancerIP: {{ index $.Values.services.loadbalancer.address (printf "kafka_%d" $ord | default "kafka_0") | quote }}
  {{- end }} 
  selector: 
    {{- include "app.selectorLabels" $ | nindent 4 }}
    statefulset.kubernetes.io/pod-name: kafka-{{ $ord }}
  ports:
  - name: external
    port: {{ $.Values.servicePorts.external.port }}
    protocol: {{ $.Values.servicePorts.external.protocol }}
    targetPort: external
---
{{- end -}} 