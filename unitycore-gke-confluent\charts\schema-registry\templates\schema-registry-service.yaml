{{- $target := default "GCP" .Values.target -}}
{{- if .Values.ingress.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: lb-{{ include "app.name" . }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  {{- if eq $target "GCP" }}
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/app-protocols: '{"schema-registry":"HTTPS"}'
  {{- end }}
  {{- if eq $target "ALI" }}
  annotations:
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-address-type: "intranet"
    {{- if $.Values.services.loadbalancer.switch }}
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-vswitch-id: {{ $.Values.services.loadbalancer.switch | quote }}
    {{- end }}
    {{- if $.Values.services.loadbalancer.address }}
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-ip: {{ $.Values.services.loadbalancer.address.schema_registry_0 | quote }}
    {{- end }}
  {{- end }}
spec:
  ports:
  - name: schema-registry
    port: {{ .Values.servicePorts.external.port }}
    protocol: TCP
    targetPort: client
  selector:
    {{- include "app.selectorLabels" . | nindent 4 }}
  {{- if eq $target "ALI" }}
  type: LoadBalancer
  {{- else }}
  type: ClusterIP
  {{- end }}
{{- end }}
{{- if and .Values.services (hasKey .Values.services "loadbalancer") (get .Values.services "loadbalancer") (hasKey (get .Values.services.loadbalancer) "address") (get .Values.services.loadbalancer.address) }}
---
apiVersion: v1
kind: Service
metadata:
  name: lb-static-{{ include "app.name" . }}
  labels:
    {{- include "i15n-helmchart.labels" . | nindent 4 }}
  {{- if eq $target "GCP" }}
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/app-protocols: '{"schema-registry":"HTTPS"}'
  {{- end }}
  {{- if eq $target "ALI" }}
  annotations:
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-address-type: "intranet"
    {{- if $.Values.services.loadbalancer.switch }}
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-vswitch-id: {{ $.Values.services.loadbalancer.switch | quote }}
    {{- end }}
    {{- if $.Values.services.loadbalancer.address }}
    service.beta.kubernetes.io/alibaba-cloud-loadbalancer-ip: {{ $.Values.services.loadbalancer.address.schema_registry_0 | quote }}
    {{- end }}
  {{- end }}
spec:
  ports:
    - name: schema-registry
      port: {{ .Values.servicePorts.external.port }}
      protocol: TCP
      targetPort: client
    selector:
      {{- include "app.selectorLabels" . | nindent 4 }}
    type: LoadBalancer
    loadBalancerIP: {{ index $.Values.services.loadbalancer.address (printf "schema_registry_%d" .Values.replicaIndex | default "schema_registry_0") | quote }}
{{- end }}
